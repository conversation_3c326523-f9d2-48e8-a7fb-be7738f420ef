{'label': 'document', 'content': '中兴通讯vBRAS（V2.00.10） 配置指导书_20231213_CN', 'id': '728161'}
{'label': 'text', 'content': '文档说明\nV6000系列产品是中兴通讯针对城域网边缘虚拟化场景推出NFV产品形态。我司vBRAS方案主要有两种，一是转控分离，转发面纯硬件架构，即ZXR10 V6000 vBRAS作为vBRAS控制面，转发面采用基于NP的转发设备ZXR10 M6000-S。二是转控分离，虚实共管架构，即ZXR10 V6000 vBRAS作为vBRAS控制面，转发面采用基于NP的转发设备ZXR10 M6000-S和基于X86架构的ZXR10 V6000 vRouter产品。我司的v6000产品按照ETSI NFV标准架构设计，如下图所示：\n完整的vBRAS方案部署还需要虚拟化平台、MANO、EMS网管、硬件服务器等配套设施。\n本文档为中兴通讯vBRAS的配置指导，采用模块化的方式，列出了vBRAS配置的所有部件的配置指导。\n表 1-1 vBRAS配置各部件\n| 系统构成 | 模块名称 | 对应的产品/条目 | 配置原则 | 对应章节 |\n|:-|:-|:-|:-|:-|\n| vBRAS-CP | CP | ZXR10 V6000 vBRAS | 必配 | 第3章 |\n| vBRAS-CP | CDB | ZXR10 V6000 vIPDirector CDB | 选配 | 第3章 |\n| vBRAS-CP | VNFM-Plugin | V6000 vIPDirector standard plug-in | 必配 | 第3章 |\n| License Server/Client | License Server/Client | V6000 vIPDirector License Server/Client | 选配 | 第4章 |\n| Hypervisor | 虚拟化云平台(Hypervisor+VIM) | TECS | 选配建议采用我司TECS，若客户提供别的虚拟化平台，请和产品线沟通。 | 第5章 |\n| MANO | MANO的NFVO和VNFM | Cloudstudio | 选配 | 第6章 |\n| EMS | EMS网管 | ZENIC ONE | 选配 | 第7章 |\n| vBRAS-UP | pUP | ZXR10 M6000-S | 必配 | 第8章 |\n| vBRAS-UP | vUP | ZXR10 V6000 vRouter | 选配 | 第8章 |\n| 资源池服务器资源 | 服务器 | ZXCLOUD R5300 G4 | 选配，服务器客户可自采 | 第9章 |\n', 'id': '728161_1'}
{'label': 'text', 'content': 'vBRAS-CP配置\nvBRAS-CP包括CP、CDB、VNFM-Plugin模块。', 'id': '728161_2'}
{'label': 'text', 'content': 'vBRAS-CP配置\nCP配置\nCP对应的产品型号是ZXR10 V6000 vBRAS，其架构如下图所示：\n配置条目包括各功能组件、接口以及功能和性能的license。为了简化配置，CP设置了软件套装，包含了相应数量的条目，可直接选择套装进行配置。\n', 'id': '728161_3'}
{'label': 'table', 'content': 'vBRAS-CP配置\nCP配置\n移动、电信市场按照集采的配置要求进行配置，详见表 3-1。\n表 3-1 移动、电信、联通市场CP软件条目配置指导\n| | 移动 | 电信 | 联通 |\n|:-|:-|:-|:-|\n| 销售条目 | V6000 vBRAS 10M会话软件套装 | V6000 vBRAS CP/vUP软件套装 | 未集采vBRAS，销售通用模板内的散件，按需配置 |\n| 销售条目 | V6000 vBRAS 10M会话扩容授权 | vBRAS CP/vUP 20万CT云用户license（主） | 未集采vBRAS，销售通用模板内的散件，按需配置 |\n| 销售条目 | ZTE MANO软件套装 | vBRAS CP/vUP 20万CT云用户license（备） | 未集采vBRAS，销售通用模板内的散件，按需配置 |\n| 销售条目 | —— | vBRAS CP/vUP 1万CT云用户license（主） | 未集采vBRAS，销售通用模板内的散件，按需配置 |\n| 销售条目 | —— | vBRAS CP/vUP 1万CT云用户license（备） | 未集采vBRAS，销售通用模板内的散件，按需配置 |\n| ECC配置路径 | 承载网-IP网络产品-增强型电信级业务路由器-V6000电信级虚拟路由器-ZXR10 V6000 vBRAS电信级虚拟路由器 中国移动 | 承载网-IP网络产品-增强型电信级业务路由器-V6000电信级虚拟路由器-ZXR10 V6000 vBRAS电信级虚拟路由器 中国电信 | 承载网-IP网络产品-增强型电信级业务路由器-V6000电信级虚拟路由器-ZXR10 V6000 vBRAS电信级虚拟路由器、承载网-IP网络产品-增强型电信级业务路由器-V6000电信级虚拟路由器-ZXR10 V6000 vIPDirector 管理编排器 |\n', 'id': '728161_4'}
{'label': 'table', 'content': 'vBRAS-CP配置\nCP配置\n表 3-2 CP软件条目散件配置指导\n| 代码 | 描述 | 配置原则 |\n|:-|:-|:-|\n| vBRAS-SW-v2.00.10-Elite-p | vBRAS系统软件v2.00.10-精英永久版（含1个vBRAS-Framework，2个vBRAS-MP，1个vBRAS-ARU，3个vBRAS-PP-2A，2个vBRAS-LB，1个vBRAS-VeVnfm-IF，1个vBRAS-CU-IF，2个vBRAS-256K-LCS） | 该套装已包含512K会话许可，如需达到1M用户，需另配2个256K用户许可。如需配置超过1M会话时，除需增加会话许可，还需另行增加PP-2A模块数量和CDB。CDB、网管接口、其他功能license如需要，请另行配置。PP-2A模块数量参考表 8-9 。 |\n| vBRAS-Framework-v2.00.10 | vBRAS基础平台软件v2.00.10 | vBRAS系统软件套装中已包括，仅适用于不配置软件套装时配置，不建议单独配置。每系统配置1套。 |\n| vBRAS-MP | vBRAS-MP组件 | vBRAS系统软件套装中已包括，仅适用于不配置软件套装时配置，不建议单独配置。每系统配置2套。 |\n| vBRAS-PP-2A | vBRAS-PP-2A组件 | 2A型vBRAS-PP组件，每PP-2A最大承载1M session，适用于1M session及以上的vBRAS-C系统 |\n| vBRAS-LB | vBRAS-LB组件 | vBRAS系统软件套装中已包括，仅适用于不配置软件套装时配置，不建议单独配置。每系统配置2套。 |\n| vBRAS-ARU | vBRAS-ARU组件 | vBRAS系统软件套装中已包括，仅适用于不配置软件套装时配置，不建议单独配置。每系统配置1套。 |\n| vBRAS-VeVnfm-IF | vBRAS与VNFM的接口 | vBRAS系统软件套装中已包括，仅适用于不配置软件套装时配置，不建议单独配置。每系统配置1套。 |\n| vBRAS-CU-IF | vBRAS-C与vBRAS-U之间接口 | vBRAS系统软件套装中已包括，仅适用于不配置软件套装时配置，不建议单独配置。每系统配置1套。 |\n| vBRAS-EMS-IF | vBRAS与网管的接口 | 选配。软件套装中不包含，如需与网管对接，需另外配置。每系统配置1套。 |\n| vBRAS-C hotstandby-LCS | vBRAS-C备份功能许可证 | 选配。每系统配置1套。 |\n| vBRAS-U hotstandby-LCS | vBRAS-U备份功能许可证 | 选配。每系统配置1套。 |\n| vBRAS-PPPoE-LCS | vBRAS PPPOE功能许可证 | 选配。每系统配置1套。 |\n| vBRAS-IPoE-LCS | vBRAS IPOE功能许可证 | 选配。每系统配置1套。 |\n| vBRAS-IP Host-LCS | vBRAS IP Host功能许可证 | 选配。每系统配置1套。 |\n| vBRAS-STO-LCS | 智能流量经营STO功能许可证（即智能提速） | 选配。每系统配置1套。 |\n| vBRAS-L2TP-LCS | L2TP功能许可证 | 选配。每系统配置1套。 |\n| vBRAS-ICTPIPE-LCS | ICTPIPE功能许可证（即特通） | 选配。每系统配置1套。 |\n| vBRAS-U number-1-LCS | 1台vBRAS-U的数量许可证 | 必配。按照用户实际配置vBRAS-U的数量叠加。 |\n| vBRAS-L2TP-1K-LCS | vBRAS L2TP 1K会话数许可证 | 选配。需配合基础业务会话License使用 |\n| vBRAS-STO-1K-LCS | vBRAS STO 1K用户数许可证 | 选配。按照用户实际需求选择，可叠加组合。 |\n| vBRAS-CGNv4-LCS | vBRAS NAT44功能许可证 | 选配。每系统配置1套。 |\n| vBRAS-1K-LCS | 1K基础业务会话数许可证 | 选配。含PPPoE/IPoE/IPHost/L2TP的PPP会话（L2TP用户需额外配置vBRAS-L2TP-1K-LCS）按照用户实际需求选择，可叠加组合，注意：软件套装中包含一定数目会话许可，如不能满足用户需求需另外配置。 |\n| vBRAS-32K-LCS | 32K基础业务会话数许可证 | 选配。含PPPoE/IPoE/IPHost/L2TP的PPP会话（L2TP用户需额外配置vBRAS-L2TP-1K-LCS）按照用户实际需求选择，可叠加组合，注意：软件套装中包含一定数目会话许可，如不能满足用户需求需另外配置。 |\n| vBRAS-64K-LCS | 64K基础业务会话数许可证 | 选配。含PPPoE/IPoE/IPHost/L2TP的PPP会话（L2TP用户需额外配置vBRAS-L2TP-1K-LCS）按照用户实际需求选择，可叠加组合，注意：软件套装中包含一定数目会话许可，如不能满足用户需求需另外配置。 |\n| vBRAS-256K-LCS | 256K基础业务会话数许可证 | 选配。含PPPoE/IPoE/IPHost/L2TP的PPP会话（L2TP用户需额外配置vBRAS-L2TP-1K-LCS）按照用户实际需求选择，可叠加组合，注意：软件套装中包含一定数目会话许可，如不能满足用户需求需另外配置。 |\n| vBRAS-1M-LCS | 1M基础业务会话数许可证 | 选配。含PPPoE/IPoE/IPHost/L2TP的PPP会话（L2TP用户需额外配置vBRAS-L2TP-1K-LCS）按照用户实际需求选择，可叠加组合，注意：软件套装中包含一定数目会话许可，如不能满足用户需求需另外配置。 |\n| vBRAS-10M-LCS | 10M基础业务会话数许可证 | 选配。含PPPoE/IPoE/IPHost/L2TP的PPP会话（L2TP用户需额外配置vBRAS-L2TP-1K-LCS）按照用户实际需求选择，可叠加组合，注意：软件套装中包含一定数目会话许可，如不能满足用户需求需另外配置。 |\n', 'id': '728161_5'}
{'label': 'text', 'content': 'vBRAS-CP配置\nCDB配置\n在以下两种场景下必须增加部署高性能云数据库CDB（Cloud Database）：\n部署vBRAS-CP双系统备份\n单个vBRAS-CP实例承载并发会话数＞1M\n一套CDB内包含2个DBLB模块（主备冗余保护）和若干DB模块（数量≥2且为偶数，DB模块成对1:1冗余保护），CDB内模块均部署在VM之上。\n对于vBRAS的应用场景，每个DB模块最大承载20M并发会话数，因此≤20M以下的vBRAS应用场景，CDB只需部署2个DBLB和2个DB模块即可。\n移动、电信市场按照集采的配置要求进行配置，详见表 3-1。下面介绍CDB散件配置：\nCDB散件在CCG系统的配置路径：承载网-IP网络产品-增强型电信级业务路由器-V6000电信级虚拟路由器-ZXR10 V6000 vIPDirector管理编排器。\n表3-3 CDB软件条目配置指导\n| 代码 | 描述 | 配置原则 |\n|:-|:-|:-|\n| vIPD-CDB-PKG-vBRAS | vIPDirector CDB软件套装，用于vBRAS部署场景，包含2个CDB DB模块，2个DBLB模块，每个DB模块承载20M并发会话数，1:1冗余备份 | 选配，部署vBRAS-C双系统热备或单个vBRAS-C>1M session时必配时必配。每vBRAS-C系统配一套 |\n| vIPD-CDB-DB | vIPDirector CDB的DB组件 | 选配，CDB软件套装中已包含，无须单独配置 |\n| vIPD-CDB-DBLB | vIPDirector CDB的DBLB组件 | 选配，CDB软件套装中已包含，无须单独配置 |\n', 'id': '728161_6'}
{'label': 'text', 'content': 'vBRAS-CP配置\nVNFM插件配置\nVNFM插件vIPD-Plugin-Standard必配。在配置MANO时，VNFM插件用于vBRAS-CP与MANO的VNFM连；不配置MANO时，VNFM插件有助于减少vBRAS-CP的开通配置工作量并防止误配。1套VNFM插件可同时接入多个V6000系统软件，VNFM插件的数量等于MANO VNFM的数量。注意：当MANO的VNFM配置1:1冗余时，也只需配置1套VNFM插件。\n移动、电信市场按照集采的配置要求进行配置，详见表 3-1。下面介绍VNFM插件的散件配置：\nVNFM插件在CCG系统的配置路径：承载网-IP网络产品-增强型电信级业务路由器-V6000电信级虚拟路由器-ZXR10 V6000 vIPDirector管理编排器。\n表3-4 VNFM插件软件条目配置指导表\n| 代码 | 描述 | 配置原则 |\n|:-|:-|:-|\n| vIPD-Plugin-Standard | vIPDirector 标准版插件 | 必配。 |\n', 'id': '728161_7'}
{'label': 'text', 'content': 'License server/client配置\nLicense server用于管理V6000的所有功能/性能License，安装在Windows操作系统之上，可直接装在宿主机上也可以部署在VM上。License client是提供客户查看、管理license界面的客户端软件，安装在客户的任何一台PC机上，配置1套可多次安装。\n移动、电信市场按照集采的配置要求进行配置，详见表 3-1。下面介绍License server/client的散件配置：\nLicense server/client在CCG系统的配置路径：承载网-IP网络产品-增强型电信级业务路由器-V6000电信级虚拟路由器-ZXR10 V6000 vIPDirector管理编排器。\n表 4-1 License server/client软件条目配置指导表\n| 代码 | 描述 | 配置原则 |\n|:-|:-|:-|\n| vIPD-LCS -Server | vIPDirector License Server软件，用于V6000 vBRAS/vRouter的license管理 | 选配，每运营商/每项目只需配置1套 |\n| vIPD-LCS-Client | vIPDirector License Client软件，用于V6000 vBRAS/vRouter的license管理 | 选配，每运营商/每项目只需配置1套 |\n', 'id': '728161_8'}
{'label': 'text', 'content': '虚拟化平台TECS配置\n虚拟化云平台负责对计算、存储和NFVI进行集中调度和管理，为用户快速构建云环境基础，可采用我司自研的TECS产品。\n注意：电信vBRAS项目TECS配置原则如下。（其他项目具体TECS配置及版本确认请和产品线沟通。）\n2023年电信vBRAS报价清单表中“vBRAS软件-CP/vUP基础软件包”条目所包含的CloudOS基础功能软件，即指我司TECS产品，为保障vBRAS顺利开局，合同申请必须配置TECS软件。\nTECS 中国电信模板调用路径：\n配置原则：\n', 'id': '728161_9'}
{'label': 'table', 'content': '虚拟化平台TECS配置\n| 销售条目 | 配置原则 |\n|:-|:-|\n| TECS基本软件 | 新建CP场景配置，每个资源池配置1个 |\n| TECS虚机软件 | 配置数量=配置CPU的核数 |\n| TECS Director软件基础版 | 新建CP场景配置，每个省配置1个 |\n| TECS Director软件license | 配置数量=配置CPU的个数，如需做网络设备的PIM管理（如交换机、路由器、防火墙、负载均衡等），1个网络安全设备折算为1个CPU（电信vBRAS项目需要PIM管理）。 |\n| TECS Director容灾软件 | 需要部署Director容灾站点时配置，每个容灾站点配置1套 |\n| TECS Inspector软件 | 新建CP场景配置，每个资源池配置1个 |\n| AIC工具 | 新建CP场景配置，每个省部署1个 |\n', 'id': '728161_10'}
{'label': 'text', 'content': '虚拟化平台TECS配置\n举例：\n1、新建CP场景：2个资源池，一个资源池部署6台CP/vUP服务器+3台智能运维工具服务器，一个资源池部署6台CP/vUP服务器+3台TECS Director 服务器。Director无容灾，每个资源池内还各有5台交换机、2台DCGW、2台防火墙，Director做DC内所有设备的PIM管理。每台服务器配置CPU型号为6230N（20核）。\n', 'id': '728161_11'}
{'label': 'table', 'content': '虚拟化平台TECS配置\n| 销售条目 | 配置数量 |\n|:-|:-|\n| TECS基本软件 | 1*2=2 |\n| TECS虚机软件 | （6+3+6+3）*2*20=720 |\n| TECS Director软件基础版 | 1 |\n| TECS Director软件license | （6+3+6+3）*2+（5+2+2）*2=54 |\n| TECS Director容灾软件 | 0 |\n| TECS Inspector软件 | 1*2=2 |\n| AIC工具 | 1 |\n', 'id': '728161_12'}
{'label': 'text', 'content': '虚拟化平台TECS配置\n2、服务器扩容场景：2个资源池共扩容4台服务器\n', 'id': '728161_13'}
{'label': 'table', 'content': '虚拟化平台TECS配置\n| 销售条目 | 配置数量 |\n|:-|:-|\n| TECS虚机软件 | 4*2*20=160 |\n| TECS Director软件license | 4*2=8 |\n', 'id': '728161_14'}
{'label': 'text', 'content': 'MANO的NFVO和VNFM配置\nMANO中NFVO与VNFM主要负责管理NFVI提供的资源以及各VNF的资源分配和VNF之间的业务编排，采用我司自研的CloudStudio产品。 \n对于vBRAS的部署，MANO仅在vBRAS-CP安装阶段可以起到降低配置工作量的效果，vBRAS系统运行阶段无需MANO的参与，部署MANO至少需要3台服务器，且必须部署云盘存储，因此自建vBRAS场景建议不使用MANO，vBRAS-CP安装阶段采用手动部署方式。如果不部署MANO，VNFM插件也必配，可有助于减少vBRAS-CP的开通配置工作量并防止误配。 \n如客户强制要求部署MANO，现阶段我司vBRAS系统可对接的VNFM只能是我司自己的产品，NFVO可以解耦。如部署MANO，VNFM plug-in插件必配。MANO的具体配置可联系MANO产品线提供相应支撑。 \nCloudStudio在CCG系统配置路径：云及核心网-管理和编排-核心网-管理和编排-资源编排器，具体配置条目请参考MANO配置指导书《CloudStudio软件配置说明》，TSM链接地址： \nhttp://tsm.zte.com.cn/tsm/FileCenter/File.aspx?Mode=read&FileID=30628158', 'id': '728161_15'}
{'label': 'text', 'content': 'EMS网管配置\nEMS业务网管APP采用ZENIC ONE产品，主要负责管理控制面和转发面，完成相关的运维功能，包括网元管理、拓扑展示、告警性能统计等，ZENIC ONE可根据实际需求选配。 \n电信市场按照集采的配置要求进行配置，其他市场网管详细配置及规格请参考配置指导书《ZENIC ONE R22 & ElasticNet UME R22 裸机部署配置指导书》、《ZENIC ONE R22 & ElasticNet UME R22 虚机集群部署配置指导书》，TSM链接地址： \nhttps://tsm.zte.com.cn/TSM/FileCenter/File.aspx?Mode=read&FileID=30670941 \nEMS在CCG系统配置路径：承载网-IP网络产品-弹性网络管理和控制软件产品-管控融合系统-ZENIC ONE R22。', 'id': '728161_16'}
{'label': 'text', 'content': 'vBRAS-UP配置\n我司vBRAS-UP产品有两种，一是基于NP的转发设备ZXR10 M6000-S，称为pUP。二是基于X86架构的ZXR10 V6000 vRouter产品，称为vUP。', 'id': '728161_17'}
{'label': 'text', 'content': 'vBRAS-UP配置\npUP配置\npUP 产品为ZXR10 M6000-S，推荐采用V5.00.10及以上版本，配置详见ZXR10 M6000-S的配置指导书。 \nhttps://tsm.zte.com.cn/tsm/FileCenter/File.aspx?Mode=read&FileID=30359539', 'id': '728161_18'}
{'label': 'text', 'content': 'vBRAS-UP配置\nvUP配置\nvUP 产品为ZXR10 V6000 vRouter。目前采用集中式架构，转发面和控制面部署在同一个虚机上，最大支持2个虚机的部署，称为ZXR10 V6000 vRouter Standard版。其架构如下图所示：\n电信市场按照集采的配置要求进行配置，详见表 3-1。下面介绍vUP散件配置：\nV6000 vBRAS在CCG系统的配置路径：承载网-IP网络产品-增强型电信级业务路由器-V6000电信级虚拟路由器-ZXR10 V6000 vRouter虚拟路由器。\nvUP软件条目的配置指导见下：\n表 8-1 vUP软件条目配置指导\n| 代码 | 产品描述 | 配置说明 |\n|:-|:-|:-|\n| vRouter-STD-v2.00.10-M2 | vRouter-STD V2.00.10 M2套装，主控冗余。推荐并发session数≤512K时配置该套装。含1K并发会话许可。如需超过1K会话，需另配会话许可。 | 必配。2台服务器最大可以部署2套vRouter-STD V2.00.10 M2套装。 |\n| vRouter 1K sessions license | vRouter 1K 基础业务会话数 | 选配。按照用户实际需求选择，可叠加组合，注意：软件套装中包含一定数目会话许可，如不能满足用户需求需另外配置。 |\n| vRouter 32K sessions license | vRouter 32K 基础业务会话数 | 选配。按照用户实际需求选择，可叠加组合，注意：软件套装中包含一定数目会话许可，如不能满足用户需求需另外配置。 |\n| vRouter 64K sessions license | vRouter 64K 基础业务会话数 | 选配。按照用户实际需求选择，可叠加组合，注意：软件套装中包含一定数目会话许可，如不能满足用户需求需另外配置。 |\n| vRouter 256K sessions license | vRouter 256K 基础业务会话数 | 选配。按照用户实际需求选择，可叠加组合，注意：软件套装中包含一定数目会话许可，如不能满足用户需求需另外配置。 |\n| vRouter 1M sessions license | vRouter 1M 基础业务会话数 | 选配。按照用户实际需求选择，可叠加组合，注意：软件套装中包含一定数目会话许可，如不能满足用户需求需另外配置。 |\n| vRouter 2M sessions license | vRouter 2M 基础业务会话数 | 选配。按照用户实际需求选择，可叠加组合，注意：软件套装中包含一定数目会话许可，如不能满足用户需求需另外配置。 |\n| vRouter 4M sessions license | vRouter 4M 基础业务会话数 | 选配。按照用户实际需求选择，可叠加组合，注意：软件套装中包含一定数目会话许可，如不能满足用户需求需另外配置。 |\n| vRouter 10M sessions license | vRouter 10M 基础业务会话数 | 选配。按照用户实际需求选择，可叠加组合，注意：软件套装中包含一定数目会话许可，如不能满足用户需求需另外配置。 |\n', 'id': '728161_19'}
{'label': 'text', 'content': '服务器配置\n服务器类型及注意事项\nvBRAS的部署除NP形式的vBRAS-pUP （ZXR10 M6000-S）外，其他所有部件均部署在DC内的通用X86服务器之上，DC内基础设施环境已按照服务器的功能不同划分为计算域、存储域、和管理域三个区域。每个区域包含的服务器类型如下：\n表 9-1 计算域、存储域、和管理域服务器类型\n| 区域 | 服务器类型 | 安装的产品/部件 |\n|:-|:-|:-|\n| 计算域 | 计算节点 | CP（V6000 vBRAS）、TECS的计算组件（如果配我司TECS的话）；可选：CDB（vIPDirector CDB）、License Server；vUP（V6000 vRouter） |\n| 计算域 | MANO节点 | MANO的VNFM和NFVO（CloudStudio）、VNFM插件（vIPDirector standard plug-in） |\n| 计算域 | 网管节点 | EMS网管（ZENIC ONE）、可选：License Server |\n| 管理域 | 控制节点（管理计算合一） | TECS的控制组件 |\n| 存储域 | 存储节点 | 自建资源池目前推荐本地存储方式部署，不单独配置存储节点。 |\n如上图所示，虚线内节点非必需，几项注意点如下：\n仅当部署vBRAS-CP双系统热备或单个vBRAS-CP实例承载并发会话数＞1M时才需配置CDB；\n出于可靠性的考虑，同一网元内相同类型的组件建议分布在不同的服务器之上；\n出于系统稳定性的考虑，CP的MP组件不能与PP和ARU组件部署在一台服务器上；\nvBRAS-CP所有模块都不能跨物理CPU绑核。（如同一台服务器内2个物理CPU，一个模块VM只能部署在1个物理CPU之上，否则性能、稳定性都会受到严重影响）；\n出于可靠性的考虑，每台服务器的硬盘建议冗余备份，网口做bond部署；\nLicense Server既可以部署在VM之上，也可部署在宿主机之上，可灵活选择安装在计算节点之上还是网管节点之上；\n自建vBRAS场景目前推荐采用本地硬盘部署，不单独部署存储节点。\n当前有些云平台在部署VM时允许超分部署。比如部署一个VM需要的资源是：6vCPU、12G内存、60G硬盘，但如果部署1:6超分，则只需要给这个VM分配1vCPU、2G内存，10G硬盘的资源。超分部署看似非常节省服务器物理资源，但对VM上运行的APP性能影响很大。通常情况下，超分部署只适用于一些主机类的应用，如云桌面，但对于像vBRAS这种系统级的电信网元应用，超分部署会严重影响其性能，因此禁止采用超分部署模式。\n', 'id': '728161_20'}
{'label': 'text', 'content': '服务器配置\n各产品对服务器资源要求\n注意：本节表中所列“硬盘资源要求”是服务器给虚机提供的存储资源的需求，每个服务器本身还需要硬盘安装操作系统，因此建议系统盘和数据盘分开，要求必须配置RAID。建议硬盘偶数配置，便于配置RAID1或RAID10。', 'id': '728161_21'}
{'label': 'table', 'content': '服务器配置\n各产品对服务器资源要求\nCP对服务器资源要求\n表 9-2 CP Elite对服务器资源要求\n| 模块 | 模块 | 每VM硬件资源（CP每模块占一VM） | 每VM硬件资源（CP每模块占一VM） | 每VM硬件资源（CP每模块占一VM） | 每VM硬件资源（CP每模块占一VM） |\n|:-|:-|:-|:-|:-|:-|\n| 名称 | 数量 | vCPU | CPU主频（GHz） | 内存（GB） | 硬盘（GB） |\n| MP | 2 | 10 | ≥2.2 | 64 | 40 |\n| PP | Elite：3 | 10 | ≥2.2 | 48 | 40 |\n| LB | 2 | 16 | ≥2.2 | 64 | 40 |\n| ARU | 1 | 2 | ≥2.2 | 8 | 40 |\n', 'id': '728161_22'}
{'label': 'text', 'content': '服务器配置\n各产品对服务器资源要求\nCDB对服务器资源要求\nCDB内包含2个DBLB模块（主备冗余保护）和若干DB模块（数量≥2且为偶数），CDB内模块均部署在VM之上，每个DB模块承载20M并发会话数，1:1冗余备份，\n各模块对服务器资源需求如下：\n表 9-3 CDB各模块对服务器资源要求\n| 模块 | VM数量 | vCPU | CPU主频（GHz） | 内存（GB） | 硬盘（GB） |\n|:-|:-|-:|:-|-:|-:|\n| DB | ≥2且为偶数 | 10 | ≥2.2 | 160 | 80 |\n| DBLB | 2 | 10 | ≥2.2 | 64 | 80 |\n', 'id': '728161_23'}
{'label': 'table', 'content': '服务器配置\n各产品对服务器资源要求\nVNFM插件对服务器资源要求\n表 9-4 VNFM插件对服务器资源要求\n| 模块 | VM数量 | vCPU | CPU主频（GHz） | 内存（GB） | 硬盘（GB） |\n|:-|-:|-:|:-|-:|-:|\n| VNFM-Plugin | 1 | 2 | ≥2.2 | 4 | 40 |\n', 'id': '728161_24'}
{'label': 'text', 'content': '服务器配置\n各产品对服务器资源要求\nLicense Server对服务器资源要求\nLicense server装在Windows操作系统上，可直接装在宿主机上也可以部署在VM上。\n表 9-5 License Server宿主机安装对服务器资源要求\n| 模块 | CPU主频（GHz） | 内存（GB） | 硬盘（GB） |\n|:-|:-|-:|-:|\n| License Server | ≥2.0 | 4 | 10 |\n表 9-6 License Server VM安装对服务器资源要求\n| 模块 | 管理服务器数量 | VM数量 | vCPU | CPU主频（GHz） | 内存（GB） | 硬盘（GB） |\n|:-|:-|-:|-:|:-|-:|-:|\n| License Server | 1000台以内 | 1 | 4 | ≥2.0 | 4 | 10 |\n| License Server | 3000台以内 | 1 | 8 | ≥2.0 | 4 | 10 |\n', 'id': '728161_25'}
{'label': 'text', 'content': '服务器配置\n各产品对服务器资源要求\nTECS对服务器资源要求\nTECS计算组件需在每一台计算节点和MANO节点服务器上安装。包括CP、CDB、MANO服务器。 \nTECS管理（TECS Opentack）采用3节点集群部署，计算控制合一设计。 \n对服务器资源要求参见：《中兴通讯云基础设施产品硬件配置指导书》、《TECS软硬件兼容性列表》。 \nTSM链接地址： \nhttp://tsm.zte.com.cn/tsm/FileCenter/File.aspx?FileID=30552099', 'id': '728161_26'}
{'label': 'text', 'content': '服务器配置\n各产品对服务器资源要求\nMANO对服务器资源要求\n独立建设vBRAS场景建议不配备MANO，如客户强制要求部署MANO，可联系MANO产品线提供相应支撑。MANO对于服务器的资源要求参见《CloudStudio（V5.21.10.B6）售前配置指导原则》 \nTSM链接地址：http://tsm.zte.com.cn/tsm/FileCenter/File.aspx?Mode=read&FileID=30618638', 'id': '728161_27'}
{'label': 'text', 'content': '服务器配置\n各产品对服务器资源要求\nvUP对服务器资源要求\n表 9-7 vUP对服务器资源要求\n| 模块 | 模块 | 每VM硬件资源（CP每模块占一VM） | 每VM硬件资源（CP每模块占一VM） | 每VM硬件资源（CP每模块占一VM） | 每VM硬件资源（CP每模块占一VM） |\n|:-|:-|:-|:-|:-|:-|\n| 名称 | 数量 | vCPU | CPU主频（GHz） | 内存（GB） | 硬盘（GB） |\n| MP | 2 | 30 | ≥2.2 | 64 | 40 |\n| ARU | 1 | 2 | ≥2.2 | 8 | 40 |\n注意：\nvUP的MP组件和CP的MP组件不能共用，二者仅名称相同，是不同组件。\nvUP的ARU占用资源较小，可与CP的组件部署在同一台服务器之上，但不可与CP的ARU合并部署。\n', 'id': '728161_28'}
{'label': 'table', 'content': '服务器配置\n各服务器规格及数量需求\nvBRAS-CP&TECS管理（TECS Openstack）服务器规格\n表 9-8 vBRAS-CP&TECS管理服务器规格\n| CPU | 内存 | 硬盘 | 网卡 | 电源 |\n|:-|:-|:-|:-|:-|\n| CPU 主频：≥2.2GHz，每个CPU≥20物理核推荐：2个R5300 G4-CPU-6230N（20核） | ≥384GB | ≥6*960G（SSD SATA） | 3*双口10GE（两个10GE网卡同芯片类型）Intel 82599、Intel X710、Mellanox mlx4型号芯片的网卡 | DC:48V/AC:220V |\n', 'id': '728161_29'}
{'label': 'text', 'content': '服务器配置\n各服务器规格及数量需求\nvBRAS-CP&TECS管理（TECS Openstack）服务器数量计算方法\nvBRAS计算节点的部署，除NP形式的vBRAS-U （ZXR10 M6000-S）外，其他所有部件均部署在DC内的通用X86服务器之上。如表10-1所述计算节点安装的产品/部件包括：vBRAS-CP、TECS的计算组件（虚拟化平台用TECS时），可选组件包括：CDB、License Server。各组件的资源情况在9.2章节中已明确，此部分重点描述如何根据资源占用情况计算服务器台数的方法。\n出于可靠性的考虑，同一网元内相同类型的组件建议分布在不同的服务器之上；出于可靠性的考虑，CP的MP组件不能与PP和ARU组件共用一个服务器；因此，建议将MP模块和LB模块部署在两台服务器上，PP模块按需部署在另外的服务器上。DB模块反亲和部署，DBLB模块反亲和部署，DB和DBLB不允许和MP部署在一台服务器上。\n对于自建场景，PP模块（N+1）：1冗余部署，并且需满足任一PP模块所在的服务器故障后，其余服务器仍有资源承载所需会话。对于已有资源池的情况，建议PP模块全散列部署，或保障单服务器故障后仍有资源再起PP模块虚机。\n如需部署CDB，每套vBRAS-CP配置一套CDB。需2台服务器，每台服务器上各部署DB和DBLB各1个。\nTECS管理（TECS Openstack）服务器配置3台，采用管理、计算合一部署。\n参照表 9-8 vBRAS-CP&TECS管理服务器规格 中vBRAS-CP&TECS管理服务器规格，配置CPU （2个CPU，单个CPU 内核：20核）的服务器为例，包含 CPU Cores共20*2=40个，对应80个vCPU（每个物理CPU核对应2个vCPU），三台TECS管理服务器都需要给TECS管理组件各预留20个vCPU（每个物理核上各占10vCPU）。\n自建资源池目前推荐本地存储方式部署。以提供服务器的CPU选型：2*R5300 G4-CPU-6230N （单CPU:20核,2.3GHz主频）为例，\nvBRAS-CP各模块在服务器上部署示意图参考文件《vBRAS服务器部署方案》。vBRAS-CP&TECS管理服务器数量与承载会话数量对应关系见表9-9。\n表 9-9 vBRAS-CP&TECS管理服务器数量&pp模块数量与承载会话数量对应关系\n| 承载会话数量 | PP模块数量 | vBRAS-C&TECS管理（TECS Openstack）服务器数量 |\n|:-|-:|-:|\n| 1M | 3 | 5 |\n| 2M | 4 | 5 |\n| 3M | 5 | 5 |\n| 4M | 6 | 5 |\n| 5M | 7 | 6 |\n| 6M | 8 | 6 |\n| 7M | 10 | 6 |\n| 8M | 12 | 6 |\n| 9M | 13 | 7 |\n| 10M | 14 | 7 |\n| 11M | 15 | 7 |\n| 12M | 16 | 7 |\n| 13M | 18 | 7 |\n| 14M | 20 | 7 |\n| 15M | 21 | 8 |\n| 16M | 22 | 8 |\n| 17M | 23 | 8 |\n| 18M | 24 | 8 |\n| 19M | 25 | 8 |\n| 20M | 26 | 8 |\n', 'id': '728161_30'}
{'label': 'text', 'content': '服务器配置\n各服务器规格及数量需求\nMANO服务器规格及数量\nMANO国内商用部署需要至少3台服务器，并且需要部署在云存储环境下，因此独立建设vBRAS场景建议不配备MANO。如客户强制要求部署MANO，可联系MANO产品线提供相应支撑。', 'id': '728161_31'}
{'label': 'text', 'content': '服务器配置\n各服务器规格及数量需求\n网管服务器规格\n网管服务器非必配，可根据实际项目需求选配网管服务器规格。 \n电信市场按照集采的配置要求进行配置，其他市场网管服务器详细配置及规格请参考配置指导书《ZENIC ONE R22 & ElasticNet UME R22 裸机部署配置指导书》、《ZENIC ONE R22 & ElasticNet UME R22 虚机集群部署配置指导书》，TSM链接地址： \nhttps://tsm.zte.com.cn/TSM/FileCenter/File.aspx?Mode=read&FileID=30670941', 'id': '728161_32'}
{'label': 'text', 'content': '服务器配置\n各服务器规格及数量需求\nTECS Director服务器规格及数量\n选配。电信市场按照集采的配置要求进行配置。其他市场配置建议如下：\n建议采用3台独立的物理机方式进行部署，管理节点规模为1000个，省中心部署一套，可以纳管多个资源池。\n资源需求如下：\n表 9-10 TECS Director 资源需求\n| CPU | 内存 | 硬盘（GB） | 网卡 | 电源 |\n|:-|:-|:-|:-|:-|\n| CPU 主频：≥2.2GHz，（要求至少2*12CPU核，推荐R5300 G4-CPU-5218N、R5300 G4-CPU-6230N ） | ≥256GB | ≥2*480G+2*1.92T（SSD SATA） | 2对网卡（两个10GE网卡同芯片类型），至少4*10GE | DC:48V/AC:220V |\n注意：上表为TECS Director服务器最低配置。\n详细内容参见：《中兴通讯云基础设施产品硬件配置指导书》、《TECS软硬件兼容性列表》。\nTSM链接地址：\nhttp://tsm.zte.com.cn/tsm/FileCenter/File.aspx?FileID=30552099\n', 'id': '728161_33'}
{'label': 'text', 'content': '服务器配置\n各服务器规格及数量需求\nvUP服务器规格及数量\n表 9-11 vUP服务器规格\n| CPU | 内存 | 硬盘 | 网卡 | 电源 |\n|:-|:-|:-|:-|:-|\n| CPU 主频：≥2.2GHz，每个CPU≥20物理核推荐：2个R5300 G4-CPU-6230N（20核） | ≥384GB | ≥6*960G（SSD SATA） | 3*双口10GE（两个10GE网卡同芯片类型）Intel 82599、Intel X710、Mellanox mlx4型号芯片的网卡 | DC:48V/AC:220V |\n注意：\n1套vUP（含2个MP）可承载512K并发会话，2个MP分别部署在2台服务器之上。在服务器资源够的情况下，2台服务器可接入2套vUP，共1M并发会话。承载2M并发会话时，需4台服务器，以此类推。\n', 'id': '728161_34'}
{'label': 'text', 'content': '服务器配置\n服务器配置推荐\n服务器若采用我司自研服务器，CP及CDB服务器推荐选型ZXCLOUD R5300 G4 \n自研服务器CCG配置路径： \n算力及核心网-服务器及存储-IT产品及服务-机架服务器-模板名称：2023年vBRAS-R5300 G4', 'id': '728161_35'}
{'label': 'table', 'content': '附录\n表 10-1 自建资源池vBRAS项目服务器数量需求一览表（CP服务器以R5300 G4为例）\n| 节点 | 产品名称 | 承载会话数 | 服务器台数 | 备注 |\n|:-|:-|:-|:-|:-|\n| vBRAS-CP+TECS管理节点 | ZXR10\xa0V6000 vBRAS、TECS | 1~4M | 5台 | 必配，管理、计算合一部署；部署vBRAS-CP双系统备份或单个vBRAS-CP实例承载并发会话数＞1M，均需配置CDB。 |\n| vBRAS-CP+TECS管理节点 | ZXR10\xa0V6000 vBRAS、TECS | 5~8M | 6台 | 必配，管理、计算合一部署；部署vBRAS-CP双系统备份或单个vBRAS-CP实例承载并发会话数＞1M，均需配置CDB。 |\n| vBRAS-CP+TECS管理节点 | ZXR10\xa0V6000 vBRAS、TECS | 9~14M | 7台 | 必配，管理、计算合一部署；部署vBRAS-CP双系统备份或单个vBRAS-CP实例承载并发会话数＞1M，均需配置CDB。 |\n| vBRAS-CP+TECS管理节点 | ZXR10\xa0V6000 vBRAS、TECS | 15~20M | 8台 | 必配，管理、计算合一部署；部署vBRAS-CP双系统备份或单个vBRAS-CP实例承载并发会话数＞1M，均需配置CDB。 |\n| EMS | ZENIC\xa0ONE | ZENIC\xa0ONE | 视ZENIC ONE所提供的能力而定 | 选配 |\n| MANO | CloudStudio | CloudStudio | 至少3台 | 选配，自建资源池不建议部署。 |\n| TECS Director | TECS Director | TECS Director | 3台 | 选配，用于单个或多个资源池的统一管理。 |\n| vUP | ZXR10\xa0V6000 vRouter | ZXR10\xa0V6000 vRouter | 1M 会话需2台服务器 | 选配 |\n', 'id': '728161_36'}
