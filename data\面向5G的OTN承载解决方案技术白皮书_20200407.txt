{'label': 'document', 'content': '面向5G的OTN承载解决方案技术白皮书_20200407', 'id': '785760'}
{'label': 'table', 'content': '| |\n|:-|\n| |\n| |\n| |\n| |\n| |\n| |\n|-|\n', 'id': '785760_1'}
{'label': 'text', 'content': '概述\n近几年来，移动网络向5G迈进成为业界焦点。5G将渗透到未来社会的各个领域，以用户为中心构建全方位的信息生态系统，为用户提供极致的业务体验。ITU为5G定义了三大类应用场景：增强移动宽带（eMBB），海量机器类通信（mMTC）和超可靠低时延通信（uRLLC）。这些场景不再单纯地强调峰值传输速率，而是综合考虑八个关键能力技术指标：峰值速率、用户体验速率、频谱效率、移动性、时延、连接密度、网络能量效率和流量密度。不同应用场景下对网络有不同的技术要求。总的说来，5G技术在带宽、时延、连接数和高速移动性等方面有别于以往的无线通讯技术，性能指标将有极大的提高。\n随着应用场景逐渐清晰，标准制订在加速，技术研发不断取得突破，5G网络商用指日可待。5G无线网络建设需要有线承载网络支撑和配合，才能满足5G应用场景和关键能力要求，得到持续演进发展。\n5G发展给承载网带来挑战\n5G将有更宽的无线频谱，并采用massive MIMO、高阶QAM等技术提升无线空口带宽，结合高频段甚至可以达到几十Gbps，与4G网络相比，峰值带宽和用户体验带宽提升了数10倍，更加方便开展高清视频、VR/AR等eMBB业务，但也对承载网提出了10倍以上的带宽需求。\n5G时代，触觉互联网、自动驾驶、工业互联及自动化等业务会逐渐引入和普及，这些uRLLC业务要求极低的时延和极高的可靠性，现有的承载网设备以及组网方式必须进行优化，以降低时延，提升安全性，满足新型业务发展需求。\n5G网络对频率同步的要求与LTE一致，没有显著提高。但5G网络将采用eMBMS，eICIC，massive MIMO，COMP等新技术，对时间同步（相位同步）提出了更高的要求，相对于LTE +/-1.5微秒的同步要求，提高一个数量级。5G承载网络需要支持高精度时间同步。\n5G提出了网络切片，针对eMBB、mMTC和uRLLC等不同的业务对带宽、时延、服务质量等不同的要求，分配不同的网络资源，这就要求5G承载网提供网络切片能力，可以将不同业务所需的网络资源灵活动态地分配和释放，并进一步动态优化网络连接，降低整网成本，提升效益。5G承载网还需要提供相互隔离的管道来为多个租户和各个垂直行业分别提供服务。\n5G网络架构变化对承载网络的影响\n5G网络架构相对于4G有几个变化：\n核心网云化和虚拟化部署。5G核心网引入了NFV和SDN，不再沿袭传统的“烟囱”架构，而是采用统一的物理基础设施，在云数据中心间实现池化、虚拟化、容器化的资源共享。5G核心网控制面和用户面分离，用户面下沉，由原来的集中式演变成分散式，便于不同类型的业务在不同的层面终结，基于虚拟化技术将核心网物理实体分离成多个虚拟网元，分布在网络中，进行云化部署，地理位置上靠终端更近，可以带来时延的减小。\n无线接入网更多地采用C-RAN。3G/4G时代C-RAN在降低整体成本、无线协作化抗干扰、节能降耗、简化运维等方面已经体现出一些优势。5G阶段采用C-RAN架构更便于实现灵活的无线资源管理，便于实现功能灵活部署满足移动边缘计算的需求，便于软硬件解耦，进一步增强无线网的软件化能力。\n基站密度加大。为了尽可能获取更大的频谱带宽，5G网络通常部署于更高的频段，根据无线传播特性，频率越高，传播损耗越大，基站的覆盖能力也就越弱，需要增加数倍于4G的基站才能弥补。但5G无线网络采用空分复用等新技术，不但可成倍提升容量，还可使得覆盖不收缩或仅少量收缩，实际基站数量与4G网络相当或略有增加。另一方面，对于热点高容量地区，会采用超密集组网，对于盲点、弱覆盖场景，还会采用small cell增强覆盖。总体来说，5G的基站密度比4G大。\n5G网络架构的这些变化也给承载网带来了影响：核心网业务锚点下移，回传网更加扁平化；C-RAN架构带来更多的前传网络，前传网要满足低成本、灵活组网的需求；光纤进一步下移，需要部署更多的承载节点。\nC-RAN下的前传网技术方案\nC-RAN架构下前传网有多种传输技术选择，各有优缺点。\n裸纤直驱。该方式在BBU和RRU间无需传输设备，时延最低，部署最简单。但是耗费大量光纤资源，在5G阶段基站密度增加时，会遇到光纤资源不足的问题。另外裸纤方案是点到点直连，没有网络保护，不能给业务提供高可靠性。\n无源WDM。该方式采用无源合分波器将多路波长复用到一根光纤传输，可以节省宝贵的光纤资源，光器件引入的时延很小，无源设备不需加电，维护比较简单，设备成本也比较低。但是需要RRU和BBU出彩光接口，增加了一些无线设备的成本。在组环网或链型网时，由于中间节点的合分波器插损累积，光功率不足时传输距离会受限。没有线路的OAM和故障管理能力，一般不做保护。\nOTN。该方式采用OTN设备实现多个站点多路前传信号的复用和透明传输，可以节省光纤资源，支持光层和电层的性能和故障检测等OAM功能，并能提供网络保护，保障业务的高可靠。OTN是L0/L1的传输技术，天然具有大带宽低时延的特性，可以对所有的业务同时进行低延时传输。该方案不要求无线设备出彩光，减少了无线设备部署的麻烦，而且已建网络从非C-RAN架构向C-RAN架构迁移时，不需要替换无线设备的光接口。缺点是设备成本相对要高一些，需开发低成本方案。\nWDM PON。该方式采用星型组网，可以利用已经部署的PON网络接入层光纤资源，设备成本比较低。目前接入速率达到10G，适合small cell接入。相关技术和标准也在发展之中。\nEthernet。目前业界也在讨论基于以太网的前传方案。该方式采用分组技术，利用统计复用特性实现流量汇聚提高线路带宽利用率，支持点到多点传输，节省光纤资源。但该方案需要解决低时延业务的区分识别和快速转发、高精度同步等问题，并要兼容基于TDM技术的CPRI信号传输。目前IEEE已经成立了802.1 TSN任务组来研究时延敏感的以太网转发技术，成立了1914 NGFI工作组来研究CPRI over Ethernet和研究新的基于以太网的前传接口NGFI。\n5G前传网变化和WDM/OTN承载方案\n5G的RAN功能将被重新划分，原来的BBU和RRU被重构为CU、DU、RRU/AAU三个功能实体。CU主要包括非实时的无线高层协议处理功能，采用通用平台实现，同时也支持部分核心网功能下沉和边缘应用业务的部署。DU主要处理物理层功能和实时HARQ流程，可采用专用设备平台或通用+专用混合平台实现。对于大规模MIMO天线，部分物理层功能需下移至RRU/AAU实现，以大幅减小RRU/AAU与DU之间的传输带宽，降低传输成本。高层功能划分方案（CU和DU之间）以OPTION2为主（PDCP和RLC之间），其带宽特性接近回传网。底层功能划分方案（DU和RRU/AAU之间）的标准化还没有达成共识，可能要等到5G新空口协议栈足够成熟和稳定后才开始，目前业内有NGFI、eCPRI等各类方案，对于天线通道数不太多的RRU，也可以沿用CPRI接口。\n根据CU、DU、RRU/AAU的放置位置不同，可以有不同的前传组网模式，如图5。具体采用哪种方式，要依据运营商的光纤分布、机房条件、运维模式等情况来定。\n如果DU和RRU/AAU同站部署，它们之间通常以裸纤直连为主。如果DU按一定规模集中部署，DU和RRU/AAU之间对应一级前传，由于DU实时性处理对时延的要求，一级前传距离应小于10km。这种情况下，可以采用裸纤直连，也可以采用WDM/OTN节省光纤，提供保护。OTN的Muxponder将多个RRU/AAU的10G或25G的CPRI或eCPRI信号复用到100G/200G高速信号后传送到DU，满足了大带宽的传输需求，按照光纤路由可灵活组建点到点、链型、环形网络，在点到点组网的情况下还可采用单纤双向技术进一步减少光纤使用。DU集中池化节省无线设备投资，同时提供最佳的协同增益。如图6。一级前传采用OTN设备成本还比较高，目前在考虑成本优化方案。\nCU和DU之间对应二级前传，一般以环网为主。采用WDM/OTN技术可实现波长在光层穿通中间站点一跳直达，满足大带宽低时延要求，可配置光通道保护满足高可靠业务要求。由于不同传输站点的DU容量可能不同，各传输站点的波长可配置不同的速率，以满足不同的DU容量需求，并且各接入站点可单独扩容和升级，不影响其他站点。如果OTN集成分组增强功能（E-OTN），在CU站点可实现业务汇聚和灵活转发，在DU站点可对多个DU的业务进行汇聚收敛。\n采用相同的E-OTN设备，也可以提供100G环网方案。DU数量较少业务量小的多个站点可以用ODUflex子波长相连组成一个分组环，多站点业务统计复用，提高带宽利用率；业务量较大的站点（DU池），可以ODUflex子波长在中间站点交叉连接穿通直达CU站点。或者不同类型的业务采用不同的ODUflex分片传送，如eMBB业务采用分组环网逐点转发，uRLLC业务采用L1穿通直达，减少延时。ODUflex的带宽以1.25G为颗粒灵活可调，100G的环网总带宽可以在多个站点多个逻辑环网间灵活分配。\n固移融合统一回传和OTN承载方案\n在移动网络向5G演进的同时，CO重构也在进行中。本地网内传统的CO机房逐步改造为属地化的边缘DC，基于SDN/NFV技术，使用通用服务器取代传统网元的各类专用设备，进行云化部署。5G核心网vEPC的用户面将会下沉，与固网的vBNG、vCPE、vCDN等一起，分散部署在边缘DC。通过共同部署和资源共享，实现CO机房数量的大幅下降，节约机房维护成本。\n同时，运营商综合业务接入点（PoP）的建设和完善，也实现了移动业务、固网业务、专线业务的统一接入和汇聚。随着CU、MEC、OLT、CDN等网元的虚拟化，未来综合业务接入点也将演进成一个小型DC。\n未来城域网的流量将会是以Edge DC到PoP点之间的南北向流量，以及Edge DC之间和PoP点之间的东西向流量为主。5G阶段的回传网也将会是固移业务统一承载的数据中心互联网络。各级DC通过OTN光传送网高速互联，光网络构建带宽资源池，根据DC间流量进行带宽按需配置和合理调整。\n5G回传网可以通过IP网络和光网络协同来实现。IP网络和光网络是未来承载网络最基础的设施。路由器间的大流量IP业务通过光层通道直连，减少中间路由跳数，降低了网络时延，提升了路由器的吞吐量，IP网络和光网络协同实现多层网络保护和恢复，提升业务的安全性。采用IP+光方案，路由器的业务灵活转发能力和光网络的大容量低时延传送能力都能发挥到最大，有利于IP和光各自独立演进。\n5G回传网也可以基于E-OTN来实现。OTN集成分组功能，既可以在L2和L3实现业务的汇聚和灵活转发，又可以在L0和L1实现大容量低时延的业务传送。OTN节点之间可以根据业务需求配置IP/MPLS-TP over ODUk通道，实现一跳直达从而保证5G业务的低时延和大带宽。采用单一的传输设备，整网的建设和维护成本最低。\n回传网拓扑复杂，OTN节点设备采用光交叉和电交叉的光电混合调度是满足高速传送、灵活调度、多样性组网的最好方式。大颗粒业务在光层调度，中小颗粒业务在电层梳理调度，光电配合整体功耗也是最低的。网络可分层次建设，汇聚层以环网为主，线路侧单波速率100G或超100G，采用4维mini ROADM和10T级别的电交叉。核心层以Mesh网为主，线路侧单波速率超100G，采用9~20维ROADM和大容量电交叉。基于智能控制平面实现端到端业务部署、资源动态计算和调整、备用路径自动计算和故障时快速倒换。既满足业务发展的带宽需求，又保证业务调度的灵活性和网络的可靠性。\n光网络SDN化有效支撑5G网络分片和智能化运营\n5G网络切片是端到端的，包括无线接入网、核心网和承载网。OTN传送平面能在波长、ODU、VC这些硬管道上进行切片，也能在分组的软管道上进行切片。作为承载网的一部分，OTN光传送网SDN化，可以开展带宽按需配置和调整BoD、光虚拟专网OVPN等应用，可在跨域跨厂商的大网环境下实现快速业务部署，减少运营人力，可进行IP+光的跨层协同降低整网的建设和运营成本，可对数据中心间互联带宽进行自动调度，这些都为未来融入整体的网络架构，支持端到端的5G网络分片和智能化运营做好充足的准备。\n5G承载中OTN关键技术\n高速率低成本传送技术。\n5G带来海量的带宽增长，基于光网络实现高速率低成本低功耗的业务传送成为关键。对于100G速率以上信号的中长距离传输，线路光接口需要采用相干技术，但要进一步降低成本，引入硅光技术是一个重要的发展方向，可实现光组件大规模集成，减小光模块体积和功耗，通过量产降低成本。对于短距离传输（如5G前传网），线路光接口采用DMT、PAM4等调制技术，结合DSP补偿算法，可利用较低波特率的光电器件实现数倍带宽增加，也是低成本实现高速传输的好办法。\n大容量光电混合调度。\n5G业务有大带宽和高速传送的需求，在拓扑复杂的城域网，采用具有光电混合调度能力的OTN设备组网是较理想的方式。ROADM光交叉技术与OTN电交叉技术配合，可以实现更大的交叉容量和更灵活的调度能力，同时降低系统的成本和功耗，减少占地面积。在城域核心、汇聚层面引入光电混合交叉，实现电层业务汇聚和光层业务调度，网络进行MESH改造，实现多路径通达，一方面减少设备处理时延，另一方面减少网络层次、实现网络的扁平化，降低业务转发时延，提升网络安全性。\n分组与光传送的融合。\nOTN设备支持ODUk/Packet/VC4统一交换，多业务统一传送，避免了目前传输网络技术体系多、设备种类庞杂、网络建设运维成本高等缺点，既有小颗粒业务处理的灵活性，又有海量的传送容量，提供刚柔并济的传送管道，其对IP层业务的感知，使光网络能够经济高效的传送IP业务，满足运营商发展5G业务的需要，同时也支撑各类业务长期发展。中兴通讯E-OTN设备采用统一的硬件平台， P与O可任意比例配置，无缝接合，统一管控。\n低时延传送和转发。\nOTN设备引入的时延相对其他技术来说要低很多，但5G前传网络对时延要求非常苛刻，一级前传光纤加传输设备总的时延要求在50us以内，二级前传和回传的时延也是越低越好，OTN设备引入的时延需要从以前的几十us减少到10us以内。中兴通讯在OTN设备内部映射复用、转发、接口等各环节优化，通过减少缓存时间、自动调整缓存深度、内部若干处理步骤串行转并行、提升内部处理时钟频率、FEC模式优选，算法优化，光模块优化等多种技术和措施，可以将设备引入的时延降到us量级，能更好地支撑业务的发展。\n面向前传的轻量级OTN标准。\n针对5G前传，业内也在研究新的轻量级的OTN标准，以降低设备成本、进一步降低时延、实现带宽灵活配置。比如：对OTN帧结构进行优化，线路侧接口采用n*25G，可以引入低成本的光器件；改变检错和纠错的机制，缩短缓存时间降低时延；前传组网通常比较简单，可以简化OTN开销减少设备处理；在业务映射和时隙结构方面考虑兼容3G/4G前传的CPRI，5G的eCPRI和NGFI，以及small cell的回传等。\n灵活带宽调整技术。\n传统的OTN定义了几种标准容器，业务封装到几种标准容器中进行传输，在业务颗粒与容器大小不匹配时，会造成传输带宽资源的浪费。为支持更多类型更多颗粒的业务，可采用ODUflex，即灵活速率的ODU，能够以1.25G为步长灵活调整通道带宽，\n保证高效承载，适配多种业务。另外，在时隙带宽调整的时候做到业务无损，还要支持ITU-T的G.HAO（Hitless Adjustment of ODUflex，ODUflex的无损伤调整）协议，控制源宿之间所有站点都调整到所分配的时隙个数。\n高精度时间同步。\n为满足5G高精度时间同步要求，中兴通讯OTN设备基于1588（V2.1）方案，采用相位检测技术和零延迟PLL技术，在频率同步优化基础上进行相位同步，确保相邻站点间同步误差低于1个时钟周期。同时通过复帧定位触发、高速接口底层触发等多种方式提升时间戳精度。针对时间源选择、时间同步融合算法进行优化。采用单纤双向消除时延不对称性。综合运用这些技术，时间同步精度大幅提升。\n无损和低时延保护倒换。\n传统的保护倒换是以信号LOS、LOF、误码越限等为触发条件，保护倒换发生时数据流已经中断，由于倒换时间短（<50ms），绝大多数业务不受影响，但未来某些高可靠业务有可能受影响。中兴通讯研究无损和低时延保护倒换机制，在接收端实现2个并发数据块的对齐和同时接收，以数据块纠错后误码作为判据进行优选，保护倒换发生时数据流没有中断。这种机制适合未来某些关键业务场景。\n软件定义光网络SDON。\n充分利用光网络的可编程特性，实现光网络的SDN化。中兴通讯SDON的研发聚焦在高效智能的路由计算能力，完整和符合主流标准的信息模型，开放的北向和南向接口，跨层跨域的协同，管理和控制的融合，安全的可扩展的控制器软件平台和硬件平台等。\n总结\n5G给人们的工作和生活带来更多样的服务，更好的业务体验，5G网络需要以承载网为依托，并对承载网提出了更高的要求。OTN光传送网作为基础的承载技术，提供大带宽、低时延、灵活分片、高可靠性、开放协同的能力，适合在5G时代新网络架构下的前传和回传组网，并能同时支撑运营商固网等其他业务的发展，满足未来网络持续演进的述求。光与无线的结合，必将打造一个极速极致的万物互联世界。\n附录：缩略语\n', 'id': '785760_2'}
{'label': 'table', 'content': '| 缩略语 | 全称 | 说明 |\n|:-|:-|:-|\n| AAU | Active Antenna Unit | 有源天线单元 |\n| BBU | Base Band Unit | 基带处理单元 |\n| BoD | Bandwidth on Demand | 按需带宽 |\n| CO | Central Office | 中心局 |\n| CPRI | Common Public Radio Interface | 通用公共无线电接口 |\n| C-RAN | Centralized Processing, Collaborative Radio, Cloudization , Clean Radio Access Network | 集中，协作，云化，绿色的无线接入网 |\n| CU | Centralized Unit | 集中单元 |\n| DU | Distributed Unit | 分布单元 |\n| DMT | Discrete Multi-Tone | 离散多频音调制 |\n| eCPRI | Enhanced CPRI | 增强通用公共无线电接口 |\n| eICIC | Enhanced Inter Cell Interference Coordination | 增强的小区间干扰协同 |\n| eMBB | Enhanced Mobile Broadband | 增强移动宽带 |\n| eMBMS | Multimedia Broadcast Multicast Service | 多媒体广播多播服务 |\n| LOS | Loss of Signal | 信号丢失告警 |\n| LOF | Loss of Frame | 帧丢失告警 |\n| LTE | Long Term Evolution | 长期演进 |\n| MEC | Mobile Edge Computing | 移动边缘计算 |\n| MIMO | Multiple Input Multiple Output | 多输入多输出 |\n| mMTC | Massive Machine Type of Communication | 海量机器类通信 |\n| NGFI | Next Generation Fronthaul Interface | 下一代前传接口 |\n| NFV | Network Function Virtualization | 网络功能虚拟化 |\n| ODUflex | Flexible Optical Data Unit | 灵活的光数据单元 |\n| OTN | Optical Transport Network | 光传送网 |\n| OVPN | Optical Virtual Private Network | 光虚拟专网 |\n| PAM4 | Pulse Amplitude Modulation | 四电平脉冲幅度调制 |\n| PLL | Phase Locked Loop | 锁相环 |\n| PON | Passive Optical Network | 无源光网络 |\n| PoP | Point of Presence | 服务呈现点 |\n| QAM | Quadrature Amplitude Modulation | 正交振幅调制 |\n| RRU | Radio Remote Unit | 无线远端单元 |\n| ROADM | Reconfigurable Optical Add Drop Multiplexing | 可重构光分插复用 |\n| SDN | Software Defined Network | 软件定义网络 |\n| SDON | Software Defined Optical Network | 软件定义光网络 |\n| TDM | Time Division Multiplexing | 时分复用 |\n| uRLLC | Ultra Reliable Low latency Communication | 超可靠低时延通信 |\n| vEPC | Virtualized Evolved Packet Core | 虚拟化的分组核心网 |\n| vBNG | Virtualized Broadband Network Gateway | 虚拟化的宽带网关 |\n| vCDN | Virtualized Content Distribution Network | 虚拟化的内容分发网 |\n| vCPE | Virtualized Customer Premier Equipment | 虚拟化的客户驻地设备 |\n| WDM | Wavelength Division Multiplexing | 波分复用 |\n', 'id': '785760_3'}
{'label': 'text', 'content': '深圳市科技南路55号中兴通讯大厦\n邮编: 518057\nWeb: www.zte.com\nTel: +86-755-26770000\nFax: +86-755-26771999\n', 'id': '785760_4'}
