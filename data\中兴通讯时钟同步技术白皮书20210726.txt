{'label': 'document', 'content': '中兴通讯时钟同步技术白皮书20210726', 'id': '961684'}
{'label': 'text', 'content': '时钟同步技术白皮书\n时钟同步技术白皮书\n', 'id': '961684_1'}
{'label': 'table', 'content': '| 版本 | 日期 | 作者 | 审核者 | 备注 |\n|:-|:-|:-|:-|:-|\n| V1.1 | 2021-07 | | | |\n| | | | | |\n| | | | | |\n| | | | | |\n', 'id': '961684_2'}
{'label': 'text', 'content': '目 录\n技术简介\n时钟和时间同步是承载设备的基本功能要求之一，它提供了网络运维所需要的时钟和时间的同步功能。\n时钟同步\n同步技术包括：时钟同步(频率同步）和时间同步（相位同步），时钟同步是通过频率比对将分布在不同地方的频率源的率值调整到一定的准确度或一定的符合度，而时间同步是通过时刻比对将分布在不同地方的钟时刻值调整到一定的准确度或一定的符合度。\n时钟同步和相位同步其关系可以用下面的秒表的例子来说明（Error: Reference source not found所示），假设有两块具有秒针的秒表，如果两块表的频率同步，意味着两块表的秒针具有相同的“跳跃”周期，也就是两块表走得一样快。但是这并不意味着两块表所表示的相位相同。相位同步首先要求两块表有相同的时标（Time Scale），也就是时间的起始点（Epoch）和固定的时间间隔（Time Interval）。\n通常情况下，我们选择其中一块表作为同步参考源，即主时钟；另一块表作为从时钟，使其保持与主时钟的频率和相位同步。为了消除相位差，主时钟可以提供一个以60秒为周期的基准脉冲信号（即“对表”信号），使从时钟秒针的跳变位置在每一个基准信号脉冲出现时，与主时钟秒针的跳变位置保持一致，则两块表就能保持相位的对齐（或相位同步）和频率同步。\n同步需求来源\n基本同步需求\n5G系统采用TDD制式，其基本同步要求主要是为了防止不同基站之间的干扰以及不同终端之间的干扰。\n为避免不同基站之间和UE之间的上下行干扰，提出了基站空口的时间同步需求。通过对不同干扰场景的分析，可见时间同步精度由保护周期（）、基站同步误差、基站收发转换时间、UE收发转换时间、小区覆盖半径等多方面因素共同决定，且这些因素之间需要满足下述约束关系：。其中，是基站之间的同步偏差，是保护间隔时间，是基站之间由距离引入的传输时延，是基站从“开”到“关”的转换延迟，是基站从“关”到“开”的转换延迟。\n在5G基本时间同步方面，3GPP采用设置的方式防止上下行时隙干扰，考虑到5G系统中高频子载波的较小。3GPP通过设置多个符号数，使时间相同，仍为3us，与4G系统保持一致，需要网同步实现每个基站\uf0b11.5us的绝对时间精度。其中，中国移动建议采用取最小符号数的原则，把时间同步精度归一化到0.6us，对应的绝对时间精度为\uf0b10.3us）。此外，在5G基本频率同步方面，5G的频率同步要求仍为0.05ppm，与4G相同。\n协同业务的需求\n站间协同特性是指到同一个用户的数据可以通过不同基站的RRU收发，使用户可以在交叠覆盖区合并多个信号，从而提升业务增益。多信号间的时延差须满足一定要求，否则无法合并，时延差主要由站间同步偏差和站间传播时延组成，可以推导出协同业务对站间同步的要求。假定UE采用一套定时机制，根据3GPP TS36.922协议描述，空口协同要求。其中，为空口到用户不同距离产生的时间差，为多径传播导致的时延差，为同步定时偏差，为用户接收到来自不同基站信号的时间总差。为了避免不同路径符号间的干扰，符号和符号之间通过（循环前缀）隔离，不同RRU到UE的信号延时差异要小于CP的长度。\n在5G协同业务时间同步方面，不同子载波间隔的协同业务具有不同的时间同步需求，低频（15kHz）、中频（30kHz）、高频（120kHz）需求分别为3us、0.7us、190ns；MIMO、发射分集（3GPP）要求相对时间偏差为65ns，一般在单个RRU内部，无需网同步；带内连续载波聚合（CA）要求相对时间偏差260ns，一般在单个RRU内部，无需网同步，可能发生在一个基站的不同RRU之间，需要前传解决相对同步问题；带间CA或带内非连续CA，要求相对时间偏差3us，一般发生在一个基站的不同RRU之间，需要前传解决。此外，5G协同业务频率同步方面无特殊要求。\n承载网的需求\n时间同步在承载网的应用，主要在时间片调度和量化时延两个领域。\n5G网络对承载提出了低时延和确定性时延的需求，引入了Inband OAM技术，解决当前OAM技术的不足。基于真实业务的报文，该技术可进行端到端及逐点检测业务的时延和丢包情况，实现对真实业务全网覆盖的“逐包”检测和纳秒级的时延检测，从而保证用户的服务体验。\nInband OAM的时延检测在发送端对被检测业务流的其中一个报文进行时延染色，记录该报文的入口时戳，在接收端记录被检测业务流时延染色报文的出口时戳，从而进行时延检测。时延的精度取决于发送端和接收端两侧的时间同步精度。\n5G垂直行业应用\n除了基站同步需求，5G网络支撑的多种新业务可能具备高精度同步需求，比如高精度定位业务、电力、高速移动业务覆盖和其他新业务（如物联网，工业互联网），不同的细分领域时间同步的精度需求存在差异。\n基于测距的算法是目前比较典型的定位技术，包括到达时间法（Time of Arrival，TOA）、到达时间差法（Time Difference of Arrival，TDOA），基站定位可以采用类似的方法。TOA定位方法是一种通过计算信号由发生器到达接收点的延迟时间来确定两者相对距离的方法。基于TOA（或TDOA）的基站定位技术，时间同步精度与定位精度要求直接相关。例如，要满足3m的定位精度，要求基站间的空口信号同步误差为10ns；要满足m级的定位精度，要求基站间的空口信号同步误差为3ns。需要网同步实现，可结合其它相关定位技术。\n另外的典型应用是电力系统同步，电力系统同步主要涉及三类设备，第一类为保护设备，主要用于保护变压器、线路、断路器等重要装置；第二类为自动化设备，主要用于提高对设备运行情况的监视、测量、控制和协调水平；第三类为显示时钟、电子挂钟等其他设备。不同的设备对时间同步的精度要求不同，覆盖范围从1μs到1s，其中线路行波故障测距、同步相量测量及雷电定位的同步精度要求在1μs内。\n时间同步是许多工业应用的必要需求，例如运动控制中的精准事件序列、实时数据记录等场景。在工业领域中，通常要求独立的设备单元系统（产线、车间）的设备时间同步精少小于1µs。精准的时间同步还是时间敏感网络其他特性的先决条件。业领域大量典型应用存在分布式实时性部署要求，如运动控制、过程控制、高清机器视觉、实时数据采集等。\n通讯网时间同步解决方案\n通讯网时钟时间同步主要有两种解决方案，\n一种是基站直接搜时，一种是通过时间服务器连接有线时间网，进行网络搜时。\n这两种授时方式各有优点和缺点。可以相互补充，弥补各自的不足。\n', 'id': '961684_3'}
{'label': 'table', 'content': '| 方案 | 优势 | 劣势 |\n|:-|:-|:-|\n| 基站GPS方案 | 问题定位定界方便 | 低可靠、低安全安装施工困难 |\n| 1588V2方案 | 较高可靠较高安全 | 1588开局工作量大1588故障定位较困难 |\n', 'id': '961684_4'}
{'label': 'text', 'content': '有线授时方案主要有时间服务器、地面时钟网和时间网构成。时钟网和时间网构成双层平面。用基于物理层比特流的同步以太网技术实现频率同步，在此基础上采用1588实现相位调整，从而实现时间同步。\n关键技术\nITU-T的G.826x和G.827x系列提出了分组网络传递电信级时钟的要求，包括了时钟网络、时间网络，时钟源、时间源等，到2020年已经发布的标准如下：\n时钟同步\n同步以太网\n同步以太网是一种采用以太网链路码流恢复时钟的技术,发送侧设备将高精度时钟注入以太网的物理层芯片(PHY)，PHY芯片用这个高精度的时钟将数据发送出去。接收侧的设备的PHY 芯片可以从数据码流中提取这个时钟，在这个过程中时钟的精度不会有损失，可以与源端保证精确的时钟同步。\n同步以太网从物理链路恢复时钟，因此从恢复的时钟质量不受链路业务流量影响，可提供与SDH/SONET网络相同的时钟树部署和时钟质量，满足G.823规定的接口指标。\n时钟同步级联\n采用同步以太的构建时钟同步网络，每个节点可以从链接上游节点的物理链路提取线路时钟，并从多个时钟源中进行时钟质量选择，使本地时钟锁定在最高质量的时钟源，并将锁定后的时钟传送到下游设备，通过逐级锁定，从而实现全网逐级同步到主参考时钟。其原理如图所示。\n锁相环模块\n时钟同步可以通过物理链路方式获取上游时钟，也可以通过1588报文恢复时钟，运营商网络通常使用物理链路恢复时钟，其核心模块为满足G.8262.1标准的锁相环模块。\n同步以太网节点的时钟锁相模块（PLL）实现时钟锁相、分发功能，时钟锁相模块（PLL）有三种工作模式：锁相、保持和自由运行，锁相模式即为PLL锁定参考基准，系统时钟精度和参考基准的精度保持一致，参考基准是从上一级节点内置数据码流中下发的时钟，保持模式为PLL失去参考基准，根据已往的时钟参数在一段时间内保持原有的时钟精度，保持模式一般可持续24小时，经过保持模式，PLL即进入自由运行模式，在自由运行模式下，系统时钟精度取决于本地振荡器时钟精度。\nSSM消息\n时钟同步网络使用SSM报文进行同步网络构建。 SSM算法源于SDH的时钟同步控制，使用规则和时钟选择算法符合G.781的规范，同步以太网的SSM控制继承了SDH网络特性，在传统时钟网的基础上通过增加太网同步消息信道（ESMC）丰富了同步以太网的支持，标准G.8264里对其有所描述。\n以太网同步消息信道（ESMC）是MAC层的单向广播协议信道，用于在设备间传送同步状态信息SSM。设备根据ESMC报文的SSM信息选择最优时钟源。\n在同步以太网的SSM信息封装公式参见G.8264，符合IEEE802.3标准。\n同\n', 'id': '961684_5'}
{'label': 'table', 'content': 'IEEE802.3头定义的结构如下表：\n表 1 SSM报文IEEE802.3头定义的结构\n| Octet number | Size | Field |\n|:-|:-|:-|\n| 1-6 | 6 octets | Destination Address = 01-80-C2-00-00-02(hex) |\n| 7-12 | 6 octets | Source Address |\n| 13-14 | 2 octets | Slow Protocol Ethertype = 88-09 (hex) |\n| 15 | 1 octets | Slow Protocol Subtype (0A (hex)) |\n| 16-18 | 3 octets | ITU-OUI = 00-19-A7 (hex) |\n', 'id': '961684_6'}
{'label': 'text', 'content': '现在按照先后顺序分别说明：\nA：目的地址：为慢协议多播地址01-80-C2-00-00-02\nB：源地址：出端口的mac地址\nC：协议类型：0X8809\nD：子类型：0X0A\nE：ITU OUI：定义协议组织身份识别，00-19-A7\nF：ITU子类型： 0X0001\nG：版本：0X01\nH：事件标志：当为普通SSM通告报文时值0，当为G.781中定义的时间发生时，导致SSM信息发生变化，该字段置1\nI： 缺省字段：为了将来扩充协议\nJ：报文内容：包括时钟质量信息内容，与其他扩展信息.采用的格式都为TLV形式。\nK：FCS 校正字段。\n时间同步\n时间同步模式\n1588v2边界时钟（BC）和普通时钟（OC）模式是，E2E透明时钟（E2E TC）模式。\n普通时钟OC\nOC（Ordinary Clock）是网络始端或终端设备，该设备只有一个1588端口，该端口只能作为SLAVE或MASTER。\n边界时钟BC\nBC（Boundary Clock）是网络中间节点时钟设备，该设备有多个1588端口。其中一个端口可作为SLAVE，设备系统时钟的频率和时间同步于上一级设备，其他端口作为MASTER，可以实现逐级的时间传递。\n(1) BC的所有端口的本地时钟是相同的；\n(2) 每个端口的协议状态机都有一个辅助功能：分析每个端口状态，根据所有端口的状态决定来自那个端口的时间信号用来同步本地时钟。\n用来同步和建立主从关系的Announce报文以及Signaling报文中止于BC节点，不会往下透传。管理报文可以通过BC的其他端口继续向前传。\n透传时钟TC\n网络中间透传时钟设备为TC（Transparent Clock）设备，其可分为E2ETC（END TO END\nTC）和P2PTC（PEER TO PEER TC）两种：\n(1) E2ETC：该设备实现1588报文透传。对于Event报文，计算报文设备内驻留时间，修正时间戳信息。对于General报文，直接透传。\n(2) P2PTC：该设备实现1588报文透传。对于Event报文，通过Pdelay协议计算路径延时，同时计算报文在设备内驻留时间，修正时间戳信息。对于General报文，直接透传。\nTC方式由于不易控制时间传递路由，难以控制非对称性，所以一般在应用中不使用。\n时间端口状态\n实现PTP协议，各以太网端口可选支持IEEE1588时间同步功能，各启用时间同步功能的端口可工作于Master、Slave或Passive模式，任何时候系统最多应只有一个端口工作于Slave模式（可以没有Slave端口，此时本点使用自己的时间源），所有IEEE1588时间同步协议的端口使用本系统同一时间源信息。Slave端口用于同步到上游设备的绝对时间，并测量到上游设备Master端口的链路时延，其它启用端口工作于Master或Passive模式，Master端口用于向下游设备发布同步时间，Passive端口不进行任何操作，既不发布同步时间，也不同步于对端端口，除了Pdelay_Req, Pdelay_Resp, Pdelay_Resp_Follow_Up消息和信令以及必须响应的管理消息外不发送任何PTP消息。\nPTP消息分类\n1588V2协议定义了PTP event消息和general消息，event消息是定时消息，在于发送和接收事件消息时要生成准确的时间戳。general消息不要求生成准确的时戳。\n- Event 消息集包括： \\- a）Sync \\- b）Delay_Req \\- c）Pdelay_Req \\- d）Pdelay_Resp\n- General 消息集包括： \\- a）Announce \\- b）Follow_Up \\- c）Delay_Resp \\- d）Pdelay_Resp_Follow_Up \\- e）Management \\- f）Signaling\nSync、Delay_Req、Follow_Up和Delay_Resp消息用于时间戳信息传递，用延迟请求-响应机制来同步OC和BC设备时需要这些定时信息。\nPdelay_Req、Pdelay_Resp和Pdelay_Resp_Follow_Up消息用于测量两个实现Pdelay机制的时钟端口间的链路延时，链路延时用于修正P2P TC系统中Sync和Follow_Up消息中的定时信息，OC和BC通过执行Pdelay机制应用测量的链路延时和Sync和Follow_Up消息中的信息实现同步。\nAnnounce消息被用来建立同步体系。\nmanagement消息用来查询和更新时钟维护的PTP数据集，这些消息也被用来定制一个PTP系统、初始化和故障管理。\nSignaling消息用在时钟设备间实现其他用途的通信，比如Signaling消息可用于协商在主从时钟间单播消息的速率。\n报文发送间隔\n连续消息间的平均时间间隔用秒测量，以对数（以2为底）方式表示。这个对数属性的值应该从整数范围-128到127之间选择，服从于特定PTP模板确定的进一步限制。实际发包间隔的准确率和和恢复时间的精度没有必然关系，标准里指出，对于一个节点90％包，以申明值正负30％的误差范围内的间隔发送消息即可。\n下面列出各种类型的典型发包间隔：\n平均Sync消息发送间隔：表示为2^k，其中k的取值范围-1到7的整数。\n平均pdelay消息发送间隔：表示为2^s，其中s的取值范围0到4的整数。\ndelay_req消息发送时间间隔：表示为2^（t+k），其中t的取值范围为0到4的整数。k的值即平均Sync消息发送间隔中的k。\n平均Announce消息发送间隔：表示为2^n，其中n的取值范围-1到4的整数。\nAnnounce消息超时时间：表示为m，其中m取值范围为2到10的整数。\nPTP消息格式\n- 一般消息格式要求\n每一个消息都有一个header，body和后缀。后缀长度可能为零。保留的字段在传输给接收端时，所有的bit位应该为零并忽略。\n- 所有PTP消息通用字头\n如所示。\n', 'id': '961684_7'}
{'label': 'table', 'content': '表 2通用消息报头\n| Bits | Bits | Bits | Bits | Bits | Bits | Bits | Bits | Bits | Octets | Octets | Offset |\n|:-|:-|:-|:-|:-|:-|:-|:-|:-|:-|:-|:-|\n| 7 | 6 | 5 | 4 | 4 | 3 | 2 | 1 | 0 | | | |\n| transportSpecific 传送相关 | transportSpecific 传送相关 | transportSpecific 传送相关 | transportSpecific 传送相关 | messageType 消息类型 | messageType 消息类型 | messageType 消息类型 | messageType 消息类型 | messageType 消息类型 | 1 | 1 | 0 |\n| Reserved 保留 | Reserved 保留 | Reserved 保留 | Reserved 保留 | versionPTP版本PTP | versionPTP版本PTP | versionPTP版本PTP | versionPTP版本PTP | versionPTP版本PTP | 1 | 1 | 1 |\n| messageLength 消息长度 | messageLength 消息长度 | messageLength 消息长度 | messageLength 消息长度 | messageLength 消息长度 | messageLength 消息长度 | messageLength 消息长度 | messageLength 消息长度 | messageLength 消息长度 | 2 | 2 | 2 |\n| domainNumber 域编号 | domainNumber 域编号 | domainNumber 域编号 | domainNumber 域编号 | domainNumber 域编号 | domainNumber 域编号 | domainNumber 域编号 | domainNumber 域编号 | domainNumber 域编号 | 1 | 1 | 4 |\n| Reserved 保留 | Reserved 保留 | Reserved 保留 | Reserved 保留 | Reserved 保留 | Reserved 保留 | Reserved 保留 | Reserved 保留 | Reserved 保留 | 1 | 1 | 5 |\n| flagField 标志域 | flagField 标志域 | flagField 标志域 | flagField 标志域 | flagField 标志域 | flagField 标志域 | flagField 标志域 | flagField 标志域 | flagField 标志域 | 2 | 2 | 6 |\n| correctionField 修正域 | correctionField 修正域 | correctionField 修正域 | correctionField 修正域 | correctionField 修正域 | correctionField 修正域 | correctionField 修正域 | correctionField 修正域 | correctionField 修正域 | 8 | 8 | 8 |\n| Reserved 保留 | Reserved 保留 | Reserved 保留 | Reserved 保留 | Reserved 保留 | Reserved 保留 | Reserved 保留 | Reserved 保留 | Reserved 保留 | 4 | 4 | 16 |\n| sourcePortIdentity 源端口标识符 | sourcePortIdentity 源端口标识符 | sourcePortIdentity 源端口标识符 | sourcePortIdentity 源端口标识符 | sourcePortIdentity 源端口标识符 | sourcePortIdentity 源端口标识符 | sourcePortIdentity 源端口标识符 | sourcePortIdentity 源端口标识符 | sourcePortIdentity 源端口标识符 | 10 | 10 | 20 |\n| sequenceId 序列号ID | sequenceId 序列号ID | sequenceId 序列号ID | sequenceId 序列号ID | sequenceId 序列号ID | sequenceId 序列号ID | sequenceId 序列号ID | sequenceId 序列号ID | sequenceId 序列号ID | 2 | 2 | 30 |\n| controlField 控制域 | controlField 控制域 | controlField 控制域 | controlField 控制域 | controlField 控制域 | controlField 控制域 | controlField 控制域 | controlField 控制域 | controlField 控制域 | 1 | 1 | 32 |\n| logMessageInterval 录入消息周期 | logMessageInterval 录入消息周期 | logMessageInterval 录入消息周期 | logMessageInterval 录入消息周期 | logMessageInterval 录入消息周期 | logMessageInterval 录入消息周期 | logMessageInterval 录入消息周期 | logMessageInterval 录入消息周期 | logMessageInterval 录入消息周期 | 1 | 1 | 33 |\n', 'id': '961684_8'}
{'label': 'text', 'content': 'TransportSpecific（Nibble ，4bit）：指定报文的封装类型。\nmessageType（Enumeration4，4bit）：指定报文类型\n', 'id': '961684_9'}
{'label': 'table', 'content': '表 3 报文类型字段取值\n| Message type 消息类型 | Message class 消息分类 | Value （hex）取值（16进制） |\n|:-|:-|:-|\n| Sync | Event | 0 |\n| Delay_Req | Event | 1 |\n| Pdelay_Req | Event | 2 |\n| Pdelay_Resp | Event | 3 |\n| Reserved | — | 4-7 |\n| Follow_Up | General | 8 |\n| Delay_Resp | General | 9 |\n| Pdelay_Resp_Follow_Up | General | A |\n| Announce | General | B |\n| Signaling | General | C |\n| Management | General | D |\n| Reserved | — | E-F |\n', 'id': '961684_10'}
{'label': 'text', 'content': 'versionPTP（UInteger4，4bit）：协议版本，当前为第二版。\nmessageLength（UInteger16，16bit）：消息长度的值为所有字节长度， Octet的计数从字头的第一个octet到后缀的最后一个octet。\ndomainNumber（UInteger8，8bit）：缺省数据集的domainNumber初始值。\n', 'id': '961684_11'}
{'label': 'table', 'content': 'Flags（Octet[2]，16bit）：数组的每一位的值在中定义。下表中没有定义的，其值为False。\n表 4 Values of flagField flagField取值\n| Octet | Bit | Message types | Name | Description |\n|-:|-:|:-|:-|:-|\n| 0 | 0 | Announce, Sync, Follow_Up, Delay_Resp | alternateMasterFlag | 如果发送侧端口处于MASTER状态，则为FALSE。 |\n| 0 | 1 | Sync, Pdelay_Resp | twoStepFlag | 对于一步时钟，twoStepFlag取值要求为FALSE。对于双步时钟，twoStepFlag取值要求为TRUE。 |\n| 0 | 2 | ALL | unicastFlag | 如果此消息发送到的传送层协议地址是一个单播地址，则置为TRUE。如果此消息发送到的传送层协议地址是一个多播地址，则置为FALSE。 |\n| 0 | 5 | ALL | PTP profile Specific 1 | 由一个备选PTP模板定义；否则置为FALSE。 |\n| 0 | 6 | ALL | PTP profile Specific 2 | 由一个备选PTP模板定义；否则置为FALSE。 |\n| 0 | 7 | ALL | Reserved | |\n| 1 | 0 | Announce | leap61 | timePropertiesDS.leap61的取值 |\n| 1 | 1 | Announce | leap59 | timePropertiesDS.leap59的取值 |\n| 1 | 2 | Announce | currentUtcOffsetValid | timePropertiesDS.currentUtcOffsetValid的取值 |\n| 1 | 3 | Announce | ptpTimescale | timePropertiesDS.ptpTimescale的取值 |\n| 1 | 4 | Announce | timeTraceable | timePropertiesDS.timeTraceable的取值 |\n| 1 | 5 | Announce | frequencyTraceable | timePropertiesDS.frequencyTraceable的取值 |\n', 'id': '961684_12'}
{'label': 'text', 'content': 'CorrectionField （Integer64，64bit）：CorrectionField为校正测量的值，以纳秒为单位，乘以2的16次方，例如，2.5ns表示为0x0000000000028000。CorrectionField的值依赖于表15中定义的消息的类型。\nsourcePortIdentity （80bit）：端口配置数据集元素，port_identity值。\nsequenceId （UInteger16，16bit）：由发信端分配队列ID。\ncontrol （UInteger8，8bit）：控制字段值由消息类型确定，定义见。\n', 'id': '961684_13'}
{'label': 'table', 'content': '表 4 controlField enumeration controlField枚举\n| Message type | controlField value （hex） |\n|:-|:-|\n| Sync | 00 |\n| Delay_Req | 01 |\n| Follow_Up | 02 |\n| Delay_Resp | 03 |\n| Management | 04 |\n| All others | 05 |\n| Reserved | 06-FF |\n', 'id': '961684_14'}
{'label': 'table', 'content': 'logMeanMessageInterval （Integer8, 8bit）：指报文的发送频率，logMeanMessageInterval字段的值由消息类型确定，定义见下表。\n表 5 Values of logMessageInterval field logMessageInterval字段取值\n| Message type | Value of logMessageInterval |\n|:-|:-|\n| Announce | 数据集中portDS.logAnnounceInterval成员取值 |\n| Sync, Follow_Up | 多播消息中置为数据集portDS.logSyncInterval成员取值，单播消息中置为7F (16进制) |\n| Delay_Resp | 多播消息中置为数据集portDS.logMinDelayReqInterval成员取值，单播消息中置为7F（16进制） |\n| Delay_Req | 7F |\n| Signaling | 7F |\n| Management | 7F |\n| Pdelay_Req | 7F |\n| Pdelay_Resp | 7F |\n| Pdelay_Resp_Follow_Up | 7F |\n', 'id': '961684_15'}
{'label': 'text', 'content': '- Announce消息\n', 'id': '961684_16'}
{'label': 'table', 'content': 'Announce消息字段定义如表所示。\n表 6 Announce消息字段\n| Bits | Bits | Bits | Bits | Bits | Bits | Bits | Bits | Octets | Offset |\n|:-|:-|:-|:-|:-|:-|:-|:-|:-|:-|\n| 7 | 6 | 5 | 4 | 3 | 2 | 1 | 0 | Octets | Offset |\n| Header报头 | Header报头 | Header报头 | Header报头 | Header报头 | Header报头 | Header报头 | Header报头 | 34 | 0 |\n| originTimestamp源时间戳 | originTimestamp源时间戳 | originTimestamp源时间戳 | originTimestamp源时间戳 | originTimestamp源时间戳 | originTimestamp源时间戳 | originTimestamp源时间戳 | originTimestamp源时间戳 | 10 | 34 |\n| currentUtcOffset | currentUtcOffset | currentUtcOffset | currentUtcOffset | currentUtcOffset | currentUtcOffset | currentUtcOffset | currentUtcOffset | 2 | 44 |\n| Reserved保留 | Reserved保留 | Reserved保留 | Reserved保留 | Reserved保留 | Reserved保留 | Reserved保留 | Reserved保留 | 1 | 46 |\n| grandmasterPriority1 | grandmasterPriority1 | grandmasterPriority1 | grandmasterPriority1 | grandmasterPriority1 | grandmasterPriority1 | grandmasterPriority1 | grandmasterPriority1 | 1 | 47 |\n| grandmasterClockQuality | grandmasterClockQuality | grandmasterClockQuality | grandmasterClockQuality | grandmasterClockQuality | grandmasterClockQuality | grandmasterClockQuality | grandmasterClockQuality | 4 | 48 |\n| grandmasterPriority2 | grandmasterPriority2 | grandmasterPriority2 | grandmasterPriority2 | grandmasterPriority2 | grandmasterPriority2 | grandmasterPriority2 | grandmasterPriority2 | 1 | 52 |\n| grandmasterIdentity | grandmasterIdentity | grandmasterIdentity | grandmasterIdentity | grandmasterIdentity | grandmasterIdentity | grandmasterIdentity | grandmasterIdentity | 8 | 53 |\n| stepsRemoved | stepsRemoved | stepsRemoved | stepsRemoved | stepsRemoved | stepsRemoved | stepsRemoved | stepsRemoved | 2 | 61 |\n| timeSource | timeSource | timeSource | timeSource | timeSource | timeSource | timeSource | timeSource | 1 | 63 |\n', 'id': '961684_17'}
{'label': 'text', 'content': 'originTimestamp （Timestamp,96bit）：公告发送时本地时间估计值。\nannounceFlags （OctetArray[2],16bit）：没有用到的标志位接收端认为为0或者被忽略。\ncurrentUtcOffset，是timePropertiesDs.currentUtcOffset字段的值；\ngrandmasterPriority1，是parentDs.grandmasterPriority1字段的值；\ngrandmasterClockQuality，是parentDs.grandmasterClockQuality字段的值；\ngrandmasterPriority2，是parentDs.grandmasterPriority2字段的值；\ngrandmasterIdentity，是parentDs. grandmasterIdentity字段的值；\nstepsRemoved，是currentDS.stepsRemoved字段的值；\ntimeSource，是timePropertiesDs.timeSource字段的值；\nSync及Delay_req消息\n', 'id': '961684_18'}
{'label': 'table', 'content': '表 7 Sync和Delay_Req消息字段\n| Bits | Bits | Bits | Bits | Bits | Bits | Bits | Bits | Octets | Offset |\n|:-|:-|:-|:-|:-|:-|:-|:-|:-|:-|\n| 7 | 6 | 5 | 4 | 3 | 2 | 1 | 0 | Octets | Offset |\n| Header 报头 | Header 报头 | Header 报头 | Header 报头 | Header 报头 | Header 报头 | Header 报头 | Header 报头 | 34 | 0 |\n| originTimestamp 源时间标签 | originTimestamp 源时间标签 | originTimestamp 源时间标签 | originTimestamp 源时间标签 | originTimestamp 源时间标签 | originTimestamp 源时间标签 | originTimestamp 源时间标签 | originTimestamp 源时间标签 | 10 | 34 |\n', 'id': '961684_19'}
{'label': 'text', 'content': 'OriginTimestamp（Timestamp,96bit）：OriginTimestamp字段在header字段后，为精准时间戳。\nFollow_Up消息\n', 'id': '961684_20'}
{'label': 'table', 'content': '表 8 Follow_Up message fields Follow_Up消息字段\n| Bits | Bits | Bits | Bits | Bits | Bits | Bits | Bits | Octets | Offset |\n|:-|:-|:-|:-|:-|:-|:-|:-|:-|:-|\n| 7 | 6 | 5 | 4 | 3 | 2 | 1 | 0 | Octets | Offset |\n| Header报头） | Header报头） | Header报头） | Header报头） | Header报头） | Header报头） | Header报头） | Header报头） | 34 | 0 |\n| preciseOriginTimestamp 精确源时间标签 | preciseOriginTimestamp 精确源时间标签 | preciseOriginTimestamp 精确源时间标签 | preciseOriginTimestamp 精确源时间标签 | preciseOriginTimestamp 精确源时间标签 | preciseOriginTimestamp 精确源时间标签 | preciseOriginTimestamp 精确源时间标签 | preciseOriginTimestamp 精确源时间标签 | 10 | 34 |\n', 'id': '961684_21'}
{'label': 'text', 'content': 'preciseOriginTimestamp （Timestamp,96bit）：preciseOriginTimestamp字段在header字段后，为精准时间戳。\nDelay_Resp消息\n', 'id': '961684_22'}
{'label': 'table', 'content': '表 9 Delay_Resp消息字段\n| Bits | Bits | Bits | Bits | Bits | Bits | Bits | Bits | Octets | Offset |\n|:-|:-|:-|:-|:-|:-|:-|:-|:-|:-|\n| 7 | 6 | 5 | 4 | 3 | 2 | 1 | 0 | Octets | Offset |\n| Header报头） | Header报头） | Header报头） | Header报头） | Header报头） | Header报头） | Header报头） | Header报头） | 34 | 0 |\n| receiveTimestamp 接收时间戳 | receiveTimestamp 接收时间戳 | receiveTimestamp 接收时间戳 | receiveTimestamp 接收时间戳 | receiveTimestamp 接收时间戳 | receiveTimestamp 接收时间戳 | receiveTimestamp 接收时间戳 | receiveTimestamp 接收时间戳 | 10 | 34 |\n| requestingPortIdentity 请求端口标识 | requestingPortIdentity 请求端口标识 | requestingPortIdentity 请求端口标识 | requestingPortIdentity 请求端口标识 | requestingPortIdentity 请求端口标识 | requestingPortIdentity 请求端口标识 | requestingPortIdentity 请求端口标识 | requestingPortIdentity 请求端口标识 | 10 | 44 |\n', 'id': '961684_23'}
{'label': 'text', 'content': 'receiveTimestamp，报文接收时的时间；\nrequestingPortIdentity，延时请求报文中携带的sourcePortIdentity\nPdelay_Req消息\n', 'id': '961684_24'}
{'label': 'table', 'content': '表 10 Pdelay_Req消息字段\n| Bits | Bits | Bits | Bits | Bits | Bits | Bits | Bits | Octets | Offset |\n|:-|:-|:-|:-|:-|:-|:-|:-|:-|:-|\n| 7 | 6 | 5 | 4 | 3 | 2 | 1 | 0 | Octets | Offset |\n| Header （see 13.3） 报头（参见13.3） | Header （see 13.3） 报头（参见13.3） | Header （see 13.3） 报头（参见13.3） | Header （see 13.3） 报头（参见13.3） | Header （see 13.3） 报头（参见13.3） | Header （see 13.3） 报头（参见13.3） | Header （see 13.3） 报头（参见13.3） | Header （see 13.3） 报头（参见13.3） | 34 | 0 |\n| originTimestamp 源时间戳 | originTimestamp 源时间戳 | originTimestamp 源时间戳 | originTimestamp 源时间戳 | originTimestamp 源时间戳 | originTimestamp 源时间戳 | originTimestamp 源时间戳 | originTimestamp 源时间戳 | 10 | 34 |\n| Reserved 保留 | Reserved 保留 | Reserved 保留 | Reserved 保留 | Reserved 保留 | Reserved 保留 | Reserved 保留 | Reserved 保留 | 10 | 44 |\n', 'id': '961684_25'}
{'label': 'text', 'content': 'OriginTimstamp，报文发送时的时间\n对等延时应答（pdelay_resp）报文\n', 'id': '961684_26'}
{'label': 'text', 'content': 'requestReceiptTimestamp，请求报文到达时的时间；\nrequestingPortIdentity，请求报文中携带的sourcePortIdentity\n对等延时应答跟随（pdelay_resp_follow_up）报文\n', 'id': '961684_27'}
{'label': 'text', 'content': 'ResponseOriginTimstamp，请求报文到达时间；\nrequestingPortIdenty，请求报文中对应的sourcePortIdentity\n信号（signaling）报文\n', 'id': '961684_28'}
{'label': 'text', 'content': '该类型的消息报文在时钟之间交互，主要用于单播频率协商等。\n管理消息（management）报文\n', 'id': '961684_29'}
{'label': 'text', 'content': '管理消息用于管理和更新数据集。\nPTP消息传输承载类型\nUDP over IPV4封装方式\n', 'id': '961684_30'}
{'label': 'table', 'content': 'PTN设备对PTP消息也可通过IP报文发送。此时报文的封装格式遵循IEEE 1588-2008 Annex D的规则，即通过UDP/IPv4报文进行封装，如图所示。PTP报文使用的UDP协议端口号如表1-xx所示。当PTP消息通过IP组播方式发送时，组播地址的分配如表所示。\n表 15 UDP协议端口号\n| PTP消息类型 | UDP端口号 |\n|:-|-:|\n| 事件消息 | 319 |\n| 组播一般消息 | 320 |\n| 单播一般消息 | 320 |\n', 'id': '961684_31'}
{'label': 'table', 'content': '表 16组播地址的分配\n| IANA分类名 | PTP消息类型 | 地址 |\n|:-|:-|:-|\n| PTP-primary | 所有非Peer delay机制消息 | *********** |\n| PTP-pdelay | Peer delay机制消息 | *********** |\n', 'id': '961684_32'}
{'label': 'text', 'content': '协议地址\n基于UDP over IPV4 的PortAddress类型addressLength长度为4；addressField表示为16进制8位位组，例如IPV4地址***************表示为CF8E83EB（16进制）。\nIEEE Std 802.3/Ethernet\n', 'id': '961684_33'}
{'label': 'table', 'content': '承载设备支持对PTP报文采用IEEE 1588–2008 Annex F的封装方式，即PTP报文直接封装到IEEE 802.3以太网帧。如图所示。以太网类型值Ethertype为x88F7。以太网帧使用如表1所示的目的MAC地址。\n表 17 IEEE 802.3以太网帧地址\n| PTP消息类型 | 地址 |\n|:-|:-|\n| 所有非Peer delay机制消息 | 01-1B-19-00-00-00 |\n| Peer delay机制消息 | 01-80-C2-00-00-0E |\n', 'id': '961684_34'}
{'label': 'text', 'content': 'PTN设备应能支持基于802.1Q的具有VLAN格式以太网封装，封装格式如图所示。PTP消息可以实现基于VLAN的转发，可配置的VLAN ID范围为1-4094。\n', 'id': '961684_35'}
{'label': 'text', 'content': 'FlexE接口\nPTP报文通过FlexE开销方式承载，FlexE开销帧结构中第6行的完整66B block用于同步报文填写，采用FlexE复帧方式对PTP报文进行传输。FlexE开销帧结构第1行66B block中的Synchronization Configuration(SC)比特用于指示该FlexE PHY是否支持同步信息传送。\nFlexE Group支持绑定多个物理PHY层链路，PTP报文在FlexE Group的第一个物理PHY的开销中进行传递，SC比特默认设置为1（支持同步传送）。当检测到用于传输同步信息的PHY number号为一的物理PHY链路失效时，将所述同步信息按照原FlexE Group链路编号顺序依次倒换至其他PHY链路进行传输，倒换至的PHY链路将SC比特设置为1。当原group PHY number号为一的物理PHY链路恢复正常时，同步信息倒换回第一个物理PHY链路传送。同步信息通过同一个FlexE Group中不同物理PHY链路传送时，同步信息中携带的源端口ID应填写一样的数值。\n频率同步SSM倒换回第一个物理PHY链路时，WTR的设置规则应满足ITU-T G.781中相关规定。在发生多次倒换时，WTR应独立设置。在倒换回第一个物理PHY链路的WTR期间，第一个PHY链路应首先恢复传送SSM信息。\n在FlexE开销中传送的PTP报文应支持完整的以太网封装方式，包括前导码和MAC封装。\nPTP时间戳以PTP报文前导码所在的FlexE复帧开销的帧头经过打戳平面时的时间作为PTP时戳。如果FlexE接口一个物理PHY内部如果存在多个Lane，PTP报文打戳需要多Lane之间的延时对齐，以延时最长（最晚到达）的Lane的PTP时戳为准。\nBMC算法\nIEEE 1588v2定义了标准时间选源算法，ITUT Q13标准组织为了使其更符合电信使用要求，支持部署多时间源，在G.8275.1标准里在标准BMC基础上扩展形成了扩展BMC算法。\n两种算法的基本模型都相同，承载设备设备通过Announce报文，提取时钟源属性，用BMC算法计算出最佳时钟源属性信息，提供给1588协议状态机处理模块进行处理，并同时产生并输出和本网元相符的Announce报文给下游站点；通过逐级节点的处理实现时钟节点树，完成整个网络的选源控制。对于单个时钟节点来说，这个过程按照下面几个步骤来建立：\n接收和认证来自其它端口的announce报文。\n利用BMC算法决策出端口的推荐状态。\nBMC算法本身由两部分组成的: 状态决定算法和数据集比较算法。状态决定算法用来选择完成后确定每个port的推荐状态（Master、Slave、Passive），最终实现数据集的更新，数据集比较算法用来计算两个相关时钟端口数据集的比较关系。\n根据端口决策算法中进入推荐状态的决策点完成端口数据集合的更新。\n各个PTP端口使用状态决策算法决策出一个数据集更新类型，使用这些更新类型，更新相关的协议数据集内容。\n按照推荐状态和“状态决策事件”，根据端口状态机，决定端口的实际状态，建立主从关系。\nIEEE 1588v2的BMC算法和8275.1的算法两种处理逻辑有一些区别：\n标准BMC：先判断GM ID（时间源的标识号）是否相同，如果不相同判断GM（时间源））优选级1、GM质量等级、精度、优先级2的信息,GM ID，然后进行跳数、节点ID（节点的标识号）、端口信息比较；如果GM ID相同，则不比较GM比较流程，直接根据跳数、节点ID、端口信息进行判断。\n扩展BMC（G.8275.1)：先判断GM质量等级、精度和优先级2的信息，再比较本地优先级，当clockclass等于或优于127时，后续根据跳数、节点ID、端口信息进行判断，否者根据GM ID选择。\n如果GM优先级2配置不一样（优先级1配置一样，优先级1扩展算法不使用），两种方法都可以跟踪到全网唯一GM优先级2最高的源。\n扩展BMC算法特殊处： 如果优先级1和优先级2配置一样，可以实现多源时源部署，节点跟踪最近的时间源。\n标准BMC和扩展BMC可以兼容，在网络中共存。\n标准BMC中国移动在PTN/SPN设备上使用，其他运营商多数采用ITUT 的扩展BMC算法。\n中国移动SPN对标准BMC协议进行扩展，通过算法中引入累计设备精度，形成精度加强BMC算法，精度加权算法为新老不同精度的设备混合组网提供了优选方案，可以实现与普通协议设备对接。\n算法处理逻辑如下：\n若多个端口接收而来的GM ID不同时：\n先比较报文中的优先级1，收到较小值的则为SLAVE端口。若两者相同，则比较时钟精准级别较小值为SLAVE端口；\n再比较报文优先级2；同样较小值为SLAVE端口。\n以上比较相同情况下，则比较两个GM ID的大小，接收到较小值的为SLAVE端口。\n若多个端口接收而来的GM为同一个GM ID：\n比较级联精度，级联精度较小的端口为SLAVE端口；\n比较跳数多少，接收报文跳数少的那个端口则为SLAVE端口；\n若跳数相同，则比较与本节点相连的PTP节点的ID大小，ID小的PTP节点所对应的本节点的端口为SLAVE端口。\n1588时间校正\n同步过程：\n1588 实现的基本原理\nIEEE 1588（PTP）通过协议报文的应答实现主从时间同步，1588的协议实现过程如所示。\nMaster端口周期性的发送Sync消息，并响应对端Slave端口的Delay_Req消息；\nSlave端口根据对端Master端口的Sync消息启动协议，在一定时间内都未能完成一次正常的流程，上报对端Master不可用告警，并根据端口状态选择算法且选Slave端口。Slave端口应能统计正常协议运行次数，异常中断协议次数以及Sync消息超时、Follow_up消息超时以及Delay_Resp消息超时次数。支持两步协议。一步协议可选\n具体步骤如下：\n源端Master端口等待Sync消息发送周期到，当发送周期到，发送Sync报文。对于一步协议Sync报文中的时戳是精确的发送时间。对于两步协议Sync报文中的时间标签是个粗略的估算值。\n如果是两步协议，Sync报文发送后，发送Follow_up消息，消息报文中的时戳是Sync的精确发送时间；宿端Slave端口接收到Sync消息，记录接收到报文的时间T2。\n如果是一步协议则提取Sync报文中的时戳为T1。\n如果是两步协议， 宿端Slave端口接收到Follow_up消息，提取Follow_up消息中的时戳为T1。\n宿端Slave端口等待Delay_Req发送周期到，当发送周期到，发送Delay_Req报文，并记录发送报文的精确时间T3；\n源端Master端口接收到Delay_Req消息，记录接收到Delay_Req消息的时刻T4。源端Master端口产生Delay_Resp消息，报文中填写前面Delay_Req消息的接收时刻T4时戳。\n宿端Slave端口接收到Delay_Resp消息并提取Delay_Resp消息中的T4时戳。\n宿端Slave端口软件根据：T2=T1+Delay+Offset T4=T3+Delay-Offset两个公式计算Delay和Offset，分别代表链路时延和时间偏差\n宿端Slave端口软件根据Offset调整FPGA中绝对时间值。\n时间同步过程\n时间同步网的建立，需要满足网络里所有节点为ptp节点，支持OC或BC\n时间同步网的实现主要包括以下两个方面：\n通过SSM算法建立频率同步网；\n通过BMC算法建立时间同步网。\n时钟同步网络以物理同步方式为优选。\n智能时间网\n1在4G时期大规模时间网建设暴露的主要问题是时钟网规划复杂，配置效率不高，故障定位困难。为了解决规模部署的问题，逐步发展出智能时间网综合解决方案，其核心思路是核心汇聚环部署单纤双向，接入网采用基站回送GNSS信号，网络分割，利用大数据和AI技术实现有效管控。\n自动规划\n自动规划，根据物理拓扑和网元时钟同步属性，自动计算和规划所有或指定区域内网元的主备用时钟同步拓扑，根据规则解决时钟时间配置，消除新老不同设备差异性问题，实现端口的自动优选，例如，建网优先单纤双向路径。\n自动更新，根据变化后的拓扑自动对变化的网络区域重新进行同步规划及配置，以实现最小变更，对网络其他区域的已有同步配置不受影响。\n这样的步骤高效简易，配置简单，减少建网规划的复杂度，以降低人为的出错。\n时间网络分割\n时间网的组网算法由适应MESH结构的BMC算法动态实时生成，如果为了加快故障定位，约束故障涉及范围，可以将网络根据等级划分片区。\n分割时间网，可以使用下沉时间源到汇聚接入网交汇处，物理上实现多源多区域划分，也可以通过逻辑上将网络切分，将一些承载节点抽象成虚拟时间源注入点。\n网管控制器根据节点所处的网络层次，将时间网分为核心汇聚、普通汇聚、接入环时间环。不同层级间单向授时，授时的方向为核心汇聚->普通汇聚->接入环时间环，处于较低层次的环路不能向高级环路授时，实现接入环间故障隔离。每个层次的故障及影响范围明确，易于手动BMC算法快速隔离故障区域。\n单纤双向时间传递技术\n承载网汇聚环以上设备部署，存在长距，OTN和分组设备共存的情况，时间精度直接影响下游接入环精度，利用专用链路采取GE/10GE单纤双向光模块组建汇聚层以上专用时钟时间环，可以减少光纤不对称导致的时延偏差，也能规避相干光模块引入的不确定误差，在增强稳定性的同时，也保证了OTN和分组设备同方案的对接。\n引入单纤双向后，就需要考虑经过长距离不同波长在光纤中传输的时延差：\n以GE 40km光模块方案为例，IEEE标准定义激光器波长范围1310nm~1490nm，1310nm和1490ns窗口色散系数不同，如果采用G.652光纤，对应传输时延差为+/-1.28ns/km，40km的传送将引起+/-25.6ns的时间差。因而，采用单纤双向光模块组网，如果没有进行补偿，在不适当的匹配关系下，现网波长累积带来的时间误差就不能忽略。\n由于波长和时延是线性关系，在已知波长的情况下，可以通过自动估算时延推算时延偏差，从而降低波长偏差带来的误差。\n基站时间回送承载网\n无线基站主要采用GNSS和1588V2地面同步技术实现空口时间同步。\n为保证同步性能的稳定可靠，基站通常同时开通两种技术，相互之间形成保护。通过将基站GNSS获取的时间与1588获取的时间差值回传给接入层承载设备，并上报给承载网管控制器系统。\n控制器可以根据上下游各站点的数据进行分析，从而推断出基站或承载的性能，更能进一步进行故障根源性确定。\n基站回送的卫星与承载设备的比对信息，提供了基站相对于地面授时的绝对偏差，承载接入设备根据自身收到的多个基站及上下游节点获取的信息，可以推算当前设备的时间的可信度。\n由于基站回送的卫星与承载设备的比对信息是绝对偏差，可以作为重要的智能故障诊断特征，与其它时钟时间告警、配置和性能数据，共同组成智能故障诊断的特征数据，用于计算网络中非接入GNSS设备的状态。\n时钟时间智能故障诊断\n时钟时间智能故障诊断，主要包括时钟时间网故障识别、时钟时间故障位置的确定和故障根因的确定，其基本思路是网管控制器利用时钟时间相关的配置、告警和性能数据，进行大数据分析，基于AI技术进行智能故障诊断。\n时钟时间网故障识别\n时钟时间网故障识别，主要基于采集到的时钟时间网拓扑结构，以及各网元节点的时钟和时间类配置、告警和性能特征数据，基于AI技术识别时钟时间网是否存在故障。\n为了完成时钟时间网的故障识别，首先，需要采集已标注的时钟时间网样本数据，并将其转换为训练样本集；然后，需要建立相应的AI模型；最后采用训练样本集训练模型，从而让模型具备时钟时间网故障识别的能力。\n故障位置的确定\n故障位置的确定有两种技术方案，一种是基于故障依赖关系图+规则的故障位置定位技术，另一种是基于图神经网络的故障位置定位技术。\n基于故障依赖关系图+规则的故障位置定位\n该方案首先需要基于现网时钟时间配置以及时钟和时间实际路径，建立故障依赖关系图。当需要进行故障位置定位时，基于已建立的故障依赖关系图进行搜索，根据节点判断规则，找出存在异常的实体点状态过滤法：只有当前实节点，通常位于依赖关系最底层的异常实体节点，就是要确定的故障位置。具体搜索算法有两种：\n节点遍历法：无论当前实体节点状态正常或异常，均通过故障依赖关系进行搜索，直到将所有节点遍历。\n节体节点状态异常时，才继续沿故障依赖关系向下搜索，如果该节点状态正常，则终止该路径的搜索。\n基于图神经网络的故障位置定位\n为了实现基于图神经网络的故障位置定位，首先，需要对故障定位的场景进行抽象定义，将故障位置定位问题转换为图神经网络的节点分类问题；其次，需要建立一个端到端的基于图神经网络的节点分类模型；然后，需要采集时钟时间网已标注的样本数据，并将其转换为训练样本集，对已建立的基于图神经网络的故障定位模型进行训练，从而具备故障位置定位的能力。\n故障根因的确定\n时钟时间故障根因的确定主要基于故障传播有向图+贝叶斯网络的技术方案，基于故障传播有向图找到所有疑似故障根因，基于贝叶斯网络找到疑似故障根因的概率。\n首先，需要基于已标注的故障样本数据通过关联算法学习到故障传播关系，或者借助运维专家的知识经验，建立故障传播有向图。然后，基于故障传播有向图按一定的规则生成对应的贝叶斯网络，并基于已标注的故障样本数据进行训练。\n当需要确定故障根因时，首先采集时钟时间网的故障特征数据，依据故障传播有向图，找到所有疑似故障根因节点。然后，将对应的故障特征数据作为证据，将所有疑似故障根因节点作为要推理的变量，基于对应的贝叶斯网络计算出疑似故障根因节点的概率。\n总结\n时钟时间技术是通讯网的底层支撑技术，为移动通讯网基于时间片的调度及量化时延的应用提供保障。时钟网已经有数十年稳定运营的历史，规模时间网近年来也趋于成熟。中国三大运营商的时间网建设是世界上最大规模的同步网建设，由于同步设备数量巨大，时间网的可靠性是巨大挑战，其建设完成，将对我国的授时体系的安全性提供重要的保障。\n2.\t缩略语\n', 'id': '961684_36'}
{'label': 'table', 'content': '| AIS | Alarm Indication Signal | 告警指示信号 |\n|:-|:-|:-|\n| ADEV | Allan DEViation | 阿伦方差 |\n| BC | Boundary Clock | 边界时钟 |\n| BMC | Best Master Clock | 最佳主时钟算法 |\n| CES | Circuit Emulation Services | 电路仿真服务 |\n| E2E | end-to-end | 端到端 |\n| EEC | Synchronous Ethernet Equipment Clock | 同步以太网设备时钟 |\n| ESMC | Ethernet Synchronization Messaging Channel | 以太网同步信息通道 |\n| FCS | Fame Check Sequence | 帧校验序列 |\n| FE | Fast Ethernet | 快速以太网 |\n| GE | Gigabit Ethernet | 千兆比特以太网 |\n| GPS | Global Positioning System | 全球定位系统 |\n| GNSS | Global Navigation Satellite System | 全球导航卫星系统 |\n| ID | Identification | 标识符 |\n| IPv4, IPv6 | Internet Protocol version 4 / 6 | 网络协议4.0/6.0 |\n| IEEE | Institute of Electrical and Electronics Engineers | 电气和电子工程师学会 |\n| IWF | Inter-Working Function | 互通功能 |\n| MAC | media access control | 媒体存取控制 |\n| MTIE | Maximum Time Interval Error | 最大时间间隔误差 |\n| NTP | Network Time Protocol | 网络时间协议 |\n| OC | ordinary clock | 普通时钟 |\n| P2P | peer-to-peer | 对等 |\n| PDV | Packet Delay Variation | 包时延抖动 |\n| PHY | physical layer | 物理层 |\n| PPS | pulse per second | 秒脉冲 |\n| PTP | Precision Time Protocol | 精确时间协议 |\n| QL | Quality Level | 质量等级 |\n| SSM | Synchronisation Status Message | 同步状态消息 |\n| TAI | International Atomic Time | 国际原子时间 |\n| TC | Transparent Clock | 透明时钟 |\n| TDEV | Time DEViation | 时间偏差 |\n| TDM | Time Division Multiplexing | 时分复用 |\n| TLV | type, length, value | 类型、长度、内容 |\n| TOD | Time of Day | 日时间 |\n| UDP/IP | User Datagram Protocol | 用户数据报文协议 |\n| UTC | Co-ordinated Universal Time | 通用协调时间 |\n', 'id': '961684_37'}
