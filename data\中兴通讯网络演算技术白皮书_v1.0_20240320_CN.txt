{'label': 'document', 'content': '中兴通讯网络演算技术白皮书_v1.0_20240320_CN', 'id': '1151074'}
{'label': 'text', 'content': '| 版本 | 日期 | 作者 | 审核者 | 备注 |\n|:-|:-|:-|:-|:-|\n| V1.0 | 2024/03/20 | 高陈强,喻敬海 | | 新建 |\n| | | | | |\n| | | | | |\n| | | | | |\n关键词：网络演算，Network Calculus， NC\n摘要：网络演算是一种基于到达曲线和服务曲线的端到端时延计算技术。相较于传统排队论，侧重于端到端性能描述，天然适用于确定性的时延需求。相较于机器学习方法，模型可解释性强，扩展性好。传统网络演算在低带宽利用率和跳数有限情况下，可以得出有界时延上界，但是存在时延上界估计不够紧致，误差大的问题。本文在DiffServ模型下，针对各种现有调度算法给出服务曲线，并针对不同的业务流类型给出达到曲线。通过达到曲线和服务曲线计算端到端的时延上界，指导业务路径的选择和编排，并提供确定性的端到端时延。另外利用截断指数改进的网络演算规划工具可以提供较低的平均边界时延，同时降低整个网络中的拥塞程度，达到负载均衡的效果。\n目录\n1 前言\t5\n1.1 网络演算技术产生背景\t5\n1.2 网络演算技术优势\t6\n2 网络演算技术实现原理\t6\n2.1 网络演算原理\t6\n2.1.1. 网络演算服务模型\t6\n2.1.2. 确定性到达曲线\t7\n2.1.3. 确定性服务曲线\t7\n2.1.4. 随机网络演算\t8\n2.1.5. 基本定理\t9\n2.2 调度算法原子模型\t11\n2.2.1. SP调度\t11\n2.2.2. WRR调度\t12\n2.2.3. WFQ调度\t13\n2.2.4. CBS调度\t15\n2.2.5. CQF调度\t16\n3 网络演算实现和改进\t17\n3.1 传统网络演算\t17\n3.2 峰值速率改进\t19\n3.3 截断指数有界突发改进\t19\n3.3.1. 截断指数的突发优化\t20\n3.3.2. 累积突发优化\t21\n3.3.3. 峰值流速优化\t22\n3.3.4. 截断指数有界突发到达曲线\t22\n4 网络演算实测和大规模仿真\t23\n4.1 设备端到端测试\t23\n4.2 大规模仿真\t25\n5 参考文献\t28\n6 缩略语\t28\n', 'id': '1151074_1'}
{'label': 'text', 'content': '前言\n网络演算技术产生背景\n随着计算机和通信技术的快速发展，网络通信速度不断提高，对网络性能的要求也越来越高。传统的网络性能分析方法（如排队论）已经难以满足高速网络环境下的性能分析和设计需求[1]。现代网络系统变得越来越复杂，包括多种服务类型、不同的通信协议和多种网络资源分配策略，这使得网络性能分析和设计变得更加困难，需要一种更为通用和严格的理论框架来解决这些问题。网络的规模也越来越大，如何有效地利用网络资源（如带宽、缓冲区等）成为了一个关键问题，网络演算为网络资源分配提供了理论依据，有助于实现网络资源的高效利用。另外在许多应用场景中，如实时音视频传输、在线游戏和金融交易等，对网络服务质量的要求越来越高，网络演算提供了一种系统性的分析方法，可以帮助设计者确保在复杂的网络环境下满足各种服务质量要求。 \n随着业务服务保障质量的要求越来越高，提出了一系列的互联网技术体系架构来满足业务的转发需求，从综合服务(IntServ)、区分服务(DiffServ)到时间敏感网络(TSN)和确定性网络(DetNet)的逐步演进，队列调度机制越来越多，网络结构越来越庞大复杂，同时需要保障业务端到端的确定性时延，选取何种理论方法对复杂非线性网络建立数学模型成为了问题分析的关键。多样化的业务对网络提出了不同的服务质量（Quality of Service， QoS）要求[2] 。 \n在网络演算理论出现之前，排队论一直作为对排队问题分析的主要理论。对于给定精确流量与服务模型的网络，排队论可以给出较为精确的分析结果，该结果是系统处于稳态时的平均分析结果，而如今通信系统的设计中，除了平均服务性能外，更加关注性能指标的极限值与概率分布情况，这些指标在排队论理论中相对欠缺，因为排队论是针对当时确定性系统（电路交换系统）的数学理论，随着网络结构的复杂化与业务的多样化，该理论出现了分析的局限性。另一方面，排队论进行分析之前，必须知道流量与服务单元的具体参数，而过于精确的要求在实际网络环境中往往难以获得，这也限制了该方法在实际网络中的应用。 \n网络演算理论是一种新型的网络性能分析理论，由1991年首先由Cruz提出[3]，并将其首先用于分析分组交换网络的时延与积压上界。同时发现了令牌桶的降低网络延迟，限制流量突发的作用，提出了到达曲线的概念。1993年Parekh和Gallager[4]提出了服务曲线的概念，简化了计算的过程 。2000年，Chang汇总了当时的网络性能分析理论[5] 。2005年，Jiang等人研究了随机到达曲线与随机服务曲线。推动了随机网络演算的发展。2008年，Jiang和Liu总结出了一套适用于随机网络演算理论体系[6]。 \n网络演算理论通过引入最小加代数，将复杂的非线性排队问题转变成易于分析的数学模型，再通过模型之间的数学关系推导出系统性能。它给出的并不是精确性能结果，而是性能的（概率性）边界，对于输入的要求较为灵活。因此通过网络演算，不需要限定到达流量或者服务模型的分布，也可以得出网络的时延及积压边界。', 'id': '1151074_2'}
{'label': 'text', 'content': '前言\n网络演算技术优势\n网络演算作为一种数学方法，为网络性能分析和设计提供了一种严格的理论框架。它的技术优势主要体现在以下几个方面： \n精确的性能分析：网络演算通过引入最小加代数，可以对网络中的延迟、带宽和其他性能指标进行精确的上下界分析。根据不同业务流的需求，计算出对应的时延上界、带宽、缓存大小等，给业务流的部署提供精确的指导。这使得设计者能够在网络设计阶段就对系统性能有一个清晰的了解，从而做出更合理的决策。 \n通用性：网络演算适用于各种不同类型的网络环境，包括有线和无线网络、单播和组播通信、有损和无损网络等。这使得网络演算成为一种通用的性能分析和设计方法，可以广泛应用于多种网络场景。 \n模块化分析：网络演算允许对网络进行模块化分析，即将复杂的网络划分为简单的子系统（称为“服务曲线单元”），然后对这些子系统进行独立的性能分析。这有助于简化复杂的网络问题，便于理解和分析。 \n易于与现有技术集成：网络演算可以与其他网络技术和协议（如SDN、NFV、TSN、DetNet等）结合使用，为这些技术提供性能分析和设计的理论支持。这使得网络演算能够与现有的网络技术和发展趋势保持一致，推动网络性能的持续改进。 \n可预测性和可扩展性：网络演算提供了一种可预测的性能分析方法，有助于在网络规模扩大或网络条件发生变化时，预测和保证网络性能。这使得网络演算在大规模网络系统和不断发展的网络环境中具有很高的价值。 \n有助于资源优化：网络演算可以帮助设计者更有效地分配网络资源（如带宽、缓冲区等），以实现网络资源的最大化利用。这有助于提高网络的整体性能，降低成本，并满足各种应用场景的需求。 \n这些优势使得网络演算在现代网络性能分析和设计中具有重要价值。', 'id': '1151074_3'}
{'label': 'text', 'content': '网络演算技术实现原理\n网络演算原理\n网络演算理论分为确定性网络演算（Deterministic Network Calculus，DNC）与随机网络演算（Stochastic Network Calculus，SNC）两个分支[7]。其中DNC计算的是系统的性能边界，对应系统极限情况下（比如网络最拥塞时）的系统性能。在实际情况中，该种分析方法一般用于输入模型的确定性较高的分析，如周期性业务与均匀服务模型中。SNC是DNC的扩展，定义了统计性能边界，允许实际情况以一定概率超过统计边界，从而提高了资源利用率，能够利用统计复用增益，根据具体需求优化网络性能。从这种角度来讲，确定性网络演算实际上是随机网络演算的一种特殊情况，对应超过统计边界的概率为0。 \n网络演算服务模型 \n网络数据包转发的抽象模型如图2-1所示。通过抽象网络转发行为的方式，实现复杂网络拓扑的数学模型搭建。 \n假设数据按到达顺序退出服务节点并且网络是无损网络。进入服务节点的业务数据累积过程（即到达过程）为，服务节点提供的数据累积过程（即服务过程）为S(t)，业务离开服务节点数据累积过程（即离开过程）为。已知在0 ≤ s ≤ t时间间隔内通信系统一定存在 \n其中，等号在服务模型有数据积压时成立。 \n确定性到达曲线 \n流量的到达累积过程存在上限，为了方便分析流量的性能边界，需要获取描述流量行为特征上限的数学形式。确定性网络演算是通过使用确定性到达曲线的概念来描述流量行为特征的最大数据量。到达曲线就是描述业务到达累积过程上界的函数，用于描述流量在任何时间间隔内发送的最大数据量。 \n（定义：到达曲线）对于所有0 ≤ s ≤ t，如果数据流量的到达过程满足 \n则数据流量具有确定性到达曲线。另外，到达曲线的定义等效于对所有t ≥ 0都有 \n其中数学符号是最小加卷积运算。 \n确定性服务曲线 \n在网络演算中，服务节点的数学模型的建立是基于累积到达函数和累积离开函数之上。在单位时间传输数据比特数已知的前提下，应用累积函数可以得到累积到达和累积离开之间的关系。本质上，服务曲线定义了服务节点提供的服务的下限，将服务节点抽象为服务曲线后有利于后续服务性能计算。 \n为了分析服务保证，有多种调度算法。其中，确保速率调度算法(Guaranteed Rate, GR) 和延迟速率调度算法(Latency Rate, LR)已被广泛用于分析IntServ网络和DiffServ网络。 \n随机网络演算 \n随机网络演算将研究系统的数据流分为到达、服务和离开过程，从而根据到达过程刻画出随机到达曲线(Stochastic arrival curve，SAC)和根据服务过程刻画出随机服务曲线(Stochastic service curve，SSC)。 \nDNC中严格的确定边界被SNC中的概率边界所代替，在SNC中边界函数是可以被违反的，但被违反这一事件发生的概率是受控制的。具体来说，在SNC中实际到达过程可以超过到达曲线，实际离开过程可以小于网络节点所承诺的离开过程最小值，由此分析得到的性能边界不是最差网络情况下的，而是违反某个时延取值的概率。 \n随机到达曲线 \n以流量为中心（traffic-amount-centric, t.a.c.）的随机到达曲线模型是根据一个时间间隔内数据流产生的流量来定义的。 \n（定义：t.a.c.随机到达曲线）一个数据流具有以数据流流量为中心(t.a.c)的随机到达曲线，其边界函数，记为，需要满足对于所有 和，均有下式成立， \n其中，为广义递增函数，为广义递减函数。 \n以虚拟积压为中心(v.b.c.)随机到达曲线模型探索了确知到达曲线的虚拟积压属性，即具有确定性到达曲线的相同数据流的SSQ长度是有上界的。 \n（定义：v.b.c.随机到达曲线）一个数据流被称为具有以虚拟积压为中心(v.b.c.)随机到达曲线，其边界函数为，记为，需要满足对于所有和，均有下式成立： \n其中，为广义递增函数，为广义递减函数。 \n随机服务曲线 \n随机服务曲线模型是基于其对偶原理从确定性服务曲线模型推广而来的。特别地，具有输入和输出的系统具有服务曲线，当且仅当对于所有，满足 \n上述不等式提供了将确定性服务曲线模型推广到如下定义的随机服务曲线模型的基础。 \n（定义：随机服务曲线）系统能够提供随机服务曲线为，边界函数为的服务，记为，则对于所有和所有，需要满足： \n其中，为广义递增函数，为广义递减函数。 \n对SNC中和的图形解释如图2-3所示。 \n在随机网络演算的框架下，如果已知输入到系统中的业务数据流的到达曲线及系统提供的服务曲线，那么就可以计算系统中数据流的延迟边界。SNC时延上界定理如下所示。 \n（定理：SNC时延上界）假设具有随机到达曲线和边界函数的输入流A，即，输入到系统S中，提供随机服务曲线且边界函数为的服务，即，则对于所有的和，时延边界D (t)为 \n其中，为和之间最大水平距离。 \n基本定理 \n时延定理 \n服务保证特性意味着在给定的流量模型和服务节点模型下推导出的QoS 性能界限，例如延迟界限和积压界限。对于确定性网络演算，如果系统的输入流具有到达曲线，且系统提供的服务曲线可知，那么就可以计算系统中数据流的延迟边限。 \n（时延定理）考虑一个向输入流提供确定性服务曲线的系统。假设具有确定性到达曲线，则在时间t处流的延迟受限制于 \n其中，，表示函数和函数的最大水平距离。 \n输出定理 \n所谓输出特性，就是用输出流的到达曲线来描述数据流特征。本质上，输出流与输入流具备相同的数据特性。 \n（输出定理）假设数据流有确定性到达曲线，经过确定性服务曲线的系统，那么对于所有，的离开过程的确定性到达曲线可表示为： \n其中%数学符号是最大加卷积运算，利用流量模型提供的到达曲线和服务模型提供的服务曲线，可以使用逐节点分析方法来研究多跳节点的端到端服务性能。 \n串联定理 \n使用串联定理是为了分析计算数据流经过多个服务节点的性能。数据流在最坏情况下流经一系列服务节点的延迟要小于每个服务节点在最坏情况下延迟的总和。因此，除了之前逐节点分析，串联定理的引入使得性能分析增加了端到端分析。在进行端到端演算时，流量所经由的所有服务节点可通过串联定理被看作成一个单节点，如图2-4所示。 \n（串联定理）考虑一个按顺序依次通过节点的数据流。假设每个系统向数据流提供一条确定性的服务曲线，则这些节点串联后为数据流提供确定性服务曲线可表示为： \n叠加定理 \n使用相同的流量模型来表示流的叠加。利用此特性，可以将单个流的聚合视为单个聚合流，以便可以按照与单个流相同的方式得出聚合的QoS 性能。 \n（叠加定理）以n个流的叠加为例，如果每个流都具有到达曲线，则聚合流具有到达曲线。 \n剩余服务定理 \n在差异化服务模型中，聚合流模型在演算分析中时必不可少的。为了得到聚合流中每个流的QoS边界，需要研究每个流的所被提供的服务曲线。关注流所获得的剩余服务可由聚合流中的背景流到达及输出特性及服务节点特性所决定。 \n（剩余服务定理）考虑一个服务于两个流和构成的聚合流的系统。假设系统为聚合流提供服务曲线，且具有到达曲线，则系统向流提供的服务曲线在满足的条件下，均有', 'id': '1151074_4'}
{'label': 'text', 'content': '网络演算技术实现原理\n调度算法原子模型\n不同的设备在出端口会使用不同的调度算法，导致到达曲线也不同，针对现有设备的SP、WRR、WFQ以及确定性中提出的新调度算法CBS和CQF，分别分析对应的服务曲线，给出原子模型。 \nSP调度 \n考虑图2-5所示的一个交换机节点，此时一共有N条不同优先级的业务流流经该节点。其中，第i（i=1,2,…,N）条数据业务流为周期业务，可表示为，其中代表数据包包长，代表数据包发包周期，假设当i>j时，第i个流量队列的优先级高于第j个流量队列，交换机的服务曲线用模型来表达。 \n第i条用表示的数据流，其到达过程可用下式表达： \n其中代表数据包包长，代表数据包发包周期。根据模型，设交换机服务速率为R，时延因子为e，则交换机的总服务曲线为： \n对于优先级为i的数据流，使用SP调度的交换机为其提供的相应的服务曲线如下所示： \n于是，根据网络演算可推导出对应于第i个数据流的时延边界： \nWRR调度 \n如图2-6所示的一个交换机节点，共有N条业务流流经该节点，到达节点时对应进入不同的队列。其中，第i（i=1,2,…,N）条数据业务流为周期业务，可表示为，其中代表数据包包长，代表数据包发包周期，假设分配给各个队列的权重为，交换机用模型表述。 \n根据模型，设交换机服务速率为R，则交换机的总服务曲线为： \n采用LR模型推导交换机的服务曲线，对于第i条数据流，使用WRR调度的交换机为其提供的相应的服务曲线如下式所示： \n其中，代表一次轮询中队列i获得服务的最小数据量比特数，代表一次轮询中除队列i外，其他队列获得服务的最大数据量比特数，为队列的WRR权重。 \n于是，根据网络演算得到对应于第i个数据流的时延边界： \nWFQ调度 \n如图2-7所示的一个WFQ交换机节点，此时共有N条业务流流经该节点，到达节点时对应进入不同的队列。其中，第i（i=1,2,…,N）条数据业务流为周期业务，可表示为，其中代表数据包包长，代表数据包发包周期，假设分配给各个队列的权重参数为，交换机用模型表述。 \n交换机是一个提供严格服务曲线的WFQ服务节点，根据模型，设交换机服务速率为R，则交换机的总服务曲线为： \n采用LR模型推导交换机的服务曲线，则对于第i条数据流，使用WFQ调度的交换机为其提供的相应的服务曲线如下式所示： \n其中，，是各优先级队列中数据包长度的上限值，。 \n于是，根据网络演算得到对应于第i个数据流的时延边界： \nCBS调度 \nCBS将两种业务区分开来传输，一种为对实时性敏感的音视频流，另一种为普通业务流。同时又将需要优先调度的时间敏感业务分为两类：class A和class B，class A的优先级要高于class B。普通业务流用BE流表示。如图2-7所示，考虑这样一个场景：交换机接收到三种数据流，BE流、SR_A类流和SR_B类数据流，并在此处进行CBS调度。其中，SR_A类业务流为周期业务，可表示为，其中代表数据包包长，代表数据包发包周期，SR_B类业务流为周期业务，可表示为，BE流为周期业务，可表示为，交换机用模型表述。 \n对于SR_A类、SR_B类和BE类数据流，其到达过程如下式所示： \n其中代表数据包包长，代表数据包发包周期。交换机是一个提供严格服务曲线的服务节点，根据模型，设交换机服务速率为，则交换机的总服务曲线为： \n采用LR模型推导交换机的服务曲线，则对于SR_A类和SR_B类数据流，使用CBS调度的交换机为其提供的相应的服务曲线如下式所示，其中式中，，、分别为SR_A、SR_B的预留速率： \n最低优先级的BE流，采用剩余服务的方式进行计算 \nCQF调度 \n如图2-9所示的一个CQF交换机节点，此时共有N条业务流流经该节点，到达节点时对应进入不同的队列。其中，第i（i=1,2,…,N）条数据业务流为周期业务，可表示为，代表数据包包长，代表数据包发包周期，最高优先级的两个队列使用门控机制，其他队列始终处于打开状态，在偶数时间间隔期间，队列输入端口门开启，该队列接收数据帧进行缓冲，输出端口门关闭（即只接收不发送）；队列 输入端口时间门关闭，输出端口时间门打开，发送在先前的奇数时间间隔缓存的数据帧（即只发送不接收）。而在奇数间隔时，两个队列的操作对调。交换机用模型表述。 \n802.1Q定义了8个优先级队列，其中Q7的优先级最高。为支持CQF模型，将其中的Q7和Q6队列用于存储时间敏感流，而且只有这两个队列需要入队和出队的时间门控机制，其他各队列的门始终处于打开状态。 \n对于进入CQF调度队列的用表示的数据流，其到达过程如下式所示： \n其中代表数据包包长，代表数据包发包周期。交换机是一个提供严格服务曲线的CQF服务节点，根据模型，设交换机服务速率为R，则交换机的总服务曲线为： \n采用LR模型推导交换机的服务曲线，则对于进入CQF队列的数据流，使用CQF调度的交换机为其提供的相应的服务曲线如下式所示： \n上式即交换机为进入奇队列的流量提供的服务曲线，其中为队列转发的交替周期，R为端口转发速率。同理，偶队列等同于在奇队列前再加入一个恒定突发延迟函数。偶队列的服务曲线满足下列公式：', 'id': '1151074_5'}
{'label': 'text', 'content': '网络演算实现和改进\n传统网络演算\n对业务的建模是为了刻画业务的到达曲线，针对不同的网络模型，业务到达曲线也不同。 \nIntServ模型中，保证服务的预留原则基于“准入控制”，一方面通信量经过令牌桶整形，其中b是令牌通容量，r为平均速率；另一方面通信量受网络链路容量和数据包最大长度限制，C为链路速率，M为数据包最大长度[8]。结合令牌桶约束和网络链路和包长限制，由TSpec定义的通信量的总到达曲线为：，其对应的图如图3-1所示。 \nDiffServ模型中，加速转发EF的网络模型如图3-2所示，属于同一类型的所有流视为聚合流，在网络边缘每个微流符合某种达到曲线（每个微流单独整形），有延迟和挤压的闭式界限定理[8]： \n如果，则EF的端到端可变量界限为 \n其中，节点m是EF聚合的保证速率节点，速率rm，时延em，即服务曲线是LR（速率-时延）服务曲线；e是所有m在em中上界，h是跳数上界，通常为10或更小；利用率因子，v是所有vm的上限；归一化突发度因子 ，τ是所有τm的上界。 \n以上端到端时延上界即传统的网络演算端到端时延计算公式，其中算出聚合保证速率节点最大的时延上界D1，再乘跳数h，得到端到端的时延上界。限制随着跳数的增加，节点的带宽利用率不能高，重载情况下无法计算时延上界。', 'id': '1151074_6'}
{'label': 'text', 'content': '网络演算实现和改进\n峰值速率改进\n假设Cm表示节点m所有输入低延迟流峰值速率的界限，所有输入链路比特之和（如果无法获取，设为）。Im是表示节点m的扇入数（即注入链路数），是传输时有多个汇聚输入数据包的最长时间，Lmax是EF数据包长度最大值。则重新定义突发因子。 \n可以证明，EF数据包在节点m的输入流的到达曲线为。输入流受到T-Spec(M,p,r,b)的约束，达到曲线图如图3-3所示，其中，，，。', 'id': '1151074_7'}
{'label': 'text', 'content': '网络演算实现和改进\n截断指数有界突发改进\n针对现有网络演算中业务流达到曲线刻画不准确，导致时延上界估计偏大的问题。中兴通讯提出截断指数有界突发（Truncated Exponential Bounded Burstiness ，TEBB） 。关键特征是基于聚合流的指数分布简化流量特征，并在累积分布函数上进行概率截断对突发进行上界估计，得到紧致的到达曲线，从而演算出端到端时延上界。TEBB的技术优势包括，数据轻量级需求、采用概率截断优化微突发的时延估计、采用上游最大服务能力，优化下游到达曲线，精确刻画到达曲线、支持多种调度算法并推演多种服务曲线。 \n如图3-4所示，当有多条流进入到同一端口时，报文之间可能发生碰撞，某些报文可能会滞后于原始的到达时间，从而引起时延抖动。实际情形下从多个端口流入的流量可能需要汇聚到一个端口流出。当多条相同优先级的流同时从某个端口流出时，它们之间可能会产生竞争。假设有两个报文到达时间上几乎一致或者说发生碰撞，那么其中必定有个报文需要排队，从而实际发送时间将会落后于原本的到达时间。假设端口发送速率为10Gbps，则根据最大包长约12000bit，可以知道排队时间可以达到，这也就是IP网络中时延抖动的重要来源。一个报文在最坏情形下，端到端碰撞次数比例于网络中节点的平均度数*转发路径跳数。 \n截断指数的突发优化 \n在DiffServ模型中，汇聚节点的的流量可以近似为泊松分布，泊松分布流的排队等待时延互补累积分布函数CCDF如下公式： \n公式一 \n其中λ是流量到达速率(即单位时间内到达的数据包个数，，其中为系统输入流量比特率，到达率与包长相关，μ是对应服务速率(即单位时间内服务的数据包个数)，ρ=λ/μ是系统利用率，W(n)是第n个数据包在队列中的排队等待时间，公式一对应的曲线图如图3-5所示，大部分数据包的时延都很小，时延很大的数据包的概率很低，具有长尾分布效应。 \n将带入公式一，即将公式一转换成碰撞次数m的互补累积概率分布公式二 \n公式二 \n其中m是碰撞次数，从概率分布曲线可知，随着碰撞次数的增加，碰撞的概率越来越小。这里引入概率阈值P，求碰撞次数就是让公式二的左边等于概率阈值，带入得 \n公式三 \n超出这个碰撞次数m的概率只有P，即1-P的概率不会超过这个最大碰撞次数m。设置不同的概率阈值，可以得到不同的碰撞次数，阈值是个可调参数。突发量等于碰撞次数乘体积（包长），碰撞一次就要增加一个包长的突发累积，碰撞m次就要增加m*Lmax的突发累积。3.2节的突发量优化为，其中m通过公式二计算。 \n累积突发优化 \n传统的网络演算采用端到端的时延上界计算，无法准确估算每节点的实际流量，从而准确计算每节点的时延上界，导致端到端时延上界估算过高，结果与实际测试值偏差过大。从节点的上游端口获取流量突发量或峰值流速信息，相对于传统确定网络演算由于采用逐跳累积突发量的处理方式，不易造成突发量的高估，从而大大降低时延上界的估计。 \n如图3-6所示是节点的上下游流量示意图，以port3作为计算参考，本端口port3的上游流量来自port1和port2，根据网络演算的流叠加定理，多个流聚合的到达曲线可以通过对多个流的到达曲线进行加和得到，假设两个数据流和的聚合，的到达曲线为，的到达曲线为，则可视为聚合流的到达曲线。根据叠加定理可知，对上游端口的流量突发和峰值流速可以进行叠加，从而得到下游节点的流量突发b和峰值流速p，这是由于上游端口的流量突发和峰值流速反映了上游端口的输出流量信息，这也就是下游节点感知到的流量到达信息。因此可以通过上游端口的输出流量信息，对当前节点的流量到达曲线进行演算。 \n其中，本端口port3的流量信息参数包括： \ningressTraffic： 端口进入流量 \negressTraffic：端口出口流量 \nbandwidth：出端口的带宽，即服务处理能力 \ndualIngressTraffic：所有上游节点的入口流量总和 \ndualBandwidth：所有上游节点带宽之和 \neffectiveBurst：所有上游节点总突发量 \nupstreamPortDelayBound：上游端口时延上界 \n上游节点出端口的流量突发根据排队时延*服务能力进行估计，即burst = bandwidth * upstreamDelayBound，将所有上游节点（A1和A2）的突发累加得到effectiveBurst，即所有上游节点总突发量。 \n将所有上游节点的ingressTraffic（A1和A2的）累加得到dualIngressTraffic，由于所有的到达流量可能不是都从port3端口出去，还有其他出端口有流量出去，而实际从port3出去的流量egressTraffic是已知的（通过测量或者管理模块获取），则根据公式 \n推导出 \n公式五 \n通过上游节点流量突发演算可求出叠加后到达曲线的突发量b,，即上游所有节点到达流的并从port3出去的到达曲线的突发量b。 \n另外流量的平均速率即流量从port3发送的速率，此处为egressTraffic，则远离远点的到达曲线（图3-7上面的直线）可表示为，其中r=egressTraffic。 \n峰值流速优化 \n和3.3.2节类似，上游节点流量突发和峰值流速进行叠加，从而得到下游节点的流量突发b和峰值流速p。 \n对于本节点的处理模块，它能够看到的最大峰值流速应该为流入节点的所有链路的带宽之和，理论最大突发量b即为上游端口的累积突发量。对于调度模块，它能够看到的最大峰值流速即为处理模块的处理能力，理论最大突发量为处理模块的累积突发量。这里的理论最大突发量实际并不可能出现，这是由于峰值流速的存在，巨大的突发量采用峰值流速的形式进入模块。 \n所有上游节点的累积流量为dualIngressTraffic，从port3出去的流量egressTraffic是已知的，而所有上游节点带宽之和（即所有链路的带宽之和）为dualBandwidth，则入端口的最大峰值流速为 \n公式六 \n另外流量的碰撞概率突发量M由3.3.1节计算出，则可得到接近原点的到达曲线（图3-7下面直线）可表示为。 \n截断指数有界突发到达曲线 \n基于3.3.2节可以得到当前节点某个出端口的所有上游节点汇聚叠加后的到达曲线，基于3.3.1节和3.3.3节可以获得基于截断指数的突发到达曲线，即得到了到达曲线远离原点和接近原点的特征，根据最小加卷积公式可以得到每个节点最终的到达曲线，如图3-7所示：', 'id': '1151074_8'}
{'label': 'text', 'content': '网络演算实测和大规模仿真\n设备端到端测试\n路由设备组网图如图4-1所示，共6个路由器，端口带宽10GE。业务流T3的路径经过节点201、202、203和206（棕色实线所示），并将节点201的端口P5和节点206的端口P2连接到测试仪。业务流T3中共有5条高优先级流T3.1-T3.5，优先级都设为7，另外有3条低优先级的BE流T3.7-T3.9 \n轻载情况下，T3.1-T3.5的业务流带宽分别配置为100Mbps、100Mbps、100Mbps、9.472Mbps和17.024Mbps，包长分别为128Byte、512Byte、1500Byte、128Byte和512Byte。背景流带宽2Gbps，总带带宽利用率23%。通过网络演算工具计算5条业务流端到端时延，以及测试仪测量对应的端到端最大时延如图4-2所示。其中准确度是计算值相对测量值偏差程度，计算公式为1-（计算值-测试值）/测试值，准确度最小值为90.12%，最大值为93.78%，可以看到准确度都达到90%以上。 \n重载情况下，T3.1-T3.5的业务带宽分别配置为500M、500M、500M、94.72M和170.24M，总带带宽利用率54%。通过网络演算工具计算5条业务流端到端时延，以及测试仪测量对应的端到端最大时延如图4-3所示。准确度最小值为86.43%，最大值为92.51%，准确度都达到86%以上。 \n汇聚情况下，业务流T2的路径经过节点202、203和204（图4-1浅蓝色实线所示），业务流T3的路径经过节点201、202、203和206（棕色实线所示），业务流T4的路径经过节点201、202、203和204（棕色虚线所示），业务流T5的路径经过节点205、202、203和206（黑色虚线所示），业务流T6的路径经过节点205、202、203和204（深蓝色实线所示），所有流在202节点的端口xgei-1/9/0/9汇聚。其中每个TX中有5条确定性流TX.1-TX.5，优先级都设为7，另外3条背景流。通过网络演算工具计算15条业务流端到端时延，以及测试仪测量对应的端到端最大时延如图4-4所示。准确度最小值为91.1%，最大值为97.72%，准确度都达到91%以上。', 'id': '1151074_9'}
{'label': 'text', 'content': '网络演算实测和大规模仿真\n大规模仿真\n网络演算作为一种网络路径规划的优化工具，提出平均路径边界时延指标和链路带宽利用率指标。通过网络演算可以优化全网的平均路径边界时延，降低带宽利用率，更加负载均衡。开启网络演算前后，平均路径边界时延提升的幅度以及平均链路带宽利用率、超80%利用率占比作为相应参数的指标。 \n现网的路由算法类型主要有最小metric和最小delay。以下的对比主要是有网络演算算法和无网络演算的最小metric、无网络演算的最小delay的对比。设置的拓扑如图4-5所示，共34个节点，每条链路的时延delay按照局域几公里、城域几十公里和广域几百公里配置，对应的时延为us级（5us-10us），百us级（250us-600us）和毫秒级（2.5ms-3ms）；metric值按照现网模板进行配置：接入节点间10，接入与汇聚节点间6220，汇聚节点间200，骨干汇聚与汇聚节点间2210，骨干汇聚节点间4410，核心环（不包括骨干汇聚节点间）20。不同域的链路带宽也不同，接入侧和汇聚侧10Gbps、骨干有50G和100G，图中链路的三元组配置为{metric，delay，bandwidth}。 \n所有业务流的源节点从7-14随机生成， 目的节点从27-34随机生成，业务的带宽设置为1M，不同业务数目的平均路径边界时延数据对比如图4-6所示，不同业务流条数，最小metric算路的平均路径边界时延最大，最小delay和网络演算差不多，其中相对最小metric算法，网络演算的平均路径边界时延指标降低10%-17%。 \n在同样的拓扑和业务流下，对比不同算法的带宽利用率如图4-7所示，分别比较了平均带宽，80%-100%带宽利用率段的占比。轻载情况下，业务流少于1000，平均带宽利用率降低16.7%左右；重载情况下，业务流高于5000，相对于最小metric算法，平均带宽利用率降低21.3%-38.2%；相对最小delay算法，平均带宽利用率降低12.4%-38.2%。超过80%利用率的占比降低30.7%-75.9%(业务流条数大于10000条时才有带宽利用率超过80%)，更加不容易出现拥塞，负载更均衡。 \n基于TEBB的网络演算，在轻载和汇聚的情况下和实际测量值的准确度能达到90%以上，重载情况能达到86%以上。在大规模仿真中，相对传统路径算法，平均路径边界时延降低10%-17%，平均带宽利用率降低12.4%-38.2%，超过80%利用率的占比降低30.7%-75.9%，有效的降低全网的时延和带宽利用率，更加不容易出现拥塞，负载更均衡。', 'id': '1151074_10'}
{'label': 'text', 'content': '参考文献\n张晓楠. 基于随机网络演算的网络流量性能测量与分析[D]. 北京邮电大学, 2021 \n林闯. 多媒体信息网络QoS的控制[J].软件学报，1999(10). \nCruz R L. A calculus for network delay,Part I: Network elements in isolation[J]. IEEE Transactions on Information Theory,. 1991,37(1):114-131 \nParekh A K,Gallager R G. A Generalized processor sharing approach to flow control in integrated services networks: The multiple node case[J]. . IEEE/ACM Transactions on Networking. 1994,2(2):137-150. \nChang C. Performance Guarantees in Communication Networks[M]. Springer Verlag,2000 \nJiang Y,Liu Y. Stochastic Network Calculus[M]. London: Springer-Verlag . 2008 \n李焕忠. 基于随机网络演算的性能分析技术研究[D]. 国防科学技术大学, 2011. \nJean-Yves L B,Patrick T. Network calculus: a theory of deterministic queuing systems for the internet[M].Berlin: Springer-Verlag,2001', 'id': '1151074_11'}
{'label': 'table', 'content': '缩略语\n表 6-1 缩略语说明表\n| 缩略语 | 英文名称 | 中文含义 |\n|:-|:-|:-|\n| NC | Network Calculus | 网络演算 |\n| IntServ | Integrated Services | 综合服务模型 |\n| DiffServ | Differentiated Services | 区分服务模型 |\n| TSN | Time Sensitive Networking | 时间敏感网络 |\n| DetNet | Deterministic Networking | 确定性网络 |\n| DNC | Deterministic Network Calculus | 确定性网络演算 |\n| SNC | Stochastic Network Calculus | 随机网络演算 |\n| GR | Guaranteed Rate | 确保速率调度算法 |\n| LR | Latency Rate | 延迟速率调度算法 |\n| SP | Strict Priority | 严格优先级调度算法 |\n| WRR | Weighted Round Robin | 加权轮叫调度 |\n| WFQ | Weighted Fair Queuing | 加权公平排队 |\n| CBS | Credit-based Shaper | 基于信用整形 |\n| CQF | Cyclic Queuing and Forwarding | 循环排队与转发 |\n| SAC | Stochastic arrival curve | 随机到达曲线 |\n| SSC | Stochastic service curve | 随机服务曲线 |\n', 'id': '1151074_12'}
