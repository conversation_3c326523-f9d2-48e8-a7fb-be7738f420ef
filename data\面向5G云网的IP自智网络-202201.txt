{'label': 'document', 'content': '面向5G云网的IP自智网络-202201', 'id': '1057232'}
{'label': 'text', 'content': '（Autonomous  Network）\nZENIC ONE管控规划团队 @2022.1 \n自智网络业界进展 1 \nIP自智网络解决方案 2 \nIP自智网络应用案例 3 \nZENIC ONE能力及分级演进 4', 'id': '1057232_1'}
{'label': 'text', 'content': '网络自智等级定义\nSAE美国汽车工程师协会的定义： • Level 0：无自动化（No Automation）驾驶员需要完全 负责转向、加速、制动和观察道 路状况； • Level 1：驾驶辅助（Driver Assistance）某些功能可以自动 进行，驾驶员无法做到手和脚同 时不操控； • Level 2：部分自动化（Partial Automation）驾驶员和汽车来 分享控制权； • Level 3：有条件自动化 （Conditional Automation） 自动驾驶，当遇到紧急情况， 驾驶员仍需要在某些时候接管汽车； • Level 4：高度自动化（High Automation）受限的条件下， 人类驾驶员可以全程不用干预 • Level 5：完全自动化（Full Automation）对行车环境不加 限制，可以自动地应对各种复杂 的交通状况和道路环境等。', 'id': '1057232_2'}
{'label': 'text', 'content': 'Ø tmforum Ø 2020年各大运营商分级预期调研结果\n• 2019.05，发布《自智网络白皮书1.0》 • 2020.02，自智网络技术架构立项 • 52%认为当前处于L1；48%认为2年达到L2，并且已经有明确途径达到该级别 \n• 2020.10，发布《AN商业架构1.0》和《AN白皮书2.0》 ；68%认为2~4年达到L3，但依然有32%认为暂无达到L3的明确途径和时间计划 Ø 中国运营商情况 • 2021.10，规划发布《AN商业架构/技术架构2.0》和《AN白皮书3.0》', 'id': '1057232_3'}
{'label': 'text', 'content': "Ø ESTI\n• 2019.10，发布电信网络智能化分级研究报告 • 中国移动/中国联通积极在TMF等标准组织立项自智网络，发布白皮书 • 2020.10，发布《Autonomous Networks, supporting tomorrow's ICT business》白皮书 \n• 2021.04，发布《ETSI Technology Radar》，将自智网络定为2021年十大技术趋势之一 • 中国移动、中国联通牵头在TC7 WG2就IP和光网络智能化分级立项，开始具体", 'id': '1057232_4'}
{'label': 'text', 'content': 'Ø 3GPP 产品的自智网络标准\n• 2019.08，发布《移动网络自智白皮书》 • 中国电信，已达L2，2年内L3，24~25年达到L4，2030达到L5； • 2020.06，R17自智网络分级立项 \n• 2021.03，自智网络研究进入第二阶段 • 中国移动规划，2020-2021年实现L2级，2022~2025年全网达到L3~L4级别 Ø ITU-T 阶段1（2022 全网L2,部分省份L3）；阶段2（2023~24，全网L3,部分L4）；阶段 \n• 2020.12，在SG13研究组成立FG-AN焦点组 3（2025，全网L4）； • 2021.02，FG-AN第一次会议，中移专家曹汐当选副主席', 'id': '1057232_5'}
{'label': 'text', 'content': 'Ø CCSA\n• WG1进行移动通信网络管理与运营智能化水平分级技术要求研究；WG2进行IP/光网络管控与智 能化水平分级能力研究 设备厂商进展 Ø 华为 • 2021年底将形成IP/传输AN白皮书初稿，2022年4月发布 • 联合运营商，积极牵头推动国内外各大标准组织的自智网络分级标准，产品智 \n能化水平业界翘楚', 'id': '1057232_6'}
{'label': 'text', 'content': '7个运营商+中国信通院，14个厂商，35个单位：\n• 承载网总体处于L2+，高于我司水平 \n• 运营商：中国移动、中国联通、Orange、TIM、VDF、NTT、BT Ø 国外厂家 \n• 设备商：华为、Erisson、Nokia、Ciena、FutureWei \n• IT厂商：亚信、亿阳、Microsoft、cognizant、fujitsu、blueplanet、 TEOCO、bearingpoint、ubiqube • 思科，意图网络支持自己设备，方案/产品成熟，暂没有参与自智网络 • 爱立信、诺基亚，参与TMF等自智网络标准指定，尚未查到其产品对标情况', 'id': '1057232_7'}
{'label': 'text', 'content': '业界自智网络进展-TM  Forum\nTMF提出了“单域自智、跨域协同”的三层框架与四个闭环的自智网络参考框架；2019年5 月及2020年10月，TMF联合中国移动及部分设备厂商等先后推出了自智网络白皮书1.0和2.0； 2021.10发布《Autonomous Network 自智网络白皮书3.0》 \nL0 – 人工运维： 系统提供辅助监控能力，所有动态任务都需要人工执行； L1 – 辅助运维： 系统可根据预先配置，执行特定的重复性子任务，以提高执行效率。 L2 – 部分自智网络： 在特定外部环境中，系统可以根据预定义的规则/策略， 面向特定单元 使能自动化闭环运维。 L3 – 条件自智网络： 在L2的基础上，系统可以实时感知环境变化，并在特定网络专业中进 行自优化和自调整，以适应外部环境。 L4 – 高度自智网络： 在L3的基础上，系统可在更复杂的跨多网络领域环境中，实现业务和 客户体验驱动网络的预测式或主动式的闭环管理，从 而进行分析并做出决策。 L5 – 完全自智网络： 这个等级是电信网络演进的终极目标，系统具备面向多业务、多领域、 全生命周期的全场景闭环自智能力。 \n目标：用户体验视角：零接触、零等待、零故障；网络运维视角：自配置、自修复、自优化；', 'id': '1057232_8'}
{'label': 'text', 'content': '业界自智网络进展-国际大T\nTarget is 50% of functions to be zero-touch by end of 2020. 提出要在2025年达到L4级自智网络； CIAS: Cognitive Intelligence and Automation Suite (CIAS) acg-economic-benefits-of-network-automation', 'id': '1057232_9'}
{'label': 'text', 'content': '中国移动自智网络规划\n参考TM Forum网络运营架构模型，构建“234”AN能力分层架构，以2大业务目标牵引，实现3个闭环流程，构建4层协同的 网络运维数智化能力。 \n• 2大业务目标：客户发展、质量领先 \n• 3个闭环流程：客户需求管理闭环、跨专业端 到端业务管理闭环、单专业资源管理闭环 \n• 4层数智化能力：商务层 - 客户服务支撑，业 务层 - 跨域协同，网络层 - 单域自治，网元 层 - 内生自动、内生智能 \n自智网络旨在构建全生命周期的自动化、智能化的网络能力与运维能力，为客户和运维带来新的价值和能力，面向消费者和垂 直行业客户提供“零等待、零故障、零接触”的新型网络与ICT服务，面向网络智慧运维打造“自配置、自修复、自优化”数智 化运维能力。实现自智网络，是网络与运维自身的数智化转型。', 'id': '1057232_10'}
{'label': 'text', 'content': '业界自智网络进展-中国移动\n策略：聚焦直接关联指标，配合完成间接关联指标 \n<table><tr><th>序号 </th><th>类别 </th><th>指标名称</th><th>L22021 </th><th>L2.52022 </th><th>L32023 </th><th>L42025 </th><th>当前值</th></tr><tr><th rowspan="4">4 5 6 7 </th><th rowspan="4">零等待</th><th>家宽业务自动开通成功率 </th><th>95% </th><th>96% </th><th>97% </th><th>99% </th><th>95% </th></tr><tr><th>5G专网业务平均开通时长 </th><th>周级 </th><th>日级 </th><th>日级 </th><th>小时级 </th><th>1周 </th></tr><tr><th>云互联业务平均开通时长 </th><th>分钟级 </th><th>分钟级 </th><th>分钟级 </th><th>分钟级 </th><th>10分钟 </th></tr><tr><th>物联网业务平均开通时长 </th><th>周级 </th><th>日级 </th><th>日级 </th><th>小时级 </th><th>5天 </th></tr><tr><th>14 </th><th>零故障 </th><th>IP设备指令巡检自动化率 </th><th>70% </th><th>80% </th><th>90% </th><th>95% </th><th>86% </th></tr><tr><th>19 </th><th>自配置 </th><th>IP配置自动化率 </th><th>70% </th><th>80% </th><th>90% </th><th>90% </th><th>68%\u3000 </th></tr><tr><th>23 </th><th>自修复 </th><th>IP故障处理自动化率 </th><th>30% </th><th>40% </th><th>50% </th><th>70% </th><th>20%\u3000 </th></tr><tr><th>26 </th><th>自优化 </th><th>IP流量调优自动化率 </th><th>/ </th><th>10% </th><th>50% </th><th>90% </th><th>17%\u3000 </th></tr></table> \n2 • 加快“2+5+N”网管系统架构调整、建设 工 • 加快省级IP运维工作台的建设，增强 • 对网元实时感知、精准控制、故障自愈和本地AI等能力', 'id': '1057232_11'}
{'label': 'text', 'content': '+ 网 提出具体要求，后续推动纳入集采要求\n• 省内云专网同步建设控制器，支持SRv6，BGP 作 控制和分析能力，向管控析一体化系', 'id': '1057232_12'}
{'label': 'text', 'content': '5 元\nFlowSpec等新技术，实现省内云网融合业务 台 统演进，针对自智网络场景需求增强 • 省公司重点关注TWAMP测量和telemetry采集技术， + 系统能力，探索AI应用 netconf、SR policy和FlowSpec等控制调度技术，SRv6转 的自动开通和灵活调度 \nN 发技术和IOAM等，根据设备类型和现网需求适时启用 分析总结：中移总体上着眼业务，工作台及上层系统着重实现跨厂家、跨域的智能化； IP部门关注直接关联指标； \n我司OMC应发挥自身优势，在业务管理、故障和质量管理、网络恢复方面做深做厚，根据中移不同省份情况，适当扩展功能，满足工作台要求 © ZTE All rights reserved \n10', 'id': '1057232_13'}
{'label': 'text', 'content': '业界自智网络进展-中国联通\n中国联通以“ 2023年自智网络等级达到L3级别，2025年自智网络等 级达到L4级别” 为目标； 四零四自” （四零：零等待、零故障、零接触、零风险；四自：自 规划、自配置、自修复、自优化；', 'id': '1057232_14'}
{'label': 'text', 'content': '业界自智网络进展-中国电信\nIP 业务自智网络能力分解', 'id': '1057232_15'}
{'label': 'text', 'content': '中国电信云网融合目标架构\n<table><tr><th colspan="2">一级</th><th>二级</th><th colspan="2">三级</th></tr><tr><th></th><th>自治水平</th><th rowspan="5">意图</th><th>意图获取</th><th></th></tr><tr><th></th><th></th><th>意图识别</th><th></th></tr><tr><th></th><th></th><th>意图解析</th><th></th></tr><tr><th></th><th></th><th>意图分类</th><th></th></tr><tr><th></th><th></th><th>意图反译</th><th></th></tr><tr><th></th><th></th><th rowspan="3">数据</th><th>数据采集 （包括主动上报的数据，告警、 性能等）</th><th></th></tr><tr><th></th><th></th><th>数据处理</th><th></th></tr><tr><th></th><th></th><th>数据管理</th><th></th></tr><tr><th></th><th></th><th>分析</th><th>资源分析</th><th></th></tr><tr><th></th><th></th><th></th><th>业务分析</th><th></th></tr><tr><th></th><th></th><th></th><th>客户分析</th><th></th></tr><tr><th></th><th></th><th>决策</th><th>策略生成</th><th></th></tr><tr><th></th><th></th><th></th><th>决策判断</th><th></th></tr><tr><th></th><th></th><th>执行</th><th>系统执行</th><th></th></tr><tr><th></th><th></th><th></th><th>网元执行</th><th></th></tr></table> \n参考意图网络，按照云网融合2030年目标和架构，进行IP业务自 智网络能力分解，划分上层编排系统、采控平台（含控制器）职 能；采控平台负责采集、分析、单域故障、质量、修复功能。', 'id': '1057232_16'}
{'label': 'text', 'content': '业界自智网络进展-CCSA\nü 2021年，CCSA正在建立网络管理与运营智能化标准体系，按信通院的计划2022年就要落地 ü 信通院在介绍时明确提出，建立智能化评估体系的目的，避免集采时，各厂商自说自话。 ü CCSA的已立项和发布的标准基本是华为的内容，其已制定的IP网络管理智能化分级标准，即将制定的IP网络智能化分级评估方法，直接可用于集采测 试，对集采测试造成很大影响', 'id': '1057232_17'}
{'label': 'text', 'content': '华为  ADN（自动驾驶网络）\n分析： • 方案完整，形成多层闭环，包括云端； 华为ADN成功案例： 1，站点自动化- 用于业务发放 L2.8 2，基站业务自动开通 - 用于业务发放 L2.8 3，智能故障管理 - 用于监控排障之“故障识别、定界定位” L3 4，IOAM智能运维 - 用于监控排障之“故障识别、定界定位” L3 5，IOAM智能运维之AI 故障诊断方案 - - 用于监控排障之“故障识别、定界定位” L3 6，站点自动化之接入环网元破环加点 - 用于网络变更 L2.5 7，链路拥塞带宽自动调优 - 用于网络调优 L2 • 打造AI生态链，NAIE平台积极开放，扩大品牌影响力和竞争力 • 单域自智能力强，推进快，形成一定优势 • 合作积极，与运营商合作积极，引导运营商智能化思路和方案 • 标准投入大，TMF组织合作密切。', 'id': '1057232_18'}
{'label': 'text', 'content': "Cisco  Crosswork  Network  Automation\n分析： • 方案完整，架构上支持管控析编多产品集成部署及微服务部署，灵活性好； IP+OTN、云网编排及端到端自治能力较强； • 意图网络突出，意图开通业务及SR Policy，形成闭环优化； • 网络洞察、situation智能及变更自动化能力强，形成闭环架构； • 网络安全及可信洞察，和其它系统相互协作，形成整体闭环； \nCisco Automaton Use Cases： 1，Accedian Skylight for Cisco network automation 2，Vitria VIA AIOps for Cisco network automation 3，Cox Communications' automation drives business outcomes 4，SR-PCE/COE/LCM、WAE规划仿真、incident AI告警、chang automation、BMP、IP+OTN with sedona HCO、DDoS等；", 'id': '1057232_19'}
{'label': 'text', 'content': '• 突出能力包括意图开通业务、IP流优化及DDoS安全闭环；\nuse cases for automating all aspects of the network lifecycle： 1，Network and service discovery 网络及业务发现 Use an automated and deterministic approach to reliably discover and visualize the network elements, physical topology and existing services 2，Service fulfilment业务发放 （意图管理：翻译验证、自动部署、网络状态感知、动态优化和保障） Automate business service provisioning and enhance service-level monitoring and visibility 3，Network lifecycle management网络生命周期管理 Automate repetitive manual tasks using pre-defined templates and workflows 4，Network and service assurance网络及业务保障 Harmonize operational visibility and enhance proactive resolution capabilities 5，Path placement, optimization and simulation路径优化及仿真 Centralized, instant network resource control with real-time visibility 6，IP-optical multilayer IP+OTN多层协同 Gain full control over the optical topology and use SRLG constraints and optical latency for network optimization 7，流量分析及优化、DDoS with DeepField \n自智网络价值提升 \n<table><tr><th>阶段</th><th>环节</th><th>生命周期描述</th><th>子环节</th><th>痛点</th><th>自智网络目标</th><th>价值提升</th></tr><tr><th rowspan="2">Dayo</th><th>规划设计</th><th>根据对客户现网和业务需求的洞察，输出网络规划方案和网络设计。</th><th>规划设计意图翻译->洞察分析->网络规划->规划仿真->网络设计->设计仿真</th><th>承载组网复杂，规划设计工作量大容易错误仅从理论上分析可靠性，无法直观的评估设计后的性能和对业务的满足度</th><th colspan="2">零接触：操作极简，仅输入规划需求网络规模、可靠性指标，或系统预测出规划需求，系统自动翻译规划时间：小时级别零等待：自动输出高级设计和低阶设计规划出错率：5%以内，零故障：仿真可靠，科学决策+自动决策，评估直避免返工，精准规划观，验证充分，自动纠偏，精准规划</th></tr><tr><th>部署开通</th><th>根据网络设计，完成软硬件硬件安装和调测/优化，输出验收报告，完成设备及网络配置。</th><th>数据制作->数据下发->工程调测/优化->业务验证</th><th>逐台配置，耗时长手工执行大量测试用例，耗费人力和时间</th><th colspan="2">零接触：参数自生成，即自动制作网元参数与策略网络部署时间：缩短零等待：自动感知设备上线，批量配置下发（ZTP）80%，分钟级别，网络零故障：自动生成测试用例，自动生成验收报告一键激活；</th></tr><tr><th>Day1</th><th>业务发放</th><th>规划建设后，通过对客户业务对网络的意图下发到设备</th><th>业务意图翻译->网络状态感知->业务方案设计及仿真->方案评估决策->业务下发->业务验证</th><th>参数多而复杂，耗时长（小时级）无仿真和验证，人工操作的准确率低只有70%</th><th>零接触：业务意图，操作极简，只需输入业务端点和SLA需求，路径自动计算；零等待：自动生成方案，自动下发，自动回退；零故障：下发前的仿真验证、下发后的自动验收保证正确性：对现网已有业务影响最小；</th><th>发放成功率：90%以上发放时长：下发时间减少50%~90%，达到分钟级</th></tr><tr><th>Day2</th><th>监控保障</th><th>对网络状态进行实时监测，及时发现网络故障或隐患，并进行定界定位，分析问题根因并进行故障修复或隐患消除。</th><th>监控排障意图翻译->监控可视->故障识别->隐患预测->定界定位->方案生成->评估决策->方案实施->业务验证</th><th>人工分析告警，问题的定界/定位通常需要数小时修复预案靠人工判断，准确率只有70%左右。修复动作靠人工操作，平均处理一次故障需要5个小时修复执行后需人工验证，耗时可达小时级甚至天级</th><th colspan="2">零接触：自动实时采集全网KPI及业务KPI数据，自动关联分析及可视化展示（高体验）业务中断次数和持续时零等待：故障自识别、RCA、自定位、自修复闭环间：至少减少50%零故障：准确的SLA趋势/资源占用趋势预测，多维√故障修复时间：分钟级全流程可视化，并可推荐可视内容；</th></tr><tr><th rowspan="2">DayN</th><th>网络优化</th><th>根据网络性能测试、客户投诉/反馈、专题分析、资源利用情况等，制定网络优化方案并实施。</th><th>网络优化调参意图翻译->性能优化识别->劣化预测->定界定位->方案生成及仿真->评估决策->方案实施->业务验证</th><th>无法实时在网络侧感知质差问题缺乏快速定位手段，导致资源浪费效率低下</th><th>零接触：网络劣化自识别与预测，SLA质量、流量拥塞、流量预测增长零等待：自动生成调优方案，并自动实施零故障：下发前的仿真验证，下发后的自动验收保证调优正确性；网络资源利用率最高；</th><th>客户体验：满足质量指标</th></tr><tr><th>网络变更</th><th>根据监控排障、优化调参、规划设计产生的网络变更诉求，分析对用户业务的影响，输出变更约束，制定网络变更方案并实施变更。</th><th>网络变更意图翻译->数据采集->方案设计及仿真->评估决策->方案实施->结果验证->异常回退</th><th>手工操作，耗时长（小时级）网络变更风险高，易致业务中断，手工回退操作无法保证100%准确，恢复的时间一般为小时级</th><th colspan="2">零接触：自动评估影响以及执行条件：零等待：变更自实施，网络割接及业务自动完成；√变更效率：分钟级零故障：变更前的仿真验证，业务流自动割接，下√变更准确率：90%以上发后的自动验收，自动异常回退</th></tr></table>', 'id': '1057232_20'}
{'label': 'text', 'content': '网络自智趋势总结 三大运营商智能化策略总结\n<table><tr><th>运营商 </th><th>智能化策略 </th><th>对厂家OMC要求</th></tr><tr><th>中移</th><th>总体上着眼客户业务，工作台及上 层系统着重实现跨厂家、跨域的智 能化；网络部门关注直接关联指标 （业务开通、业务恢复、故障处理、 网络优化） ，具有详细的能力划分 和智能化评级规则 </th><th>厂家OMC负责单域单厂家 的智能化能力；IP产品重 点是工作台，如果参与， 需要具有单域跨厂家的智 能化能力。</th></tr><tr><th>联通</th><th>以“单域自治、跨域协同”理念为 指导，划分36项运维任务，但还未 进行功能细化和智能化评级规则</th><th>厂家OMC具体承担职责未 定</th></tr><tr><th>电信</th><th>按照云网融合2030年目标和架构， 进行IP业务自智网络能力分解，划 分上层编排系统、采控平台（含控 制器）职能；采控平台负责采集、 分析、单域故障、质量、修复功能。 </th><th>TN可能发挥单厂家管理 智能化部分能力，重点 采集、分析、修复；IP 以发挥作用。</th></tr></table>', 'id': '1057232_21'}
{'label': 'text', 'content': '网络智能化断言： • 国内：智能化在中移的推动下将迅速推进\n• 国际：智能化分级逐步明确，进展总体慢一拍 • 同行在智能化方面行动积极，具备一定优势 （HW、Cisco） • 自治网络能力将成为管控产品的核心竞争力 \n<table><tr><th>时间</th><th>判断 </th><th>趋势判</th><th>断</th></tr></table> \n自智网络业界进展 1 \nIP自智网络解决方案 2 \nIP自智网络应用案例 3 \nZENIC ONE能力及分级演进 4', 'id': '1057232_22'}
{'label': 'text', 'content': 'IP网络生命周期及自智（intent+insight+intelligent）\n自智网络愿景：虚实融合，在虚拟空间中仿真，在现实空间中运行； 由虚入实，由实入虚，虚实迭代，持续优化，逐步形成深度学习自我进化的内生发展模式。 \n秘密▲', 'id': '1057232_23'}
{'label': 'text', 'content': 'IP自智网络总体方案 iONE=iPipe+iClock+iDevice+iQoS+iSituation+iRoute+iSlice+iTE+iService+iTree+iFlow+iCross （i  =  intent/insight/intelligent） 层次 方案描述\n用户 1.iWant：用户意图（提供商-网络意图、客户-业务意图、运维 保障人员-优化意图等） 层 应用 2.iCross：算力实时感知、云网意图算法及优化、算力自动均衡、 vBRAS负载均衡；云网SFC最优路径计算及自动优化； 层 IP层 3.iFlow：IP流量监控分析、DDoS及流量自动优化/疏导、 netflow流量矩阵、Twamp/IOAM IP流质量监控分析及自动优化； \n业务 4.iService：基于意图的业务开通、仿真验证、业务健康度/KPI 及业务SLA监控分析、业务故障快速定位及自愈、变更自动化； 层 5.iTREE（组播MVPN over mLDP/TreeSID/BIER）； \n路径 6.iTE：基于意图的隧道部署、TE仿真分析、全网流量实时监控分 析、实时闭环优化、流量均衡、流量预测及扩容；TE镜像及仿真、 预测；网络拓扑、带宽分析、SR流量矩阵及容量规划； 层 虚拟 网络 7.iSlice：基于意图的拓扑/业务切片自动部署、切片仿真、实时 监控及自动变更等； 8.iRoute：基于模型的IGP、BGP、PIM基础协议自动配置ZTP、 IGP及BGP路由仿真分析及Cost规划、路由问题及故障预测； BMP路由镜像及安全分析；IGP/BGP参数变更仿真及自动配置/ 变更、拓扑分析等； \n9.iSituation：网络状态实时感知、异常检测、告警/事件感知、 故障分析、相关性规则AI分析及自动生成、网络故障RCA及修复； \n物理 层 10.iDevice：设备自动化，例如设备健康度、AI节能、扫码上传 设备信息、3rd设备快速纳管和YANG适配、设备版本自动升级、 扩缩环自动化、维护窗及仿真、CPU/MEM/资源容量预测、协议 稳定性分析预测、TCAM表项分析预测等； \n11.iQoS：基于意图的QoS配置、QoS性能实时采集及AI分析、 QoS仿真、QoS参数的自动调整（结合IOAM及业务流QoS性能 分析）；vlan+QoS业务切片仿真部署感知及自动优化；确定性 网络（资源预留、超低丢包/时延、QoS保障、算路优化等） \n12. iClock智能时钟基于意图部署，质量监控及自动调整优化； \nOTN 层 13.iPipe：IP+OTN协同，包括多层网络规划、仿真及what-if分 析，多层部署、业务开通及保障、多层优化、多层维护窗，多层 告警及RCA； \n自智网络业界进展 1 \nIP自智网络解决方案 2 \nIP自智网络应用案例 3 \nZENIC ONE能力及分级演进 4', 'id': '1057232_24'}
{'label': 'text', 'content': '目录             2022落地建议及讨论 5\n1，TE数据建模：在线实时数据+离线规划数据（IETF BGP-LS格式）； 2，部署前：通过仿真，可以模拟业务部署TE资源是否足够、对网络其它业务的影响、网络的各种事件的仿真，实现业务精准部署； 3，部署后：采集全网流量及质量数据，验证是否合规；进行仿真及what-if分析，验证是否存在风险；对历史数据进行预测，预测未来潜在风险； 4，对TE数据及操作历史记录，实现AI故障预测、RCA回溯分析； \n1，网络发现及流量采集（在线/离线） • 网络资源和拓扑发现，通过 snmp/netconf and bgp-ls南向接口； • 采集器实时采集端口/链路/接口/LSP 流量统计信息； 2，网络仿真及分析 \n• 对流量进行建模，进行流量仿真 • 在业务流量源宿端口导入流量需求； • 基于业务流量矩阵及PCE算法，建立 E2E LSP路径； • 变更仿真，包括拓扑、Metric等，输 出风险评估报告； • 故障仿真，输出业务风险评估报告. • 流量仿真，导入业务流量，输出对网 络的影响分析报告； • Worst Case最坏情况仿真，输出网络 风险评估报告； 3，网络编程 \n• 网络调整/变更，包括拓扑变更、业务部 署等； \n方案流程： 1，配置拥塞阈值，配置调优的两个目标值分别是50% （无故障）和70%（单次故障），并配置拥塞后触发调 优； 2，模拟增加隧道的流量，使汇聚侧的流量达到拥塞， 在流量调优界面看到物理链路的流量情况，汇聚环的链 路存在拥塞，例如超过50%； 3，触发调优，控制器给出优化结果，从界面上能够直 接看到调优之后的链路流量的情况，尽量控制在50%以 下； 4，人工确认/自动下发 隧道优化路径； 5，单次故障仿真发生后，局部链路拥塞超过70%；触 发调优，控制器给出优化结果，从界面上能够直接看到 调优之后的链路流量的情况，尽量控制在70%以下； 6，当50%和70%都不能满足，则对网络拓扑进行编辑/ 变更（例如增加/删除节点、链路、增加新的lsp等）， 同时给出相应的流量仿真分析报告，例如全网链路拥塞 情况等）； \n故障仿真及流量仿真： 1，全部业务over SR-TE（需要支持SR-TE仿真）； 2，部分业务over SR-TE，部分业务over SR-BE（需要支持SR- BE仿真）； 3， \n方案流程： 1，流量模型变化及网络规划 （1），全局流量增长，例如10% （2），全局流量降低，例如10% （3），特定的LSPs流量增长 或 降低 （4），为新的源和目的节点 创建新的流量需求 （5），完成规划后，LSP优化可执行； （6），完成规划后，故障仿真可执行； \n网络扩容 及网络规划 （1），在已有的bundle接口上增加新的链路； （2），在已有的区域（不在同一个bundle接口）上增加 新的链路 （3），增加新的节点 （4），在一些链路上调整isis metric （5），规划完成后，可进行LSP优化 （6），规划完成后，可进行故障仿真 \n报告/报表 （1），资产报表（硬件列表、软件版本、IP地址） （2），资产变更报告，及对应的配置脚本； （3），流量统计报告 （4），网络利用率/效率 报告 网络效率= 全部隧道利用率(Gbps）/ 全部链路带宽（半 双工） \n方案流程： 1，控制器采集全网Cost； 2，通过netflow采集业务流量矩阵（A-Z-带 宽）； 3，控制器基于业务流量矩阵（A-Z，无SLA 要求） 计算业务矩阵的最小cost路径；并把业务 流量叠加到路径经过的链路上，并进行展示； （控制器算法和设备IGP算法一致，避免算路 不一致问题！） 4，进行故障仿真，重新计算 受影响的路径； 并重新对流量进行疏导、优化及展示； 另外，还需要分析对SR-BE业务的影响，例如 新的SR-BE路径的cost/跳数/时延的变化；是 否引起链路拥塞？影响哪些业务等； 5，进行流量仿真，对现有业务流量进行增加 /降低，对路径沿路流量进行更新、展示； 6，模拟增加新的业务，并重新进行路径计算 及流量疏导、展示； \nZENIC ONE控制器 方案流程： 1，业务 A、Z及SLA（带宽、时延、丢包、抖动等） 需求及建模，离线导入控制器； 2，基于业务A-Z及SLA，进行拓扑、节点容量、链 路带宽、时延、Cost规划和设计； 3，控制器基于业务SLA、拓扑及链路带宽、时延 Cost等信息，计算BE和TE路径；并把流量矩阵及 流量模型叠加到路径及链路上，进行流量展示； 4，控制器可对拓扑、Cost等信息进行 合理性分析 （结合网络仿真），在满足业务SLA要求前提下， 尽量提升全网带宽资源利用率（网络效率最高）； 5，进行故障仿真及（节点/链路故障）Worst Case仿真，分析故障场景下，全网流量是否存在拥 塞、对现有业务SLA是否有影响？并输出分析报告； 6，进行流量仿真，模拟业务流量未来增长，例如 未来半年，PE1-PE2流量增加30%，对网络容量及 业务SLA的影响，并输出分析报告； 7；当网络及业务部署后，可采集实际的网络拓扑、 链路、接口及隧道流量，包括流量矩阵 （netflow/SRTM）；和之前离线的分析结果进行 对比分析及验证； 8，基于实际的业务及流量信息，进行故障仿真及 流量仿真，输出what-if分析报告，并对网络进行 优化和调整； 9，对流量矩阵进行分析及预测，结合网络仿真， 提前进行风险评估、容量规划等； 网络效率= 全部隧道利用率(Gbps）/ 全部链路 带宽（半双工） \n1.业务开通前验证：通过资源检查、仿真验证的等措施保障配置方案正确性； \n2.业务开通后验证：基于实际转发表，例如ping/trace，验证网络转发行为与方案设计的一致性；基于业务流量和志良，例如twamp/ioam，对连通性、SLA指标完成性能 测试，最终保障部署方案和业务意图一致性。', 'id': '1057232_25'}
{'label': 'text', 'content': '1、部署前验证 2、部署后验证\n部署前验证 1、在线仿真验证：包括VPN可达性， SRv6-Policy隧道可达性，路由策略有效性，路由环路， 路由黑洞，VPN隔离 2、验证后处理：基于专家经验库和知识图谱对验证结果进行影响评估，并提供配置修改/方案修 该建议，可降级发放等处理预案，供用户选择继续下发or返回修改 部署后验证： 1. 系统自动执行数据面验证,包括VPN可达性， SRv6-Policy隧道可达性，路由策略， 路由环路，路由黑洞，VPN隔离，带宽、时延、丢包验证，生成数据面验证结果 2. 自动完成业务连通性、测速、时延丢包检测、可靠性测试 3. 输出验证报告 \n部署前验证关键能力： 1、存量网络配置定时同步、配置变更实时同步 2、仿真验证能力，支持协议ISIS/OSPF/BGP,IPv6, SRv6 ,EVPN 3、仿真验证效率：分钟级大规模网络仿真验证，支持增量验证 4、基于专家经验库和知识图谱对验证结果进行风险评估，推荐修复预案 部署后验证关键能力： 1. 网络快照数据采集，包括配置、转发表等，实时采集 2. 数据面验证能力，包括VPN可达性， SRv6-Policy隧道可达性，路由策略，路 由环路，路由黑洞，VPN隔离 \n方案流程（PCE-init） \n远端PE通告带color的VPN路由 1 头端收到VPN路由，并向PCE请求路 径计算 2 \nPCE根据BGP-LS收集的拓扑拓 扑信息及业务SLA要求进行路 径计算； 路径结果下发 4 \n头节点创建相应的SR-LIST 6 不同颜色的VPN路由引流到不 同的SR-LIST \n<table><tr><th>Color </th><th>TE type </th><th>Forwarding path </th><th>Path O</th><th>ption</th></tr></table> \n7 当网络出现故障、流量拥塞 及性能劣化，则触发PCE重优 化，进行流量/时延优化；通 过PCE集中计算，使得全网链 路利用率尽量均衡，提升全 网资源利用； \n路径分离、共路、ECMP 网络负载均衡/拥塞控制 多约束灵活按需组合 \nu 高利用率：全网带宽利用率超过 80% u 高性能：拓扑剪枝算法，分布式 计算 u 多约束算路，按需灵活定制 u 基于网络切片子拓扑的算路及重 优化 u 装箱算法，提升资源利用率； u 结合网络大数据的智能算路及业 务快速自愈； u 基于意图的按需TE部署； u SRv6 SRH灵活高效的压缩算法， 降低硬件要求，提升网络可编程 能力； u 基于AI的网络优化算法 u 基于AI的网络震荡预测 \nn 结合区域地图实现全网资源（业务、端口、链路、环网）未来3个月的流量预测可视化呈现，并且可以通过播放功能 实现预测结果的快速预览，定位出现瓶颈的日期和资源 \nn 流量增长预测目前采用时间序列预测，预测准确性大于80%； \n主要功能点： 1.资源瓶颈可视化：区域地图形成 全网预测可视化呈现，资源TopN 排名和瓶颈趋势跟踪 2.预测结果可播放：未来3个月的 预测结果可自动播放，并且可切换 播放速度 3.预测趋势可下钻：单个资源可进 一步下钻预测趋势，方便对比实际 数据和预测数据 4.告警显示：某个区域内有指标超 过阈值，显示气泡和数字，表示有 多少个超过阈值，双击气泡可以看 到详细信息 5，基于精准预测的结果，自动进 行带宽调整及扩容（增加链路、节 点、FlexE带宽调整等）；', 'id': '1057232_26'}
{'label': 'text', 'content': '方案流程\niFlow智能流分析： 1，用户IP业务流从gNB到EPC； 2，用户通过控制器为IP业务流分配Flow ID，采用SIP+DIP+AC来标识业务流， 封装节点配置染色周期，封装节点和流 路径配置基于Flow ID统计，封装节点开 启染色功能； 3，用户通过控制器订阅业务流路径上网 元的统计数据，开启计算丢包和时延 （在哪些网元配置/订阅，控制器可以根 据业务配置或其它的辅助手段获得，例 如SR隧道的trace功能等）； 4，订阅后设备开始上报统计数据，控制 器根据上报的数据计算并呈现结果给用 户。 5，结合IOAM性能测量数据（丢包、时 延、抖动等），快速定位性能相关问题， 并通过人工/自动方式 调整路径，实现业 务快速自愈； 例如，当基站空口由于承 载网丢包导致性能下降，则触发 IOAM 性能监测，通过IOAM可快速精准的逐跳 检测，定位到哪个节点/链路 出现问题， 并快速进行排查；', 'id': '1057232_27'}
{'label': 'text', 'content': 'UC7-iFlow（netflow，业务流量精准洞察及闭环自动化）\nn 支持IP流定义，IP流采集分析、报表及doshboard展示； n 支持IP流量BGP-FS流量导流到SR/SRv6 Policy； n 触发条件：链路带宽利用率超过阈值；', 'id': '1057232_28'}
{'label': 'text', 'content': '方案流程\n智能路由分析： 1，控制器南向支持BMP协议（RFC7854），全面 监控BGP状态：peer up/down、路由监控、增量 路由通告和回收、状态报告，统计计数、路径镜像、 TOPN路由、时间戳等； 2，结合网络VPN/EVPN 业务配置信息，可以进一 步分析 BGP peer和承载的隧道的关系，监控流量 信息； 3，通过BMP，可以监控传统 L3VPN/L2EVPN/L3EVPN/SRv6 EVPN 前缀路由 信息及状态，例如异常注入的路由，进行安全分析； 4，可以监控IPv4、IPv6前缀信息，对应的VRF和 color，通过路由的 源+目的+color id可以下钻到 对应的隧道/SRv6 policy； 5，按需选择某个节点、peer及协议类型进行监控 分析； 6，基于外部数据源和在线数据进行半/非监督学习， 构建BGP ROA数据库，识别路由劫持； 通过对历史BGP泄露，对BGP时间进行训练，实现 BGP异常的AI检测； 通过路由监控，将异常路由信息实时上报ZENIC ONE，提前发现并预防故障 若发生路由问题，自动优先审查异常路由，实现分 钟级故障排查和业务恢复 7，通过BMP采集算力路由信息，对网络周边算力 进行分析和预测，提供最优路径和算力均衡服务； \n1、网元和业务、隧道相关上报给控制器 2、用户选择要维护的节点或者链路以及维护时间 1、网元维护结束，拓扑重新上报给控制器 2、控制器把原来保存的隧道路径通过PCEP重新下发 3、维护窗事件下发给控制器 3、如果因为链路信息变化，导致原来LSP路径无法下发，通过控制器通告给APP 4 、控制器对多个重叠事件进行仿真，给出影响分析给用户确认； 4 、控制器进行业务分析，提示用户LSP无法切回原来路径 如果无法进行维护窗事件，提示需要进行节点/链路扩容； 5、用户对当前链路进行调整，保证LSP按照原来保存的路径能够正常下发 5、维护窗事件执行，通过PCEP通道下发隧道的路径切换 6、结合快速上线，快速构建功能，完成网元基础配置的自动处理，并进一步进行 隧道的批量迁移处理，提升网络效率。 \n痛点：设备长时间运行，功耗大', 'id': '1057232_29'}
{'label': 'text', 'content': '设备信息采集：\n采集全网设备的流量（端口、子接口及隧道）、设备/单板功耗、CPU利用率等信息， 并可视化展示；从网元采集各端口15分钟流量数据，计算单板物理端口接收和发送流 量；', 'id': '1057232_30'}
{'label': 'text', 'content': '设备流量AI学习及训练：\n1、基于单板流量模型进行AI学习和训练. 2、模型训练结果传递到设备（模型训练结果 是以网元为单位的） 3、将设备发起的保护带宽预测请求进行AI训练； 4、将预测的保 护带宽信息下发到设备 5、修改设备节能开关，同时发出设备的单板流速模型训练。 6、基于环网保护、板卡流量预测及可视化展示；7、实现环网保护流量预测 及预留带 宽预测 ，通过控制器和设备联动协同环网保护带宽预测； 8、基于30天历史数据，进 行网元单板长周期流量预测，并下发推理模型给设备；', 'id': '1057232_31'}
{'label': 'text', 'content': '节能：\n1、节能开关、节能模式选择、节能效果显示 \n方案流程： ① 采集全网OLT、BRAS资源利用信 息。（第三方系统/直采） \n② 根据每OLT流量，对一个BRAS池 下的全部OLT进行分组，使得正常 情况下每组OLT的总流量（对应一 个BRAS-U的100GE接口）贴近均 匀分布，且满足接口利用率、单 板并发会话数等阈值要求。 \n③ BRAS-U slot、shelf用户表资源利 用率达到设置的阀值；例如： 64k/slot、512k/shelf，用户上线数 达到80%时触发自动重优化或者 手工重优化提醒。 \n④ BRAS-U接入链路流量利用率达到 设置的阀值；例如：当端口流量 达到80%时触发自动重优化或者 手工重优化提醒。 \n⑤ 操作员可选择对历史的流量/上线 用户数进行建模预测，预测的结 果可同步给出优化建议，供操作 员选择确认后进行重优化。 \n⑥ 根据网络运行信息，刷新资源分 配方案并展示预期效果，（可经 人工调整后）生成Leaf、P-Leaf、 BRAS-C/U的配置更新文件。 \n⑦ 自动触发配置更新。 \n技术特征：由集中控制点（调度系统/网络控制器）进行计算资源和网络资源的统一调度。 \n资源池功能/服务能力通告： n PE设备获得资源池提供的功能/服务能力，通告给 网络控制器 调度系统/控制器进行调度决策： n 根据计算资源和网络信息进行统一调度决策，调 度策略下发给CPE/PE设备 资源池资源信息收集： n 调度系统通过云管理平台收集资源池内计算资源 信息 CPE/PE执行策略： n CPE/PE执行策略（分类、路径选择等） \n网络质量参与节点选择的STEP 0、CDN节点上报算力信息 1、用户请求CDN访问节点 2、TCS向控制器发送IP请求网络质量 3、控制器基于IP下发网元检测业务 4、SR基于检测结构返回网络质量 5、控制器返回网络质量信息 6、TCS计算后返回用户最优节点 7、用户向最优节点请求内容并返回 \n提高CDN网络传输质量，实时反应CDN服务质量，提高运营商CDN服务竞争力 均衡CDN使用率和负载，提高CDN带宽利用率，优化用户体验和客户服务 路径调优的STEP 1. TCS向控制器请求路径调优 2. 控制器计算调优方式并下发检测 3. 网元实际建立调优路径 4. 返回新的网络质量检测信息 5. 控制器更新网络质量信息 6. TCS重新计算最优节点 \n2，多层仿真 l NCO负责IP网络和光网络的协同，IP 和光网络控制器分别负责各自网络的业务开通和 路径计算 l IP/MPLS网络的控制平面保留在转发设备，PCE功能迁往IP控制器 l 光网络控制平面完全迁往光控制器 l IP与光利用GMPLS UNI交互信息 l 多层网络部署、资源发现、业务部署；多层仿真；多层告警关联及AI告警RCA分析； l 多层流量监控及Auto BoD； \n© ZTE All rights reserved 3，多层维护窗 \n自智网络业界进展 1 \nIP自智网络解决方案 2 \nIP自智网络应用案例 3 \nZENIC ONE能力及分级演进 4', 'id': '1057232_32'}
{'label': 'table', 'content': 'ZENIC  ONE总体架构（管控析编融合，可分可合）\n<table><tr><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th></tr><tr><th></th><th>产品门户 统一北[</th><th></th><th></th><th></th><th></th><th>口</th><th>OpenAPI</th><th></th><th></th><th></th><th></th><th></th></tr><tr><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th>统一资源</th><th></th></tr><tr><th></th><th></th><th rowspan="2"></th><th colspan="2"></th><th></th><th colspan="2"></th><th></th><th></th></tr><tr><th>业务意图</th><th>网络切</th><th></th><th colspan="2">网络协同</th><th>见划仿真</th><th colspan="2">巡检工具</th><th>工作流引擎</th><th></th></tr><tr><th>网络 分析</th><th>流量 质量 分析 预测</th><th></th><th></th><th></th><th></th><th rowspan="4">业务开通</th><th></th><th></th><th></th><th></th><th>策略中心</th><th></th></tr><tr><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th>L2VPN L3VPN</th><th></th><th></th><th></th><th></th></tr><tr><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th>告警管理 性能管理</th><th></th></tr><tr><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th>MVPN EVPN</th><th></th><th></th><th></th><th></th></tr><tr><th>网络 管理</th><th>设备 配置 跨专业 割接 管理 管理 管理 迁移</th><th></th><th></th><th></th><th></th><th rowspan="7">网络控制</th><th></th><th></th><th></th><th></th><th></th><th></th></tr><tr><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th>iTE算法 网络优化</th><th></th><th></th><th></th><th></th></tr><tr><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th></tr><tr><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th>安全中心</th><th></th></tr><tr><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th>SRv6 SR-MPLS</th><th></th><th></th><th></th><th></th></tr><tr><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th></tr><tr><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th>日志中心</th><th></th></tr><tr><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th></tr><tr><th colspan="2">管理接口（netconf/cli等） 控制接口（pc</th><th></th><th></th><th></th><th></th><th>ep/bgp等）</th><th>采集接口（telemetry等）</th><th></th><th></th><th></th><th></th><th></th></tr><tr><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th></tr><tr><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th>统一拓扑</th><th></th></tr><tr><th colspan="2">公共应用</th><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th></tr><tr><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th></tr><tr><th></th><th></th><th></th><th>统一安装部署 应用开发框架</th><th>第三方插件开发框架 系统自监控</th><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th></tr><tr><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th></tr><tr><th></th><th>K8S容器云集群 DevOps</th><th></th><th></th><th></th><th>微服务平台</th><th></th><th></th><th></th><th></th><th></th><th></th><th></th></tr><tr><th></th><th></th><th></th><th></th><th></th><th rowspan="2">容基础数据</th><th>灾 库</th><th>应用中间件 大数据服务</th><th></th><th></th><th></th><th>应用中间件</th><th></th></tr><tr><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th>基础数据库</th><th></th></tr><tr><th></th><th>Docker</th><th>高可用&容灾</th><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th></tr><tr><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th></tr><tr><th>laas</th><th></th><th>操作系统</th><th></th><th></th><th></th><th></th><th></th><th></th><th>裸金属</th><th></th><th></th><th></th></tr><tr><th></th><th></th><th>VMWare TECS OpenStack</th><th>OpenStack</th><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th></tr></table>', 'id': '1057232_33'}
{'label': 'text', 'content': 'ZENIC  ONE总体架构（管控析编融合，可分可合）\np 系统架构： 微服务、BS架构 的分布式系统，管理+控制+分 析融合系统； \np 设备管理：支持 vBRAS\\IP\\IPRAN\\PTN\\SPN\\O TN统一管理，统一模型、统一 资产、统一界面； \np 管理能力：支持集群弹性扩展， 负载均衡，具备30万+超大网络 管理能力； \np 能力开放：具备北向可编程能 力，支持第三方扩展应用开发和 部署； \np 闭环运维：具备采集、分析、 监控、控制的闭环运维能力，在 一个APP中完成应用场景； \np 智能运维：利用AI+大数据技 术，提供仿真、预测、分析、控 制能力，降低网络运维难度', 'id': '1057232_34'}
{'label': 'table', 'content': 'ZENIC  ONE总体架构（管控析编融合，可分可合）\np 智能运维：利用AI+大数据技 术，提供仿真、预测、分析、控 制能力，降低网络运维难度\n<table><tr><th colspan="2">生命周期</th><th rowspan="2">评估级别</th><th>ZENIC ONE (1.95)</th><th colspan="2">华为iMasterNCE（2.4）</th><th colspan="2">Cisco Crosswork (2.75)</th><th colspan="2">Nokia NSP (1.91)</th></tr><tr><th rowspan="7">阶段DayoDay1Day2DayN</th><th rowspan="7">环节规划设计部署开通业务发放监控排障网络变更优化调参</th><th></th><th>自音网络</th><th>评估说明</th><th>评估级别</th><th>评估说明</th><th>评估级别</th><th>评估说明</th><th>评估级别</th><th>评估说明</th></tr><tr><th></th><th>零接触：操作极简，仅输入规划需求网络规模、 可靠性指标，或系统预测出规划需求，系统自 动翻译 零等待：自动输出高级设计和低阶设计 零故障：仿真可靠，自动决策，评估直观，验 证充分，自动纠偏</th><th colspan="2">基本人工操作+部分仿真L1.8IP:正在网络规划工具（马来TM），IPPlanner（TOP500）仿真能力弱</th><th>L2.5</th><th>IPCRYSTAL可完成物理网络及逻辑网络的搜集；完成网络的仿真规划</th><th>L3</th><th>WAE规划仿真能力很强，已商用部署，业界第一；</th><th>L1.8</th><th></th></tr><tr><th></th><th>零接触：参数自生成，即自动制作网元参数与 策略 零等待：自动感知设备上线，批量配置下发 零故障：自动生成测试用例，自动生成验收报 告</th><th colspan="2">具备IP网络模型的数据制作能力，以及远程软件调测能力L1.8但要人操作。支持基于规划工具及智能脚本下发配置（ZTP）；无仿真能力；</th><th>L2.2</th><th>华为ZENICONE5G-IP的极简部署数据制作：L2网络配置：L2SLA配置：暂无软件调测：L2</th><th>L2.5</th><th>ZTP</th><th>L2.2</th><th></th></tr><tr><th></th><th>零接触：操作极简，只需输入业务端点和SLA 需求 零等待：目 自动生成方案，自动下发 零故障：下发前的仿真验证、下发后的自动验 收保证正确性</th><th colspan="2">自动激活能力具备2、IPRAN极简业务开通，支持业务模板；部分资源自动分L2.2配，不具备批量导入和资源池查看3，支持人工触发ping/trace联通行检测；</th><th>L2.5</th><th>1.自动激活能力具备2、网管上IP-L3VPN具备批量导入能力；站点自动化3、具备开通后快速免仪表测试验证</th><th>L2.8</th><th>NSO支持业务极简部署，易用性好支持业务仿真；</th><th>L2</th><th>意图管理：IBNframeworkwithworkflowmanagement</th></tr><tr><th></th><th>零接触/零等待：故障自识别、自定位、自修复 闭环 零故障：准确的SLA趋势/资源占用趋势预测 多维全流程可视化，并可推荐可视内容</th><th colspan="2">传统的告警维护功能L1.82、传统的人工故障分析，少量场景工具化能力</th><th>L2.5</th><th>实现故障聚合和故障分析2、可视化网络感知及自动定位能力</th><th>L2.7</th><th>CrossworkSituationManager购买了成熟的moogsoftAl技术，智能告警Al能力强：支持networkinsightBMP路由监控分析；</th><th>L2</th><th></th></tr><tr><th></th><th>零接触：自动评估影响以及执行条件 零等待：变更自实施 零故障：下发前的仿真验证，业务流自动割接 下发后的自动验收，自动异常回退</th><th colspan="2">传统手工方式定制割接能L1.9力2、暂未提供变更实施/自动校验能力</th><th>L2.2</th><th>具备一定割接场景仿真验证能力2、方案生成能力暂不了解</th><th>L2.5</th><th>Crosswork支持KPIhealthinsight及changeautomation闭环自动化；</th><th>L1.5</th><th></th></tr><tr><th></th><th>零接触：网络劣化自识别与预测，SLA质量、 流量拥塞、流量预测增长 零等待：自动生成调优方案，并自动实施 零故障：下发前的仿真验证，下发后的自动验 收保证调优正确性</th><th colspan="2">L2.2定程度的自动优化</th><th>L2.5</th><th>自动流量调优，支持流量仿真；</th><th>L3</th><th>CrossworkOptimizationEignie支持网络优化、均衡、LCM拥塞管理及流量仿真分析；</th><th>L2</th><th></th></tr></table>', 'id': '1057232_35'}
{'label': 'table', 'content': 'ZENIC  ONE总体架构（管控析编融合，可分可合）\nIP自智网络当前进展及分级演进\n<table><tr><th>方案 </th><th>L2+(Now） </th><th>L3（22~23） </th><th>L4（23~25)</th></tr><tr><th>i Want </th><th>满足，业务自动配置，非实时采集业务及 网络KPI，系统分析，人工决策和优化</th><th>满足，业务自动配置，实时采集业务及网络KPI，系统 分析决策和优化</th><th>基于意图开通业务，实时监控分析， AI预测及决策，意图验证及优化；</th></tr><tr><th>i Cross </th><th colspan="2">基本满足，21Q4； 算网路由监控分析及预测、云网一体算法、自动优化及算 支持算网融合及自动优化，支持SFC业务链；力均衡；</th><th>算网意图，自动规划仿真，自动部署， 自优化及算力均衡；</th></tr><tr><th colspan="2">i Flow 基本满足，21Q4支持IOAM及netflow采集， 人工分析；</th><th>P流量及质量分析预测、闭环优化、DDo S安全分析预测 及BGP-FS闭环；</th><th>仿真、意图</th></tr><tr><th colspan="2">i Service 满足，支持业务模板 </th><th>业务意图，业务仿真及验证，业务SLA实时保障及自愈；</th><th>变更自动化</th></tr><tr><th colspan="2">i Tree MVPN over m LDP配置，不支持自动配置 </th><th>BIERv6配置、组播业务规划、仿真，组播树路径计算， 组播业务快速部署，组播业务监控及保障，自优化/自愈；</th><th>基于意图的组播部署、分析预测及自 优化；</th></tr><tr><th colspan="2">i TE 满足，支持TE算路、优化、流量预测； </th><th>流量矩阵、规划仿真（TE+BE）、预测、容量规划及自动 扩容</th><th>意图、数字孪生</th></tr><tr><th colspan="2">i Slice 满足，支持FA/多IGP实例切片； </th><th>Flex带宽预测、扩容及闭环优化、仿真、预测及自动扩容</th><th>意图切片开通</th></tr><tr><th colspan="2">i Route 基本满足，支持BMP路由监控及基本分析 </th><th>IGP+BMP分析、路由安全分析、预测 </th><th>协议仿真、变更仿真</th></tr><tr><th colspan="2">i Situation 基本满足，支持告警、过滤、有限的告警关 联规则；</th><th>告警及AI关联、AI节能及故障快速诊断，故障预测； </th><th>故障自修复及变更自动化</th></tr><tr><th colspan="2">i Device 基本满足，telemetry采集设备cpu/mem/ 温度/流量等；</th><th>设备及网络健康洞察，扩缩环/维护仿真及自动化，设备 健康分析及预测；</th><th>设备健康精准预测、变更自动化</th></tr><tr><th colspan="2">i Qo S 满足，支持Qo S配置，Qo S性能采集 </th><th>Easy Qo S部署，采集及分析；小颗粒切片、自动开通及自 动优化；</th><th>意图Qo S部署，洞察及变更自动化</th></tr><tr><th colspan="2">i Clock 满足，支持1588+Sync E部署 </th><th>1588+Sync E一键部署，质量监控及自优化 </th><th>时钟网意图部署</th></tr><tr><th colspan="2">i Pipe 基本满足，21Q4；支持IP+OTN多层网络部 署、多层业务开通、多层仿真及多层AI告警</th><th>层 规划、仿真、闭环优化</th><th>多层意图部署、意图仿真及监控和优化；</th></tr></table>', 'id': '1057232_36'}
{'label': 'text', 'content': '一个抓手，两个复制，两个拉通：\n以中移为抓手，满足中移要求，移动集采前二 两个复制：国内向联通、电信复制，国际向重点运营商复制； 两个拉通：SPN拉通IP，与OTN互相拉通； 等级：全面提升智能化，22年L3，23年L3.5，24年L4 \n差异竞争：选定1-3个具体功能，2022年实现L4，扩大影响，与同行竞争 积极合作：选择中移重点省份，进行智能化能力创新合作，巩固市场，扩大影响，提升能力 体系运作：加强标准、技术和产品投入，深化智能化单域整体方案，提升竞争力，拓展价值', 'id': '1057232_37'}
{'label': 'text', 'content': 'L4    2024.09\nL3.5 2023.09 • 2024.09满足中移L4要求，满足 集采要求 • 2023.09满足中移L3.5，满足集采要求 • 功能复制到电信、联通、国际市 场（OTN、IP） L3 2022.09 • 部分功能复制到电信、联通及国际重点运 营商（OTN、IP） • 2023具备意图网络和数据孪生商用能力 • 2021.12完成智能化演进规划 • 2022.09优先满足中移L3要求，对齐HW', 'id': '1057232_38'}
{'label': 'text', 'content': '数字孪生技术的应用及价值\n1.美国《阿波罗13》，地面模拟舱，救援宇航员 2.德国西门子及工业4.0，虚拟制造、仿真及调试、闭环优化 1.特斯拉基于数字孪生制造、数据采集及闭环优化', 'id': '1057232_39'}
{'label': 'text', 'content': '数字孪生网络的价值：\n续优化，逐步形成深度学习自我优化的内生发展模式， 大大提升网络的运维能力和水平。此\n外数字孪生模式下，通过数字空间的信息关联，可增进现实世界的实体交互，实现情景交融式\n的网络服务，真正做到意图驱动的自智、自我优化和进化的网络；', 'id': '1057232_40'}
{'label': 'text', 'content': '数字孪生驱动的自智网络\n55\n1，从物理网络空间，通过南向接口实时 采集现网拓扑、流量及状态等数据，并完 成网络数字空间的建模及数据镜像； 2，在网络数字空间完成仿真分析，例如 故障仿真、流量仿真、变更仿真、worst case分析等，分析预测物理网络存在的潜 在风险及瓶颈，并告知用户提前扩容； 3，用户基于仿真分析结果，对网络进行 变更，例如增加节点、增加链路、FlexE 带宽调整、参数优化等； 4，当新设备、新链路上线，则自动下发 相关配置到设备，完成网络的变更调整； 5，物理网络形成新的平衡，例如流量基 于新的拓扑重优化，之前出现的拥塞消失 或减轻； 6，物理网络空间的状态通过telemetry 实时同步给数字孪生体，和前期方案进行 验证及方案优化；数字孪生基于时序记录 网络的历史状态，实现历史回溯及未来预 测；最终，通过数字孪生，实现网络的自 智。', 'id': '1057232_41'}
{'label': 'text', 'content': '自智网络业界进展 1\nIP自智网络解决方案 2 \nIP自智网络应用案例 3 \nZENIC ONE能力及分级演进 4', 'id': '1057232_42'}
