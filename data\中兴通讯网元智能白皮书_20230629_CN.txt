{'label': 'document', 'content': '中兴通讯网元智能白皮书_20230629_CN', 'id': '1121763'}
{'label': 'text', 'content': '网元智能白皮书\n（V1.0）\n网元智能白皮书\n| 版本 | 日期 | 作者 | 审核者 | 更新内容 |\n|:-|:-|:-|:-|:-|\n| V1.0 | 20230629 | 施云涛10294198 | 李宏飚10005649 | 发布第一版 |\n| | | | | |\n| | | | | |\n| | | | | |\n', 'id': '1121763_1'}
{'label': 'text', 'content': '前言\n近十年来，人工智能技术（Artificial Intelligence）迅猛发展，在挖掘大数据样本的非线性规律、与环境交互的在线精准决策等方面快速超越了以人工为主的专家经验（Human Intelligence）模式，在计算机视觉、自然语言处理等领域取得了巨大的成功。 \n自 5G 开始，人工智能在通信网络领域得到广泛应用，如从网管级别的网络配置化，到网元级别的资源调度优化，甚至网络物理层的智能化。对于运营商而言，需要利用网络智能化技术大幅降低网络运营成本，网络运营维护需要从局部的智能化运维向高水平的网络自治演进。 \n目前的人工智能主要是以云端智能为主，在云端汇聚大量的数据，利用集中的算力对数据进行预处理，AI 模型训练和验证等。但是在网络中传输大量的原始数据，一方面会对网络的传输带宽带来巨大压力，另一方面对数据隐私保护也会带来巨大的挑战。边缘侧的智能化应用由于算力，算法模型，数据等不足，目前还有较大的提升空间。 \n面对上述挑战，在网络中引入网元智能，对网元内数据进行采集、存储、处理和分析推理，充分利用网元上的数据和算力。同时在网元和网管间引入协同机制，实现分布与集中处理的融合，一方面保护数据隐私，另一方面也可提升数据处理效率、决策推理的实时性。 \n本白皮书首先从需求和技术两方面分析了网元智能的驱动力，从现有网络智能化现状，到未来网络时代对网络高水平自治、智能普惠、高价值的新型业务和极致业务体验、网络安全等需求出发引出网元智能。然后阐述了网元智能架构，包括网元内生智能架构及网元、网管两级智能架构，并从数据、算法、算力等维度介绍网元智能关键技术。接下来基于智能节能、智能运维、智能安全等三个应用场景，以场景本身和技术需求为切入点进行场景分析介绍。最后对本白皮书内容进行总结整理，并展望后续研究方向。', 'id': '1121763_2'}
{'label': 'text', 'content': '网元智能驱动力分析\n人工智能技术在通信网络中的应用促进了通信网络智能化的发展，但目前的智能化主要集中在网络层面。随着运维、安全等低时延智能的需求增加，及网元自身的数据存储、处理能力增强，网元智能应运而生。', 'id': '1121763_3'}
{'label': 'text', 'content': '网元智能驱动力分析\n网元智能需求驱动\n当前，网络智能化将AI等智能化技术与通信网络的硬件、软件、系统、流程等融合，利用智能化技术助力通信网络实现规划、建设、维护、优化、运营流程智能化，从而达到提质、增效、降本，促进网络自身的技术和体系变革，使能业务敏捷创新，推动构建智慧网络。 \nTMF、CCSA等行业组织结合AI+大数据从整体网络层面提出了自智网络概念，整个自智网络包含业务自智、网络自智和领域自智。 \n其中网元智能位于领域自智内，具有独立自闭的内部结构，主要从运维智能化、安全智能化等方面支撑自智网络，是自智网络的重要组成部分。网元智能聚焦于自身组件和运行状态的深度感知，提升单节点高可靠性和自优化能力，并在软件部署、数据配置、网络测试等方面开发自动化操作能力，具有可感知、高可靠、自优化和可操作的特点。 \n中国移动在自动驾驶网络白皮书中也提出“四层三闭环”的自动驾驶网络架构，主要从设备检测、资源配置、业务保障、业务优化、业务测试等维度对网元智能化提出需求。 \n基于上述需求，结合人工智能技术的网元智能成为必然的选择。网元智能在网元上采集、存储和分析数据，大大降低数据分析时延及数据泄漏的风险。另外，网元智能在设备节能、设备运维及网络安全等场景也能发挥重要作用。同时，通过网元与网管的实时协作，网元智能能够助力网络实现智能化决策和高效率创新，进而助力网络实现极简化和智慧化运维。 \n因此，我们认为网元智能将成为未来网络运行、管理和维护的重要方向，成为实现网络智能化、自动化的重要手段，助力网络满足以下智能化需求： \n网元设备智能运维：当网络运维中出现故障，通过网元设备自身的数据采集、存储能力，分析故障发生前后的历史数据，快速给出故障根因，并与网络智能协作，进行故障自愈及反馈优化，实现网元与网络的闭环智能。 \n网元设备安全防御：通过实时监控网元内报文数据，当设备受到攻击时，及时预警及分析，锁定攻击源头，结合网元自身及网管下发的防御策略进行主动防御，避免网元及网络层面的服务不可用行为。 \n网元设备智能节能：通过人工智能技术，对网元设备已有流量历史数据进行训练和学习，识别不同设备的流量变化趋势和所处的业务场景，对负荷较低的网元设备实现动态功耗的降低。', 'id': '1121763_4'}
{'label': 'text', 'content': '网元智能驱动力分析\n网元智能技术驱动\n面向智慧泛在的未来社会，通信网络需要助力千行百业的数智化转型，实现“随时随地”智能化能力的按需供应，提供实时性更高、性能更优的智能化能力服务以及跨域的智慧融合和共享。考虑到网络数据隐私性，需要智能协调网络和网元的算力、通信连接和算法模型等资源，为客户提供极致的业务体验和高价值新型业务。 \n网元智能是指在网元设备内通过内生设计模式支持AI，而非叠加或外挂的设计模式。目前网元自治水平不高，为了实现高水平自治和安全可信。需要结合网元内部数据及AI算法完成网络的自我设计、自我实施、自我优化、自我演进，最终实现网络的高水平自治。 \n从以上分析可以看出，除满足基本的通信需求之外，网元设备还需考虑计算、数据、模型/算法等多方面的融合。即网元需要通过架构层面的内生智能设计，满足网络智能多样化的新业务场景和网络自治优化等需求。因此，我们认为网元智能的技术驱动力包括以下几点： \n网元数据：目前通信网元上支持运维、安全及业务数据的采集存储。典型的运维数据包括Metric、Log及Alert，安全数据包括流类型及报文地址，业务数据包括不同协议的流量。这些数据是网元智能落地的首要技术驱动力。 \n网元AI推理框架：深度学习及机器学习模型部署时需要完善的AI推理框架支撑，目前业界有大量的嵌入式推理框架支撑嵌入式AI应用，典型的框架如TensorFlow Lite。 \n网元计算资源：除了传统的CPU计算资源外，目前网元上也可以使用FPGA、GPU等支持AI模型推理的计算资源。', 'id': '1121763_5'}
{'label': 'text', 'content': '网元智能架构及关键技术\n网元智能整体架构\n网络智能整体由三层架构组成，包括基础设施(网元智能) ， 管控系统(网络智能)和能力开放（跨域智能）。网元智能与网络智能关系紧密，目前主要考虑的策略是网元与网络智能协同进行，网元主要负责高实时性和域内AI应用，网络智能主要负责网络层面大规模数据的AI应用。 \n实现整体网络智能功能，需发挥网元、网管各自优势，实现端到端的智能化协同。如网元负责数据、推理及接口基础设施支持，根据具体应用场景建立数据管道，完成算法模型建立及部署，网管负责数据及训练平台的支持。 \n网元智能与网管智能的协作的整体原则是网络训练，网元推理，具体分工如下： \n网元智能：负责网元内实时数据感知，实时推理及短时在线训练。 \n网管智能：负责网络层面实时推理，跨网元数据训练、协作及在线数据标注。', 'id': '1121763_6'}
{'label': 'text', 'content': '网元智能架构及关键技术\n网元智能整体架构\n网元、网管两级智能\n网元和网络智能化协作模式可分为两级，第一级为网元实时推理，第二级为网管网络级感知。\n其中，网元侧职责主要包含：\n告警性能数据上报\n处理实时数据，对于设备产生的数据，进行精炼、智能分析\n初步诊断，将诊断结果发送至管控侧\n响应管控推理请求\n接收管控标注数据，进行增量训练\n生成执行动作\n模型评估、重训练请求\n网管侧职责主要包含：\n网元数据收集处理、模型训练\n对网络进行整体感知，将网元决策所需感知信息下发至网元侧\n对网元侧的初步诊断结果进行综合判定，决策出最终动作\n响应网元模型训练请求，下发训练模型\n业务模块、网元级AI应用、网管信息交互流图如图所示：\n网元内部署的AI模型需要在网管平台进行训练，网元将模型训练所需的数据上报至网管数据平台。模型训练主要包括几大部件：\n数据集和特征库，以数据仓库或数据湖的形式部署，用于存储海量训练数据；\n版本库，进行训练通道代码管理；\n训练/再训流程，主要进行数据管道、超参数、模型管理，可以使用业界通用的MLOps平台；\n元数据库，用于存储训练/再训练过程中产生的各种元数据；\n模型库，存储训练好的模型及模型版本管理；\nCI/CD 流程，自动化进行模型打包、测试及发布。\n网元和管控接口主要包括数据上传、下发接口和模型下发接口，具体接口描述如下：\n表3-1 两级智能接口说明\n| 接口名称 | 功能描述 | 数据类型 |\n|:-|:-|:-|\n| ftp | 上传AI-DB中的数据文件，实现网元数据批量上报功能下载AI模型文件至网元内，实现网元模型更新功能 | 文件数据 |\n| netconf | 上传网元告警数据上传网元非实时指标数据下发网管标注数据 | 告警数据/指标数据/标注数据 |\n| telemetry | 1、实时上报网元指标数据 | 指标数据 |\n', 'id': '1121763_7'}
{'label': 'text', 'content': '网元智能架构及关键技术\n网元智能整体架构\n网元内生智能\n网元内生智能通常涉及大数据及人工智能技术，从数据层面来看需要包含数据采集、数据存储、数据处理、数据可视化等模块，从分析层面来看需要包含模型管理、推理引擎及部分通用算法库。要实现网元内智能运维框架，这些基础功能必不可少。基于以上考虑，网元智能框架的抽象架构如下： \n以上架构中，数据层主要负责数据采集、处理、建模和存储，分析层对数据层获取的数据利用规则或AI模型进行分析，应用层基于数据、分析层和业务逻辑构建具体的智能运维应用。网元智能运维框架和应用部署在资源受限的场景，需要探索数据及智能算法等新技术在嵌入式设备上的应用。 \n根据网元智能的技术特点和需求应用场景，网元智能框架的分层技术架构视图： \n网元智能的应用场景主要包括：故障根因分析、安全配置自动化、故障辅助诊断、系统健康监控、系统状态可视化、主动防御和攻击溯源等。这些应用场景构成了最上面的应用层，健康监控系统、辅助诊断系统、故障分析系统和安全防御系统。健康监控系统包含内存泄漏检测、CPU异常检测和通讯异常检测等功能，辅助诊断系统包含ping不通、协议振荡、IP地址冲突诊断等功能，故障分析系统包含协议断链等分析功能，安全防御系统包含攻击检测、攻击溯源和主动免疫等功能。 \n为了实现算法、分析规则、编排等公共基础设施的复用，需要引入分析层。该层主要为上层应用提供基础工具，用于对数据进行各个分析计算等操作，主要包括：规则表示、AI分析、工作流编排等。 \n数据层为分析和应用提供数据支持，包括了数据采集、预处理和存储等子功能。数据采集功能主要是完成系统中各种类型数据的采集，不同的数据产生规则和数据特点不一样，需要使用不同的数据采集框架，主要数据类型包括：事件日志数据、时序数据、业务状态数据、统一日志和告警/性能数据。预处理功能的主要作用是对数据进行规范化处理，以便满足数据存储模型的要求，方便后续分析处理。存储功能主要用于数据的集中存储、压缩、备份、落盘等功能，同时还提供一些方便数据分析整合的查询工具，为上层应用提供数据分析源。 \n基于以上分层架构，网元内模型推理的具体流程如下： \n利用数据采集通道采集模型推理所需数据 \n数据中心将采集的数据整理后存入持久化数据库，并触发AI分析 \nAI分析启动工作流引擎，进行推理数据提取、预处理等操作 \n数据准备完成后通过推理客户端将数据发送给推理引擎 \n客户端收到推理结果后将结果提交至策略应用 \n策略应用执行相关策略', 'id': '1121763_8'}
{'label': 'text', 'content': '网元智能架构及关键技术\n网元智能关键技术\n数据采集\n网元内数据采集逻辑视图如图所示： \n为了完成网元内数据采集，需要如下几个角色相互配合与协作： \n事件管理：集中管理应用上报的各种故障，控制AI模块何时启动分析； \n数据采集任务管理：调度并控制数据采集流中的每个任务，启动数据采集； \n数据采集：根据数据采集清单采集数据，并将采集结果返回给数据采集管理； \n数据整合：将采集数据整合成满足持久化的数据格式； \n数据持久化：将数据落盘，进行持久化存储； \n数据采集完成后，由事件管理通知数据分析模块(例如AI推理分析)进行数据分析。', 'id': '1121763_9'}
{'label': 'text', 'content': '网元智能架构及关键技术\n网元智能关键技术\n数据存储\n数据存储通过网元数据库实现，为了方便应用读写数据，数据需要与具体的存储模型进行解耦，如下图： \n因此，在网元数据存储中考虑增加一层ORM框架来实现应用与数据库的解耦。ORM是随着面向对象的软件开发方法的发展而产生的。对象和关系数据是业务实体的两种表现形式，业务实体在内存中表现为对象，而在数据库中表现为关系数据。内存中的对象之间存在继承和关联关系，而在数据库中，关系数据无法直接表达多对多关联和继承关系。因此，对象-关系映射（ORM）一般以中间件的形式存在，主要实现程序对象到关系数据库数据的映射。数据库部署往往需要考虑高可用性，网元数据库通常采用主从的部署方式。', 'id': '1121763_10'}
{'label': 'text', 'content': '网元智能架构及关键技术\n网元智能关键技术\n分析管道\n分析管道的主要作用是根据不同的机器学习、深度学习任务，选择并集成来自不同数据源的相关数据，经过数据验证、数据转换和数据映射后得到模型所需的输入。 \n分析管道流程图如下所示： \n分析管道输入为推理需要查询的数据库名，表名，以及每张表的查询时间点，时间间隔和属性字段等，输出为模型推理所需的特征向量，管道实现流程如下： \n（1）生成SQL查询语句，连接数据库，调用查询API并将查询结果转换为标准数据结构； \n（2）对查询结果进行数据验证，如检查数据库表项是否齐全或数据缺失程度，若验证不通过，则终止本次数据处理流程，并返回错误码给工作流子模块； \n（3）数据预处理、异常检测等操作，输出指标值是否异常等信息； \n（4）对所有表项的转换结果按照指定规则进行映射，得到一维特征向量。', 'id': '1121763_11'}
{'label': 'text', 'content': '网元智能架构及关键技术\n网元智能关键技术\n推理引擎\n网元推理引擎通常包括客户端和服务端两部分。 \n客户端驻留在AI应用内，用于和推理引擎服务端交互。它提供接口接收打包好的数据，并在内部生成通道信息进行会话管理，然后通过消息发送数据给推理引擎，并接收推理结果。 \n服务端在推理引擎组件内，包括模型管理和推理引擎模块。模型管理模块，主要用于在设备上电时读入模型的管理信息，并在进行推理时，根据读入的模型管理信息对输入数据进行校验，同时该模块还负责动态模型文件和模型管理文件的存储落盘。', 'id': '1121763_12'}
{'label': 'text', 'content': '网元智能场景分析\n应用场景：智能节能\n场景简介\n碳排放已成为ICT行业面临的严峻挑战。5G时代，海量接入以及大带宽的需求，更对网络能效提出更高要求，承载网设备也要在设计、建设以及运维方面做到能耗优化和绿色节能。目前承载设备主要通过静态方式实现节能，无法根据业务流量负载和外部环境的变化，实时调整设备能耗。通过在节能技术中引入AI人工智能，能够高效准确地采集数据产品和能效相关的不同维度的全部数据，通过算法的训练学习，精确拟合设备运行状态，识别流量变化，提升设备的节能效果，大幅减少电力消耗和能源浪费。 \n现有的承载网络（PTN/IPRAN等）设计总体原则是注重带宽与性能优化，没有充分考虑能量效率，能效利用上存在以下几个问题： \n网络通过大量的冗余硬件来进行带宽保证和故障情况下的冗余保护，组网设计上大部分场景需要充分利用负荷分担，当流量较小时，会导致大量的端口带宽利用率较低； \n网络按照峰值业务时段的服务质量设计，在夜晚或假日的低流量时段或在业务负载暂时变得非常低的某些区域中，设备无法充分利用； \n网络设备存在不同的开通阶段，网络商用初期，用户规模较小，网络处于轻载或空载状态导致大量的无效功耗。 \n因此，IP网络设备节能的关键就是以流量为核心，通过AI人工智能技术，对已有流量历史数据进行训练和学习，识别不同设备的流量变化趋势和所处的业务场景，对负荷较低的网络设备实现动态功耗的降低。 \n通过AI可以识别出各种流量场景，如，当某场景白天、晚间流量很大, 但夜间无流量, 且周末相比工作日流量增大, 模型可能将该场景识别为商场类；当某场景周末、节假日流量大且正常工作日流量较小, 可判别为景区类等。 \n如图4-1所示，网元智能节能涉及网元和网管两部分。其中网元负责网元级数据采集、短期流量预测及节能动作执行。网管负责网络级数据感知及存储、长期流量预测等。在具体实现上，根据AI模型训练学习的优化，可以实现更详细的业务场景划分，如地铁、高铁、高速公路、高校、医院等，不同类型的场景对应不同的节能策略。但IP承载设备和基站设备不同，基站设备是整个网络设备的末端，针对特定的场景区分度比较好，相对来说能够通过流量得到明确的场景分类。而IP承载网设备，网元类型比较多，包括接入、汇聚、核心等，具有典型的流量汇聚特性，即使接入设备也是众多基站的汇聚点，因此从流量上，承载设备已经看不到基于地点特性的场景信息，但是可以根据流量的变化趋势划分抽象的场景信息。 \n对于承载设备，流量变化趋势可以划分为如下四类场景： \n场景一，流量长期变化趋势不均衡，部分时间段高负荷，部分时间段低负荷； \n场景二，流量长期变化趋势处于较低负荷水平； \n场景三，流量大部分时间处于空载状态； \n场景四，流量长期变化趋势处于较高负荷水平。 \n总体上节能的效果是与业务负荷相关，一般荷越低，节能效果越明显，场景四在流量较高负荷的情况下进行任何节能动作都没有太大意义。因此AI智能节能重点是识别场景一、场景二和场景三，即识别流量中长期的变化趋势，确定流量低负载的时间段，动态执行节能动作。', 'id': '1121763_13'}
{'label': 'text', 'content': '网元智能场景分析\n应用场景：智能节能\n技术需求\n承载网元产品设备功耗总体分为两大类，静态功耗和动态功耗。 \n静态功耗在设备开启上电后就一直存在，即使没有流量也有固定的功耗，不随负荷而变化，静态功耗的主体是网元设备的各种硬件单元和芯片。 \n动态功耗则随着流量负荷增加而上升，因此AI智能节能主要是识别网元设备的流量变化趋势，在低流量负荷时间段执行节能动作。节能动作总体分为两类，硬件关断操作和路由迁移操作。 \n网元硬件关断 \n硬件关断动作就是关闭部分空闲的硬件资源，从而达到降功耗的作用。对于网元设备，有如下几种可硬件关断操作： \n关闭线卡Serdes总线 \n对于分布式网元设备，所有线卡之间的流量转发均通过交换网进行。线卡通过Serdes总线连接到交换网SF（Switch Fabric）上，当线卡工作在低流量负荷时，可以关闭空闲Serdes达到降功耗的作用。 \n关闭交换板SF（Switch Fabric） \n对分布式网元设备，交换网通常都有较高的冗余度，因此基于网络整体流量趋势预测，当网元整体流量负荷比较低时，可以选择关闭空闲的交换板，实现动态降功耗的作用。 \n物理端口休眠 \n对于流量长期空载，仅偶尔有低负荷流量的情况下，可以将对应的物理端口休眠。端口休眠并不是彻底关闭物理端口，当有流量时会重新触发端口转发流量。 \n目前端口休眠技术主要以IEEE制定的802.3az标准为主。这一标准称之为节能以太网标准（EEE），主要原理是在设备利用率低的时段或闲置期断开电源连接，而在数据传输时期，恢复电源连接。 \n关闭线卡 \n当线卡长期流量负荷较低时，尤其是线卡在SG（Smart Group）中存在冗余保护的场景下，可选择关闭线卡，实现动态降功耗的作用。 \n网元流量重路由迁移 \n在目前运营商网络路由策略中，无论网络中是否有流量经过，或无论流量大小，所有的链路都处在工作状态，即使这些流量很低，通过一条链路就可以完成转发，但实际中与路由相连的所有链路都会处在工作状态。 \n总体上，在不影响网络性能的情况下关闭部分链路可以节省大量能耗。因此根据网络流量预测值，如果部分链路上的流量在一个较长时间段内是低流量状态，那么可以把这些链路上的流量归并到其他链路，使其空闲，进而休眠。 \n网络中存在SG（Smart Group）或者ECMP（Equal-Cost Multi-Path）负荷分担场景，当流量负荷较低时，将SG/ECMP中部分成员端口关闭或者线卡下电，流量在剩余端口上转发，当预测流量趋势上升时，提前将端口打开。 \n对于部署了隧道策略的业务场景，则可以采用基于节能策略的TE重优化技术，即通过AI预测流量，对于长期趋势为空载或者低负荷的端口，重优化端口上的TE tunnel，将其路径优化到部分设备和链路上。 \n路径优化技术不应局限于单个设备，应该从网络整体的流量预测基础上进行分析，在安全的基础上使得尽量多的设备或者链路空闲休眠，达到更好的节能效果 \nAI流量算法模型 \nAI智能节能算法模型主要是流量预测，有多种算法模型。从流量波形结构看，一般情况下，流量可以分解为三个主要成分：增长趋势、周期趋势以及节假日。针对智能节能，主要通过AI算法预测流量中的趋势部分和周期部分，找到流量低负荷的时间周期，从而进行相应的节能策略。 \n趋势流量预测 \n主要使用RNN（Recurrent Neural Networks，循环神经网络）/LSTM（Long Short-Term Memory，长短期记忆神经网络）进行长短期的流量趋势预测，RNN/LSTM模型同时考虑了当前和过去历史数据，通过回归计算能够得到趋势上升或者下降时的边界点。传统的神经网络模型以及CNN卷积神经网络，是从输入层到隐含层再到输出层，层与层之间是全连接的，每层之间的节点是无连接的。 \n相对于CNN，RNN主要是用来处理序列数据，RNN中一个序列当前的输出与前面时刻的输出也有关（结合前面的状态、当前的记忆与当前的输入）。 \nRNN网络会对前面的信息进行记忆并应用于当前输出的计算中，而LSTM则是对RNN的进一步改进。RNN的信息只能传递给相邻的后继者，当输出与其相关的输入信息间隔很长的时候，RNN 的参数训练就变得非常困难，而LSTM能够解决输入与输出的长期依赖问题。 \n周期流量预测 \n网络流量中，周期流量始终占有主要成分，比如每天白天和夜晚流量都呈现周期性能的波动，每个工作日和周末流量波动情况也不同。流量的周期性和网络所在地域、时间周期、人流活动特点、社会经济总量等强相关。 \n趋势流量预测以回归预测为主，主要是预测流量发生变化的边界点，而周期流量预测，应该以分类为主，可以基于不同时间段的流量情况标记为不同的场景。 \n周期性流量预测可以采用基于树的分类算法（决策树Decision Tree、随机森林bagging、boost等），不同算法分类的效果可以在原型开发阶段进行验证，关注分类的准确度和泛化预测性能。 \n对于基于树的分类算法属于监督学习，因此首先需要对数据样本进行场景分类，即数据标注，当完成模型的训练和学习后，对于新接入节能策略的网络设备可以根据模型分类确定的场景类型，采用合适的节能策略。 \n从现网典型配置和典型流量的效果来看，网元智能节能技术的使用，设备功耗降低10%以上。', 'id': '1121763_14'}
{'label': 'text', 'content': '网元智能场景分析\n应用场景：智能运维\n场景简介\n随着网络规模的提高，运营商对通讯设备的可靠性和故障恢复能力要求也越来越高，对设备的故障预警和自动恢复能力也有比较强烈的需求。传统的故障定位主要依靠人工分析，由于故障通常涉及多个领域，需要收集大量现场数据，分析定位时间长，无法满足网络SLA的要求。为解决以上挑战，TMF、CCSA等行业组织结合AI+大数据从整体网络层面提出了自智网络概念。图4-2为TMF提出的自智网络架构：\n从架构图中可以看出，整个自智网络包含业务自智、网络自智和领域自智。网元智能位于领域自智内，具有独立自闭的内部结构，主要从运维自动化、智能化方面支撑自智网络，是自智网络的重要组成部分。从场景分析来看，网元智能需要加强自身组件和运行状态的深度感知，提升单节点高可靠性和自优化能力，并在软件部署、数据配置、网络测试等方面开发自动化操作能力，具有可感知、高可靠、自优化和可操作的特点。\n网元智能运维主要包含设备检测、资源配置、业务保障、业务优化、业务测试等维度。句柄通过网元AI实现高性能分析预测、智能决策的能力，与管控系统网络级AI进行功能互补。近期，运营商在SPN/OTN设备能力分级表中对网元L4能力提出了明确要求。\n支持KPI异常检测。包括QOS、安全、协议、系统管理、转发面、网络基础服务等KPI，通过嵌入式AI进行大数据分析，发现异常数据。\n支持KPI预测。CPU/内存/转发表项等KPI监控的设备性能劣化预测。\n网元+网管协同，定位到最小故障单元。这部分网元主要负责设备层面最小故障单元的定界定位。\n基于以上分析，我们梳理了网元智能运维的三大典型场景:\n健康监控\n网元智能运维的首要前提是网元内关键数据的可观测和可监控。健康监控主要通过统计规则或AI算法对网元内采集的KPI指标和部分日志数据进行异常检测，当KPI或日志出现异常后，网元产生告警或触发后续的分析流程。KPI异常检测主要包括系统指标和业务指标两大类，具体分类如下表所示。\n表4-1 KPI异常检测分类\n| KPI类别 | 典型KPI | 指标粒度 |\n|:-|:-|:-|\n| 系统指标 | CPU利用率 | 秒级 |\n| 系统指标 | 内存利用率 | 秒级 |\n| 业务指标 | 设备流量 | 分钟级 |\n| 业务指标 | 协议相关计数 | 分钟级 |\n| 业务指标 | QOS计数 | 分钟级 |\n日志异常检测主要包括结构化日志和非结构化日志两大类，其中结构化日志指具有固定模板结构的日志，网元设备中典型的日志模板如下图所示\n非结构化日志通常没有固定模板，和业务逻辑强相关。\n辅助诊断\n网元设备由于存储及计算资源受限，通常只对重点KPI和日志进行监控检测。当业务产生故障时，需要结合业务领域知识采集业务故障相关的上下文数据。辅助诊断主要解决业务故障产生时，数据主动采集的问题。辅助诊断的典型流程如下图所示：\n故障分析\n网元设备在健康监控阶段监控到异常后，通常会触发故障分析流程。故障分析使用异常的KPI、日志及辅助诊断采集到的数据进行根因分析，快速定位到具体业务模块或模块内的根因，故障分析流程如下图所示：\n如图4-5所示，网元智能安全涉及网元和网管两部分。其中网元负责网元级数据采集、存储，及智能运维相关应用如健康监控、辅助诊断和故障分析的在线推理。网管负责网元及网络采集数据的长期存储、模型训练及模型下发。\n', 'id': '1121763_15'}
{'label': 'text', 'content': '网元智能场景分析\n应用场景：智能运维\n技术需求\n网元智能运维具有实时性要求高、存储及算力资源受限的特点，为了能够在网元上进行智能运维，需重点考虑如下技术： \nKPI异常检测 \nKPI异常检测是指使用之前发现的模式来确定正常行为，然后检测与正常行为的偏离度，包括单变量和多变量。常用的异常检测方法包括基于规则的方法、基于传统统计的方法和基于机器学习/深度学习的方法。基于规则的方法比较简单，计算量小，但需要根据领域经验设置异常检测规则，准确率较低。基于传统统计的方法包括Z-score、MA(滑动平均)、EWMA(指数加权滑动平均)、ARIMA等经典统计方法，此类方法计算量中等，对于一定模式的数据准确率高，适合资源受限、数据模式简单的网元场景。基于机器学习/深度学习的方法包括DeepAR、MCRNN、MQCNN/MQRNN、SR-CNN、Prophet等，此类方法需要大量的数据进行模型离线训练，网元上部署已训练的模型进行在线推理，适合有深度学习模型推理能力且数据模式复杂的网元场景。 \n日志异常检测 \n网元设备在运行过程中会产生日志，日志是一种时序文本数据，由时间戳和文本消息组成，实时记录了业务的运行状态。通过收集并分析日志，可以发现或预知网络中已发生或潜在的异常。 \n由于日志数据主要为非结构化文本数据，日志异常检测相对于KPI异常检测而言挑战性更大。日志异常检测算法模型分为序列模型和频率模型。频率模型主要对日志从总量、包含关键词的条目数等维度进行频率统计，只适合检测较简单的日志模式异常。序列模型主要通过分析日志中呈现的序列模式来检测日志是否出现异常，可分为聚类模型和深度学习模型。序列模型首先需要对原始日志数据进行预处理，如模板、参数提取，然后将日志数据转换为序列数据进行分析。常用的深度学习方法包括deeplog、Template2vec等方法。由于序列模型对算力要求较高，因此基于序列模型的日志异常检测方法通常适合有模型推理能力且数据模式复杂的网元场景。 \n故障根因分析 \n故障根因分析作为网元智能运维的重要组成部分，旨在找出哪些模块或事件真正触发了网元系统中的故障现象或症状。在智能运维之前，运维人员需要通过分析指标、日志或诊断数据判断问题在哪里，从而实现故障定位。故障根因分析通常包括基于规则的方法和基于深度学习/机器学习的方法。基于规则的方法是指运维或研发人员根据前期处理故障的领域知识，梳理故障分析规则树或规则图，将规则树和图用于故障相关的数据，最终给出根因分析结果。该类方法需要积累一定程度的领域知识，但计算复杂度较低，准确率较高，适合网元场景。基于深度学习/机器学习的方法主要通过收集历史故障数据，结合指标和日志构建分类模型，在线推理时将分析所需数据输入模型，模型给出根因分析结果。该类方法需要积累大量已标注数据，适合有深度学习模型推理能力且数据模式复杂的网元场景。 \n在多种网络配置场景下，网元KPI异常检测准确率达90%以上，异常检测时长小于10分钟，故障定位时长小于1小时，有效提升了网元及网络的运维效率。', 'id': '1121763_16'}
{'label': 'text', 'content': '网元智能场景分析\n应用场景：智能安全\n场景简介\n随着信息通信技术的迅猛发展，信息网络系统已成为不可或缺的基础设施。然而，与网络发展相伴而生的网络安全问题也被急剧推到前所未有的高度。网络安全已经成为社会发展、国家安全的基础需求，也成为决定网络能否发挥最大化潜能和价值的关键因素。 在网络架构融合开放的发展趋势下，网络安全从过去主要由安全事件驱动的静态被动式安全，到当前主要由等保合规驱动的动态主动式安全，正在向着下一阶段由具体场景需求驱动的内生智能安全演进。 \n针对网络设备的攻击在不断发展，除了常见的DDOS攻击之外，网络设备也受到路由协议等控制平面报文的直接冲击，在现网开局应用和集采测试中的各类攻击模型下，有时会出现网络设备控制平面处理负荷过重而影响业务，或者导致CPU瘫痪，甚至网络设备长时间不可用等严重问题。所以路由器、交换机等设备，必须能够提供先进的控制平面保护措施，避免这些问题。 \n当前，大多数的网络设备都提供一些用于保护本设备控制平面的手段，例如黑白名单、日志、控制平面ACL等措施。控制平面安全主要由软件和硬件协同实现，依次完成报文简单分类调度、协议报文分类并送入指定队列，根据用户的需求提供灵活的预定义调度策略和在线修改限速策略、下发驱动的协议报文分类表，报文的按优先级调度和协议上送的多优先级报文收发。 \n但现有的安全架构主要以规则配置的方式进行攻击防护，这种静态的默认配置无法覆盖各类场景，同时控制面安全攻击检测和攻击溯源能力不足，攻击发生时无法及时对攻击报文进行抑制，保护正常报文通信。为了解决传统安全架构存在的问题，网元智能安全需要具备以下能力： \n建立全局的智能安全中心，站在系统全局，统一汇总分析各个安全检测单元的上报信息，之后以简洁的形式为用户呈现出一键式的攻击事件报告、攻击溯源、根因分析、以及解决建议等。 \n支持攻击检测和溯源能力，包括：自动检测攻击，识别攻击源，记录攻击事件，展示攻击事件报告等。增强攻击流识别能力，包括：对攻击流对象，合法用户进行识别。 \n支持参数自适应能力，包括：黑白名单自动配置，限速自动调整，优先级自适应等。 \n支持攻击主动防御能力，包括：自定义流分类，黑白名单自动配置，限速自动调整，优先级自适应等。 \n支持转发面、控制面联动防攻击，将控制面的协议开关状态和底层转发引擎的协议上送关联起来。通过在上层和底层建立联系，在协议开关状态上保持一致，对于设备没有开启业务的协议，底层硬件默认是以小带宽上送其协议报文。 \n如图4-6所示，网元智能安全主要在网元内进行，涉及主控和线卡两部分。其中线卡负责流量数据采集、分析及防御动作执行。主控负责多线卡分析结果汇总，存储及防御策略选择及下发。', 'id': '1121763_17'}
{'label': 'text', 'content': '网元智能场景分析\n应用场景：智能安全\n技术需求\n网元智能安全具有实时性要求高、存储及算力资源受限的特点，为了能够在网元上实现智能安全需求，需重点考虑如下技术： \n安全指标监控 \n网元设备受到攻击时通常会有安全相关KPI指标出现异常，如流量、连接数等，同时部分系统指标也可能存在异常，如CPU利用率、内存利用率、链路时延等。安全指标监控是网元智能安全的第一阶段，由于安全相关的指标通常为KPI指标，因此可以使用KPI异常检测技术。典型的安全指标监控方式包括静态门限阈值和基于传统统计的方法。静态阈值需要安全工程师根据经验进行配置，准确率不高。统计方法包括Z-score、MA(滑动平均)、EWMA(指数加权滑动平均)、ARIMA等，具有一定的动态模式适应能力。 \n攻击检测及溯源 \n当安全相关指标异常后，需要进行攻击检测和溯源。攻击检测主要确定攻击类型，目前网络上常见的攻击类型包括DDoS 攻击和Flood攻击。DDoS 就是分布式拒绝服务攻击，这种网络攻击形式尝试用恶意流量淹没网络资源，从而导致网络服务无法正常运行。在分布式拒绝服务(DDoS) 攻击中，攻击者发出海量实际上并不需要的互联网流量，耗尽目标的资源，造成正常流量无法到达预定目的地。对于DDoS 攻击通常可以使用信息熵进行分析检测，当报文的IP或MAC信息熵大于某一阈值时，可以判断网元受到DDOS攻击。Flood攻击是一种典型的拒绝服务(DoS)攻击，是一种利用TCP协议缺陷，发送大量伪造的TCP连接请求，从而使被攻击方资源耗尽（CPU满负荷或内存不足）的攻击方式。该攻击将使服务器TCP连接资源耗尽，停止响应正常的TCP连接请求。对于Flood攻击通常可以使用统计频率方式进行分析检测，当报文的IP或MAC频率大于某一阈值时，判断网元收到Flood攻击。攻击溯源主要确定攻击源，对于DDoS 攻击通常无法确定具体攻击IP或MAC，但可以通过攻击发生的端口进行粗粒度溯源。对于Flood攻击，在攻击检测阶段可以通过频率分析具体的攻击IP和MAC。攻击溯源的结果可以为攻击防御提供输入信息，实现智能安全的流程闭环。 \n攻击防御 \n攻击防御包括下发转发面抑制策略、下发转发面失效策略、无差别限速策略和日志记录。当攻击溯源检测到攻击源时，执行下发转发面抑制策略动作。当攻击溯源未检测到攻击源时，执行无差别限速动作。当防御策略超时时，执行失效策略。网元遭受攻击时，攻击相关的上下文信息将被记录到安全日志中。 \n目前的网元智能安全可以覆盖36中协议类型的DDoS 及Flood攻击，检测及溯源准确率95%以上，有效保障网元设备的安全。', 'id': '1121763_18'}
{'label': 'text', 'content': '总结与展望\n本白皮书基于当前网元智能技术的概念和发展现状，从驱计算、数据、模型等角度，详细阐述了网元智能的概念及定义，给出了网元智能的参考架构，明确了构建网元智能的系统的四大关键技术，前瞻性的分析了网元智能的典型应用场景和需求。期望本皮书能够为业界网元智能的科学研究和应用实践提供参考，助力推动网元智能的标准化以及行业成熟应用。 \n当前业界对网元智能的概念和内涵尚未形成共识，统一的网元智能定义和架构仍有待达成。这使得网元智能产业成熟和推进面临较大挑战。因此，我们倡议运营商、设备商以及产业链各方合作伙伴携手创新，在以下几个方面通力合作，共同打造完善的网元智能产业，助力网络数字化、智能化转型。 \n推动网元智能标准化。积极参与标准组织中网元智能相关的标准化工作，争取早日使网元智能形成统一的技术标准。 \n突破网元智能关键技术。产学研密切合作，充分利用各类自有资源和协作项目，突破网元智能研究中的关键技术难题，为产业成熟应用奠定基础。 \n逐级推广网元智能应用。网元智能作为网络智能整体解决方案的一部分，全行业内初期进行全面部署将会面对较大阻力。因此，分阶段由易到难逐级推进部署将会是推动网元智能发展更可行的方式。', 'id': '1121763_19'}
