{'label': 'document', 'content': '中兴通讯无损以太网技术白皮书 v1.0_20200728_CN', 'id': '980784'}
{'label': 'table', 'content': '| 版本 | 日期 | 作者 | 审核者 | 备注 |\n|:-|:-|:-|:-|:-|\n| V1.0 | 2020.7.28 | 袁智勇 | | 本文介绍了数据中心内部无损网络的背景、主要技术、相关的问题、后续可能的发展方向等 |\n| | | | | |\n| | | | | |\n| | | | | |\n', 'id': '980784_1'}
{'label': 'text', 'content': '无损以太网背景\nRDMA技术\nRDMA技术最早应用在Infiniband网络，互联HPC高性能计算集群，价格昂贵、市场狭窄封闭。业界厂家Mellanox和Intel把RDMA移植到以太网上，降低了RDMA的部署成本、推动了RDMA技术普及 \nRDMA（远程直接数据存取），允许用户态应用程序直接读取和写入远端内存，无需CPU介入多次内存拷贝，并可绕过内核直接向网卡写数据，因此获取更低的时延、更高的吞吐、更低CPU开销 \nRDMA固有的缺陷：1） 乱序无法纠正，只能重传 2）传输过程若丢了一个包需要全部重传，比如发包序号200-300，接收端丢了第200这个包，即使201到300包都正常接收，发送端也只能全部重传', 'id': '980784_2'}
{'label': 'text', 'content': '无损以太网背景\nRDMA应用场景\n场景1：存储 \n适用网络I/O密集的业务，比如分布式存储 \n典型应用1：iSCSI Extensions for RDMA (iSER)和采用DAFS文件系统的NAS存储 \n典型应用2：NVMe over Fabric 的固态存储，全闪存存储 \n存储数据以大包（4K以上）为主 \n场景2：高速计算&人工智能 \nHPC高性能计算集群 \n深度学习系统，实现GPU 与内存数据直接交互,提高训练效率 \n人工智能数据以小包为主', 'id': '980784_3'}
{'label': 'text', 'content': '无损以太网背景\nRDMA承载网络技术\n主要分为 InfiniBand和以太网。 还有一种是FiberChanel，主要应用在FC SAN存储，应用场景较少不再描述\n在Ethernet以太网上承载RDMA协议，分为iWARP和RoCE两种技术，而RoCE又包括RoCEv1和RoCEv2两个版本(v1仅支持二层组网，v2支持三层组网)\n支持RoCE的以太网被称为无损以太网络\n各RDMA网络协议栈的对比如下图所示。\n| 承载网络技术 | 特点 | 对网络设备要求 | 主推厂家 |\n|:-|:-|:-|:-|\n| RDMA over InfiniBand | 封闭网络，InfiniBand本身是无损 | InfiniBand交换机 | Mellanox、Intel |\n| RoCE (RDMA over UDP/IP ) | RDMA over InfiniBand over UDP/IPUDP无拥塞控制机制，网卡自己做拥塞控制算法、要求网络设备支持无损 | 无损以太网交换机 | Mellanox，Marvell，Intel |\n| RoCE (RDMA over UDP/IP ) | RDMA over InfiniBand over UDP/IPUDP无拥塞控制机制，网卡自己做拥塞控制算法、要求网络设备支持无损 | 无损以太网交换机 | Mellanox，Marvell，Intel |\n| RoCE (RDMA over UDP/IP ) | RDMA over InfiniBand over UDP/IPUDP无拥塞控制机制，网卡自己做拥塞控制算法、要求网络设备支持无损 | 无损以太网交换机 | Mellanox，Marvell，Intel |\n| iWARP(RDMA over TCP/IP) | iWARP(RDMA over TCP/IP over iWARP网卡硬件卸载TCP，采用标准的TCP拥塞控制 | 普通以太网设备 | Intel、Marvell、Chelsio |\n| iWARP(RDMA over TCP/IP) | iWARP(RDMA over TCP/IP over iWARP网卡硬件卸载TCP，采用标准的TCP拥塞控制 | 普通以太网设备 | Intel、Marvell、Chelsio |\n| iWARP(RDMA over TCP/IP) | iWARP(RDMA over TCP/IP over iWARP网卡硬件卸载TCP，采用标准的TCP拥塞控制 | 普通以太网设备 | Intel、Marvell、Chelsio |\nRDMA 一开始就承载在无损的InfiniBand网络中，RDMA自身缺乏完善的丢包保护机制、对丢包异常敏感。\nRDMA从InfiniBand网络移植到以太网后，RoCE技术仍然要求底层网络支持无损。从网卡厂家公开的数据看，RoCE在小包传输时延有优势\niWARP作为承载RDMA的技术之一，市场宣传推广力度不如RoCE。从网卡厂家公开的数据看，在大包应用的场景(比如存储)，时延指标和存储操作IOPS无明显差异。该技术不要求底层的网络无损，运维方便、适用范围更广，对大多数用户来说可能更适用。\n', 'id': '980784_4'}
{'label': 'text', 'content': '无损以太网业务过程\nRDMA业务存在Incast N:1 流量模型，拥塞控制核心在服务器的网卡，网络设备起辅助作用 \n网卡（核心节点） \n支持RoCEV2 \n开启PFC \n开启DCQCN(包括CNP)算法调速：接收端网卡根据网络设备标识的拥塞标志，向发送端发送CNP通知降速，发送端网卡反复尝试调速 \nSpine&Leaf（配合设备） \n需要网络设备支持 PFC& 快速ECN，配合网卡工作 \n为防止环路（物理&配置）产生PFC死锁扩大故障范围，需要支持PFC死锁检测（硬件或者软件），本质上都属于辅助方式', 'id': '980784_5'}
{'label': 'text', 'content': '主要技术\nPFC\nPFC（Priority-based Flow Control）在IEEE 802.1Qbb中定义，该机制是在802.3ad的Pause机制上做修改，是对普通流控的暂停机制一种增强 \n原理 \n设备为端口上的8个队列设置各自的PFC门限值。 \n当队列已使用的缓存超过PFC门限值时，则向上游发送PFC反压通知报文，通知上游设备停止发包； \n当队列已使用的缓存降低到PFC门限值以下时，则向上游发送PFC反压停止报文，通知上游设备重新发包。', 'id': '980784_6'}
{'label': 'text', 'content': '主要技术\n快速ECN\nECN（Explicit Congestion Notification）显式拥塞通知 \n中间设备对报文IP头ECN域做标识 \n快速ECN需要在拥塞发生时候，出口队列调度发出的第一个报文加上标记 \n原理 \n转发设备DeviceA发现出端口拥塞，转发报文时给报文携带ECN拥塞标记（ECN字段置为11），标示网络设备拥塞 \n转发设备DeviceB收到报文后，正常转发给目的服务器 \n目的服务器发现报文中携带ECN拥塞标记，向源服务器发送CNP拥塞通知报文 \n源服务器接收到CNP报文，按一定算法尝试降低发送速率，网络拥塞缓解', 'id': '980784_7'}
{'label': 'text', 'content': '主要技术\nPFC死锁解除\n物理环路或者转发表项错误，导致转发路径成环，途径设备队列同时达到门限同时触发PFC，互相等待死锁，转发路径网络流量降为0（转发可能路径都需要开启PFC，PFC扩散影响范围太大）\tPFC死锁解除，设备在特定周期内检查到PFC频繁产生，设备忽略PFC状态把报文发出，PFC互锁条件解除。过了一定周期后再恢复PFC使能。 \n如果转发路径成环没有解除，又会产生PFC死锁，因此PFC死锁解除功能只能在一定程度上减少PFC扩散带来的影响。', 'id': '980784_8'}
{'label': 'text', 'content': '现存问题\nPFC扩散化\nPFC本身是多年前的老技术，基于8个TC分类（映射到8个队列）做流控比较粗糙，容易扩大化流量抑制的范围 \nPFC 风暴难免，导致网络大幅度流量下降，没有办法能保证不发生 \n转发可能路径都需要开启PFC，网络规模越大，发生PFC死锁的概率越大 \nPFC死锁解除不能根本解决问题，环路状态不解决、网络始终处于周期性“死锁->解除->死锁”震荡，对整体网络流量影响极大', 'id': '980784_9'}
{'label': 'text', 'content': '现存问题\n高带宽和低延迟不能兼顾\nRoCE推广的卖点“高带宽和低延迟”实际很难达到 \n低延迟->设备队列深度浅->设备配置ECN阀值低->ECN容易频繁触发->发送方流量无法增大->网络带宽利用率低', 'id': '980784_10'}
{'label': 'text', 'content': '现存问题\nRoCE网卡DCQCN算法局限性\n该算法是2015年为RDMA设计的第一代拥塞算法，限于当时硬件条件有局限性，比如链路故障期间容易触发流量振荡 \n依赖网络设备做ECN标记方式调整流量，网卡只能试探性降低流量，大规模拥塞时候收敛慢 \n网卡DCQCN有15个参数，参数调整复杂，容易带来不可靠和低利用率，运维复杂 \n实际部署，需要化几个月才能把这些参数优化调好', 'id': '980784_11'}
{'label': 'text', 'content': '后续发展情况\nPFC标准演进-802.1Qcz\nPFC针对的是traffic class，粒度太大，会导致某个TC中所有的flow都延迟发送。 \n该标准中提出的新方案是，在一个交换机的出口根据flow做一个更小粒度的Queue区分，当某个Queue发生拥塞，通过CIM通告给上游邻居交换机也根据flow做一个更小粒度的Queue区分，然后如果仍然解决不了拥塞则使用PFC机制 \n目前还是草案，业界无硬件可支持', 'id': '980784_12'}
{'label': 'text', 'content': '后续发展情况\n网卡拥塞控制算法改进\n互联大厂的改进\n2019 SIGCOMM 大会，阿里、哈佛大学、剑桥大学、麻省理工联合提出改进的高精度拥塞控制算法（HPCC）。\n采用智能网卡运行阿里自己的HPCC算法调速， 交换机采用INT技术描述报文在设备转发情况（时延、队列等）。该算法不需要交换机支持PFC/ECN， 从核心解决RoCE网络的一系列问题。已经在阿里内部小规模商用\n| | 阿里的HPCC算法 | Mellanox的DCQCN算法 |\n|:-|:-|:-|\n| 发布时间 | 2019 SIGCOMM大会 | 2015 SIGCOMM大会 |\n| 算法核心思路 | 发送端每发一次包都会根据响应报文(ACK)获取报文途径设备、时间戳、设备的队列深度，精确获准网络状态从而精确调整发送流量 | 网络即将拥塞了，只是靠一个笼统ECN标志告知，发送端多次反复尝试降速 |\n| 实际效果 | 队列深度几乎为0带宽利用率非常高达到”高带宽+低延迟“的目标 | ”高带宽+低延迟”实际无法同时达到 |\n| 实际效果 | 队列深度几乎为0带宽利用率非常高达到”高带宽+低延迟“的目标 | ”高带宽+低延迟”实际无法同时达到 |\n| 实际效果 | 队列深度几乎为0带宽利用率非常高达到”高带宽+低延迟“的目标 | ”高带宽+低延迟”实际无法同时达到 |\n| 实际效果 | 队列深度几乎为0带宽利用率非常高达到”高带宽+低延迟“的目标 | ”高带宽+低延迟”实际无法同时达到 |\n| 实际效果 | 队列深度几乎为0带宽利用率非常高达到”高带宽+低延迟“的目标 | ”高带宽+低延迟”实际无法同时达到 |\n| 网络稳定性 | 高 | PFC风暴不可避免，大幅降低网络稳定性网络拥塞时候收敛慢 |\n| 网络稳定性 | 高 | PFC风暴不可避免，大幅降低网络稳定性网络拥塞时候收敛慢 |\n| 网络稳定性 | 高 | PFC风暴不可避免，大幅降低网络稳定性网络拥塞时候收敛慢 |\n| 部署运维 | 简单，只有3个参数不需要PFC/不需要ECN | 参数调整复杂，需要几个月的时间调整算法参数 |\n| 部署运维 | 简单，只有3个参数不需要PFC/不需要ECN | 参数调整复杂，需要几个月的时间调整算法参数 |\n| 部署运维 | 简单，只有3个参数不需要PFC/不需要ECN | 参数调整复杂，需要几个月的时间调整算法参数 |\n', 'id': '980784_13'}
{'label': 'text', 'content': '后续发展情况\n网卡拥塞控制算法改进\nRoCE网卡厂家的改进\n可能的演进途径 \n允许lossy有损，只要求交换机支持ECN（PFC 不要求） \n不需要PFC，也不需要ECN，支持节点数目可能比较少 \n采用新的DC2QCCN 改进算法', 'id': '980784_14'}
{'label': 'text', 'content': '后续发展情况\n设备的改进\n动态PFC/ECN门限\n网络中N:1流量模型，N是不确定的，靠手工调整设备的PFC/ECN门限配合RoCE网卡调速，很难达到满意的效果，难以支撑运维。 \n业界个别厂家采用AI技术智能动态调整门限，具体效果有待验证。 \n动态调门限，从技术上说，是对RoCE技术缺陷的局部改进，非根本解决所有问题；从标准角度看，本质是私有技术，组网上有排他性。', 'id': '980784_15'}
{'label': 'text', 'content': '后续发展情况\n设备的改进\n设备的INT带内诊断\n根据阿里的HPCC算法要求，设备使用INT标记每一个报文在交换机转发情况（拥塞程度、时延等）。发送端网卡根据上一次报文在网络设备转发情况，调整本次报文的情况。 \n采用INT技术，网卡可以精确了解网络途径设备的情况，精确调整报文的发送 \nINT是标准技术，异厂家设备可以组网', 'id': '980784_16'}
{'label': 'text', 'content': '总结\n采用RoCE技术的无损以太网络，包括交换机设备和服务器端的RoCE网卡，目前还处于实验性研究探索阶段，本身并不完善，有各种缺陷待改进，不适合规模部署商用。 \niiWARP作为承载RDMA的技术之一，市场宣传推广力度不如RoCE。从网卡厂家公开的数据看，在大包应用的场景(比如存储)，时延指标和存储操作IOPS无明显差异。该技术不要求底层的网络无损，运维方便、适用范围更广，对大多数用户来说可能更适用。 \n本文讨论的RoCE网络的各种问题，根本原因在于RDMA协议自身的不完整。从技术发展的眼光看，某技术要获得广泛的主流应用，对底层的传输就不能有硬件依赖，RDMA自身协议的改进应该是终极演进方向。', 'id': '980784_17'}
{'label': 'text', 'content': '附录1：参考资料\n1.《Congestion Control for Large-Scale RDMA Deployments》，Yibo Zhu , Haggai Eran， Daniel Firestone，Chuanxiong Guo， Marina Lipshteyn，Yehonatan Liron，Jitendra Padhye，Shachar Raindel，Mohamad Haj Yahia，Ming Zhang。Microsoft、Mellanox、 U.C.Santa Barbara，SIGCOMM ’15 \n2.《HPCC: High Precision Congestion Control》，Yuliang Li , Rui Miao , Hongqiang Harry Liu , Yan Zhuang , Fei Feng , Lingbo Tang , Zheng Cao , Ming Zhang ,Frank Kelly , Mohammad Alizadeh , Minlan Yu 。Alibaba Group， Harvard University， University of Cambridge， Massachusetts Institute of Technology，SIGCOMM ’19', 'id': '980784_18'}
{'label': 'table', 'content': '附录2：术语&缩略语\n表8.1 术语和缩略语列表\n| 名词 | 描述 |\n|:-|:-|\n| RDMA | Remote Direct Memory Access |\n| RoCE | RDMA over Converged Ethernet |\n| iWARP | Internet Wide Area RDMA Protocol |\n| SCSI | Small Computer System Interface |\n| iSCSI | Internet Small Computer System Interface |\n| iSER | iSCSI Extensions for RDMA |\n| PFC | Priority-based Flow Control |\n| ECN | Explicit Congestion Notification |\n| HPCC | High Precision Congestion Control |\n| DCQCN | Datacenter QCN |\n| IOPS | Input/Output Operations Per Second |\n', 'id': '980784_19'}
