{'label': 'document', 'content': '中兴通讯有线网络IPv6技术白皮书', 'id': '676837'}
{'label': 'text', 'content': '1. IPv6 网络发展现状\n1.1. IPv4 地址枯竭，IPv6 地址持续增长\n全球可供分配IPv4 协议地址已经枯竭，所有的运营商不能再申请到公网的 IPv4 地址池。全球公网的IPv6 地址非常充足，目前只使用2000::/3 一个地址段。 新晋电信运营商只能申请到IPv6 的公网地址，或者去市场购买IPv4 公网地址。 市场上每个IPv4 地址的价格高达10-15 美金，到达了历史的最高点。2011 年微 软公司为解决公司的IPv4 地址不足困境，以每IPv4 地址11.25 美金的价格，从 市场上购买了666,000 个IPv4 地址。传统的运营商可能已经申请到较多的IPv4 公网地址，大部分IPv4 地址已经被分配出去或者使用，其剩余的IPv4 地址根本 无法应付移动终端、万物互联的场景，不能为每个新增的终端都分配一个IPv4 地址。 为了应对IPv4 公网地址不足问题， 运营商使用各种CGN 技术减少对公网 IPv4 地址的需求。据运营商统计全球大约94%的用户地址经过NAT 转换，而只 有0.6%的IPv6 的地址是经过IPv6 到IPv6 的NAT 转换。 普及IPv6 协议后， IPv4 私网地址到IPv4 公网地址的NAT 的转换将基本不复存在。 \n市场上大量的移动终端和固定设备都能同时支持IPv6 和IPv4 地址， 而IPv4 地址的枯竭将促使运营商申请大量的IPv6 地址分配给移动终端和固定终端，支 撑各种业务的开展如万物互联。五大地区的RIA 可供分配的掩码长度为8 的 IPv4 地址，2018 年都枯竭，如图1-1 所示。IANA 的IPv4 地址枯竭的则更早。 \n图1-1 五大地区RIA IPv4 地址分配情况 \nIPv6 地址申请变化与IPv4 的消耗密切相关， 全球申请的IPv6 地址的数量从 2003 年5 月开始持续增长，尤其在2010 年8 月14 日开始全球申请IPv6 地址的 速度开始加速，如图-2 所示。红色的曲线代表已经分配的IPv6 的地址块，绿色 代表已分配且被BGP 通告的IPv6 地址块，而蓝色则代表有流量的IPv6 地址块。 \n图1-2 全球IPv6 地址和流量双增长', 'id': '676837_1'}
{'label': 'text', 'content': '1. IPv6 网络发展现状\n1.2. 全球网络快速向IPv6 演进\n全球已经有157 国家和地区的骨干网络都支持IPv6 协议，IPv6 协议已经被 证明是稳定的且被广泛部署。各个国家和地区的IPv6 的基础设施是发展不平衡 的， 欧美国家IPv6 协议的基础设施领先其他地区。 全球IPv6 基础设施评估TOP10 中，欧洲国家占了六席，四个非欧洲的国家为美国、特立尼达和多巴哥、印度和 巴西。根据2017 年一季度的统计，比利时的IPv6 基础设施领先全球，IPv6 流量 占其全部国内流量的38%，如图1-3 所示。 \n图1-3 全球IPv6 流量排名', 'id': '676837_2'}
{'label': 'text', 'content': '1. IPv6 网络发展现状\n1.2. 全球网络快速向IPv6 演进\n1.2.1. 颁布IPv6 国家战略，推动本国IPv6 发展\n世界上主要的国家和经济体都在力争尽早完成IPv6 网络的建设，这不仅能 够提升本国在下一代互联时代的话语权， 也能促进技术和经济发展。 许多政府都 \n推出一些列促进和鼓励IPv6 产业发展的国家战略。 几个重要的国家IPv6 战略罗 列如下，这是这些政策和措施促进IPv6 网络在本国的迅速发展。 \n\uf06e 美国政府要求所有联邦机构在2008 年3 月之前都支持IPv6。 \uf06e 2012 年3 月中国政府颁布 《关于下一代互联网十二五发展建设的意见》 。 \uf06e 葡萄牙政府要求公共机构在2016 年前支持IPv6。 \uf06e 德国政府2009 年成立IPv6 委员会发布IPv6 国家行动计划和路线图。 \uf06e 巴西政府要求2008 年计划IPv6 Ready， 2016 年已开始对终端IPv6 论证。 \uf06e 比利时法律强制部门要求推广IPv6，打击Internet 犯罪。 \uf06e 澳大利亚政府要求大多数政府网站都要支持IPv6 客户端访问。 \uf06e 2017 年11 月中国政府发布《推进互联网协议第六版（IPv6）规模部署 行动计划》', 'id': '676837_3'}
{'label': 'text', 'content': '1. IPv6 网络发展现状\n1.2. 全球网络快速向IPv6 演进\n1.2.2. 大型ISP 部署IPv6 网络服务\n目前大多数传输型的ISP(向公司客户提供传输服务)都能通过BGP 协议通告 IPv6 前缀。世界上除了中东、非洲和中国外，几乎每个传输型的ISP 都能提供 IPv6 服务。 \n商业宽带ISP 也开始大规模提供IPv6 服务。根据World IPv6 Launch website 数据统计，美国Comcast 的IPv6 流量占46%，英国的SKY 宽带公司IPv6 的流 量超过75%， 美国AT&T 和比利时的Telenet 的IPv6 流量也都超过50%。 如下图 1-4 所示。ADSL 设备支持IPv6 协议对有线宽带服务商的IPv6 服务普及发挥了 重要作用。 \n图1-4 大型ISP 的IPv6 流量排名', 'id': '676837_4'}
{'label': 'text', 'content': '1. IPv6 网络发展现状\n1.2. 全球网络快速向IPv6 演进\n1.2.3. 操作系统和应用开发架构全面支持IPv6\n苹果公司在2016 年发布的iOSv9 版本开始支持IPv6，运行该版本及后续版 本的的iPhone 和iPad 都是支持IPv6 的移动终端。 同时， 苹果公司发布iOS SDK 鼓励软件开发者开发IPv6 的APP。苹果公司要求其应用商店的每个应用都要求 支持IPv6 服务。其他主流操作系统厂家，如微软、Google、IBM 都发布了支持 \nIPv6 的版本的操作系统。操作系统的DHCPv6 功能，能方便设备通过DHCPv6 获取IPv6 地址。同样，操作系统的ND RDNSS 服务，即使在IPv6-only 环境下 也能自动配置DNS server 功能。主流操作系统对IPv6 支持的情况如下表。 \n<table><tr><th colspan="3">操作系统 版本 IPv6-ready </th><th>安装IPv6 </th><th>DHCPv6 </th><th>ND RDNSS</th></tr><tr><th colspan="3">AIX 4.3 Yes </th><th>Yes </th><th>Yes </th><th>No</th></tr><tr><th colspan="3">Red Hat Enterprise 6 Yes </th><th>Yes </th><th>Yes </th><th>Yes</th></tr><tr><th colspan="3">Solaris 10 Yes </th><th>Yes </th><th>Yes </th><th>No</th></tr><tr><th>SUSE </th><th>Enterprise 11 </th><th>Yes </th><th>Yes </th><th>Yes </th><th>Yes</th></tr><tr><th>HP-UX </th><th>11i </th><th>Yes </th><th>Yes </th><th>Yes </th><th>Yes</th></tr><tr><th>IBM i </th><th>7.1 </th><th>Yes </th><th>Yes </th><th>Yes </th><th>No</th></tr><tr><th>i OS </th><th>4.1 </th><th>Yes </th><th>Yes </th><th>Yes </th><th>Yes</th></tr><tr><th>open SUSE </th><th>42.1 </th><th>Yes </th><th>Yes </th><th>Yes </th><th>Yes</th></tr><tr><th colspan="3">Ubuntu All Yes </th><th>Yes </th><th>Yes </th><th>Yes</th></tr><tr><th colspan="3">Debian 3.0 Yes </th><th>Yes </th><th>Yes </th><th>Yes</th></tr><tr><th colspan="3">Android 5.0 Yes </th><th>Yes </th><th>No </th><th>Yes</th></tr><tr><th colspan="3">mac OS 10.7 Yes </th><th>Yes </th><th>Yes </th><th>Yes</th></tr><tr><th colspan="3">Windows NT 10 Yes </th><th>Yes </th><th>Yes </th><th>Yes</th></tr></table> \n表格1-1 主流操作系统对IPv6 支持汇总 大型软件开发商积极推动平台无关和协议无关的NEAT -API 标准，目前该 标准在2017 年IETF 的98 会议上作为Demo 演示软件，获得非常好的效果。上 层应用开发不聚焦底层具体的IPv4 或者IPv6 协议，统一的NEAT API 帮助解决 具体协议相关的问题。 支持NEAT 架构有EMC, Mozilla, Celerway， Apple, Google, Cisco 等公司。 \n图1-5 NEAT:实现平台和协议无关的Internet API', 'id': '676837_5'}
{'label': 'text', 'content': '1. IPv6 网络发展现状\n1.2. 全球网络快速向IPv6 演进\n1.2.4. 建设IPv6-only 的数据中心\nGoogle, YouTube, LinkedIn, Akamai, Netflix 和Facebook 等大型内容供应商都 积极在企业内部署IPv6 网络， 方便外部IPv6 用户的访问。Facebook 的一份研究 报告指出，在美国的移动终端通过IPv6 下载速度比IPv4 速度超出15%-40%。 Facebook 已经关闭数据中心内的IPv4 服务，实现了IPv6-only 的数据中心。 LinkedIn 计划2018 年关闭其所有数据中心内的IPv4 的服务，向客户提供 IPv6-only 的数据中心。 YAHOO 则建立完全镜像IPv4 的服务数据中心的IPv6 数 据中心，满足IPv6 用户访问的需要。 \n主要云服务供应商已经宣布对IPv6 用户的支持，IPv6 的用户可以非常容易 使用它们提供的服务。这些公司有Cloudflare, Google, Office 365, OpenStack86, Amazon Web Services (AWS)，Azure, Microsoft, Digital Ocean，Hetzner, LinkedIn, Akamai1, Netflix 和Facebook 等。根据Google 的最新统计，全球大概有20%左 右的用户使用IPv6 地址使用其搜索引擎，具体图1-6 所示。 \n图1-6 Google 引擎统计全球IPv6 流量(24%)', 'id': '676837_6'}
{'label': 'text', 'content': '1. IPv6 网络发展现状\n1.2. 全球网络快速向IPv6 演进\n1.2.5. 移动运营商部署IPv6-only 网络\n移动/无线运营商是推动IPv6 流量增长的主要源动力。 海量的用户终端对IP 地址的需求， 推动移动/无线运营商网络首先向IPv6 升级。 全球IPv6 流量最大的 三张网络Verizon Wireless、T-Mobile、Reliance Jio 都是移动/无线运营商，如图 1-4 所示。 印度移动公司Reliance JIO和美国无线公司Verizon Wireless 都报告IPv6 流量占其全部网络流量的90%以上。 \n2012 年美国公司T-Mobile 宣布启动关闭IPv4 网络的进程。目前，T-Mobile 的骨干网是一张典型IPv6-only 的网络。T-Mobile 在移动终端使用部署464CLAT 技术，在出口边缘使用464PLAT 技术实现了IPv6 的用户访问IPv4 服务的场景， 如图1-7 所示： \n图1-7 T-Mobile IPv6-only Network', 'id': '676837_7'}
{'label': 'text', 'content': '1. IPv6 网络发展现状\n1.3. 中国IPv6 网络发展现状\n1.3.1. CGN 技术广泛，IPv6 应用起步晚\n中国大陆网民的数量已达到7.5 亿，而累积申请的IP 地址数量从2011 年开 始就稳定在3.3 亿左右， 占全球第二， 没有增长。 目前各种网络业务没有受到IPv4 地址短缺的影响，主要因为客户使用IPv4 的私网地址部署业务，同时部署多层 CGN 技术缓解IPv4 公网地址不足问题。 \n在5G 和万物互联时代，应用对网络质量要求(如延时、丢包等)大幅度提高， 使用多层CGN 技术已经不能满足车联网等新型业务的需要。IPv6 的128 比特地 址提供的巨大网络地址空间，IPv6 提供端到端的服务能够改善网络性能，满足 5G 和万物互联需求。 \n中国的三大运营商的骨干网设备部署IPv4/IPv6 双栈功能，很多城域网设备 都是IPv6 Ready。IPv6 的业务支撑网络、接入网、终端和应用软件对IPv6 的支 持正在提升，但没有杀手级的IPv6 应用软件拉动IPv6 的发展。中国大陆在全球 IPv6 的流量排名67 位，与香港的IPv6 流量相当。这与其申请的IPv6 地址块数 量非常不匹配。中国大陆IPv6 的流量主要来源于中国教育科研网CERNET2， CERNET2 是一张IPv6-only 网络，并通过无状态技术连接到IPv4 网络。', 'id': '676837_8'}
{'label': 'text', 'content': '1. IPv6 网络发展现状\n1.3. 中国IPv6 网络发展现状\n1.3.2. 中国政府高度重视IPv6 的发展和规划\n中国政府高度重视IPv6 基础设施和科研的发展，在十二五和十三五的规划 都对下一代互联网的建设提出具体的目标和措施，保证IPv6 相关科研和产业健 康发展。十二五计划强调对IPv6 的研究和促进IPv6 产业的发展，要求终端设备 和网络设备支持IPv6，鼓励IPv6 试点部署。 \n2017 年11 月26 日中国政府发布 《推进互联网协议第六版(IPv6)规模部署行 动计划》提出了明确的目标，“用5 到10 年时间，形成下一代互联网自主技术 体系和产业生态，建成全球最大规模的IPv6 商业应用网络，实现下一代互联网 在经济社会各领域深度融合应用， 成为全球下一代互联网发展的重要主导力量” ， 并提出路线图和各阶段的要求和行动计划。《推进互联网协议第六版(IPv6)规模 部署行动计划》提出了三大重点任务。 \n（一）加快互联网应用服务升级，不断丰富网络信源 （二）开展网络基础设施改造，提升网络服务水平 （三）加快应用基础设施改造，优化流量调度能力 \n在第二大任务中政府明确提出 “升级改造移动和固定网络、 推广移动和固定 终端应用，淘汰不支持IPv6 终端、骨干网IPv6 互联互通、升级改造广电网络支 持IPv6”。', 'id': '676837_9'}
{'label': 'text', 'content': '1. IPv6 网络发展现状\n1.4. 全球向IPv6 网络演进，争IPv6 互联网先机\n随着5G网络建设、 万物互联以及互联网人口和业务的增长， Pv4 业务被IPv6 取代是不可逆转的趋势， 而且IPv4 被替代的趋势在加速。 目前大型ISP、 内容供 应商、数据中心、操作系统、软件APP 等都在努力发展IPv6 业务。根据网络分 析报告预测，IPv6 业务在2018-2019 年超过IPv4 业务。全球IPv6 流量变化也反 应到全球IPv6 的路由表的容量的变化上， 如图1-9 所示。 Pv6 的将更快取代IPv4 走上历史的舞台。 \n图1-8 全球IPv6 路由表和转发表快速增长 对比国外主要运营商的IPv6 发展水平，国内的IPv6 发展存在较大的差距。 目前中国政府进一步加大对IPv6 互联网支持，应抓住全球网络信息技术加速创 新变革、 信息基础设施快速演进升级的历史机遇， 加强统筹谋划， 加快推进IPv6 规模部署，构建高速率、广普及、全覆盖、智能化的下一代互联网，是加快网络 强国建设、 加速国家信息化进程、 助力经济社会发展、 赢得未来国际竞争新优势。', 'id': '676837_10'}
{'label': 'text', 'content': '2. IPv6-only 网络的SRv6 技术趋势\n2.1. IPv6-only 网络更简单高效\nIPv6 的128 比特位地址提供网络空间非常巨大，甚至可以为地球上每粒沙 子分配多个IPv6 的地址。 无论建设物联网还是5G 网络， 运营商都有足够的IPv6 地址分配给固定终端和移动终端。采用IPv6 协议后从网络边缘到网络核心是点 对点的直接通讯， 不需要多层NAT 地址转换， 可节省很多CGN 设备，也不需要 维护庞大的NAT 地址溯源和日记。 \n网络建设采用IPv4/IPv6 双栈模式，网络的复杂性增加一倍，网络的维护工 作量增加一倍，甚至更多。双栈网络的规模扩展性有限制，公网IPv4 协议地址 \n数量有限，而IPv6 协议地址接近无限，双方无法同步增长。双栈的方式中IPv4 协议地址的短缺需要部署NAT 完成IPv4 公网地址和私网地址转换的NAT。 双栈 的模式驱使从终端到网络设备和数据中心， 都需要支持IPv6 和IPv4 的双栈部署。 接入网、城域网和核心网的每台路由器同时运行IPv6 和IPv4 的路由器协议，对 路由器的CPU 和内存提出更多的要求，对网络的稳定性和收敛都带来很大的压 力。这些都会要求运营商投入更多的资金升级和替换昂贵的网络设备。 \nIPv6 协议有固定的头长度和灵活的IPv6 扩展头， 固定长度的IPv6 协议更有 利于网络设备的转发处理，相比IPv4 协议的可变头长度，IPv6 协议的转发效率 更高。来自Facebook 和其他公司研究都表明，美国运营的IPv6 终端下载的速率 比IPv4 的终端下载快15%-40%。 \nIPv6 协议支持即插即用，可通过自动无状态和有状态的获得IPv6 地址及参 数，IPv6 终端部署非常方便灵活，也有利于SDN 和NFV 的自动化部署。IPv6 协议通过扩展头实现AH 和ESP 保证自身负荷安全，安全性比IPv4 协议更高。', 'id': '676837_11'}
{'label': 'text', 'content': '2. IPv6-only 网络的SRv6 技术趋势\n2.2. IPv6-only 网络为SDN/NFV 提供更多支持\nSDN 和NFV 是当今驱动网络向前发展的双轮动力，它们不会因为基础协议 从IPv4 切换到IPv6 而停滞不前，相反IPv6 协议更能促进SDN 和NFV 发展。 面向上层的SDN 和NFV，IPv6 协议除了能够对齐IPv4 协议提供的所有特性,如 BGP-LS、Telemetry、PCEP、NETCONf、YANG 等，还能提供基于IPv6 协议的 Inband-OAM、SRv6 的Network Programming 功能，给SDN 和NFV 更大的支 持，如图2-1 所示。SDN/NFV 的部署也使用户流量从双栈模式切换到IPv6-only 协议相对更容易。 \n图2-1 云、SDN/NFV 和IPv6 三者关系 \nSRv6 通过128 bit 的SID 实现Network Program 功能。 28bit 被分为Location、 Function 和Parameters 等三个部分， 其中Location 用来表示到达该节点的路由信 息，Function 和Parameter 则分别表示在该节点执行的函数及所需参数信息，如 图2-2 所示。 Location, Function, Parameter这三部分没有固定的长度， 其中Location 字段的长度只要能够表达出到该节点路由信息，其长度可变，不一定非64bit， 可以更短。Function 和Parameter 的bit 位长度远远超过传统的IPv4 协议地址的 32 比特位，这给网络程序开发者带来更多的变化可能。IETF 的相关草案定义了 使用Function 和Parameter 的比特位，定义了许多为众所所知的函数及其参数， 满足网络可编程的需求。IETF 的专家们已经定义了十几种常用的函数，如简单 转发操作函数End、 执行类似IPv4 SR的Adj操作的函数End.X。 支持IPv6 L3VPN 函数的End.DX6。部分函数名称可以参见图2-2。 \n图2-2 SRv6 的Network Programming 云服务是基于网络资源池化处理并对资源统一调度、 分配和管理。 云服务可 以利用基于IPv6 协议的SDN 和NFV 技术，为数据中心提供基于IPv6 协议的 VM、vNet 和vRouter 资源。数据中心内IPv6 虚拟网络和外部IPv6 物理网络在 逻辑上达成统一网络部署。', 'id': '676837_12'}
{'label': 'text', 'content': '2. IPv6-only 网络的SRv6 技术趋势\n2.3. SRv6 提供E2E 的优质服务\n基于IPv6 协议的Segment Routing（SRv6）源路由技术提供端到端（E2E） 优质承载服务。 SRv6 网络将IPv4 网络中分段式业务路径演进到端到端的业务路 径如图2-3 所示。 在传统的IPv4 网络中， 业务从起点到终点经过多层NAT 转换， 很难管理端到端的可控路径。在接入层和核心层都需要部署NAT，前者解决终 端设备公网IPv4 协议地址不足，后者解决数据中心虚机私有IPv4 地址的问题。 \n在接入网、城域网、骨干网和数据都中心统一到IPv6-only 网络后，可以很容易 为用户提供端到端的业务。 \nSRv6 使用MBGP 作为控制面，将基于IPv4 MPLS 转发面的L3/L2VPN、 EVPN 演进到基于IPv6 转发面的L3/L2VPN 和EVPN。SRv6 提供的IPv6 的 L3/L2VPN 和EVPN，不再使用MPLS 和LDP、RSVP 协议，网络中协议类型大 幅减少，网络越来越简化和扁平化。IPv6-only 网络释放双栈设备中IPv4 相关的 资源如CPU 和RAM，IPv6-only 简化了网络的管理和维护，IPv6-only 网络的收 敛时间和稳定性将会提升。 \nIPv6 网络可提供功能强大的in-band OAM 功能，方便IPv6 网络性能测量和 SLA 实现，也方便网络运维。In-band OAM 通过在IPv6 定义专用的扩展头，实 现对转发面的各种性能数据进行测量，如延时、乱序、丢包等。IPv6 的In-band OAM 和SRv6 技术综合使用，可以为用户提供比IPv4 业务更好更优质SLA。 \n图2-3 端到端的极简IPv6-only 网络', 'id': '676837_13'}
{'label': 'text', 'content': '2. IPv6-only 网络的SRv6 技术趋势\n2.4. SRv6 兼容传统L3VPN 和EPVN 等业务\nSRv6 网络实现对IPv6 网络的L3VPN 业务的支持， 转发面基于IPv6 而不是 MPLS， MPLS 控制面协议如LDP 或者RSVP 也不需要。 SRv6 的L3VPN 仍然使 用MBGP 作为控制面协议， 对MBGP 扩展一个支持SRv6-VPN 的SID 的前缀属 性TLV。 Pv4 网络中RD 和RT 等概念在SRv6 的L3VPN 网络中保持不变， MBGP 在向邻居通告VPN 路由时携带一个SRv6-VPN 的SID。这类似MBGP MPLS L3VPN 携带VPN 私网MPLS 标签。 Egress 节点依据报文中携带的SRv6-VPN SID 查询对应的VRF 转发表，再根据IPv6 报文目的地址查找路由和出接口。控制面 的协议工作过程如图2-4 所示。 \n图2-4 SRv6 的L3VPN 的控制面原理 1 PE1 和PE2 节点与RR 反射器建立MBGP 邻居。PE1 节点有IPv6 的 L3VPN和IPv4的两个L3VPN， PE1向PE2通告这两个VPN的私网路由2008::1/64 和*******/24，除了携带RD、RT 等属性外还携带了SRv6-VPN SID (A::6)和 SRv6-VPN SID (A::4)。同理PE2 通告的VPN1 路由2009::1/64 携带RD、RT 等 属性外还有SRv6-VPN SID 属性值D::6，通告VPN2 路由*******/24 携带RD、 RT 等属性外还有SRv6-VPN SID 属性值D::4。PE1 和PE2 分别学习对方传递过 来的VPN 私网路由， 生成本地的两个L3VPN 的VRF 转发表， 用于对应VRF 流 量转发。 \n2 RR 反射器分别与PE1 和PE2 建立MBGP 邻居，其IGP 如OSPFv3 或 者ISISv6 也与之建立正常的邻居关系，RR 反射器正常收到携带SRv6 相关的信 息的OSPFv3 或者ISISv6 链路状态。 RR 反射器与控制器通过BGP-LS 建立PEER 关系， RR 反射器向控制器发送域内IPv6 路由链路状态信息 （包括SRv6 的信息） 。 \n3 控制器对收集的所有链路状态库 （包括SRv6 的信息） ， 进行分析计算， 考虑链路的物理带宽、实际使用带宽、设备的队列转发延时、业务要求的延时， 使用的算法等多种因素，计算出L3VPN 流量从Ingress 节点到到Egress 节点的 最佳SR 路由策略（SR-Policy），并下发L3VPN 的Ingress 节点。该策略也可以 在控制器上手工指定后再下发到Ingress 节点，或者在Ingress 节点上手工配置。 如图2-4 的步骤3 所示，控制器对Ingress 节点下发两条策略A::1:1 和A::1:2 分 别对应IPv6 的L3VPN 流量和IPv4 的L3VPN 流量转发策略。 \nSRv6 的L3VPN 业务转发比较简单， ngress 节点根据控制器下发的SR-Policy A::1:6 和A::1:4，对IPv6 L3VPN 流量和IPv4 L3VPN 流量执行不同的处理策略。 每个SR-Policy 指定业务流量需要经过中间的关键节点， 以及在该节点流量如何 \n处理，如执行负荷分担或者指定特定链路或者简单转发。在流量的Egress 节点 则指明对流量执行具体的操作，如解封SRv6 报文头，查特定的VRF 路由表， 根据内层目的地址查找下一跳和出接口，或者在公网IPv6 的路由表查找下一跳 和出接口，具体如图2-5 所示。IPv6 L3VPN 流量在A 节点执行A::1:6 策略，封 装IPv6 外层头的源地址(A::1)和目的地址B::1，依据策略A::1:6 插入SRH 头及 SR-List (B::1,D::6),并根据外层IPv6 的目的地址（B::1）查IPv6 转发表获取下一 跳和出接口，并把流量通过该接口送往节点B。IPv6 L3VPN 流量到达节点B 以 后，执行常规的END 函数操作，替换IPv6 外层目的地址为D::6，并偏移SRH 头的指针。 B 节点根据外层IPv6 的目的地址D::6 查找IPv6 转发表把报文送往节 点D。报文到达节点D 以后，根据目的地址中D::6 解封外层IPv6 头，根据D::6 寻找对应L3VPN 的私网路由表，并根据内层IPv6 报文目的地址2009::1 查下一 跳和出接口。如果能查到对应目的路由则转发报文，否则丢弃。IPv4 的L3VPN 流量转发过程类似IPv6 的L3VPN 流量，这里不再重复。 \n图2-5 SRv6 的L3VPN 流量转发过程', 'id': '676837_14'}
{'label': 'text', 'content': '3. IPv6-only 演进的三个阶段及规划策略\n3.1. IPv4-only 向IPv6-only 网络演进的三个阶段\nIPv4-only 网络指只有IPv4 协议相关的服务和路由协议等服务，没有IPv6 服务和协议相关的内容，同理IPv6-only 网络是指只提供IPv6 服务的网络，该网 络没有IPv4 协议和服务相关内容。后面的文档中还提及到IPv4-only 设备，是特 指设备只支持IPv4 协议不能升级到支持IPv6 协议，无法支持双栈。这里区分和 \n强调一下，防止引起概念混淆。IPv4-only 的网络是可以设备升级或者软件升级 实现IPv4&IPv6 双栈，但是IPv4-only 设备是不能升级到IPv4&IPv6 双栈。 \n从IPv4-only 网络演进到IPv6-only 有三个阶段： Pv4-only、 Pv4&IPv6 双栈、 IPv6-only，如图3-1 所示。现网中海量的IPv4 服务和IPv4 的网络设备，决定 IPv4&IPv6 双栈中间阶段无法回避。新建设和规划的网络，可以直接建设成 IPv6-only 网络，如新晋的运营商的网络建设。目前在IPv4 流量占绝对统治地位 的中国运营商市场，规划一个IPv6-only 的网络要考虑IPv4 网络和IPv6 网络互 通场景，考虑IPv4 客户访问IPv6 服务场景、IPv6 的客户访问IPv4 服务场景、 通过IPv4 网络连接两个IPv6-only 网络场景等。本文主要介绍是从IPv4-only 向 IPv6-only 逐渐演进的场景和技术。 \n图3-1 IPv4-only 演进到IPv6-only 的三个阶段 运营商从IPv4-only 的阶段1 演进到IPv4/IPv6 双栈阶段2 或者从IPv4/IPv6 双栈阶段2 演进到IPv6-only 的阶段3，没有固定的线路图或者模式，演进过程 中“运营商什么时候提供什么样的IPv6 服务”是运营商承载网络和业务起决定 性的作用，也决定了运营商向IPv6 演进具体路线图。本文后续介绍也仅仅是小 部分演进路线图，不是所有的可能的演进路线图。', 'id': '676837_15'}
{'label': 'text', 'content': '3. IPv6-only 演进的三个阶段及规划策略\n3.2. IPv4-only 向IPv6-only 演进的规划策略\n3.2.1. 网络演进的原则和次序\nIPv4-only 向IPv6-only 演进的首要原则：“不能影响现网的业务(IPv4/IPv6) 正常运行”。IPv4 设备上部署IPv6 协议或者双栈设备关闭IPv4 协议和服务时， 用户应该不感知运营商的基础网络升级到IPv4/IPv6 双栈或者从双栈到IPv6-only 的变化。例如当运营商的基础网络升级到IPv4/IPv6 双栈，会给用户的终端分配 IPv4 和IPv6 两个地址代替原来的一个IPv4 地址，而客户终端优先使用IPv6 协 议实现网页、视屏等应用。使用移动终端的客户并不感知这些变化，用户也不想 知道运营商基础网从IPv4 升级到IPv4/IPv6 双栈的具体事宜。 总之， 用户不感知 网络演进的变化。 \nIPv4-only 向IPv6-only 的次要原则：“兼容现有设备，不能强制用户升级或 \n者更换设备”。运营商网络演进的任何阶段，都不能强迫最终用户从IPv4-only 移动终端升级到支持IPv4/IPv6 双栈的移动终端或者固网设备设备。用户升级或 者淘汰自己设备，有用户自己的选择。 \n运营商的承载网一般有接入网、 城域网和核心网三部分， 根据承载网自身的 特点，承载网从IPv4-only 演进到IPv6-only 网络的建议遵循如下次序。 \n\uf06e 核心网升级 \uf06e 城域网升级 \uf06e 接入网升级 \n承载网从IPv4-only 网络升级IPv6/IPv4 双栈时， 首先是核心网升级双栈、 其 次城域网升级到双栈，最后接入网升级到双栈，如下图所示。同样从IPv6/IPv4 双栈升级到IPv6-only 网络，也是同样的次序。 \n图3-2 承载网双栈IPv6/IPv4 升级次序', 'id': '676837_16'}
{'label': 'text', 'content': '3. IPv6-only 演进的三个阶段及规划策略\n3.2. IPv4-only 向IPv6-only 演进的规划策略\n3.2.2. IPv6 地址规划和路由协议的选择\n在部署IPv6 协议之前，要决定申请IPv6 地址的类型，考虑与上级ISP 网 络IPv6 对接或者互联网中心的IPv6 网络的对接。IPv6 地址类型申请有 PA(Provider Assigned 和PI(Provider Independent)两种类型。PA 模式的IPv6 地址 一般上级ISP 分配，与上级ISP 的IPv6 地址空间保持一致，而PI 模式则申请的 IPv6 地址独立ISP 的IPv6 地址空间，可以与多个不同的ISP 网络互连。一般情 况下，上级ISP 网络或者互联网中心的IPv6 网络接口是比较丰富的，主要是协 调互通方式和内容。向地区互联网中心登记机构RIA 或者国家互联网管理中心 申请IPv6 地址数量，不仅要满足现有业务的需要，也满足将来业务发展的需要。 目前地区互联中心可以向下级ISP 或者Org 分配多个32 位前缀的IPv6 地址， 根 据IANA 的地址分配建议，ISP 内部可再次分配48 位/56 位前缀的IPv6 地址到 \n下层网络。IPv6 地址层次分配如下图3-3 所示。ISP 拿到的一个32 位掩码长度 IPv6 网段，其拥有IPv6 网段的数量等于现有全球IPv4 地址的数量。 \n图3-3 IPv6 地址的层次化分配 路由协议的规划， 既要考虑与上级ISP 或者互联网中心的互联EBGP 路由协 议规划，又要考虑现网中IPv4 和IPv6 的IGP 路由协议的规划。在IPv4 和IPv6 双栈的情况下，路由协议有多种组合策略，可以选择IPv4 和IPv6 合一的ISIS 路由协议，也可以为IPv4 和IPv6 选择不同的IGP 路由协议。后者根据实际情况 可以选择下面不同的路由协议组合。 \n<table><tr><th></th><th>IPv4 路由协议 </th><th>IPv6 路由协议</th></tr><tr><th>策略1 </th><th>OSPFv2 </th><th>OPSFv3</th></tr><tr><th>策略2 </th><th>OSPFv2 </th><th>ISIS for IPv6</th></tr><tr><th>策略3 </th><th>ISIS for IPv4 </th><th>OSPFv3</th></tr><tr><th>策略4 </th><th>ISIS for IPv4 </th><th>ISIS for IPv6</th></tr></table> \n表格3-1 双栈路由协议组合策略 IPv4-only 网络规划部署IPv6 路由协议时， 要考虑到IPv4 网络收敛时间的变 化对业务可能的影响， 以及设备的IPv6 能力是否可以满足迁移IPv4 业务的需求。 独立的IPv4 和IPv6 路由进程运行在同一台路由器上会竞争CPU 和内存等关键 资源。新增运行IPv6 相关进程，会对原来的IPv4 路由收敛时间会有影响。通过 对IPv4 和IPv6 相关的时间参数进行优化，可以减低IPv4 和IPv6 的收敛时间。 市场上有部分低端路由器，其IPv4 流量转发是硬件实现，而IPv6 流量转发通过 CPU 实现。这样低端路由器开启双栈后对CPU 的影响就更大了，需要慎重评估 网络流量大小和CPU 的利用率。', 'id': '676837_17'}
{'label': 'text', 'content': '3. IPv6-only 演进的三个阶段及规划策略\n3.2. IPv4-only 向IPv6-only 演进的规划策略\n3.2.3. 网络支撑业务优先IPv6 升级\n网络支撑业务保证运营商网络正常运行，如网管、论证、计费、授权、监控 等业务。在向客户大规模开放IPv6 业务前，首先要完成这些支撑业务的IPv6 升 级。比如对IPv6 业务的进行安全评估和测试。以前设备只支持IPv4 业务，黑客 只有通过IPv4 协议这一种途径，当设备支持IPv6 和IPv4 双栈时，黑客们又新 增加IPv6 这种途径。根据对IPv6 业务安全评估和测试结果，部署相应IPv6 的 防护方案。类似的支撑业务有很多，在发展IPv6 业务之前就应该网络支持系统 的升级，简单罗列如下： \n\uf06e IPv6 网络的安全防护系统（Firewall） \uf06e IPv6 网络的入侵检测系统 \uf06e IPv6 网络的负载均衡系统 \uf06e IPv6 设备的网络管理系统 \uf06e IPv6 的DNS 服务 \uf06e IPv6 流量的监控系统 \uf06e IPv6 的流量采集处理系统（NETFLOW） \uf06e IPv6 的用户论证授权计费管理系统 \uf06e 设备对IPv6 的NETCONF 和YANG 支持 \uf06e 建立到上级ISP 或者互联中心的IPv6 连接 \uf06e 网络设备支持IPv4 和IPv6 双栈，设备CPU 利用率评估', 'id': '676837_18'}
{'label': 'text', 'content': '4. IPv4&IPv6 双栈演进的主流过渡技术\n4.1. 核心网双栈演进的过渡技术\n目前运营商的骨干网/核心网设备基本都能支持IPv4/IPv6 双栈，骨干网从 IPv4-only 到IPv4/IPv6 双栈相对简单，不需要大规模的资金投入。通过合理的网 络规划和购买软件license，可以分区域按照计划把核心网逐步升级到IPv4/IPv6 双栈。 核心网的IPv4/IPv6 双栈升级不是一次性能够完成的， 可能出现多种场景。 如下图所示，核心网IPv4/IPv6 双栈升级过程中首先可能部分节点之间使用隧道 实现IPv6 的业务承载的要求，也可以在核心网的网络设备专用物理接口先开通 IPv6 路由，满足特定用户IPv6 的业务承载需求。 \n图4-1 核心网双栈演进的过渡阶段 IPv4 网络向IPv4/IPv6 双栈升级分阶段分区域比较更合理。首先是核心网中 的部分路由器的部分接口运行IPv6 协议组成一张IPv6 网络，该IPv6 网络稳定 运行一段时间后，在IPv6/IPv4 双栈路由器上仔细观察CPU、内存、路由表、转 发表等资源的使用情况， 并做科学分析和评估。 根据评估结果和现网设备的功能 性能和现状，再进行更大范围的IPv4/IPv6 双栈升级部署。这样双栈升级步骤更 合理，对网络影响会更小。 \n不同业务会导致过渡场景使用不同的过渡技术，接下来介绍核心网 IPv4/IPv6 双栈演进中常用的6PE 和6vPE 过渡技术。', 'id': '676837_19'}
{'label': 'text', 'content': '4. IPv4&IPv6 双栈演进的主流过渡技术\n4.1. 核心网双栈演进的过渡技术\n4.1.1. 6PE\n在大客户通过专线连接城域网或者核心网的路由器场景中， 如果客户有IPv6 Internet 访问的需求，就有IPv6 流量需要穿越运营商的核心网。运营商不需要全 部核心网路由器升级到双栈后再承接IPv6 业务，毕竟业务不能等。运行商仅需 要在IPv6 流量的入口路由器和出口路由器升级到双栈，并部署6PE 业务就可以 支撑大客户的IPv6访问Internet 的需求。 当然这里有一个预设的前提： 运营商IPv4 核心网络已经部署MPLS 功能。 PE 就是利用已有的IPv4 的MPLS 网络承载IPv6 业务的过渡技术。 \n6PE 基本原理在IPv6 报文外层封装两层MPLS 标签，利用MPLS 标签实现 \nIPv6 流量穿越IPv4 的MPLS 核心网。两层MPLS 标签中顶层标签Egress PE 根 据IPv4 前缀分配， 这保证了IPv6 报文任意穿越IPv4 核心网。 6PE 的部署在IPv6 流量的Ingress PE 和Egress PE 节点上，中间节点维持原来的MPLS 网络不变。 \n6PE 接入IPv6 公网访问业务的部署是非常方便灵活的。 \n在Ingress PE 和Egress PE 节点部署6PE 功能， 仅需要Ingress PE 和Egress PE 运行IPv4/IPv6 双栈，其他中间节点路由器保持IPv4 单栈不变。Ingress PE 和 Egress PE 节点通过MBGP 协议通告公网的IPv6 路由，Egress PE 和Ingress PE 都有公网的IPv6 路由表，而其他中间节点没有IPv6 路由信息，只有IPv4 路由 和标签转发表。 带有MPLS 标签的IPv6 报文穿越中间节点依据顶层IPv4 标签转 \n发， 而不是IPv6 的路由表。 带有MPLS 标签的IPv6 报文在MPLS 网络中转发也 支持次末跳弹出机制。 中间节点不感知MPLS 报文的负荷是IPv4 或者IPv6。 6PE 的网络控制面工作原理如图所示。 \n图4-2 6PE 控制面工作原理 要注意的6PE 通告的IPv6 路由是全局路由，6PE 中没有任何VPN 的概念， Ingress PE 和Egress PE 的IPv6 路由表是一致的。这点与6vPE 功能不同，6PE 没有根据用户属性隔离IPv6 路由功能，没有传统L3VPN 的RD 、RT 等属性。 Ingress PE 和Egress 节点通过MBGP 的AFI=2 通告IPv6 的路由，而不是VPN 的路由。6PE 非常适用公网IPv6 业务的承载，其控制面工作原理如下： \n6PE 路由器配置IPv4/IPv6 双栈，在IPv4-MPLS 网络侧配置一个IPv4 地址， 在运行IPv6 协议用户侧配置一个IPv6 地址， 如上图所示，6PE-1 的IPv4 的地址 为*******，6PE-2 的IPv4 的地址为*******。CE-2 向6PE-2 通告两个IPv6 路由： 2003::/64 和2004:/64， 6PE-2 通过IPv4 的MP-BGP 协议 （AFI=2， IPv6） 向6PE-1 通告这两条路由。如上图步骤①所示，每条路由都携带两个重要信息如下。 \n\uf06e IPv6 报文二层MPLS 封装的内层标签值YYY ； \uf06e 2003::/64 和2004::/64 下一跳为::FFFF:*******。 \n其中::FFFF:******* 为6PE-2 的IPv4 地址转换成的IPv4 映射IPv6 地 址::FFFF:*******，该地址为6PE 自动映射的特殊的IPv6 地址，不是6PE 路由器 CE 侧的IPv6 地址。6PE 设备封装IPv6 报文时候，根据这个地址::FFFF:******* 获得Egress PE 的IPv4 地址，并根据该IPv4 地址获得外层的MPLS 标签值。为 了方便描述，上图中并没有列出6PE 的IPv6 的接口地址。 \n同理， 6PE-1 也向6PE-2 通告两条IPv6 路由2001::/64 和2002::/64,也携带了 \n关键的IPv6 标签XXX 和该节点的IPv4 映射IPv6 地址::FFFF:*******。6PE-1 和 6PE-2 根据BGP 通告的IPv6 路由信息生成本地全局IPv6 路由表。如图中②所 示。6PE-1 和6PE-2 再向各自的CE 通告新学习的IPv6 路由。 \n上面是6PE 控制面原理的简单介绍，现在以下图中从CE-1 发送到CE-2 的 IPv6 流量为例，简要介绍一下6PE 的IPv6 报文转发流程。来自CE-1 的IPv6 报 文进入6PE-1 后，6PE-1 将完成对IPv6 的报文两层MPLS 标签封装后，再通过 MPLS 核心网到达6PE-2，如下图所示。 \n图4-3 6PE 转发面工作原理 从CE-1 的IPv6 报文进入6PE-1 后， PE-1 根据IPv6 报文的目的地址2003::1 查找本地的IPv6 路由表。在上图中步骤②所示，目的地址2003::1 匹配路由表 中红色的路由，其对应的下一跳为6PE-2 的IPv4 映射IPv6 的地址::FFFF:*******。 6PE-1 根据这个地址推导出下一跳的IPv4 地址*******。 PE-1 根据IPv4 地址******* 获取其对应的MPLS 值label1。外层标签值label1 和已知内层标签值YYY，6PE-1 获得转发IPv6 报文需要的两层MPLS 值， 其中内层标签来自MBGP 通告， 外层标 签来自Egress PE 的IPv4 地址。6PE-1 把IPv6 报文封装这两层MPLS 标签后发送 到核心网，如图中步骤②和步骤③所示。 \n当双层标签的IPv6 报文到达6PE-2 时，仅剩内层的MBGP 通告标签YYY，其 外层的MPLS 标签执行次末跳弹出如图中③所示。6PE-2 根据内层YYY 标签查对 应的IPv6 全局路由表，对IPv6 地址2003::1 进行最长匹配，找到去CE-2 的IPv6 路由和下一跳如图中的④所示。如此实现IPv6 报文从CE-1 穿越IPv4 骨干网到 达CE-2。IPv6 返程流量过程类似，这里不再详细介绍。', 'id': '676837_20'}
{'label': 'text', 'content': '4. IPv4&IPv6 双栈演进的主流过渡技术\n4.1. 核心网双栈演进的过渡技术\n4.1.2. 6vPE\n6vPE使用L3VPN为IPv6的业务建立独立的VPN， Pv6 报文封装两层MPLS 标签穿越IPv4 的MPLS 核心网。 6vPE 和6PE 控制面有很多相似之处， 也常常容 易引起混淆，6vPE 相比6PE，6vPE 提供的IPv6 的路由非全局路由，6vPE 的每 个IPv6 的L3 VPN 都有独立的IPv6 路由表，不同的IPv6 的L3VPN 通过RD 和 RT 等属性标识，6vPE 在无线接入网和企业网接入有很好的应用场景。下面介绍 一下6vPE 的控制面的原理。 \n6vPE 部署在Ingress PE 和Egress PE， ngress 和Egress 节点都需要IPv4/IPv6 双栈，中间的骨干网依然是传统的IPv4 MPLS 网络。6vPE 控制面协议也使用 MBGP。如下图所示,在Ingress PE 和Egress PE 之间协商建立MBGP 邻居时，携 带参数AFI=2， SAFI=128 标识， 在Egress PE和Ingress PE 建立支持IPv6的L3VPN 能力的MBGP 邻居。而6PE 之间建立邻居协商是仅携带的AFI=2 的协商参数， 没有要求sub AFI=128 的能力协商。 如下图中①所示， vPE-2 向6vPE-1 通告IPv6 的VPN1 路由时携带RD、RT、Label 等属性，2003::/64 和2004::/64 的下一跳 地址是IPv4 映射的IPv6 地址::FFFF:*******,都携带RD=X, RT=X, label2 等属性值。 该路由及其属性到达6vPE-1 时， 6vPE-1 根据RD=X， 把其放入VPN1 的IPv6 路由 表，并通告CE-1 设备，如图中的②所示。其中::FFFF:******* 为6vPE-2 的IPv4 地址******* 的IPv4 映射IPv6 地址::FFFF:*******。同理，6vPE-2 根据6vPE-1 通告的2001::/64 和2002::/64 路由及其携带的RD=X， RT=X， 私网标签值为label2 等属性，生成本地VPN1 的IPv6 路由表，并通告CE-2 设备。6vPE 控制面的原 理介绍结束后， 下面扼要介绍一下6vPE 转发面的工作原理， 以从CE-1 发往CE-2 的IPv6 的报文转发为例,IPv6 报文的源地址为2001::1,目的地址为2003::1。 \n图4-4 6vPE 控制面工作原理 \n来自CE-1 的IPv6 报文进入6vPE-1 后，根据该IPv6 报文入接口关联的 L3VPN 属性VPN1，6vPE-1 根据报文的目的地址2003::1 查找VPN1 的IPv6 的 路由表。如下图步骤②所示匹配路由表中红色的路由，对应的下一跳为6vPE-2 的IPv4 映射IPv6 的地址::FFFF:*******。6vPE-1 根据该地址获得下一跳的IPv4 地 址*******。6PE-1 根据IPv4 地址******* 获得其MPLS 标签label1。同样6vPE-1 知 道6vPE-2 为VPN1 的私网路由标签Label2。6vPE-1 用label1 标签做外层标签，用 label2 做内层标签， 封装IPv6 报文并发送报到IPv4 MPLS 骨干网， 如图中步骤② 所示。当双层标签封装的IPv6 报文到达6vPE-2 时，仅剩内层的内层VPN1 的私 网标签label2， 其外层的IPv4 MPLS 标签执行次末跳弹出， 如图中的步骤③所示。 6vPE-2 根据label2 标签查本地对应的VPN1 的IPv6 路由表，对报文中IPv6 的目 的地址2003::1 进行最长匹配，查找去CE-2 的IPv6 路由和出接口，如图中的步 骤④所示。查找到出接口以后，直接从该接口转发IPv6 报文到CE-2。如此实现 IPv6 报文从CE-1 穿越IPv4 的MPLS 骨干网到达CE-2。同样，从CE-2 发往CE-1 的IPv6 报文的过程也是类似。这里不再详细介绍。 \n图4-5 6vPE 封装IPv6 两层标签的转发流程', 'id': '676837_21'}
{'label': 'text', 'content': '4. IPv4&IPv6 双栈演进的主流过渡技术\n4.2. 城域网向IPv6/IPv4 双栈演进的主流技术\n城域网向IPv6/IPv4 演进的过程与骨干网基本一致，演进过程中存在部分路 由器支持IPv6/IPv4 双栈，或者路由器的部分接口支持IPv6/IPv4 双栈的中间状 态，最后所有城域网设备的所有接口都升级到IPv6/IPv4 双栈。过渡状态中同样 使用6PE 或者6vPE 在IPv4 的MPLS 骨干网上承载IPv6 业务。城域网双栈升级 使用的过渡技术与骨干网/核心网向IPv6/IPv4 双栈升级使用的过渡技术6PE 和 6vPE 也是相同的。 \n城域网使用6vPE或者6PE与骨干网使用不同之处是城域网的6vPE或者6PE 部署时中间的IPv4 MPLS 骨干网可能跨多个自治域，可能存在多种IPv4 L3VPN 跨域的组网场景，如下图所示。两个6PE 或者6vPE 的节点，经过城域网和骨干 网两个AS 互通。L3VPN 跨域有多种互通模式，也可以使用无缝MPLS 的技术。 \n图4-6 城域网的6PE 和6vPE 跨域承载IPv6 流量', 'id': '676837_22'}
{'label': 'text', 'content': '4. IPv4&IPv6 双栈演进的主流过渡技术\n4.3. 接入网双栈演进的主流技术\n接入网从IPv4-only 向IPv6/IPv4 双栈演进也存在多个中间过渡状态， 可能部 分路由器的部分接口先双栈， 最后部分路由器全部双栈， 到最后整体接入网全部 双栈。 一般情况下无法同时升级所有的所有接入网设备到双栈， 有很多旧设备无 法支持双栈。如果家庭网关等CPE 设备支持IPv6/IPv4 双栈,就可以在IPv4 的接 入网使用IPv6 over IPv4 技术快速部署IPv6 的业务，也就是6RD（Rapid Deployment）技术。下面内容简单介绍6RD 的工作原理。', 'id': '676837_23'}
{'label': 'text', 'content': '4. IPv4&IPv6 双栈演进的主流过渡技术\n4.3. 接入网双栈演进的主流技术\n4.3.1. 6RD\n6RD 能够快速在IPv4 的接入网部署IPv6 的终端业务，其核心为IPv4 地址 \n和IPv6 地址无状态的映射算法。在部署6RD 场景中有两种设备类型：6RD-CE 和6RD-BR。如下图所示，其中6RD-CE 可以是用户的CPE，如家庭网关，无线 路由器等； RD-BR 可以是运营商IPv4 网络和IPv6 网络互通的边缘的路由设备。 一个6RD-BR 和多个6RD-CE 组成一个6RD 的管理域，通常使用一对6RD-BR 设备来提高网络可靠性。 \n图4-7 6RD 域外流量转发示意图 6RD是一种无状态的技术， RD-CE或者6RD-BR 设备上不维护IPv4 与IPv6 地址映射表。 6RD-CE 设备用户侧接口配置IPv6 地址， 面向接入网接口配置IPv4 地址。6RD-CE 的IPv4 地址是通过配置获取的，如DHCP 方式，IPv6 的接口地 址则是根据算法自动生成。 RD-CE 在IPv6 报文外层封装IPv4 头穿越IPv4 网络， IPv4 报文头的源地址为6RD-CE 的IPv4 地址，目的IPv4 地址根据用户IPv6 报 文自动生成IPv4 目的地址或者6RD-BR 的IPv4 地址， 前者实现两个6RD-CE 之 间的RD 域内转发，后者实现到域外的流量转发。来自IPv4 接入网的反向报文， 6RD-CE 去掉外层IPv4 头根据内层IPv6 报文的目的地址转发。 \n6RD-BR 是IPv4 网络和IPv6 网络的边界设备， 同样有IPv4 接口IPv6 接口， 6RD-BR 的IPv4 地址需要发送给6RD-CE 设备。对来自6RD-CE 的IPv4 流量， 6RD-BR 剥去IPv4 报文头， 再根据内层IPv6 报文的目的地址转发。 反向IPv6 报 文在6RD-BR 被封装一个IPv4 报文头， Pv4 的目的根据IPv6 报文的目的地址自 动获得，源地址使用6RD-BR 的IPv4 地址。 \n6RD-CE 根据规则生成报文IPv4 目的地址和接口IPv6 的报文地址，完成此 项功能， RD-CE 需要获得四个关键参数： Pv4 掩码长度， RD 前缀长度， RD-BR 的IPv4 地址和6RD 前缀。 一般情况下这几个参数通过DHCP 的可选option 212） 即6RD-option 携带， 如上图所示。 一般情况下6RD-CE 设备需要有指向6RD-BR 的缺省路由以及指向隧道接口的6RD 前缀的路由。 前者路由用于出6RD 域的流 量转发， 后者路由用于6RD 内的6RD-CE 设备之间转发。发向6RD 域外的IPv4 封装比较简单， 源地址和目的地址分别是6RD-CE 的IPv4 地址和6RD-BR 的IPv4 地址， 下面以域内6RD-CE 之间的流量来说明从IPv6 地址自动映射IPv4 地址的 过程，如下图所示。 \n图4-8 6RD 域中6RD-CE 之间流量转发示意图 6RD-CE1 和6RD-CE2 通过DHCP 或者配置方式获得IPv4 地址分别为 ************ 和************。也通过DHCP 的option212 获得IPv4 的掩码长度 8，6RD IPv6 的前缀为2001:db8::/32, 6RD IPv6 前缀长度为64,6RD-BR 的IPv4 地址********。 6RD-CE1 和6RD-CE2 分别通过IPv6 的前缀长度,IPv6 前缀及自身 的IPv4 地址和IPv4 掩码长度，合成6RD-CE 设备接口IPv6 地址前缀，如图中 显示，分别为2001:db8:6465:0100::/64 和2001:db8:6464:0100::/64。 \nIPv6 终端可以通过DHCP-PD 或者ND 自动生成各自的IPv6 地址，如上图 终端1 和终端2 的IPv6 的主机地址分别为：2001:db8:6465:0100::10/128 和 2001:db8:6464:0100::10/128。从终端1 发向终端2 的报文到达6RD-CE1 后， 6RD-CE1 根据报文目的IPv6 地址，查找本地IPv6 报文转发表，查到其出接口 为虚拟接口tunnel1。6RD-CE1 根据IPv6 目的地址2001:db8:6464:0100::10/128， 自动生成隧道目的IPv4 地址************， 这样从6RD-CE1 到6RD-CE2 终端的 IPv4 tunnel 的源和目的地址都具备了，6RD-CE1 把IPv6 报文从该隧道送到 6RD-CE2。反向从终端2 到终端1 的IPv6 流量亦是同理，这里不再介绍了。', 'id': '676837_24'}
{'label': 'text', 'content': '4. IPv4&IPv6 双栈演进的主流过渡技术\n4.3. 接入网双栈演进的主流技术\n4.3.2. IPv6 over Ethernet\nIPv6 over Ethernet 有两种CPE 使用场景，即CPE 做为二层透明桥使用或者 \nCPE 作为三层路由设备来使用，如下图所示。当CPE 作为二层透明桥使用，透 传终端DHCPv6 请求报文，使该报文通过DHCP-Relay（BARS 设备）发送到 DHCPv6 服务器并返回分配服务器分配的IPv6 的地址和其他配置信息给终端。 终端可以请求DHCPv6 服务器直接分配的全局128 位IPv6 地址，也可以通过 SLAAC 方式生成IPv6 地址， 但需要通过DCHPv6 server 获取如DNS、 NTP 等 \n相关信息。下图中步骤①和步骤②给出DHCPv6 server 直接从IPv6 的地址池中 直接给终端分配128 位IPv6 地址和相关信息的示意说明。 \n图4-9 IPv6 over Ethernet 当CPE 作为三层路由器设备部署，CPE 首先通过DHCPv6 协议获取分配的 IPv6 地址前缀和相关信息如DNS、NTP 等。DHCPv6 server 可根据CPE 的请求 分配一个或者几个IPv6 地址前缀， 并附带关于前缀的租期等重要信息。 其次CPE 获取IPv6 前缀信息后，给每个下联的接口分配一个IPv6 前缀。最后终端用户通 过SLAAC 方式自动生成IPv6 地址，或者CPE 作为简化DHCPv6 server 为终端 提供128 位的IPv6 地址，或者仅提供其他配置信息给终端如DNS、NTP 等。如 上图中的步骤③和步骤④所示。如果CPE 作为简化的DHCPv6 server 分配地址 给终端，其分配给终端的地址租期不能大于上级DHCPv6 server 分配给其前缀的 租期。', 'id': '676837_25'}
{'label': 'text', 'content': '4. IPv4&IPv6 双栈演进的主流过渡技术\n4.3. 接入网双栈演进的主流技术\n4.3.3. IPv6 over PPPoX\nIPv6 over PPPoX 支持CPE 桥接模式或者路由模式部署， 即CPE 做为二层透 \n明桥使用，PPP 会话由终端发起，BRAS 终结。CPE 作为三层路由设备来使用， PPP 会话由CPE 发起，BRAS 终结。终端可通过DHCP 或者SLAAC 方式获得 IPv6 地址。桥接模式或者路由模式都需要借助DHCP-PD 获得IPv6 前缀地址， 也使用DHCPv6 获得DNS、 NTP 等相关信息。 相比IPv6 over Ethernet 部署， IPv6 over PPPoE 多了一层PPP 报文封装格式，使用了PPP 的LCP 和NCP 协商方式， 对用户的论证和授权方式也很灵活，下图所示部署场景PPP 由终端发起的桥接 模式。 \n图4-10 IPv6 over PPPoX 部署方式 IPv6 over PPPoX 在网络协商阶段，PPP 会话两端可以协商支持多种协议支 持方式，可以协商成只支持IPv4 的网络层协议如上图的client1，可以协商成只 支持IPv6 的网络层协议如上图的client2，可以协商成同时支持IPv4 和IPv6 的 网络层协议如上图的client3。 PPPoX 可以支持二层为Ethernet 传输也支持二层为 ATM 的传输。 \nIPv6 over PPPoE 的路由模式如下图所示，PPP 的会话的两端是CPE 和 BRAS，协商多种PPP 的网络层协议支持方式如IPv4、IPv4、IPv4/IPv6。CPE 可 以通过DHCP-PD 获得IPv6 前缀和DNS、NTP 等重要信息。终端可以根据CPE 的LAN 接口获得IPv6 前缀，通过SLAAC 的方式获得IPv6 地址或者通过CPE 上简易的DHCPv6 server 分配IPv6 地址和其他重要信息。 \n图4-11 IPv6 over PPPoX 的路由方式部署', 'id': '676837_26'}
{'label': 'text', 'content': '5. IPv6-only 网络演进的主流过渡技术\n承载网络进入IPv4/IPv6 双栈向IPv6-only 演进的阶段， 要考虑支持客户IPv4 的业务， 必也要考虑IPv6-only 访问其他IPv4-only 网络的场景， 也要考虑通过IPv6 网络连接两个IPv4 网络的业务场景。在关闭承载网中IPv4 相关的网络之前，必 须解决好这些问题。 客户的业务可能暂时无法同步从IPv4 升级到IPv6， Pv6-only 网络可以使用各种IPv6 承载IPv4 过渡技术来解决这些问题， 如DS-lite， MAP-E、 MAP-T 和NAT64 等。每种技术都有自己优点和缺点，也有特定的应用场景，下 面就简单介绍这几种技术。', 'id': '676837_27'}
{'label': 'text', 'content': '5. IPv6-only 网络演进的主流过渡技术\n5.1. DS-lite\nDS-Lite 设备通过在IPv6 的网络中建立IPv4-in-IPv6 隧道实现IPv4 报文的传 输， 而IPv6 报文经过DS-Lite 设备则直接转发。 DS-Lite 涉及到B4 （Base Bridging BroadBand Element）和AFTR（Address Family Transition Router element）两种设 备形态，前者部署靠客户端，通常B4 具备双栈的能力，一般在主机或者CPE 设 备上实现，后者AFTR 部署在运营商的IPv6 和IPv4 的边界路由上，也具有双栈 能力。B4 设备建立到AFTR 设备的IPv4-in-IPv6 的隧道，通常AFTR 设备支持 NAT44 功能，完成私网IPv4 地址到公网IPv4 地址转换。B4 建立到AFTR 的隧 道的源地址为B4 的WAN 接口IPv6 地址， 该地址可通过DHCPv6 分配获得。 隧 道的目的地址为AFTR 的IPv6 地址， 该地址可手工配置、 带外配置或者DHCPv6 的option 字段选项获得AFTR 名字，再通过DNS 获取AFTR 的IPv6 地址。从 AFTR 返向B4 设备的隧道， 隧道源地址使用AFTR 的IPv6 地址， 隧道的目的地 址为B4 的IPv6 地址。AFTR 可通过查询NAT44 的表项获取B4 的IPv6 地址。 故部署DS-Lite 时需要CPE 支持DNS 代理功能， 但过多的DNS 查询会导致AFTR 的NAT44 的表项快速增长。B4 设备也需要支持NTP 协议，用来同步租期等时 间参数。 \nDS-Lite 有多种部署场景，双栈主机做B4 直接建立到AFTR 的IPv4-in-IPv6 的隧道，或者双栈CPE 做B4 直接建立到AFTR 的IPv4-in-IPv6 的隧道，如下图 所示。 每种场景中B4 设备通过DHCPv6 及其option 获取B4 的IPv6 地址， DNS 的IPv6 地址，AFTR 的名字。作为CPE 的B4 设备还为其他终端分配IPv4 地址 和的DNS 的IPv4 地址及Gateway 等信息。 \n图5-1 DS-Lite 部署场景 B4 设备通过DHCPv6 获取IPv6 地址2001:0:db8:1::1，借助DHCP option 和 DNS 获取AFTR 的IPv6 地址2001:0:db8:2::1。DS-lite 的报文转发非常简单，当 IPv4 报文到达B4 设备，如下图步骤①所示。携带私网的源IP 地址******** 和 公网的目的地址*********。 B4 在IPv4 外层封装IPv6 报文头， 其源地址分别为B4 的IPv6 地址和AFTR 的目的地址，如下图中步骤②所示。当IPv6 报文到达AFTR 设备后，剥去外层的IPv6 封装，对内层IPv4 报文的私网源地址地址进行NAT44 转换从私网地址******** 转换成*********+port，同时在AFTR 生成对应包含B4 的IPv6 地址的NAT44 表项，如下图中步骤③所示。AFTR 根据IPv4 目的地址查 本地IPv4 路由转发报文。 \n图5-2 DS-Lite 报文转发示意图 \n从外网返回内网IPv4 终端******** 的报文到达AFTR 设备之前，目的地址 为IPv4 公网地址********* 如上图步骤④。该报文到达AFTR 后先根据NAT44 的映射条目，将IPv4 目的地址********* 转换为私网地址********，并读取对应 B4 的IPv6 地址， 用作外层IPv6 封装报文头目的地址， 外层IPv6 源地址为AFTR 的地址。 最后根据IPv6 报文的目的地址查找路由转发表发送报文到B4 设备， 如 上图中步骤⑤。B4 设备收到IPv6 报文，解封外层IPv6 封装后根据IPv4 报文的 目的地址转发报文到对应IPv4 终端********。', 'id': '676837_28'}
{'label': 'text', 'content': '5. IPv6-only 网络演进的主流过渡技术\n5.2. MAP-E\nMAP-E 也是用IPv6 报文头封装IPv4 报文的一种无状态的过渡技术，这点 \n类似DS-Lite。 MAP-E 在外层IPv6 的源地址和目的地址采用不同于DS-Lite 的方 法，后面有详细的介绍。MAP-E 主要满足运营商在IPv6-only 的网络中承载用户 IPv4 业务的需求。在MAP-E 的部署网络中，有两种设备类型MAP-CE 和 MAP-BR， 前者作为IPv4 业务接入点， 后者作为IPv4 网络和IPv6 网络的边界节 点，如图5-3 所示。一个MAP-BR 设备可以接收多个MAP-CE 设备的MAP-E 报文。 多个MAP-CE 和MAP-BR 设备组成一个MAP-E 域。 同一个域中MAP-CE 和MAP-BR 有相同的IPv6 前缀、IPv4 前缀、相同地址转换规则和转发规则等。 MAP-BR 支持命令行或者网管等配置BMR、 FMR 规则， MAP-CE 可通过DHCPv6 方式获得BMR、 FMR 信息。 通过DHCPv6 的MAP-option 携带信息有如下内容： \nRule IPv6 Prefix： MAP 规则指定的IPv6 前缀和长度 Rule IPv4 Prefix： MAP 规则指定的IPv4 前缀和长度 Sharing Ratio： MAP 端口共享的比率 Number of Contiguous Ports：可分配的连续端口数量 Mode (Translation or Encapsulation)：MAP 封装的模式MAP-E 或者MAP-T BR Prefix or BR Address： MAP-BR 地址或者前缀 \n图5-3 MAP 域网络中规则的配置和传递 MAP-E 的基本工作原理是在MAP-CE 收到IPv4 的报文时， 外层再封装一个 IPv6 报文头，外层的IPv6 源地址根据BMR（Basic Mapping Rule）规则生成， 目的地址使用MAP-BR 的IPv6 地址。MAP-CE 封装后的IPv6 报文根据FMR （Forward Mapping Rule）规则转发到MAP-BR 设备或者另一个MAP-CE 设备。 MAP-CE 或者MAP-BR 收到IPv6 报文后，进行解封外层IPv6 报文头，根据内 层的IPv4 目的地址查路由表或者转发表进行转发。 MAP-CE 原有的NAT44 功能 不受影响继续保持不变，MAP-BR 设备不需要维护IPv6 和IPv4 的地址映射，地 址根据规则自动生成。 \nMAP-E 不同与DS-Lite 的重要技术特点， 在于MAP-E 的BMR 规则和FMR 规则。 BMR 主要描述IPv6 地址和IPv4+port 的映射关系。 根据MAP 的基本信息 和BMR 规则， 通过IPv6 地址推导出其对应IPv4 的地址和端口号， 反之通过IPv4 的地址和端口号，可以推导出对应的IPv6 地址。BMR 把128 位的IPv6 地址定 义成如下4 个部分，EA bits 标示嵌入在IPv6 地址中的IPv4 的地址和端口集信 息。 \n图5-4 MAP 中IPv6 地址四段式结构信息 BMR 把32 位的IPv4 地址定义成IPv4 前缀和IPv4 后缀两部分， 其中IPv4 后 \n缀对应IPv6 地址的EA-bit 部分信息。 \n图5-5 MAP 中IPv4 的地址结构 BMR 把16 位的IPv4 的PORT 定义成如下三个部分，其中A 标识预留的众 所周知的地址如1-1024 范围， BMR 排除这些知名端口范围后再进行端口块分配 所以分配起始端口块为2^（16-a）。PSID 标志分配给IPv4 地址的端口块信息， 占k 比特位，j 标志连续可分配的端口数量，占m 比特位，分给同一用户的端口 块范围，其PSID 总是相同的。 \n图5-6 MAP 中16 位端口的编码结构 下图是根据PSID 来分配端口的端口表，其中a=4,k=10,m=2 来体会一下 MAP 的PORT 编码结构对端口分配的影响。 需要注意每个PSID 对应的端口范围 是不完全连续的。 \n图5-7 MAC-CE PSID 分配端口表 MAP 的BMR 规则中128 位的IPv6 地址， 2 位的IPv4 地址和16 位的PORT 之间的关系如下图所示： \n图5-8 MAP 的BMR IPv6 地址和IPv4 及端口之间关系 MAP 对节点的Interface ID 的地址结构设计如下， 内嵌了IPv4 的地址和PSID 如下图所示。 \n图5-9 MAP Interface ID 的地址结构 假定MAP-CE 收到一个源地址为********** ，源端口为1234 的IPv4 报 文，MAP-CE 根据MAP 规则和MAP 信息：Rule IPv6 前缀2001:db8::/40、 Rule IPv4 前缀*********/24， IPv6 地址前缀2001:db8::/56， 共享比率为256 （即 EA 中PSID 的场地为8 bit）可以推导出对应的IPv6 源地址。IPv4 源地址为 ********** 和MAP 信息IPv4 前缀为*********/24，推导其IPv4 地址的后缀为 18 对应十六进制值0x12，而IPv4 端口保留缺省为6 比特位即a 等于6 bit，端 口1234 对应的PSID 的k 等于8 bit，那么对应的j 等于2 bit，推导出PSID 的 值为0x34, 计算的过程为(1234-1024)/4=52，换成16 进制为0x34。再结合 Interface ID 生成方法，可以推导出IPv4 地址********** 对应的IPv6 源地址为 2001:db8:0012:3400::c000:0212:0034，如下图所示。 \n图5-10 MAP BMR 根据IPv6 地址推导IPv4 地址和端口 MAP-域内到域外IPv4 Internet 的报文转发流程如下图所示，终端的IPv4 报 文进入MAP-CE 后，先经本地NAT44 将其私网源地址******** 和端口3333 映 射成公网地址********** 和端口1234， 如下图步骤①和部署②所示的IPv4 报文 头变化。MAP-CE 再根据MAP 信息和BMR 规则计算成IPv6 报文的源地址为 2001:db8:0012:3400::c000:0212:0034，IPv6 报文目的地址为MAP-BR 的IPv6 地 址，该地址为MAP 信息通告，无需计算。最后MAP-CE 在IPv4 报文外面封装 IPv6 报文头， 如下图中步骤②所述。 当IPv6 报文到达MAP-BR 被解封外层IPv6 报文头，MAP-BR 根据内层IPv4 报文目的地址，查询IPv4 路由转发表的下一跳 和出接口，转发IPv4 报文到IPv4 Internet，如下图中步骤③所示。 \n图5-11 MAP-E 报文转发示意图 从IPv4 Internet 的返回MAP 域的回程流量转发如上图种步骤④所示，当 IPv4 报文到达MAP-BR，MAP-BR 根据IPv4 报文目的地址********** 端口1234 和MAP 信息及BMR 规则计算出外层IPv6 报文目地址为 2001:db8:0012:3400::c000:0212:0034，外层IPv6 的源地址使用MAP-BR 的IPv6 地址2001:db8:ffff::1。MAP-BR 查找IPv6 的路由转发报文到MAP-CE 如上图中 步骤⑤。MAP-CE 收到IPv6 报文后，解封外层IPv6 报文头，对内层的IPv4 做 NAT44 会话表查找匹配， 把IPv4 报文的目的地址和端口映射成******** 和3333 再转发到终端，如上图中的步骤⑥。', 'id': '676837_29'}
{'label': 'text', 'content': '5. IPv6-only 网络演进的主流过渡技术\n5.3. MAP-T\nMAP-T 工作原理与MAP-E 的最大区别MAP-T 增加了无状态的NAT64 功 \n能， 负责对IPv4 报文和IPv6 报文转译。 NAT64 支持v4 版本的ICMP 和TCP/UDP 与v6 版本ICMP/TCP/UDP 之间的转译。 后面的5.5.1 章节对无状态的NAT64 做 了完整的介绍，这里不再介绍。 \nMAP-T 增加了缺省映射规则(DMR)，该规则主要定义如何在IPv6 中嵌入 \nIPv4 地址的方法，以及从IPv6 地址中提取IPv4 地址的方法。从外部IPv4 的 Internet 流量进入MAP 域， 根据DMR 规则MAP-BR 能够根据IPv4 源地址合成 嵌入IPv4 地址的IPv6 源地址，同样根据DMR 规则MAP-CE 从IPv6 源地址中 取出嵌入的IPv4 源地址。反向流量只是MAP-CE 和MAP-BR 的分工互换一下， 并对IPv6 报文的目的地址进行处理。IPv6 地址中嵌入IPv4 地址的方法依据 RFC6052 "IPv6 Addressing of IPv4/IPv6 Translators"，这要求IPv6 prefix 长度小于 96 bits，缺省为64 bits。下面以域外IPv4 Internet 到域内IPv4 终端流量及返程流 量来说明MAP-T 的工作原理， 这样描述过程是为了突出无状态的NAT64 一个优 点，即会话可以由IPv4 重点发起。 \n图5-12 MAP-T 报文转发原理 如上图中步骤①所示， 源地址为******** 、 目的地址192.0.18 、 源端口80、 目的端口1232 IPv4 报文从MAP 域外到达MAP-BR 设备，MAP-BR 设备首先对 该报文进行无状态的NAT64 转换，根据DMR 规则生成IPv6 的源地址，BMR 规则生成IPv6 报文目的地址，去掉原有的IPv4 报文头，换上IPv6 报文头。其 次，根据IPv6 目的地址查找MAP-BR 的IPv6 路由转发表出接口和下一跳，转 发纯IPv6 报文到达MAP-CE，如图中步骤②所示。MAP-CE 首先进行的IPv6 到IPv4 的NAT64 报文的转换，根据DMR 规则获得IPv4 的源地址，根据BMR 生成IPv4 地址如图中步骤③所示，去掉IPv6 报文头换上新IPv4 报文头。其次， \nMAP-CE 根据NAT44 的session 表进行公网IPv4 目的地址和源端口完成到私网 IPv4 地址的转换，如图中步骤④所示。 \nIPv4 报文发往MAP 域外IPv4 Internet 报文如步骤图中⑤所示， Pv4 报文到 达MAP-CE，首先对IPv4 源地址*********、端口1444 进行NAT44 转换，替换 成公网地址********** 和端口1232，如图中步骤⑥所示。其次，再对IPv4 报文 进行NAT64 转译，完成IPv4 报文到IPv6 报文的转换，IPv6 目的地址根据DMR 规则生成嵌入IPv4 地址2001:db8:ffff:0:000a:0202:0400::，根据BMR 生成IPv6 的源地址2001:db8:0012:3400:0:c000:0212:0034。 MAP-CE 根据FMR 规则转发纯 IPv6 报文到MAP-BR 设备，如图中步骤⑦所示。MAP-BR 首先使用NAT64 功 能把IPv6 报文翻译成IPv4 报文， 其中根据DMR 规则获得IPv4 报文的目的地址， 根据BMR 规则获得IPv4 报文的源地址。 其次， 根据IPv4 目的地址查询MAP-BR 的IPv4 的路由转发表和出接口， 转发到IPv4 报文到IPv4 的Internet 公网， 如图 中步骤⑧所示。', 'id': '676837_30'}
{'label': 'text', 'content': '5. IPv6-only 网络演进的主流过渡技术\n5.4. NAT64\nNAT64 结合DNS64 用来解决IPv6-only 网络与IPv4-only 网络的互访场景， \n包括IPv6-only 网络访问IPv4-Internet 的场景， 或者IPv4-only 用户访问IPv6-only 数据中心的场景等。NAT64 实现协议如IPv6、ICMPv6 到IPv4、ICMPv4 等协议 字段的翻译， 也包括从IPv6 的源地址和目的地址翻译成IPv4 的源地址和目的地 址。 当然也支持反向的从IPv4 协议到IPv6 协议翻译和地址翻译。RFC7915 详细 规定每种协议类型的翻译方式。 由于IPv6 报文头和IPv4 报文头字段不是一一对 应的，从IPv6 报文头翻译成IPv4 报文头，或者从IPv4 报文头翻译成IPv6 报文 头有字段增加或者丢失，该RFC 详细定义各种情况的处理方法。 \nNAT64 分有状态和无状态的两种技术，无状态的NAT64 不需要设备维护点 \n到点的session 状态，对IPv6 地址和IPv4 地址做1:1 的映射，不节省IPv4 的公 网地址。如下图所示。 \n图5-13 NAT64 技术分类和应用场景 有状态的NAT64 支持多个IPv6 地址与一个IPv4 之间N:1 的映射，可以节 省IPv4 地址。有状态NAT64 设备维护一个IPv6 地址与一个IPv4 协议地址和端 口范围的集合映射关系。这个映射是根据IPv6 地址动态产生的，有状态NAT64 支持IPv6 主动发起到IPv4 节点的访问。如果静态配置IPv6 地址与IPv4 地址和 端口范围的映射，也可以支持IPv4 节点主动发起到IPv6 节点访问。', 'id': '676837_31'}
{'label': 'text', 'content': '5. IPv6-only 网络演进的主流过渡技术\n5.4. NAT64\n5.4.1. 无状态的NAT64\n无状态NAT64 的基本思路在终端的IPv6 的地址上嵌入一个IPv4 的地址， 通过算法实现IPv6 地址和IPv4 地址的翻译。 RFC6052 已经定义IPv4 内嵌在IPv6 格式内容，以IPv6 地址前缀：2002:db8:100::/40 和IPv4 地址********** 来说明 IPv4 地址嵌入IPv6 方法和IPv6 地址翻译成IPv4 地址的算法，如下图所示。 \n图5-14 IPv6 地址中嵌入IPv4 地址的格式 IPv4 地址********** 前24 比特为192.0.2 转换为16 进制值为0xC00002,后8 \n比特33 转换为16 进制为0x21。根据上图中的第三行规则，在IPv6 的prefix 2001:db8:100::/40 中嵌入IPv4 地址********** 的IPv6地址为2001:db8:1c0:2:21::。 根据已知前缀长度为40 bit 的IPv6 地址2001:db8:1c0:2:21:: 生成IPv4 的地址则 非常简单，如果IPv6 的prefix 长度为96，则最后32bit 就是IPv4 的地址，如果 IPv6 的prefix 长度X 小于96 bit， 则先删除上图8 个u bit 位，得到120 bit 地址， 删除前缀X 位和后缀Y 位，剩余120-X-Y 就是IPv4 的32 比特位（16 进制）。 \n无状态的NAT64 有独立的IPv6 和IPv4 接口，分别连接IPv6 和IPv4 网络。 无状态NAT64 设备有IPv6 地址前缀(2001:db8:100::/40) 和IPv4 的地址池 (*********/24)。前者用于IPv4 地址合成IPv6 地址的前缀，后者定义对IPv4 地址 进行翻译的范围。前者使用IPv6 路由协议通告到IPv6 网络，后者使用IPv4 路 由协议通告到IPv4 的网络。无状态的NAT64 需要借助DHCPv6 将嵌入IPv4 地 址的IPv6 地址分配给终端。 \n图5-15 无状态NAT64 工作原理 以IPv6 终端向IPv4 终端发送报文为例，介绍无状态的NAT64 的工作原理 如下： \n1 左侧IPv6 终端通过DHCPv6 获得嵌入IPv4 地址********** 的IPv6 地址 2001:db8:1c0:2:21::，该地址作为IPv6 报文的源地址。目的地址则是通过DNS64 获得IPv6 地址2001:db8:1c6:3364:2::或者应用指定的IPv6 目的地址。 \n2 IPv6 的报文根据路由到达NAT64 后，NAT64 对协议报文头做IPv6 到 IPv4 的转换，其中IPv6 源地址到IPv4 的源地址转换，根据RFC 定义的方法从 IPv6 地址中剥离出IPv4 地址:**********。 Pv6 目的地址根据DNS64 的IPv6 前缀 剥离出IPv4 目的地址************。NAT64 把IPv6 报文转换成IPv4 报文后，根 \n据IPv4 报文目的地址查找IPv4 路由表，从对应的IPv4 接口发送报文到IPv4 目 的终端。 \n3 IPv4 目的终端返回IPv6 源终端的报文， 源IPv4 地址************ 目的 IPv4 地址**********，该报文根据IPv4 路由到达NAT64 设备。 \n4 NAT64 做相应协议报文从IPv4 到IPv6 的翻译， 其中从IPv4 地址到IPv6 地址的转换，使用IPv6 前缀2001:db8:100::/40。根据RFC6052 定义的规则，源 IPv4 地址************ 和目的IPv4 地址********** 合成报文IPv6 源地址和目的 地址2001:db8:1c6:3364:2::和2001:db8:1c0:2:21::。NAT64 根据IPv6 报文的目的 地址查找路由表，根据对应的IPv6 的出接口到达IPv6 终端。 \n无状态的NAT64 优点： \n\uf06e 无状态维护， 不需要往返同路径， 支持多个无状态NAT64 设备负荷分担。 \uf06e 支持IPv4 网络向IPv6 网络的主动访问。 无状态的NAT64 劣势： \uf06e 不节省IPv4 地址，IPv4 地址和IPv6 地址一一映射。 \uf06e 终端嵌入IPv4 地址的IPv6 地址需要借助DHCPv6 分配。', 'id': '676837_32'}
{'label': 'text', 'content': '5. IPv6-only 网络演进的主流过渡技术\n5.4. NAT64\n5.4.2. 有状态的NAT64 和DNS64\n有状态的NAT64 支持多个IPv6 地址和一个IPv4 地址及端口范围的映射， 每个IPv6 地址映射到IPv4 地址和一个端口范围，这个映射是动态生成的。有状 态的NAT64 维护IPv6 地址与IPv4 地址及端口范围的映射关系，这个映射首先 通过IPv6 地址触发生成，满足IPv6 终端主动访问IPv4 终端的需要。同时，生 成对应的IPv4 及端口范围与IPv6 的对应关系，满足IPv4 终端访问IPv6 终端的 需要。有状态的NAT64 也支持手工配置静态映射关系，实现IPv4 终端主动发 起访问IPv6 终端需求。有状态的NAT64 可实现ICMPv6、IPv6 协议和地址与 ICMPv4、IPv4 协议和地址的转换，协议的转换规则同样遵循RFC7915。DNS64 主要是将DNS 查询返回的A 记录IPv4 地址， 根据指定IPv6前缀合成AAAA 记 录IPv6 地址返回IPv6 用户。 \nNAT64 的部署需要DNS64 的协同工作， Pv6/IPv4 终端或服务器端不需要做 任何修改。DNS64 与NAT64 是完全独立的部分，无消息交互。DNS64 使用 64:FF9B::/96 （pre64::/n） 知名前缀， 或者指定的具体前缀合成IPv6 地址。 NAT64 对该前缀进行IPv6 地址到IPv4 地址转换。 \nIPv6-only 终端发起访问IPv6 业务时，DNS 解析的域名IPv6 地址，作为报 文IPv6 目的地址，终端使用IPv6 默认路由把流量转发至IPv6 目的终端。而 IPv6-only 终端访问的是IPv4 业务时， DNS64 根据IPv6 前缀Pref64::/n 和DNS 返 回的IPv4 地址， 合成IPv6 地址返回给IPv6-only 终端。 终端使用该地址作为IPv6 \n报文目的地址，IPv6 的Pref64::/n 前缀流量被路由到NAT64 路由器。在NAT64 设备上从而实现IPv6 与IPv4 地址和协议的转换后，访问IPv4 网络中的资源。 IPv6 报文的源地址和目的地址翻译成IPv4 报文的源地址和目的地址方式是不同 的。 IPv6 报文的源地址被映射到一个IPv4 地址和端口范围集合，该IPv4 地址作 为IPv4 报文的源地址。IPv6 报文的目地址则根据DNS64 合成规则剥离IPv6 前 缀获得IPv4 地址作为IPv4 报文目的地址。返向报文从IPv4 地址翻译成IPv4 地 址，IPv6 报文的源地址根据DNS64 的前缀与IPv4 源地址地址合成而获得，而 IPv6 目的则根据目的IPv4 地址和端口范围查询反向映射关系获得对应IPv6 目的 地址。有状态的NAT64 工作原理如下图所示。 \n图5-16 NAT64 结合DNS64 工作原理 有状态的NAT64 流程如下： \n1 IPv6 终端发送AAAA 的查询到DNS64，请求域名的IPv6 地址 2 DNS64 发送AAAA 查询到DNS server 3 DNS server 不能查到AAAA 的IPv6 地址，反馈空地址到DNS64 4 DNS64 发送A 查询到DNS server 5 DNS server 返回A 查询域名IPv4 地址到DNS64 6 DNS64 将IPv4 地址和IPv6 前缀合成IPv6 地址与AAAA 属性返回终端 7 IPv6 终端发送IPv6 报文被路由到NAT64 8 NAT64 将IPv6 报文翻译成IPv4 报文和地址后发送到IPv4 server 9 IPv4 server 返回IPv4 报文被路由到NAT64 10 NAT64 将IPv4 报文翻译成IPv6 报文和地址发送到IPv6 终端 \n有状态的NAT64 的优点： \n\uf06e 支持IPv6 地址到IPv4 地址的多对一的映射，节省IPv4 地址。 \n\uf06e 终端的IPv6 地址无需嵌入IPv4 地址，可以自由配置。 有状态的NAT64 的劣势： \uf06e 需要往返同路径， 即经过同一个有状态的NAT64 设备， 不支持负荷分担。 \uf06e 有状态NAT64 需要维护NAT64 日记和溯源，需要更多资源。', 'id': '676837_33'}
{'label': 'text', 'content': '6. IPv6 与网络安全\n6.1. IPv6 安全增强特性\n在网络安全方面IPv6 协议比IPv4 协议有了很大的进步，主要表现在IPv6 地址可溯源和防攻击、使用IPsec 加密机制、邻居发现协议NDP 和SEND 安全 增强、真实源地址检查体系，而这些特性是IPv4 地址不具备的。 \n1) 可溯源和防攻击 \nIPv6 协议的128 位地址空间巨大，理论上不会再有IPv6 地址短缺困境，也 不需要广泛使用NAT 设备节省IPv6 公网地址。 Pv6 终端之间可以直接建立点到 点的连接，无需地址转换，故IPv6 地址非常容易溯源。IPv6 地址分为64 位的网 络前缀和64 位的接口地址。一个64 位的前缀地址支持64 位的主机数量，攻击 者无法扫描一个IPv6 网段内所有可能的主机。假设攻击者以每秒扫描100 万个 主机的速度扫描，大约50 万年左右才能遍历一个64 位前缀内所有的主机地址。 64 位的主机地址使得网络扫描的难度和代价都大大增加，从而进一步防范了攻 击。 \n2) IPv6 支持IPSec 安全加密机制 IPv6 协议中集成了IPSec 安全功能，通过扩展认证报头（AH）和封装安全 载荷报头（ESP）实现加密、验证功能。其中AH 协议实现数据完整性和数据源 身份认证功能，而ESP 在上述功能基础上增加安全加密功能。集成了IPSec 的 IPv6 协议真正实现了端到端的安全， 中间转发设备只需要对带有IPSec 扩展包头 的报文进行普通转发，不对IPSec 扩展头处理，大大减轻转发压力。 \n3) 邻居发现协议（NDP）和SEND 的安全增强 \n在IPv6 协议中，采用NDP 协议取代现有IPv4 中ARP 及部分ICMP 控制功 能如路由器发现、重定向等。NDP 协议通过在节点之间交换ICMPv6 信息报文 和差错报文实现链路层地址及路由发现、 地址自动配置等功能； 并且通过维护邻 居可达状态来加强通信的健壮性。NDP 协议独立于传输介质，可以更方便地进 行功能扩展。现有的IPv6 协议层加密认证机制可以实现对NDP 协议的保护。下 一代互联网的安全邻居发现协议 （SEND） 通过独立于IPSec 的另一种加密方式， 保证了传输的安全性。 \n4) 真实源地址检查体系 \n真实源IPv6 地址验证体系结构（SAVA）分为接入网（Access Network）、 区域内（Intra-AS）和区域间（Inter-AS）源地址验证三个层次，从主机IP 地址、 IP 地址前缀和自治域三个粒度构成多重监控防御体系，该体系不但可以有效阻 止仿冒源地址类攻击； 而且能够通过监控流量来实现基于真实源地址的计费和网 管。目前基于SAVA 实现的系统在CNGI-CERNET2 网络上进行了实验性部署、 运行和测试，已经形成标准RFC。 \nIPv6 协议虽然在安全方面有提高，但依然没有解决分片攻击和地址欺骗攻 击等手段。同时会出现一些针对专门针对IPv6 协议新内容的攻击。IPv6 的网络 攻击主要有下面这些形式。', 'id': '676837_34'}
{'label': 'text', 'content': '6. IPv6 与网络安全\n6.2. IPv6 面临的安全威胁和措施\n6.2.1. 分片攻击\nIPv6 协议的分片攻击主要主要有以下两种： \nA) 通过在首片中不包含传输层信息来逃避防火墙的安全防护策略； \nB) 产生大量分片使得防火墙耗费大量资源实现重组，或者发送不完整的分 片报文强迫防火墙长时间等待集合中的其他分片。 \n通过严格限制同一片报文的分片数目， 设置合理的分片缓冲超时时间等手段 来预防此类攻击。', 'id': '676837_35'}
{'label': 'text', 'content': '6. IPv6 与网络安全\n6.2. IPv6 面临的安全威胁和措施\n6.2.2. NDP 协议攻击\nIPv6 协议虽然使用NDP 协议替代IPv4 中的ARP 功能， 但针对ARP 的攻击 如ARP 欺骗、ARP 泛洪等在IPv6 协议中仍然存在,同时IPv6 新增的NS、NA 也 成为新的攻击目标。 NDP 协议可通过IPSec 来实现安全认证机制， 但是协议并没 有给出部署指导，另一方面，SEND 协议可以彻底解决NDP 协议的安全问题， 但是目前终端及设备还普遍不支持该协议。 \nNDP 协议中路由器通告（RA）报文，通告一些网络信息给终端如PC，PC 收到以后，根据收到的信息自动配置网络。攻击者伪造RA 报文，发送错误的 RA 报文或者重定向报文，使得链路上的其他节点把攻击者作为默认网关，数据 流到达攻击者以后，攻击者可以查看敏感信息，甚至篡改信息再发向目的节点， 或者直接丢弃数据报文。 攻击者也可在RA 报文里面通告错误的前缀信息、 MTU 信息、MAC 信息甚至其他错误网络参数等，使得网络不能正常工作。如图6-1 所示。 \n图6-1 邻居发现协议攻击 \n因此现阶段对ND 欺骗的防护可以参考现有ARP 的防范手段，其基本思路 就是通过ND detection 和DHCP Snooping 得到主机IP、 MAC 和端口的绑定关系， 根据绑定关系对非法ND 报文进行过滤，配合RA Trust 和DHCP Trust 对RA 报 文和DHCP 报文进行限制。当然，通过配置端口的最大ND 表项学习数量也可 以避免ND 泛洪攻击。', 'id': '676837_36'}
{'label': 'text', 'content': '6. IPv6 与网络安全\n6.2. IPv6 面临的安全威胁和措施\n6.2.3. 扩展头攻击\nA) 扩展头顺序攻击 RFC2640 有规定如下的Next Header 负载的顺序，不按照顺序来的封装包文 会被丢弃。 \nA. Hop-by-Hop Options Header B. Destination Option Header C. Routing Header D. Fragmentation Header E. Authentication Header F. Encapsulation Security Payload Header G. Destination Option Header H. Upper Layer Header \n攻击者可以通过构造包含异常顺序扩展头的报文对防火墙进行DOS 攻击， 防火墙在解析报文时耗费大量资源， 从而影响转发性能。 这种攻击可以通过限制 扩展头的顺序来避免。 \nB) 扩展头数量攻击 当前协议对IPv6 扩展头数量没有做出限制，同一种类型的扩展头也可以出 现多次。攻击者可以通过构造包含异常数量扩展头的报文对防火墙进行DOS 攻 \n击，防火墙在解析报文时耗费大量资源，从而影响转发性能。这种攻击可以通过 限制扩展头的数量和同一类型扩展头实例的数目来避免。 \nC) Destination Option 攻击 Hop-by-Hop Option Header 与Destination Option Header 的padding 都可以被 用作攻击手段。Hop-by-Hop 扩展头是所有IPv6 节点都必须检查的头部，而 Destination Option 由目的节点来检查，PadN 用来填充封装包的Ocet 的倍数，但 是黑客可能修改padN,增加恶意信息。防火墙和入侵检测系统必须能过滤这两个 Header 的恶意修改，也要对PadN 所填充的data 不是0 的部分进行过滤。 \nD) Routing Header RH0 攻击 攻击者将目标网络的中间节点当成转运站传送原本不被防火墙的Policy 或 路由器的ACL 允许的目的端封包，从而避免防火墙的policy 或路由器的ACL。 应对这种攻击策略在路由器在inbound方向上设置deny ipv6 any any routing-type 0，过滤这类攻击报文。 \nE) Fragmentation Header 攻击 在IPv6 协议中，分片和重组都在目的节点完成，中间的路由器不负责这些 操作，但具有L4-7 层能力的防火墙和入侵检测系统必须要有能力在分片中找出 这些攻击包，并过滤掉这些攻击报文。 \nF) Upper Layer Header 的威胁 IPsec 对于应用层并无任何保护， 因此你必须用在IPv4 中使用同样的方法来 保护。如应用层防火墙。', 'id': '676837_37'}
{'label': 'text', 'content': '6. IPv6 与网络安全\n6.2. IPv6 面临的安全威胁和措施\n6.2.4. IPv6 DNS 攻击\n目前针对DNS 的攻击较多，如“网络钓鱼”，“DNS 缓冲中毒”等，这些 攻击会控制DNS 服务器，将合法网站DNS 记录中的IP 地址进行篡改，从而实 现DOS 或钓鱼攻击。 IPv6 的SLACC 协议支持任一节点都可以上报DNS 域名和 IP 地址，本意是通过该协议提高DNS 的更新速率，但是这样带来了冒用域名的 风险。DNS 安全扩展协议（DNS Security Extension）基于PKI 公私钥体系实现 对DNS 记录的认证及完整性保护， 除了可以预防上述攻击外， 对现存的DNS 攻 击同样有效。', 'id': '676837_38'}
{'label': 'text', 'content': '6. IPv6 与网络安全\n6.2. IPv6 面临的安全威胁和措施\n6.2.5. IPv6 组播攻击\nIPv6也支持组播发送网络信息， 如使用FF02::1表示网段的所有节点、 FF02::2 表示网段上的所有路由器。如果所有Service(含IPv6 的Service)都bind 到IPv4， 仅对IPv4 做扫描就可以得到双栈主机所有IPv6 的Hosts 以及上层的服务。使用 smurf6 的DDOs 工具，对FF02::1 来发送ICMPv6 Echo 使同网段的所有Nodes 发送ICMPv6 Replay 来对目标节点做Flood 攻击。', 'id': '676837_39'}
{'label': 'text', 'content': '6. IPv6 与网络安全\n6.2. IPv6 面临的安全威胁和措施\n6.2.6. ICMPv6 攻击\nICMPv6 是IPv6 的重要组成部分，ICMPv6 也容易引入各种攻击。主要表现 在下面一些方面。 \nA）利用ICMPv6 可以冒充其他节点产生恶意消息（目的不可达、超时、参 数错误等） 来影响正常通信。 比如攻击者冒充源节点到目的节点路径中的某个路 由器发送伪造的目的不可达ICMPv6 消息给源节点， 源节点收到这样的消息以后 就会停止向目的节点发送IP 报文。如图6-2 所示。 \n图6-2 目的不可达攻击 \nB） 利用ICMPv6 产生反射攻击。攻击者伪造大量的ping 请求报文发送给目 标靶机，这些包的源IPv6 地址不是攻击者本身的地址而是受害者的某个全球单 播地址，目标靶机收到ping 请求报文以后都会将ping 响应报文发往受害者，这 样的反射流将会消耗受害者或者受害者所在的网络的带宽和处理资源， 如果反射 流足够大将会造成拒绝服务攻击。如图6-3 所示。 \n图6-3 PING 攻击 C) 利用包过大报文进行PMTU 攻击。IPv6 通过PMTU 机制使得IP 包分片 \n只在源节点进行，重组只在目的节点进行，中间节点不分片，以提高网络转发效 率。攻击者如果伪造包过大报文发向源节点，就会对PMTU 的学习造成干扰， 进而影响整个网络通信。比如构造的包过大报文的携带的MTU 太小，将导致网 络性能严重下降；如果构造的包过大消息中携带的MTU 太大，将导致某些路由 器丢弃数据报文，如果这样的错误报文太大，消息不断重复，就会使得路由器不 能从它自己发出的正确包过大消息中恢复过来，不停的在进行PMTU 学习，导 致网络中大量丢包，甚至产生拒绝服务。', 'id': '676837_40'}
{'label': 'text', 'content': '6. IPv6 与网络安全\n6.2. IPv6 面临的安全威胁和措施\n6.2.7. 无状态地址自动配置攻击\nIPv6 协议支持无状态地址自动配置，设备根据一个IPv6 前缀和Interface id 自动组装一个IPv6 地址，该地址经过重复检测（DAD）,通过后正式使用。针对 DAD 原理，攻击者针对收到的DAD 检测报文，回复地址冲突的NA 报文，就会 导致合法用户的合法地址的DAD 检测失败，地址不可用。', 'id': '676837_41'}
{'label': 'text', 'content': '6. IPv6 与网络安全\n6.2. IPv6 面临的安全威胁和措施\n6.2.8. IPv6 新的应用带来的安全风险\nIPv6 强制使用IPSec， 使得防火墙过滤变得困难， 防火墙需要解析隧道信息。 如果使用ESP 加密，三层以上的信息都是不可见的，控制难度大大增加，需要 安全设备能够识别出攻击报文新方法和措施。', 'id': '676837_42'}
