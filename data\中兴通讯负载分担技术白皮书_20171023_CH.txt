{'label': 'document', 'content': '中兴通讯负载分担技术白皮书_20171023_CH', 'id': '764841'}
{'label': 'text', 'content': '负载分担技术白皮书 \n——中兴通讯IP RAN场景业务拥塞解决方案', 'id': '764841_1'}
{'label': 'text', 'content': '简介\n随着Internet的快速发展和业务量的不断提高，基于网络的数据访问流量迅速增长，特别是对数据中心、大型企业以及门户网站等的访问，对网络带宽的需求呈爆发式增长，而运营商的承载设备单端口带宽的增长却低于网络带宽和应用服务的增长，大带宽的端口技术发展速度跟不上网络对大带宽需求的增长速速，因此链路聚合（Link Aggregation）技术和等值多路径路由（ECMP）技术应运而生。 \n链路聚合，是指将多个物理端口捆绑在一起，成为一个逻辑端口，以实现出/入流量在各成员端口中的负荷分担，路由器根据用户配置的端口负荷分担策略决定报文从哪一个成员端口发送到对端的路由器。当路由器检测到其中一个成员端口的链路发生故障时，就停止在此端口上发送报文，并根据负荷分担策略在剩下链路中重新计算报文发送的端口，故障端口恢复后再次重新计算报文发送端口。链路聚合在增加链路带宽、实现链路传输弹性和冗余等方面是一项很重要的技术。链路聚合技术具有增加网络带宽和提高网络连接的可靠性的优点。 \nECMP存在于多条不同链路到达同一目的地址的网络环境中，如果使用传统的路由技术，发往该目的地址的数据包只能利用其中的一条链路，其它链路处于备份状态或无效状态，并且在动态路由环境下相互的切换需要一定时间，而ECMP可以在该网络环境下同时使用多条链路，不仅增加了传输带宽，并且可以无时延无丢包地备份失效链路的数据传输。ECMP最大的特点是实现了等值情况下，多路径负载均衡和链路备份的目的，在静态路由和OSPF中基本上都支持ECMP功能。 \n链路聚合与ECMP技术中需要解决的一个最大的难题是如何让负载分担变得均匀，本文着重讲解负载分担的一些方法以及各种方法的优缺点。', 'id': '764841_2'}
{'label': 'text', 'content': 'IPRAN场景负载分担方法\n负载分担原理\n线路上的负载分担，包含两种分担技术：一种是部署Smartgroup实现，一种是通过IP-ECMP/LDP-ECMP实现。无论是采用Smartgroup还是ECMP，其分担算法都是一样的，最后分担的效果也完全相同。 \n负载分担有两种基本模式，一种是逐流的负载分担，一种是逐包的负载分担；基于流的负载分担通过识别报文的内容，相同报文内容的流量选择相同的出端口，基于包的负载分担通过报文到达设备的次序在逐个端口上分担，相同报文内容的流量可能会选择不同的出端口，存在报文乱序的风险。以下着重介绍逐流方式的负载分担。 \nIP RAN设备负载分担算法包含：根据以太头、IP头中的信息计算一个Hash值，选择负载分担组中的某一个目的端口。负载分担算法一般有多种，例如单纯根据L2头、单纯根据IP头中五元组（源IP、目的IP、协议号、源端口号、目的端口号），或者L2+五元组的组合，或者根据标签栈（根据芯片能力不同，有的取整个标签栈，有的可能只取转发标签）。 \n从负载分担算法可以看出，不管什么分担方式，一条固定的流只能从一个出口出去，不会在不同出口之间变化选择。如果要想实现在设备之间进行分担必须有较多的流才能HASH出多个值，以达到最佳均衡效果。 \n如果设备接收的报文是裸IP报文，则出口分担可以选择L2头、或者IP五元组进行分担。设备角色可以是PE设备或者普通路由器设备，在IP RAN中L2VPN或者L3VPN PE设备可以采用这种分担方式。 \n如果设备是LSR标签转发，非PE转发，由于标签内封装的报文可能是IP封装、PWE3封装或者以太封装报文。根据报文中的信息确定封装格式是IP头、还是L2头还是PWE3信元，如果根据标签后面的封转信息，很可能将报文解析错误，因此标签转发业界都是根据标签栈进行分担的，无法依赖数据信息部分进行HASH分担。', 'id': '764841_3'}
{'label': 'text', 'content': 'IPRAN场景负载分担方法\nMPLS VPN负载分担\n在MPLS技术中，链路负载分担方式存在两种角色设备的负载分担： \n角色1： VPN PE设备或者MPLS LER设备 \n角色2： MPLS LSR设备，即P设备 \n对于PE设备或者LER设备，例如PE1，当收到用户流量，从网络侧发送时，或者从P1收到的流发往CE1时，出口的负载分担策略可以根据业务类型采用多种方式： \n模式1： 对于L2VPN，可以根据IP头中五元组或者L2层头进行负载分担，可以实现NNI/UNI侧出口的较好的负载分担 \n模式2：对于IP化业务，L3VPN或者普通MPLS转发，可以根据IP头中五元组进行负载分担，可以实现NNI/UNI出口较好的负载分担 \n模式3: 对于TDM/ATM业务，这种业务一般没有负载分担要求，如果PE NNI侧进行了捆绑，实现负载分担较为困难； \n对于角色P设备，例如P1，不管是从PE1收到的流发往P2，还是从P2设备收到流发往PE1，由于其处理方式都是相同的，负载分担方式也完全相同，但是由于P1（LSR1）设备对于收到的MPLS报文中的封装信息无法区分PWE3封装还是L3VPN封转还是L2VPN封装，是无法采用业务中五元组进行分担的，否则可能会导致一些业务乱序。只能根据标签栈进行负载分担。 \n业界对于P设备，MPLS报文的负载分担均是采用入标签栈进行负载分担的。另外，不同设备处理能力上存在不同，有的是根据LSP标签分担，有的是根据整个MPLS标签栈进行分担。有些设备可以根据整个标签栈进行分担的，但是有些设备只能根据LSP标签进行分担，还有某些设备只能根据LSP外层标签进行分担。 \n在IP RAN中 LSP标签一般采用LDP分发，LSP标签分配严格按照LDP协议分发，到某条路由的标签会采用同样的标签，即有可能链路入口LSP标签都是同一个，标签HASH时无法起到负载均衡的目的，只能根据PW或者VPN标签分担，PW标签一般是一个PW一个标签，而VPN标签存在两种标签分发方式：每路由每标签和每VPN每标签分发。IP RAN中，为了实现更高的靠性，实现NHT功能，避免网络故障频繁的刷VRF路由，业界均采用的是每VPN每标签分发方式。 \n这样对于二层业务，如果PW比较多，P设备上还可以起到分担作用，但是对于VPN，由于IP RAN所有的基站都是在同一个VPN中，通过标签分担，无法达到分担效果。', 'id': '764841_4'}
{'label': 'text', 'content': 'IPRAN场景负载分担方法\nMPLS熵标签和流标签负载分担\n根据MPLS的分担算法介绍知道，LSR上分担存在算法限制，导致逐流的分担算法对于基站类业务无法达到良好的分担效果。 \n但是在IP RAN组网场景中，中间经过LSR设备转发的现象大量存在，例如汇聚ER上进行链路捆绑，核心层ER之间进行链路捆绑。 \n如下图所示，PE1和PE2之间只有一条RSVP隧道（暂记为T1），因此，PE1上是不会认为有负荷分担存在的，但P1和P2之间走的是SmartGroup（即LAG，链路聚合组），因此，P1/P2却面临SG负荷分担的问题。 \nPE1----------P1=========P2----------PE2 \n那么，P1上应当根据报文的什么特征来进行负荷分担呢？ \n尤其需要注意的是，隧道T1可以同时承载PE1上的VPLS、PWE3仿真、L3VPN、LDP LSP甚至公网IP业务，但P1却并不知道它承载的是什么业务。 \n负荷分担特征因子的选择，直接影响到负荷分担的效果（主要是均匀程度和乱序避免），本需求描述了一种高效的、P结点不感知的P结点负荷分担因子选择机制。 \n以上述场景为例，P1上有两种选择负荷分担因子的传统办法： \nP1选择完整的标签栈作为负荷分担因子； \nP1猜测报文类型是否IP，并以IP五元组作为负荷分担因子； \n其中，第一类负荷分担因子是很粗糙的，尤其是当PE1-PE2之间跑的是L3VPN业务并且是“每VPN每标签”时，同一VPN的所有报文都只会走同一条路径（因为内层标签都是同一个标签）；第二类负荷分担因子很不可靠，因为P设备上并不能准确地知道载荷的类型（P设备不是service-aware结点），简单根据载荷的第一个nibble（半字节）是否为0x4或0x6来决定载荷是否IP包是很武断的，很容易将一些L2VPN载荷（比如PE1与PE2之间还有一条不带控制字的VPLS业务）误解为IP包，从而造成这些无辜载荷的乱序。 \n因此，这两种传统解决方案，都不能很好地解决问题。 \n在IP RAN组网场景中，中间经过LSR设备转发的现象大量存在，例如汇聚ER上进行链路捆绑，核心层ER之间进行链路捆绑。 \n为了解决MPLS负载分担算法的限制，IETF针对PW转发定义了RFC6391（通常称为流标签）、针对L3VPN定义了RFC6790（熵标签），为了在P设备上能够像在PE设备上一样实现根据IP五元组分担的效果，或者更加细致的分担效果，两个标准均定义了当收到用户流量时，PE设备在MPLS标签头中插入一个“流”标签，保证P设备在转发时基于标签栈HASH就可以实现标签负载分担的目的。', 'id': '764841_5'}
{'label': 'text', 'content': 'IPRAN场景负载分担方法\nMPLS熵标签和流标签负载分担\n场景1：SG负荷分担\n如下图所示，PE1和PE2之间只有一条RSVP隧道（暂记为T1），因此，PE1上是不会认为有负荷分担存在的，但P1和P2之间走的是SmartGroup（即LAG，链路聚合组），因此，P1/P2却面临SG负荷分担的问题。\n| PE1-P1=========P2-PE2 | PE1-P1=========P2-PE2 | PE1-P1=========P2-PE2 | PE1-P1=========P2-PE2 | PE1-P1=========P2-PE2 | PE1-P1=========P2-PE2 |\n|:-|:-|:-|:-|:-|:-|\n| 收包结点 | 收包结点 | PE1 | P1 | P2 | PE2 |\n| 所收报文 | 载荷 | **** | **** | **** | **** |\n| 所收报文 | VPN标签 | ／ | VPN标签 | VPN标签 | VPN标签 |\n| 所收报文 | EL标签 | ／ | 哈希码 | 哈希码 | 哈希码 |\n| 所收报文 | ELI标签 | ／ | 7标签 | 7标签 | 7标签 |\n| 所收报文 | 隧道标签 | ／ | TL1 | TL2 | 0标签 |\n| 标签操作 | 标签操作 | Push4 | Swap | Swap | Pop4 |\n在熵标签解决方案下，P1仍然以“完整标签栈”作为负荷分担因子，但P1上负荷分担的均匀程度却能达到与PE1相当的地步。\n这是因为，此时的标签栈中除了包含报文转发用的标签之外，还包含了虽然不用于索引任何标签转发表条目，但却增加了“标签栈”中所含的“信息量（即所谓的‘熵’）”的标签，RFC6790将其称为“熵标签（Entropy Label）”。\n这里新增的“信息量”，主要是PE1结点（作为业务感知结点）将载荷的负荷分担因子编码成为MPLS标签的格式，并添加到MPLS标签栈中传递到了P1结点（作为非业务感知结点），使得P1仅仅依靠标签栈就能获得与PE1相当的负荷分担信息量。\n因为P1仍然是以标签栈作为负荷分担因子，这与传统解决方案是相同的，这意味着P1可以是老设备。也就是说，P设备不会有升级软硬件的要求，因为所有的新增工作量都完全由PE设备完成了，代价是PE设备在不确定是否有负荷分担的情况预先进行了负荷分担的哈希运算。\n这里的载荷有可能是私网载荷（如L3VPN或VPLS），也有可能是公网IP载荷，对于后者，VPN标签是空缺的，因此VPN标签一行被标注为灰色。\n这里的隧道可以采取PHP，也可以不采取PHP，取决于PE2的配置情况，这里仅PE4上分配显式空标签的情况为例。\n', 'id': '764841_6'}
{'label': 'text', 'content': 'IPRAN场景负载分担方法\nMPLS熵标签和流标签负载分担\n场景2：LDP/BGP ECMP\n如图所示，PE3与PE4之间只有一条LSP，但这条LSP在P3和P4结点之间有一个负荷分担（分别经过P5和P6）区段。\n| PE3-P3-P5-P4-PE4 | | +-P6-+ | PE3-P3-P5-P4-PE4 | | +-P6-+ | PE3-P3-P5-P4-PE4 | | +-P6-+ | PE3-P3-P5-P4-PE4 | | +-P6-+ | PE3-P3-P5-P4-PE4 | | +-P6-+ | PE3-P3-P5-P4-PE4 | | +-P6-+ | PE3-P3-P5-P4-PE4 | | +-P6-+ |\n|:-|:-|:-|:-|:-|:-|:-|\n| 收包结点 | 收包结点 | PE3 | P3 | P5/P6 | P4 | PE4 |\n| 所收报文 | 载荷 | **** | **** | **** | **** | **** |\n| 所收报文 | VPN标签 | ／ | VL1 | VL1 | VL1 | VL1 |\n| 所收报文 | EL标签 | ／ | 哈希码 | 哈希码 | 哈希码 | 哈希码 |\n| 所收报文 | ELI标签 | ／ | 7标签 | 7标签 | 7标签 | 7标签 |\n| 所收报文 | LSP标签 | ／ | TL1 | TL2 | TL3 | TL4 |\n| 标签操作 | 标签操作 | Push4 | Swap | Swap | Swap | Pop4 |\n这时，在PE3看来并没有负荷分担发生，但它却要预先进行负荷分担运算，因为只有它才知道报文的完整的负荷分担特征因子，并且报文到了P3要面临负荷分担处理。\n引入EL标签以后，P3上面就可以仅根据EL标签进行分担，但如果P3是老设备的话，它是根据整个标签栈（应该剔除所有保留标签）来进行负荷分担的。\n这里的LSP可以是LDP/BGP LSP，但应该不可能是TE隧道，目前还没有听说TE隧道可以在其transit结点负荷分担的情况。\n这里的载荷有可能是私网载荷（如L3VPN或VPLS），也有可能是公网IP载荷，对于后者，VPN标签是空缺的，因此VPN标签一行被标注为灰色。\n这里的LSP可以采取PHP，也可以不采取PHP，取决于PE4的配置情况，这里仅以PE4上分配非空标签的情况为例。\n', 'id': '764841_7'}
{'label': 'text', 'content': 'IPRAN场景负载分担方法\nMPLS熵标签和流标签负载分担\n场景3：LDP ECMP over RSVP\n如图所示，在LER1与LER4之间只有一条LDP LSP，但这条LDP LSP在LSR2与LSR3之间有一个负荷分担（分别经过P7和P9）区段。此外，在LSR2和LSR3之间只有一条RSVP隧道（暂记为T2，不考虑Hot Standby），但隧道T2因为经过了P7和P8之间的一条smartGroup（即LAG，链路聚合组）而同样面临负荷分担。\n其中，LER1和LER4都必须是能够识别ELI/EL标签的新设备，其它设备则既可以是新设备，也可以是旧设备。\n不失一般性，我们假设LSR2和P8是不能识别ELI标签的旧设备，而P7和LSR3则是可以识别ELI标签的新设备。\n| PE1-P2-P7=======P8-P3-PE4 | | +-P9-P10-+ | PE1-P2-P7=======P8-P3-PE4 | | +-P9-P10-+ | PE1-P2-P7=======P8-P3-PE4 | | +-P9-P10-+ | PE1-P2-P7=======P8-P3-PE4 | | +-P9-P10-+ | PE1-P2-P7=======P8-P3-PE4 | | +-P9-P10-+ | PE1-P2-P7=======P8-P3-PE4 | | +-P9-P10-+ | PE1-P2-P7=======P8-P3-PE4 | | +-P9-P10-+ | PE1-P2-P7=======P8-P3-PE4 | | +-P9-P10-+ |\n|:-|:-|:-|:-|:-|:-|:-|:-|\n| 收包结点 | 收包结点 | PE1 | P3 | P7/P9 | P8/P10 | P4 | PE2 |\n| 所收报文 | 载荷 | IP包 | IP包 | IP包 | IP包 | IP包 | IP包 |\n| 所收报文 | EL标签 | ／ | 哈希码 | 哈希码 | 哈希码 | 哈希码 | 哈希码 |\n| 所收报文 | ELI标签 | ／ | 7标签 | 7标签 | 7标签 | 7标签 | 7标签 |\n| 所收报文 | LDP标签 | ／ | L1 | L2 | L2 | L2 | ／ |\n| 所收报文 | RSVP标签 | ／ | ／ | TL1 | TL2 | ／ | ／ |\n| 标签操作 | 标签操作 | Push3 | SwapPush | Swap | PHP | PHP | Pop2 |\n因为P3是旧设备，不能识别ELI标签，因此，它会以整个标签栈为负荷分担因子，由于标签栈中已经包含了PE1添加的哈希码（即熵标签）在内，因此LSR3上的负荷分担是均匀的。\n因为P7是新设备，能够识别ELI标签和EL标签，它是只按EL标签来负荷分担呢，还是按整个标签栈（但要排除所有保留标签）来负荷分担呢？只有当ELI标签恰好位于次外层时（也就是熵标签正好封装在本次查标签转发表的那层LSP时），P设备才只按EL标签来负荷分担，否则就按整个标签栈来负荷分担，但这一点目前还没有找到明确的协议依据。\n实际上，这里仍然可能是VPN业务（如L3VPN或VPLS），但由于前面一直都在强调VPN业务（都是以VPN业务为例编制表格的），这里破例以公网IP业务为例来进行分析。因此，对于VPN业务的情况，表中还需要补充VPN标签。\n这里的LDP LSP和RSVP隧道可以采取PHP，也可以不采取PHP，取决于Egress结点的配置情况，这里仅以Egress结点上分配隐式空标签的情况为例。\n', 'id': '764841_8'}
{'label': 'text', 'content': 'IPRAN场景负载分担方法\nMPLS熵标签和流标签负载分担\n场景4：MSPW业务SPE结点\n如下图所示，在PE5和PE6之间有一条以太VPWS业务（使能了流标签能力），该VPWS业务为跨域业务，它通过在ASBR1和ASBR2上部署MSPW实例来实现跨域。\nASBR1与ASBR2之间直接以PW标签跨域，这意味着所有外层标签都无法从一个AS域带到另一个AS域，也包括外层的ELI标签和EL标签。\nASBR2与PE6之间走的是由两条RSVP隧道T3和T4（分别经过P12和P13）捆绑而成的ECMP组；PE5与ASBR1之间只有一条LDP LSP1；T3、T4和LSP1都使能了熵标签能力。\n| PE5-P11-ASBR1======ASBR2-P12-PE6 | | +-P13-+ | PE5-P11-ASBR1======ASBR2-P12-PE6 | | +-P13-+ | PE5-P11-ASBR1======ASBR2-P12-PE6 | | +-P13-+ | PE5-P11-ASBR1======ASBR2-P12-PE6 | | +-P13-+ | PE5-P11-ASBR1======ASBR2-P12-PE6 | | +-P13-+ | PE5-P11-ASBR1======ASBR2-P12-PE6 | | +-P13-+ | PE5-P11-ASBR1======ASBR2-P12-PE6 | | +-P13-+ | PE5-P11-ASBR1======ASBR2-P12-PE6 | | +-P13-+ |\n|:-|:-|:-|:-|:-|:-|:-|:-|\n| 收包结点 | 收包结点 | PE5 | P11 | ASBR1 | ASBR2 | P12/P13 | PE6 |\n| 所收报文 | 载荷 | **** | **** | **** | **** | **** | **** |\n| 所收报文 | 控制字 | ／ | CW | CW | CW | CW | CW |\n| 所收报文 | FL标签 | ／ | 哈希码 | 哈希码 | 哈希码 | 哈希码 | 哈希码 |\n| 所收报文 | PW标签 | ／ | L1 | L1 | L2 | L3 | L3 |\n| 所收报文 | 隧道标签 | ／ | TL1 | 0标签 | ／ | TL2 | ／ |\n| 标签操作 | 标签操作 | Push3 | Swap | PopSwap | SwapPush | PHP | Pop2 |\n好在RFC6391定义了一种“流标签（FL）”，由于FL标签是打在PW标签以内的，因此不会因为外层隧道的终结而剥去。所以ASBR2上仍然能够获取到PE5添加的“哈希码”，从而保证了负荷分担的均匀性。\n值得注意的是，虽然LDP LSP1和隧道T3、T4均具有熵标签能力，这里却没有必要再在它们的层面上打上熵标签，因为这种熵标签不过是原有标签栈（含流标签）的“同义反复”而己，并不能真正增加标签栈的“熵（即信息量）”。\n此处隧道可以采取PHP，也可以不采取PHP，取决于Egress结点的配置情况（隐式空/显式空/非空），此处每条隧道仅取一种情况为例。\n', 'id': '764841_9'}
{'label': 'text', 'content': 'IPRAN场景负载分担方法\nMPLS熵标签和流标签负载分担\n场景5：OPTION B跨域L3VPN\n在“场景4”的基础上，除了上述VPWS业务以外，PE5与PE6之间还有一条L3VPN业务，且该业务使用“每VPN每标签”的分配方式，使用OPTION B方式跨域。\n| PE5-P11-ASBR1======ASBR2-P12-PE6 | | +-P13-+ | PE5-P11-ASBR1======ASBR2-P12-PE6 | | +-P13-+ | PE5-P11-ASBR1======ASBR2-P12-PE6 | | +-P13-+ | PE5-P11-ASBR1======ASBR2-P12-PE6 | | +-P13-+ | PE5-P11-ASBR1======ASBR2-P12-PE6 | | +-P13-+ | PE5-P11-ASBR1======ASBR2-P12-PE6 | | +-P13-+ | PE5-P11-ASBR1======ASBR2-P12-PE6 | | +-P13-+ | PE5-P11-ASBR1======ASBR2-P12-PE6 | | +-P13-+ |\n|:-|:-|:-|:-|:-|:-|:-|:-|\n| 收包结点 | 收包结点 | PE5 | P11 | ASBR1 | ASBR2 | P12/P13 | PE6 |\n| 所收报文 | 载荷 | **** | **** | **** | **** | **** | **** |\n| 所收报文 | VPN标签 | ／ | VL1 | VL1 | VL2 | VL3 | VL3 |\n| 所收报文 | EL标签 | ／ | 哈希码1 | 哈希码1 | ／ | 哈希码2 | 哈希码2 |\n| 所收报文 | ELI标签 | ／ | 7标签 | 7标签 | ／ | 7标签 | 7标签 |\n| 所收报文 | 隧道标签 | ／ | TL1 | ／ | ／ | TL2 | ／ |\n| 标签操作 | 标签操作 | Push4 | PHP | PopSwap | SwapPush | PHP | Pop3 |\n如上表所示，PE5打上的EL标签（哈希码1）是无法带到ASBR2的，那么，ASBR2上的EL标签（哈希码2）只能是ASBR2自行计算了，即使最终计算的结果碰巧哈希码2与哈希码1相等，此EL标签也已经不是彼EL标签了。\n那么，ASBR2上是如何计算出哈希码2来的呢？\nASBR2从标签VL2的标签转发表中可以知道这是L3VPN Option B跨域的情况，因此可以确定载荷是IP包，也就可以执行跟PE5同样的哈希计算；这实际上是在假设L3VPN标签一定是栈底标签，从而L3VPN的载荷一定是IP载荷。\n另一种办法就还是走靠猜载荷类型的老路。\n上述对于“L3VPN标签一定是栈底标签”的假设，在CSC（运营商承载的运营商）情况下（参见“场景6”）并不成立，当L3VPN标签不是栈底时，还是要走猜载荷的老路，或者干脆就放弃封装熵标签。\n', 'id': '764841_10'}
{'label': 'text', 'content': 'IPRAN场景负载分担方法\nMPLS熵标签和流标签负载分担\n场景6：OPTION B跨域C2C业务\n在“场景5”的基础上，PE5和PE6是一级运营商的设备，CE1和CE2是二级运营商的设备，它们作为一级运营商的CE的同时，在二级运营商的网络里仍然充当PE，并且在这一对二级PE之间可以同时承载L3VPN、VPLS、PWE3仿真等各种业务。\n| PE5-P11-ASBR1======ASBR2-P12-PE6-+ / | | \\ CE1 +-P13-+ CE2 | PE5-P11-ASBR1======ASBR2-P12-PE6-+ / | | \\ CE1 +-P13-+ CE2 | PE5-P11-ASBR1======ASBR2-P12-PE6-+ / | | \\ CE1 +-P13-+ CE2 | PE5-P11-ASBR1======ASBR2-P12-PE6-+ / | | \\ CE1 +-P13-+ CE2 | PE5-P11-ASBR1======ASBR2-P12-PE6-+ / | | \\ CE1 +-P13-+ CE2 | PE5-P11-ASBR1======ASBR2-P12-PE6-+ / | | \\ CE1 +-P13-+ CE2 | PE5-P11-ASBR1======ASBR2-P12-PE6-+ / | | \\ CE1 +-P13-+ CE2 | PE5-P11-ASBR1======ASBR2-P12-PE6-+ / | | \\ CE1 +-P13-+ CE2 |\n|:-|:-|:-|:-|:-|:-|:-|:-|\n| 收包结点 | 该结点所收报文的格式 | 该结点所收报文的格式 | 该结点所收报文的格式 | 该结点所收报文的格式 | 该结点所收报文的格式 | 该结点所收报文的格式 | 标签操作 |\n| 收包结点 | 载荷 | 栈底 | 外1层 | 外2层 | 外3层 | 外4层 | 标签操作 |\n| CE1 | **** | ／ | ／ | ／ | ／ | ／ | Push2 |\n| PE5 | **** | VLx | TLx | ／ | ／ | ／ | SwapPush |\n| P11 | **** | VLx | VL1 | 哈希码1 | ELI | TL1 | PHP |\n| ASBR1 | **** | VLx | VL1 | 哈希码1 | ELI | ／ | Swap |\n| ASBR2 | **** | VLx | VL2 | ／ | ／ | ／ | SwapPush |\n| P12/P13 | **** | VLx | VL3 | 哈希码1 | ELI | TL2 | PHP |\n| PE6 | **** | VLx | VL3 | 哈希码1 | ELI | ／ | PHP |\n| CE2 | **** | VLx | ／ | ／ | ／ | ／ | Pop |\n如上表所示，灰色底色表示由二级运营商封装的部分，其中除隧道（二级运营商）标签TLx被替换L3VPN（一级运营商）标签VL1之外，其它部分（比如二级运营商的私网标签VLx）都被从CE1一直带到CE2；黄色底色表示由一级运营商封装的部分。\n此场景中，不仅在ASBR2上无从知道确切的载荷类型，即使在PE5上也无从知道确切的载荷类型，因此这种情况下即使外层隧道协商出有熵标签能力的结果，也没有必要再去封装熵标签，因为即使勉强封装熵标签，这个熵标签也无非是原有标签栈信息的“同义反复”，并没有真正增加标签栈中所含的“熵（即信息量）”。\n每条隧道/LSP的Ingress结点本来就有拒绝封装熵标签的权力，即使该隧道/LSP的协商结果是有熵标签能力的。\n当熵标签只能成为原有标签栈所含信息量的“同义反复”时，Ingress结点均可拒绝封装熵标签，这一点并不局限于这一个场景，此外还有很多类似场景。\n如果实在想要负荷分担做得更均匀，可以由二级运营商负责封装流标签（对于L2VPN业务），因为流标签在二级运营商的私网标签以内（载荷以外），它能完全地穿过一级运营商的各个设备。\n对于L3VPN业务，暂未找到明确的协议依据可以将熵标签封装到私网标签以内。\n', 'id': '764841_11'}
{'label': 'text', 'content': 'IPRAN场景负载分担方法\nMPLS熵标签和流标签负载分担\n熵标签能力嵌套\n熵标签能力嵌套是指连续封装多层隧道标签时，转发面发现有2层及2层以上隧道标签具有熵标签能力的情况，此时应当只有其中最内的那层实际添加熵标签，外面各层熵标签能力应当被忽略。 \n熵标签能力嵌套涉及的转发表中，都含有出标签信息，但其含有的出标签信息，在最终封装的完整标签栈中，可能都只占一部分，但不管它含有几层标签，应该做到每层标签有一个相应的熵标签能力（因此，如果出标签在某个表中是以数组的形式来表示的，那相应地熵标签能力就也可以使用数组来表示）。 \n这里需要注意的是“3标签”，当出标签为“3标签”时，转发面是不封装相应的标签头的，但对应的熵标签却可以封装，因此，即使出标签为“3标签”，相应的熵标签能力标志也不能忽略，必须下发给项目。', 'id': '764841_12'}
{'label': 'text', 'content': 'IPRAN场景负载分担方法\nMPLS熵标签和流标签负载分担\n熵标签能力分歧\nB ---- E \n/ \\ \nD—---A G \n\\ / \nC ---- F \n这个组网，假设是LDP ECMP，如果B通知A一个前缀FEC1有熵标签能力，C通知A同一前缀FEC1没有熵标签能力，A通知给D的时候，通知有还是没有？ \n整体上，这种情况是不应该存在的，因为无论是B上的通告还是C上的通告，都有一个共同的来源，那就是G，G的熵标签能力是一定的，它不可能向E和F通告不同的ELC TLV，所以B和C向A通告不同的ELC TLV的可能性也是不存在的。 \n但时，在时序上，当A从B收到了更新后的mapping消息（添加了ELC TLV），但还没有从C收到更新后的mapping消息时，A确实遇到了ELC标志有分歧的情况，此时，如果A是新结点，按协议A是不能向上游通告ELC TLV的。 \n如果A是老设备，由于ELC TLV的U标志为1，虽然A结点不认识该TLV，也不会认为mapping消息出了错误，只是会忽略它的作用，同时，由于ELC TLV的F标志为1，A设备应该传递该TLV到上游，这时可能就顾不上一致不一致了。 \n此外，由于G结点上往外通告的ELC TLV只受性能参数影响，性能参数不变，ELC TLV也不会变，应该也不会发生更新ELC TLV的情况。 \n但如果G设备是其它厂家的设备，还是会有ELC TLV更新的情况的。', 'id': '764841_13'}
{'label': 'text', 'content': 'IPRAN场景负载分担方法\nMPLS熵标签和流标签负载分担\n总结：熵标签vs流标签\n| 对比项 | 熵标签 | 流标签 | 备注 |\n|:-|:-|:-|:-|\n| 标准 | rfc6790：Entropy Label | rfc6391：FAT PW | |\n| 标签位置 | 隧道层面 | PW层面 | |\n| 是否栈底 | 不一定是栈底 | 一定是栈底 | |\n| OPTION B | 无法穿透 | 可以穿透 | 注1 |\n| 适用的业务 | 非L2VPN业务 | L2VPN业务 | |\n| 标签生成机制 | 由业务层面生成 | 由业务层面生成 | |\n| 是否需要ELI | 必须添加ELI标签 | 无需ELI标签 | |\n| 标签值的本质 | 负荷分担的哈希码 | 负荷分担的哈希码 | 注2 |\n| 是否预先计算 | 预先计算 | 预先计算 | 注3 |\n| 信令的作用 | 确定远端是否有EL接收能力 | 确定本地是否必须添加FL | |\n注1：对于Martini方式的L2VPN来说，MSPW就是OPTION B跨域的实现方式；\n注2：只是这个哈希码要在［16，1048575］范围内；\n注3：预先计算就是指“业务头结点在没有发现存在负荷分担的情况下，也要计算熵标签或流标签；\n', 'id': '764841_14'}
{'label': 'text', 'content': 'IPRAN场景负载分担方法\nP设备负载分担优化\n由于LSR设备根据现有标准定义的报文封装无法区分标签后的数据封装是IP头还是PWE3，还是以太封装，只能根据标签进行HASH负载分担。 \n由于PW报文封装支持CW，PW标签后紧跟的是4个BIT的数据：数据报文CW的头4BIT是0000b，BFD/VCCV等控制报文前4BIT是0001b，L3VPN/MPLS数据封装标签后是IPV4或者IPV6的，则标签后紧跟的是0100b或者0110b，参见附件IP头定义。 \n这样就可以正常的识别出来MPLS报文封装的是L3VPN（（包含公网MPLS））报文，还是ETH或者PWE3报文，对于L3VPN报文，如果判断是L2VPN报文则可以按照五元组进行负载分担，实现对于三层业务良好的负载分担效果，对于L2VPN（VPWS、PWE3）业务就按照标签分担即可，满足IP RAN业务流承载以L3VPN为主的特点。 \n因此，在L3VPN业务为主的承载场合，对于L2VPN业务强制部署CW功能，使用5元组分担可以满足负载分担要求，部署也更加容易，相对于熵标签方式，适用性更强。', 'id': '764841_15'}
{'label': 'text', 'content': '解决拥塞典型应用场景\n业务承载模型\n根据逐流负载分担的算法原理知道，不管是采用L2+五元组负载分担还是根据标签栈进行分担，实现最佳负载均衡的前提是流量规模大。如果该链路上承载的流少，即L2和五元组变化小，或者标签栈中的标签单一，则无法获得最佳负载均衡效果，甚至无法实现流量分担。 \nIP RAN采用PW+L3VPN的业务部署方式，接入汇聚全采用LDP组网方式：', 'id': '764841_16'}
{'label': 'text', 'content': '解决拥塞典型应用场景\n接入层应用\n裂环解决方式\n裂环是一种最常用的降低环上节点数，避免环路拥塞的办法。IP RAN集采的设备，接入层一般采用GE互联的较多，在工程实施中往往有可能突破集团要求的一个环上LTE基站的数目最大6~8个的限制，在LTE规模应用的后期面临GE链路带宽不足的情况。 \n而采用GE捆绑面临由于接入环业务流数少、LSR标签分担算法限制导致的分担效果不好的问题。这种情况下，采用GE裂环是一种很好的方法。裂环一般选择环对半位置，将互联的A设备的光纤拆除，分别找两根光路，把拆除线的A各自连到另外一个B上，将一个大环改造为两个小环。 \n裂环操作除了需要具备从A到汇聚存在两条光路问题。另外，控制平面需要新规划一对IP地址将A于B互联起来。对于网管平面，如果采用即插即用方式上线的，还需要规划一套新的网管IP地址，如果采用DCN自通开通的，可以通过手动在新接入链路上将DCN启动则可。 \n控制层面为了避免裂环后，当光纤多点故障时,LSP绕环问题，可以通过两种方法解决： \n方式一：可以重新规划其中一个新环为Area N+1，为了避免修改过程降低对流量的影响，应该逐次修改链路的Area； \n方式二：如果不希望重新规划一个新的Area，则可以在汇聚B上通过路由过滤避免绕环。', 'id': '764841_17'}
{'label': 'text', 'content': '解决拥塞典型应用场景\n接入层应用\n升级10GE链路方式\n裂环方式只是一种最常用的解决接入层链路拥塞的方式，裂环首要的问题是解决光路的问题，在一些不具备条件的场景，有的情况下也可以采用升级10GE链路提升线路带宽。 \n局部升级到10GE的前提，是组网中往往只是局部线路拥塞，一般是星型网络，可以采用只升级局部线路解决拥塞问题。 \n对于环形网络，如果是环上拥塞，则环上每一条链路均可能承载全环业务流量，即每条环上链路均可能拥塞，这种情况下需要全环采用10GE组网。 \n采用10GE组网解决链路拥塞的场景，要求环上的设备具备10GE支持能力，同时，设备具备扩容10GE接口能力。升级10GE链路对光路无需求，只是要求具备10GE接口条件。 \n由于涉及到端口扩容，需要视条件考虑，这种方法的好处是不需要不需要额外的管路资源。', 'id': '764841_18'}
{'label': 'text', 'content': '解决拥塞典型应用场景\n接入层应用\nGE捆绑+LACP\n对于接入层主要的流量是LTE S1或者PW业务大客户，其中根据组网要求基站和大客户不共环。由于一个接入环上挂的基站不是很多，所涉及的流<10条，则环上的流的规模也不是很大，如果接入层采用GE负载分担，即使能根据5元组负载分担，分担效果不佳。对于接入层大客户，如果可能存在30条左右，并且每个PW内L2层头也不一样，可以分担，但是分担效果不是太好。 \n在接入层，走的均是PW，基本上一条PW对应一条流，其中采用LDP标签分发标签时，上行LSP均是到汇聚设备的Loopback地址，中间的标签均相同： \n上行依靠LSP标签无法分担，下行LSP数量少分担效果不佳 \n依靠PW标签能起到分担效果，但是一个环上对应的PW业务较少，实际上起不到分担作用。 \n基站接入层双GE由于环上流数少，无法起到分担效果，因此，接入层环形组网不建议采用链路捆绑方式提升线路带宽。 \n大客户A，平均一个A上可能承载超过20个大客户，并且每个PW上可能还有L2层头也不一样，接入层不经过P设备，负载分担效果比较好。 \nBBU组组网时，如果接入设备采用的是双上行组网，也可以采用GE捆绑，但是如果接入是环的情况下，组不建议进行捆绑提供链路带宽。 \n说明 \n大多数情况下，A类设备一般采用低端芯片，作为LSR设备时，一般只能根据LSP标签负载分担，即使经过链路上的流比较多，也无法达到分担效果，这种情况下只能考虑裂环或者升级10GE链路解决。 \n接入层部署ECMP负载方式要求较高，在接入层一般建议采用LACP捆绑。', 'id': '764841_19'}
{'label': 'text', 'content': '解决拥塞典型应用场景\n汇聚层应用\n扩容汇聚对\n汇聚上行链路拥塞，一般是由于工程上没有按照集团部署规范导致的，一对汇聚下配置的业务规模比较大。这种情况下应该优先考虑的是扩容汇聚设备，原则上新建的接入环接入到新的汇聚对上，老的汇聚对不向新汇聚上迁移，如果实在需要迁移则需要修改环上的配置，相对比较麻烦。', 'id': '764841_20'}
{'label': 'text', 'content': '解决拥塞典型应用场景\n汇聚层应用\nGE升级到10GE\n汇聚上行主要考虑10GE组网，对于采用GE链路的并没有给出具体建议，由于LTE S1业务比较大，稍微放置几个基站都有可能造成汇聚上行链路拥塞，这种情况下，应该优先考虑将上行链路从GE升级到10GE。 \n对于汇聚原先采用GE上行的组网，在LTE阶段要优先考虑将GE升级到10GE链路。', 'id': '764841_21'}
{'label': 'text', 'content': '解决拥塞典型应用场景\n汇聚层应用\n10GE/GE捆绑+LACP/IP-ECMP\n对于汇聚层上行链路，每条链路承载的流量超过50 LTE S1，50条左右的PW，可以分担，但是对于基站业务分担效果不佳。下行如果经过汇聚ER，则汇聚ER作为P设备只能根据标签栈分担，在采用每VPN每标签的情况下，LTE S1下行流量无法获得良好的分担效果。即使没有汇聚ER汇聚，完全是PE到PE终结，但是由于业务流较少分担效果同样不是太好。 \n当工程上汇聚对下超出集团要求的规模，但是超出不大，可以采用捆绑方式解决链路拥塞问题； \n对于采用GE/10GE组网，如果汇聚与核心之间没有经过汇聚ER的情况下，也可以考虑通过捆绑提升汇聚上行带宽。 \n一般汇聚层上行链路推荐采用10GE链路组网，LTE基站在一定规模内，不需要考虑10GE链路捆绑。对于采用GE链路组网的地方，可以捆绑，但是建议一个汇聚对下挂的LTE基站小于30个，并考虑后期进行升级。 \n如果汇聚B和核心之间经过核心ER，为了达到最佳负载分担效果，需要网络熵标签或者3.4节按照CW部署方式实现负载分担；如果是汇聚和核心ER直连方式，可以采用捆绑方式，但是捆绑的链路数对于10GE不建议超过2条，对于GE链路不建议超过4条。 \n捆绑可以配置LACP，也可以采用IP-ECMP方式。', 'id': '764841_22'}
{'label': 'text', 'content': '解决拥塞典型应用场景\n汇聚层应用\n10GE/GE双上行\n双上行是另外一种解决链路带宽不足的方式，如果网络中存在汇聚节点，但是设备又不支持熵标签时，通过交叉组网避免链路捆绑的缺陷，在业务带宽需求不是很大时，通过交叉组网提升带宽的组网方法。 \n双上行组网最大只能将带站能力提升1倍，采用GE双上行组网时，不建议汇聚对下带的LTE基站超过20台，采用10GE双上行不建议基站超过100 LTE基站。 \n另外，这种组网和标准组网方式有些不同，应该尽量避免这种组网方式。', 'id': '764841_23'}
{'label': 'text', 'content': '解决拥塞典型应用场景\n汇聚ER上行应用\n口字组网改为双上行组网\n采用双上行组网，上行链路带宽提升1倍，组网能力提升1倍，汇聚ER/省会ER双上行到ER或者EPC-CE。', 'id': '764841_24'}
{'label': 'text', 'content': '解决拥塞典型应用场景\n汇聚ER上行应用\n10GE捆绑+LACP/IP-ECMP\n对于核心层设备每条物理链路上，承载几百到几千条流量，采用逐流进行分担是可以实现最佳负载分担效果的。但是对于省会ER/DR或者地市DR上存在作为P设备的情况，要想获得良好的负载分担效果，需要支持熵标签。 \n对于省会ER和汇聚DR首先考虑双挂组网，在双挂不能满足带宽的情况下，如果存在拥塞情况，再考虑部署链路捆绑解决，捆绑的时候可以考虑部署LACP也可以考虑部署IP-ECMP。 \n汇聚核心具备较多的流量承载前提，但是还要考虑负载分担算法，如果是MPLS转发，为了达到最佳负载分担效果，需要网络熵标签或者3.4节按照CW部署方式实现负载分担。进行链路捆绑时，由于LTE S1业务上行流量较小，如果上行流量没有拥塞，也可以不部署熵标签。', 'id': '764841_25'}
{'label': 'text', 'content': '解决拥塞典型应用场景\n核心ER/EPC-CE UNI侧应用\n10GE捆绑+LACP/IP-ECMP\nIP RAN ER/EPC-CE UNI侧，主要是和骨干网对接，承载LTE S1流量，需要考虑10GE捆绑，UNI侧是IP转发，可以根据5元组进行分担，可以配置ECMP或者LACP。', 'id': '764841_26'}
{'label': 'table', 'content': 'IP RAN线路拥塞推荐解决方法\n| 拥塞位置 | 拓扑 | 推荐方法 | 建议 | 备注 |\n|:-|:-|:-|:-|:-|\n| 接入环 | 环形 | 裂环 | 优选 | 后期运营过程中，不具备升级10GE时推荐使用方式 |\n| 接入环 | 环形 | 环升级到10GE | 次选 | 新建网络规划时，根据业务规模事先考虑，或者硬件具备条件采用方式 |\n| A与B | 双上行/链 | 升级10GE | 优选 | 由于A设备大多无法支持熵标签，当A是多级链或者双上行时，在A与B之间将GE升级到10GE |\n| A与B | 双上行/链 | GE捆绑+LACP | 优选 | 当A是单级链或者双上行B，A下挂了几十个基站，或者几十个大客户，GE带宽不足时，可以采用GE捆绑方式 |\n| B与核心ER | 口字 | 扩容汇聚对 | 优选 | 工程规划时采用，严格控制汇聚下的基站规模，保证10GE轻载组网要求 |\n| B与核心ER | 口字 | 10GE捆绑+LACP/IP-ECMP | 优选 | 优选扩容汇聚对，必须采用时需要保证B的端口密度才能实施，不需要开启熵标签 |\n| B与核心ER | 口字 | GE升级到10GE | 优选 | LTE阶段优选，将GE升级到10GE |\n| B与核心ER | 口字 | GE捆绑+LACP/IP-ECMP | 优选 | 优选升级到10GE，不具备条件时，可以考虑GE捆绑，区县需要的长途光纤较多不建议，尽在市区考虑，采用GE捆绑的汇聚对下LTE基站规模不超过30，不需要启用熵标签 |\n| B与核心ER | 口字 | 10GE/GE双上行 | 次选 | 双上行组网最大只能将带站能力提升1倍，采用双上行组网时，当采用GE链路时不建议汇聚对下带的LTE基站超过20台，当采用10GE链路时，带LTE站不建议超过100 |\n| B与汇聚DR | 口字 | GE升级到10GE | 优选 | LTE阶段优选，将GE升级到10GE |\n| B与汇聚DR | 口字 | 扩容汇聚对 | 优选 | 工程规划时采用，严格控制汇聚下的基站规模，保证10GE轻载组网要求 |\n| B与汇聚DR | 口字 | 10GE捆绑+LACP/IP-ECMP | 次选 | 优选扩容汇聚对，必须采用捆绑方法时需要保证B的端口密度才能实施，需要开启熵标签或者使用CW方式实现P设备五元组负载分担 |\n| B与汇聚DR | 口字 | GE捆绑+LACP/IP-ECMP | 次选 | 优选升级到10GE，不具备条件时，可以考虑GE捆绑，区县需要的长途光纤较多不建议，尽在市区考虑，采用GE捆绑的汇聚对下LTE基站规模不超过30，需要启用熵标签或者使用CW方式实现P设备五元组负载分担 |\n| B与汇聚DR | 口字 | 10GE/GE双上行 | 次选 | 双上行组网最大只能将带站能力提升1倍，采用双上行组网时，当采用GE链路时不建议汇聚对下带的LTE基站超过20台，当采用10GE链路时，带LTE站不建议超过100，此种方式不需要开启熵标签，当无法启用熵标签，也无法采用升级链路，扩容汇聚对时，可以采用此种方法 |\n| DR与核心ER或ER与PC-CE或ER对之间 | 双上行或口字 | 口字改为双上行 | 优选 | 当采用的是口字组网时，优选修改为双上行组网，带宽提升1倍 |\n| DR与核心ER或ER与PC-CE或ER对之间 | 双上行或口字 | 市区扩容汇聚DR | 优选 | 当不具备部署熵标签的条件，在市区、区县可以优先考虑扩容DR方式组网，但是这种情况下会增加设备投资，区县不推荐这种方式 |\n| DR与核心ER或ER与PC-CE或ER对之间 | 双上行或口字 | 10GE捆绑+LACP/IP-ECMP | 优选 | 当设备支持熵标签或者使用CW方式实现P设备五元组负载分担的时候，可以考虑这种方式（根据LTE基站的特点，如果上行流没有拥塞，只是下行拥塞，不支持熵标签也可） |\n| DR与核心ER或ER与PC-CE或ER对之间 | 双上行或口字 | 升级40GE/100GE | 次选 | 如果光路资源紧张，不具备捆绑条件，也不具备开启熵标签条件，可以考虑升级端口，预计2~3年随着成本减低，可以采用这种方式，优先在市区使用 |\n| ER/EPC-CE UNI侧链路 | U型 | 10GE捆绑+LACP/IP-ECMP | 优选 | 目前在IP RAN中推荐部署方式，注意ER对内部互联也需要部署捆绑 |\n| ER/EPC-CE UNI侧链路 | U型 | 升级40GE/100GE | 次选 | 当工程具备条件，40GE/100GE成本降低后，可以采用40GE/100GE对接 |\n', 'id': '764841_27'}
{'label': 'text', 'content': '参考资料\nMPLS报文封装格式\nPWE3报文\n', 'id': '764841_28'}
{'label': 'table', 'content': '参考资料\nMPLS报文封装格式\n| L2头 | Vlan-tag | 8847 | LSP标签 | PWE3标签 | CW（可选） | PDU | CRC |\n|-|-|-|-|-|-|-|-|\n', 'id': '764841_29'}
{'label': 'text', 'content': '参考资料\nMPLS报文封装格式\nL2VPN报文\n', 'id': '764841_30'}
{'label': 'table', 'content': '参考资料\nMPLS报文封装格式\n| L2头 | Vlan-tag | 8847 | LSP标签 | PWE3标签 | L2头 | Vlan-tag | IP头 | PDU | CRC |\n|-|-|-|-|-|-|-|-|-|-|\n', 'id': '764841_31'}
{'label': 'text', 'content': '参考资料\nMPLS报文封装格式\nL3VPN报文\n', 'id': '764841_32'}
{'label': 'table', 'content': '参考资料\nMPLS报文封装格式\n| L2头 | Vlan-tag | 8847 | LSP标签 | VPN标签 | IP 头 | PDU | CRC |\n|-|-|-|-|-|-|-|-|\n', 'id': '764841_33'}
{'label': 'text', 'content': '参考资料\nMPLS报文封装格式\nnative-IP报文\n', 'id': '764841_34'}
{'label': 'table', 'content': '参考资料\nMPLS报文封装格式\n| L2头 | Vlan-tag | 0800 | IPv4/v6 | PDU | CRC |\n|-|-|-|-|-|-|\n', 'id': '764841_35'}
{'label': 'text', 'content': '参考资料\nMPLS报文封装格式\nCW使用方式要求\n数据报文中PW-CW格式\nBFD检测报文中PW-ACH的格式\n', 'id': '764841_36'}
{'label': 'text', 'content': '参考资料\nIP头格式\nRFC791定义IPV4头\nVersion: 4\nIHL: IP头长度，按照4个字节对齐计算，如果不包含选项子的，IHL=5。\nRFC2460定义IPV6头\nVersion: 6\n缩略语\n', 'id': '764841_37'}
{'label': 'table', 'content': '参考资料\nIP头格式\n| | 缩略语 |\n|:-|:-|\n| MPLS | Multi-Protocol Label Switching |\n| VPN | Virtual Private Network |\n| SG | Smart Group |\n| LDP | Label Distribution Protocol |\n| BGP | Border Gateway Protocol |\n| ECMP | Equal-Cost MultiPath |\n| RSVP | Resource Reservation Protocol |\n| MSPW | Multi-Segment Pseudo Wire |\n| LACP | Link Aggregation Control Protocol |\n| ER | Edge Router |\n| ASBR | Autonomous System Boundary Router |\n| LER | Label Edge Router |\n| LSR | Label Switch Router |\n| LSP | Label Switch Path |\n| CE | Customer Edge |\n| PE | Provider Edge |\n| VPLS | Virtual Private LAN Service |\n| EL | Entropy Label |\n| LAG | Link Aggregation Group |\n| BFD | Bidirectional Forwarding Detection |\n| PW | Pseudo Wire |\n| CW | Control Word |\n| PWE3 | Pseudo-Wire Emulation Edge to Edge |\n| SPE | SuperStratum PE |\n| TPE | Terminated PE |\n| UPE | Underlayer PE |\n', 'id': '764841_38'}
{'label': 'text', 'content': '参考资料\nIP头格式\n术语\n', 'id': '764841_39'}
{'label': 'table', 'content': '参考资料\nIP头格式\n| | 缩略语 | 说明 |\n|:-|:-|:-|\n| AL | Alert Label | 告警标签，即保留标签之1标签 |\n| BoS | Bottom of Stack | MPLS标签头的一个bit，标识是否标签栈底 |\n| EL | Entropy Label | 熵标签 |\n| ELI | Entropy Label Indicator | 熵标签指示符，即保留标签之7标签 |\n| ELC | Entropy Label Capability | 熵标签能力 |\n| ECMP | Equal Cost Multi-Path | 等价多路径 |\n| FEC | Forwarding Equivalence Class | 转发等价类 |\n| LAG | Link Aggregation Group | 链路聚合组 |\n| LER | Label Edge Router | 标签边缘路由器 |\n| LSR | Label Swapping Router | 标签交换路由器，即P设备 |\n| PW | Pseudowire | 伪线 |\n| TC | Traffic Class | MPLS标签头的一个字段，原名EXP |\n| 地址：深圳市高新技术产业园科技南路中兴通讯大厦 |\n|:-|\n| 邮政编码：518057 |\n| 电话：+86-755-26770000 传真：+86-755-26771999 |\n| 网站：www.zte.com.cn |\n', 'id': '764841_40'}
