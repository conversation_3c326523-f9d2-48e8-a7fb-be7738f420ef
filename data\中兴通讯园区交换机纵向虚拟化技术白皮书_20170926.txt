{'label': 'document', 'content': '中兴通讯园区交换机纵向虚拟化技术白皮书_20170926', 'id': '848603'}
{'label': 'table', 'content': '| 版本 | 日期 | 作者 | 审核者 | 备注 |\n|:-|:-|:-|:-|:-|\n| V1.0 | 2017/8/24 | 袁智勇10042619 | 夏迎春10042626 | |\n| | | | | |\n| | | | | |\n| | | | | |\n', 'id': '848603_1'}
{'label': 'text', 'content': '概述\n背景\n在园区网络中，尤其是大、中型园区网络，接入设备数量繁多且位置分布范围广，传统的做法是对接入设备逐台进行管理与配置，较为繁琐，配置与维护工作量大。此外，对于接入设备，其配置的业务功能相对简单，且配置也比较相似，故而大量的都是重复性工作。因此，如何提高网络的运维效率，将人力与运维成本极大的释放出来，是新一代园区网络的诉求之一。 \n为此，中兴公司之前提出了堆叠技术（Virtual Switch Cluster 2.0，简称VSC2.0），即在不改变网络物理拓扑的情况下，将网络中同一层次的多台设备横向整合，虚拟化成一台设备，从逻辑上简化了网络架构，进而简化了网络的运维成本。但是，该技术只是在横向维度上将同层次同位置的节点进行了优化，对于大规模多层次广分布的网络来说，依旧存在大量的管理点，运维工作依旧繁杂，所以需要从另一个维度——纵向，对园区网络进行进一步优化。 \n中兴公司便基于上述考虑，推出新一代纵向虚拟化技术（VSC3.0），在纵向维度上对网络设备进行异构扩展，将不同层次的设备（汇聚层设备parent和其下的接入层设备client）虚拟化成一台逻辑设备，由汇聚层设备统一管理和配置接入层设备，从而减少网络管理的逻辑节点，实现园区网络集中控制和简化管理的目的。 \n下图为纵向虚拟化技术组网应用示意图：', 'id': '848603_2'}
{'label': 'text', 'content': '概述\n特点\n相对于传统组网，VSC3.0具有以下特点： \n- 集中管理 \n- VSC3.0技术将汇聚层设备（称为控制设备Parent）和接入层设备（称为接入设备Client）虚拟化为一台逻辑设备，用户在Parent设备上便可对虚拟化系统内的所有设备进行统一化的管理。 \n- 简化维护 \n- 在Parent设备上对所有设备统一维护版本和补丁升级，统一查看端口状态、CPU利用率、内存利用率等信息 \n- 网络扩展性强 \n- 由于Client设备就像Parent设备的一个远程业务板，故增加client设备，就相当于扩展了Parent设备的端口，提高了端口密度。 \n- 高可靠性 \n- Client设备与Parent设备间支持链路捆绑，实现链路的冗余备份。同时VSC3.0系统中设备亦支持横向堆叠VSC2.0，故实现了节点的冗余保护。 \n- 低成本 \n- 由于Parent设备负责协议运算、表项生成与下发，Client设备仅负责数据转发，所以Client设备的性能、规格要求不高，这样将相对低端的设备款型和高端设备虚拟化在一起，从而扩展高端设备的端口密度和带宽，节省了投资成本。', 'id': '848603_3'}
{'label': 'text', 'content': '技术原理\n基本概念\nVSC3.0基本概念示意如图所示： \n涉及概念有： \n角色 \n父交换机（Parent 设备）：作为系统中的控制设备，负责整个系统的配置、控制与管理。 \n叶子交换机（Client 设备or Leaf设备）：作为系统中的接入设备，相当于父设备的远程接入板，负责将数据流转送到父设备处理。 \n端口 \n级联接口：Parent设备上与Client设备的互联接口，用于连接下挂Client设备的上行流量接口。可以是聚合接口，保证互联带宽和链路可靠性。 \n上行接口：Client设备上用于连接Parent设备的接口。其与对端级联接口相对应。同样可以是聚合接口。 \n扩展接口：Client设备上除了级联接口和上行接口之外的接口，用以连接用户终端。 \n运行模式 \n普通模式：即正常模式，该模式下设备之间为传统组网形式，不能与其他设备形成纵向虚拟化系统。 \n虚拟化模式：该模式设备可以通过级联接口和上行接口进行交互，形成VSC3.0系统。 \n虚拟机框号 \n用在Parent设备上，标识并管理系统内的Client设备。一台Client设备对应一个虚拟机框号。', 'id': '848603_4'}
{'label': 'text', 'content': '技术原理\n拓扑连接\nVSC3.0系统拓扑有以下特点： \n组网结构为树形结构。 \n系统中Parent与Client之间，可以配置聚合链路。同样，用户接入也可以以聚合链路方式接入，进而保证带宽和链路可靠性。 \n支持一层Parent设备和一层Client设备，每层设备均可以接入用户。 \nParent设备可以是横向堆叠（VSC2.0）系统，Client设备也可以是VSC2.0系统，具体选择可以根据组网规模、业务需求、可靠性等方面进行考量。', 'id': '848603_5'}
{'label': 'text', 'content': '技术原理\n系统建立\n邻居发现\nParent设备采用LLDP协议来发现Client设备。即，当Parent设备上完成功能使能、配置级联端口等操作后，设备处于收集侦听状态， 当Parent设备与Client设备连接的物理链路状态为 UP 后，Parent设备和 Client设备互相发送 LLDP 报文发现邻居设备。', 'id': '848603_6'}
{'label': 'text', 'content': '技术原理\n系统建立\n建立连接\nParent设备与Client设备通过LLDP建立邻居关系后，分别向对方发送检测协议报文，如果双方在有效时间内接收到对端回复报文，则Parent设备和Client 之间的连接建立成功。', 'id': '848603_7'}
{'label': 'text', 'content': '技术原理\n系统建立\n注册设备\nClient设备操作有： \n1）告知Parent设备，该设备的扩展接口信息 \n2）接收Parent设备对上行接口、扩展接口的配置 \nParent设备操作有： \n为Client设备分配唯一ID，并将上报的端口重新映射为新的逻辑接口 \n将Client的端口状态、端口速率等接口信息，同步到Parent设备所创建的虚端口上', 'id': '848603_8'}
{'label': 'text', 'content': '技术原理\n系统建立\n状态维护\nParent设备和 Client设备之间定时相互发送链路检测报文来探测链路是否正常工作。如果Parent和Client采用动态聚合口连接， 则该链路上也会通过LACP协议维来护聚合口状态。当接口down或者检测报文长时间超时未收到回应，则认为Client设备离线，Parent设备会删除虚拟线卡及其扩展端口资源等操作。', 'id': '848603_9'}
{'label': 'text', 'content': '技术原理\n报文转发原理\nVSC3.0系统支持集中式的报文转发方式。Client设备将收到的所有报文先转发给Parent设备，在Parent设备上查表进行转发决策，然后将报文从正确的出接口转发出去。这种方式的好处是便于用户对流量集中控制与精细化管理。 \n以单播为例，其报文转发流程为： \nClient A设备从扩展接口上收到User a向User c的数据报文，Client A会将该报文的出接口重定向到设备的上行接口。同时，上行接口处会将报文封装后再发送给Parent。 \nParent设备从级联端口上收到从Client A的封装报文后，进行解封装，查表，判定要通过Client B发送给User c。然后将信息再次封装入报文，从对应Client B的级联端口发出去。 \nClient B设备从其上行接口收到报文后，解封装，根据封装的信息，确定出接口，最后将无封装的报文发送给User C。完成User a到User c的通信。', 'id': '848603_10'}
{'label': 'text', 'content': '技术原理\n设备加入与离开\n分两种情况： \nClient设备的加入与离开。 \n对于VSC3.0系统来说，Client设备的加入与离开，相当于某个接口单板的插入或拔出。所以，按照前文所述的系统建立流程或者反向清除处理即可；不影响已建好的系统的运行。 \nParent设备的分裂与合并。 \n该情况特指Parent为VSC2.0的情况。此时如果堆叠的Parent分裂，可通过VSC2.0的MMD（Multiple Master Detect，多主检测）检测机制与退避机制，保证VSC2.0系统中仅有一个Master在网，进而保证VSC3.0系统中仅有一个Parent存在。Client设备则通过未退避的Parent继续进行数据报文的转发。', 'id': '848603_11'}
{'label': 'text', 'content': '技术原理\n管理与维护\n配置管理\nVSC3.0系统根据配置与加载位置的不同，可分为三种情况： \nClient自动配置，Client加载。包括虚拟化功能使能、LLDP协议开启、LACP协议开启等，通过零部署功能自动完成配置。 \nParent上配置，Client加载。由于VSC3.0系统的控制设备在Parent上，Client设备相当于虚拟的一块业务板卡，故，一般业务配置需要登陆Parent来进行配置。包括VLAN、PORT、L2、L3、QOS等等，通过通道传递到Client设备，Client解析配置数据后下发到本地硬件。 \nParent上配置，Parent加载。与正常的非虚拟化一样，如ACL、QOS、L2、L3等特性配置。 \nVSC3.0系统的配置保存与恢复，可分为两类： \nClient设备上保存零部署生成的配置，供下次启动使用。 \nParent设备上保存系统的当前配置，其中包括Client设备对应的配置。设备重启时，根据配置命令分类，或者通过通道配置到Client，或者进行本地配置恢复。', 'id': '848603_12'}
{'label': 'text', 'content': '技术原理\n管理与维护\n版本管理\nClient设备的版本由Parent设备统一管理。 \n根据交互的LLDP所携带信息，首先判断版本的一致性。若Client设备的本地版本和Parent设备上保存的Image版本不一致时，则Client设备需要从Parent设备上取新版本并重新加载。 \nClient设备可以通过管理VLAN的三层IP接口，以FTP方式获取最新版本。 \nClient设备从Parent设备上获取版本后，自动保存到本地FLASH，然后自动重启。', 'id': '848603_13'}
{'label': 'text', 'content': '技术原理\n管理与维护\n零部署\nVSC3.0系统中，零部署的核心是Client设备的即插即用。即Client脱离对OAM（CLI、SNMP、NETCONF等）的直接依赖，网管不需要直接去配置Client设备，Client设备就可实现自启动和业务正常运行的目的，就像Parent上虚拟插入一块业务板，即插即用。只有在Parent设备上才有手工配置。 \n自动化零部署包括如下几个方面： \nClient设备能自动开启LLDP功能，从而完成对Parent的发现、上行链路的发现，以及确认自己的Client身份。 \nClient设备能自动开启LACP功能，在上行链路和Parent之间自动形成LAG连接。 \nClient设备能自动与Parent设备交互，形成虚拟端口资源。 \nClient设备发现版本不一致时，能自动从Parent下载版本并保存重启。 \nClient设备能自动接收Parent配置并加载。', 'id': '848603_14'}
{'label': 'text', 'content': '技术原理\n管理与维护\n显示与维护\n在Parent 设备上可以统一查询 Client设备的状态信息，如：CPU利用率、内存占用率、设备状态、端口状态等。 \nClient设备能够主动向Parent设备上报关键的维护信息，如：端口状态变化、错误告警等， 便于在Parent设备统一查看。', 'id': '848603_15'}
{'label': 'text', 'content': '应用场景\nVSC3.0将园区网中接入设备（Client）都集中在汇聚设备（Parent）上进行管理，便于用户集中部署，简化网络，使得管理更加简单。 \n如上图所示，某办公楼A网络，是园区中常见两层组网，即，整栋楼宇的出口为汇聚设备，做网关；办公楼每层楼配置接入设备，业务配置简单，为楼内用户提供网络接入。 \n此时，就可以将整栋楼宇环境作为一个VSC3.0系统。 \n汇聚层配置为ZXR10 8900E设备，作为Parent设备。 \n接入层配置为ZXR10 5950设备，作为Client设备。 \n为了提高可靠性，汇聚层可采用8900E设备堆叠（VSC2.0）技术，实现设备节点的备份。 \n如果大型园区存在多栋办公楼，则可将每栋办公楼均配置为一个VSC3.0，做为一个逻辑节点，与其他楼宇节点再综合组网，整网逻辑拓扑变得清晰简单，易于管理。', 'id': '848603_16'}
{'label': 'text', 'content': '结束语\nVSC3.0技术，是园区网络技术方案的不断延伸与演进，符合用户对简化网络管理的诉求，减轻对繁多但业务简单的接入设备的维护，统一地将控制面、管理面、转发面集中在汇聚设备上，对流量与业务进行集中且自动化管理， 使得网络管理更加便捷，运维更加高效。', 'id': '848603_17'}
{'label': 'table', 'content': '附录\n| 缩略语 | 英文全称 | 中文说明 |\n|:-|:-|:-|\n| VSC2.0 | Virtual Switch Cluster 2.0 | 虚拟交换机集群，堆叠 |\n| VSC3.0 | Virtual Switch Cluster 3.0 | 纵向虚拟化 |\n| LLDP | Link Layer Discovery Protocol | 链路层发现协议 |\n| TLV | Type/Length/Value | 类型/长度/值 |\n| LACP | Link Aggregation Control Protocol | 链路聚合控制协议 |\n| MMD | Multiple Master Detect | 多主检测 |\n| OAM | Operation, Administration and Maintenance | 操作管理维护 |\n| SNMP | Simple Network Management Protocol | 简单网络管理协议 |\n| VLAN | Virtual Local Area Network | 虚拟局域网 |\n| QOS | Quality of Service | 服务质量 |\n| FTP | File Transfer Protocol | 文件传送协议 |\n', 'id': '848603_18'}
