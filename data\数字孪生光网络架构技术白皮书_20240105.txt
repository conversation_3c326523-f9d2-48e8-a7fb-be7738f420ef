{'label': 'document', 'content': '数字孪生光网络架构技术白皮书_20240105', 'id': '1143854'}
{'label': 'text', 'content': '数字孪生光网络架构\n技术白皮书\n数字孪生光网络架构技术白皮书\n| 版本 | 日期 | 作者 | 审核者 | 备注 |\n|:-|:-|:-|:-|:-|\n| V1.0 | 2023/01/05 | 方瑜、王东、杨三威、王其磊、闫宝罗 | | 第一版 |\n| | | | | |\n| | | | | |\n| | | | | |\n© 2024 ZTE Corporation. All rights reserved.\n2024版权所有 中兴通讯股份有限公司 保留所有权利\n版权声明：\n本文档著作权由中兴通讯股份有限公司享有。文中涉及中兴通讯股份有限公司的专有信息，未经中兴通讯股份有限公司书面许可，任何单位和个人不得使用和泄漏该文档以及该文档包含的任何图片、表格、数据及其他信息。\n本文档中的信息随着中兴通讯股份有限公司产品和技术的进步将不断更新，中兴通讯股份有限公司不再通知此类信息的更新。\n目录\n1 智能光网络挑战\t4\n1.1 光网络需要智能化\t4\n1.2 数字孪生助力智能化\t4\n2 数字孪生综述\t5\n2.1 核心要素\t5\n2.2 关键技术\t6\n2.3 应用场景\t7\n2.4 融合系统\t7\n3 数字孪生关键技术\t7\n3.1 映射：实与虚的关联\t7\n3.2 模型：能力的来源\t9\n3.3 数据：系统的基石\t14\n3.4 交互：信息的桥梁\t20\n3.5 AI：智能提升的机遇\t21\n4 数字孪生技术应用\t23\n4.1 应用场景\t23\n4.2 层次结构\t23\n4.3 组合编排\t25\n4.4 实验效果\t25\n5 数字孪生管控融合系统\t27\n5.1 关键需求\t27\n5.2 业务架构\t28\n5.3 功能流程\t29\n6 总结与展望\t30\n7 参考文献\t30\n8 缩略语\t31\n', 'id': '1143854_1'}
{'label': 'text', 'content': '智能光网络挑战\n光网络需要智能化\n光网络属于基础通信设施的承载网络，具有大容量、高带宽、长距离、低时延和确定性等特征优势。最近十几年间，光器件、光模块和光线路等技术的快速发展，推动了光网络更大规模应用，也给光网络全生命周期运营维护带来巨大挑战，如光网络开通扩容效率低、光网络性能优化周期长、光网络故障排查成本高等。 \n光网络全生命周期运营维护难度大，主要原因有：一、光网络本质是模拟信号网络，各种参数关联关系不唯一和非线性，专业门槛高；二、光网络系统优化很大程度上依赖业务受损后的被动响应；三、光网络承载信号分光电两层，模型层次多，差异很大的光电两层进一步加剧运营维护难度。 \n面对光网络技术快速发展、光网络规模日趋庞大的情况，当前业界共同大力推进的自智网络应该是未来发展的必由之路，将光网络技术与智能化技术紧密融合，推动光网络沿着“超高速率、超大容量、超长距离、超级智能”趋势演进，最终构建一张在“规划、建设、维护、优化、运营”全生命周期内具备自优、自愈、自智能力的光网络。 \n自智光网络的关键包括对光网络各项参数进行精准感知和自主分析、对各项操作指令进行严格验证、对光网络全生命周期的各项状态进行监控和预测，但这些功能在物理网络实体很难实现，迫切需要构建起数字孪生网络，通过模拟物理实体的各种工作机理，精准仿真各项参数指标，实现光网络的网络拓扑、运行状态、动作流程和处理结果全方位可视可管可控，最后达到自动分析决策、自主配置执行的目标。', 'id': '1143854_2'}
{'label': 'text', 'content': '智能光网络挑战\n数字孪生助力智能化\n数字孪生技术是一种基于先进计算算法和高效数据处理的技术，通过建立与实体物理系统高度相似的虚拟数字模型，实现对物理系统的全生命周期仿真、分析、优化和管理的技术，具有实时同步、忠实映射、高度保真特性，能够实现物理世界与虚拟世界交互与融合的技术手段。 \n数字孪生技术尚未在通信网络领域大规模应用，究其原因最主要有两点：一是通信网络的实时数据量巨大，叠加孪生系统需要的同步数据，会给系统带来巨大的额外处理压力；二是通信网络的模型精度低，如无线通信网络和光通信网络的模型参数众多，部分参数难以获取，基于经验估算降低了模型精度，模型建立成本高收益小。 \n近几年，随着自智网络建设的大力推进，数字孪生技术在通信网络的应用也步入快车道。 \n基于数字孪生技术的自智光网络将带来低成本试错、加速网络服务创新迭代的全新优势，让系统具备更强大的自主决策能力；同时进一步减少人工干预在运营运维流程中的比重，全面提升网络的可扩展性、资源利用效率和客户响应速度，将有效支撑自智光网络向更高等级演进升级。 \n伴随着人工智能（AI，Artificial Intelligence）、大数据、大模型技术的快速成熟和普及，以及算力的快速提升，数字孪生有望迎来窗口性机遇，在光网络中真正落地，并创造实际价值。', 'id': '1143854_3'}
{'label': 'text', 'content': '数字孪生综述\n核心要素\nITU-T Y.3090 数字孪生网络-需求和架构定义了数字孪生网络（DTN，Digital Twin Network）核心要素，如图 2-1所示。 \n其中，“模型、映射、数据、交互”属于数字孪生实现的关键技术，“分析、诊断、仿真、控制”属于数字孪生具体功能应用。“网络孪生体”作为核心，体现了数字孪生最重要的技术特征。 \n数字孪生网络的核心要素可以在“实现”与“应用”两个维度得到对应： \n“实现”基于“模型、映射、数据、交互”等关键技术，通过模型将物理网络数字化，获得数据构造数字网络孪生体，形成物理网络与数字网络孪生体的映射和交互。 \n“应用”基于网络孪生体的“分析、诊断、仿真、控制”等功能应用，解决实际物理光网络运维中存在问题。 \n最后，基于现有管控应用系统扩展数字孪生功能，构建数字孪生管控融合系统，做到数字孪生“实现与应用”的融合。', 'id': '1143854_4'}
{'label': 'text', 'content': '数字孪生综述\n关键技术\n映射：“物理网络与虚拟孪生网络的实时交互映射是DTN虚拟孪生网络与网络仿真系统不同的最典型特征”[1]。 “映射”不仅支持物理光网络中物理组件和支持的真实功能，与数字孪生网络中的虚拟组件和关联的仿真功能进行一一对应，而且支持物理网络和孪生网络之间的实时信息交互。 \n模型：“模型是数字孪生网络的能力来源”[1]。“模型”使数字孪生网络可以有效地模拟实际网络的复杂物理行为，并在此基础上提供深入的分析和优化策略，满足各种网络应用需求。光网络的光器件仿真模型和光传输性能模型，是实现光网络数字孪生最核心的技术，需要不断研究改进，提升效率与精度。 \n数据：“数据是构造数字孪生系统的基石”[1]。“数据”经过采集、上报、加工、存储等处理，及时、准确地将物理网络的底层信息同步到数字孪生网络，作为系统数据统一的真实数据来源。 \n交互：“标准化接口是有效保证DTN系统兼容性和可扩展性的关键角色”[1]。“交互”定义的接口是数字孪生系统与周边系统进行信息交互的桥梁，南向接口连接物理网络和数字孪生网络，北向接口在数字孪生网络和网络应用之间交换信息。依托接口标准化，满足不同系统间互通的要求。 \n除传统数字孪生核心要素之外，新兴技术对于数字孪生的影响不可忽视。AI技术，尤其是大语言模型模型的广泛应用，为网络智能化打开了新的大门。各种新技术要素与数字孪生的结合，不断为数字孪生赋予新的内涵，不断拓展数字孪生的功能边界。 \n本文第3节“数字孪生关键技术”针对具体技术点进行介绍。', 'id': '1143854_5'}
{'label': 'text', 'content': '数字孪生综述\n应用场景\n数字孪生的应用场景，贯穿网络智能运维的全生命周期，波及智能运维各种功能，有广泛的应用前景。 \n数字孪生系统可以划分为多个层次，通过不同层次间的配合，逐层将用户应用场景，拆分为基础服务，转换为内部的业务特性，依托数字孪生体的行为模型实现仿真计算，完成功能场景的逻辑闭环。 \n基于层次结构，并对每个层次进行维度细分，通过层次维度的编排组合，满足各种应用要求。 \n根据已有模型仿真算法的结果进行评估，可以预见数字孪生在特定应用场景下改善的效果。 \n本文第4节“数字孪生技术应用”针对应用场景进行阐述。', 'id': '1143854_6'}
{'label': 'text', 'content': '数字孪生综述\n融合系统\n数字孪生网络支持新功能特性的同时，还需要考虑兼顾用户对现有管控系统的使用和操作习惯，因此有必要将数字孪生与管控系统进行融合。 \n为了实现融合系统，需要分解与细化关键需求，整合管控与数字孪生系统的业务处理流程，形成统一架构，实现多种业务流程兼容。 \n详见第5节“数字孪生管控融合系统”。', 'id': '1143854_7'}
{'label': 'text', 'content': '数字孪生关键技术\n映射：实与虚的关联\n映射需要将物理网络的各个实体，转换对应到数字孪生网络模型。通过统一的数字孪生光层底座，实时模拟光网络的运行状态，形成数字孪生光网络同物理光网络的精准映射。图 3-1中描述了多波段光传输系统不同的组成单元映射到相应模型的过程。发射机、传输介质光纤、可重构光分插复用器（ROADM，Reconfigurable Optical Add-Drop Multiplexer）、光放大器（OA，Optical Amplifier）、接收机等光网络的典型组成单元，分别映射到底层相干光模块、特定型号光纤、波长选择开关(WSS，Wavelength Selective Switch)、掺杂光纤放大器（EDFA/TDFA/PDFA）等模型。 \n端到端光传输性能模型，反映了端到端光信号传输整体效果。由系统线性与非线性损伤来组成，内容参见图 3-2中的定义。例如：光转发板（OTU，Optical Transport Unit）的光模块收发模型中包括光信噪比（OSNR，Optical Signal Noise Ratio）容限、FEC纠错能力、线性与非线性容限等性能指标、WSS级联产生的滤波损伤、通道串扰代价等、光放大增益与噪声性能对传输后功率与OSNR平坦度的影响、光纤信道效应如波长相关损耗、拉曼附加损耗、非线性效应等。相关指标需要在数字孪生网络模型中呈现并与实际物理网络参数的完成对应。', 'id': '1143854_8'}
{'label': 'text', 'content': '数字孪生关键技术\n模型：能力的来源\n基于数字孪生技术的光网络模型，提供了模拟物理光网络运行状态和行为的能力，为网络的智能控制和优化提供支持，提升网络的性能和可靠性。本节列举了一些光系统典型模型，包括具体光器件仿真模型和光传输性能模型，介绍了一些提升模型精度的思路与方法。', 'id': '1143854_9'}
{'label': 'text', 'content': '数字孪生关键技术\n模型：能力的来源\n光纤模型\n光纤作为光传输中信息的介质管道，传输效果受多种复杂因素的影响，光纤模型典型指标包括：波长相关损耗（WDL，Wavelength-Dependent Loss）、色散（CD，Chromatic Dispersion）、非线性效应等，典型的相关模型为光纤损耗模型、光纤色散模型和光纤受激拉曼散射（SRS，Stimulated Raman Scattering）模型。', 'id': '1143854_10'}
{'label': 'text', 'content': '数字孪生关键技术\n模型：能力的来源\n光纤模型\n光纤损耗模型\n光纤损耗是指光信号经光纤传输后，由于吸收、散射等原因引起光功率的损失，可以归纳为光纤本身固有的本征损耗、制造工艺缺陷引入的制造损耗和实际应用环境带来的附加损耗。G.652光纤损耗谱如图 3-3。普通标准单模G.652光纤在1550nm的损耗系数约为0.2dB/km，即用光纤每传输1km，光功率下降0.2dB。 \n实际建模时，按是否和波长相关，将总损耗Loss(λ)分为了两个部分，如下： \n其中α为光纤损耗系数，表示波长无关的平均衰减，主要由光纤类型决定，同时也需要考虑制造和附件损耗的影响，通常要根据上下游测量的实际功率进行系数校准。WDL(λ)为相对波长损耗系数，与具体波长相关，初始值可以根据经验确定，再利用对于实际通道波长功率的测量来校准修正。用系数乘以光纤长度L计算实际的总衰耗值。', 'id': '1143854_11'}
{'label': 'text', 'content': '数字孪生关键技术\n模型：能力的来源\n光纤模型\n光纤色散模型\n色散是光纤的一个重要参数，不同频率和不同模式的光信号在光纤中的传输速度不同，导致信号在传输一段距离后发生展宽，引起传输信号畸变，使通信质量下降，从而限制通信容量和通信距离。 \n在信号接收处理过程中，相干光传输系统目前已经有比较成熟的电域色散补偿技术，但仍需要在构建模型时考虑与色散关联的损伤。单波速率提升场景，速率提升需要占用更宽的频谱，带来波段扩展需求，系统波段扩展后，要考虑不同波段色散上的差异，例如L波段长波与C波段短波色散相关的损伤代价不同。而且色散特性和业务速率相关，对于速率提升后的高速信号影响更大。一些特殊的非线性效应，如均衡增强相位噪声（EEPN，Equalization Enhanced Phase Noise）在高波特率、大色散场景下引入的损伤明显，需要结合色散特性、激光器线宽、光模块色散补偿机制统一进行评估。', 'id': '1143854_12'}
{'label': 'text', 'content': '数字孪生关键技术\n模型：能力的来源\n光纤模型\n光纤SRS模型\n光纤中的SRS效应，是入射光子被光纤中的分子散射而生成新的低频光子，同时分子跃迁到高一级的振动态而产生的。这个过程会导致光信号的能量转移到更低的频率，表现为信号功率从短波长转移至长波长。传统C波段业务占用的频谱宽度小，一般可以忽略SRS导致的功率转移，而在扩展C+L波段系统中，由于占用了更宽的频谱（12THz），导致光功率从短波长向长波长转移量变得非常显著，且SRS转移具有累积效应，经历多个跨段的传输后，短波长处功率转移明显，不能忽略。 \nSRS效应是C+L系统中最具有代表性的损伤，是评估C+L系统中其他损伤的基础，也是影响系统性能的关键因素。图 3-4所展示了SRS模型校准的效果，针对拉曼转移谱，通过对于预设SRS参数的调整，逐步迭代逼近实际SRS参数，完成SRS参数的校准。以某单跨段实测为例，相较于无SRS校准，进行SRS校准可以将出纤功率谱精度提升约1dB。有效提升光纤输出功率谱的预估/预测，进一步提升级联后端到端光系统性能评估精度。 \nSRS转移谱\tb) 单跨段SRS校准\tc) 光纤SRS模型精度', 'id': '1143854_13'}
{'label': 'text', 'content': '数字孪生关键技术\n模型：能力的来源\nWSS滤波模型\n光信号在链路中传输时，光谱边缘会受到滤波器的滤波作用而发生衰减，导致整个光谱边缘形状发生变化。当链路中信号穿通多级滤波器，滤波器级联带宽过小时，穿通滤波器引起的光性能损失会变得十分明显，产生相应的OSNR代价。由滤波器引入的光谱变形不仅造成码元间串扰，还会影响收端数字信号处理（DSP，Digital Signal Processing）芯片中的时钟恢复算法，在相同OSNR水平下，当滤波带宽较小时，时钟恢复算法无法有效地提取信号中的时钟分量，准确补偿采样时钟的定时误差，从而造成后续算法均衡性能下降，导致光模块性能劣化。 \nWSS滤波的OSNR代价，需要结合WSS本身特性、引入噪声分布情况以及给定模块、码型信息等因素，仿真综合获取。如图 3-5所示。 \n滤波器统计特性模型，需要体现级联情况下滤波器对于信号的影响，先建立单级WSS模型，考虑同一型号滤波器个体之间由于制造工艺可能产生参数波动，生成单级概率模型后，针对单级概率模型进行叠加计算，可以获得不同级数级联滤波概率模型。 \n噪声分布情况，代表了噪声分布在滤波场景下对于信号质量的影响，根据模块光谱特征、链路衰减与增益、OA噪声系数、WSS光谱形状等链路参数计算关联指标。 \n不同相干光模块、不同码型对滤波损伤容忍能力都有所区别，基于噪声分布和滤波模型的仿真平台，可以在不同环境条件下，统一评估滤波代价指标。', 'id': '1143854_14'}
{'label': 'text', 'content': '数字孪生关键技术\n模型：能力的来源\n光放大器模型\n光放大器主要包括增益光纤放大器，例如EDFA/TDFA/PDFA等，线路中还还包括光纤拉曼放大器（FRA，Fiber Raman Amplifiers）、半导体光放大器（SOA，Semiconductor Amplifiers）、光纤参量放大器（FOPA，Fiber-optic parametric amplifiers）等，需要根据其原理分别建模，获取高精度的通道功率/增益估算。以常用的EDFA为例，模型旨在基于设定增益、输入配置下，预测/预估光放大器的输出功率谱，在模型类型方面主要包括解析模型、AI黑盒模型以及基于数据驱动的灰盒孪生模型，后者由于具有高泛化能力、高精度特点逐渐被采纳应用。 \n按照波道是否满配可分为满波与少波两种应用场景，在少波场景光放大器往往存在频谱烧孔（SHB，Spectral Hole Burning）效应，对模型的精度挑战更大。按照光放大器的工作模式可分为自动增益控制（AGC，Automatic Gain Control）、恒定电流控制(ACC，Automatic Currnet Control)，上述两种不同控制逻辑下，SHB的影响不同，需要针对性修正。在任意工作模式下，少波场景下的增益谱可表示为： \n即实际增益需在定标的满波增益谱的基础上叠加SHB效应对增益谱的影响，进一步考虑不同控制模式下增益谱的变化得出。', 'id': '1143854_15'}
{'label': 'text', 'content': '数字孪生关键技术\n模型：能力的来源\n非线性评估模型\n大功率场景下，入纤光功率密度超过一定数值，导致光纤材料的非线性极化，引起自相位调制（SPM，Self-Phase Modulation）和互相位调制（XPM，Cross-Phase Modulation）、四波混频（FWM，Four-Wave Mixing）等非线性效应。特别是在DWDM系统中，随着波道数的增加以及入纤功率的提高，Kerr效应对系统的影响也随之加重，是影响系统性能的关键因素之一。具体可以通过Manakov方程来描述，在偏振复用系统中信号的传输过程可以描述成： \n其中为非线性系数，公式中的第三项体现了非线性效应的影响作用。目前增强高斯噪声（EGN，Enhanced Gaussian Noise）模型由于具有较高的精度被广泛应用于传输系统非线性评估中。其基本思想是：将频谱信号切割成足够窄频谱碎片组合，在光纤中传输时，基于类四波混频理论，通过三次拍频生成频谱碎片之间的非线性作用模型。同时EGN非线性评估模型与业务调制码型或峰值平均功率比（PAPR，Peak-to-Average Power Ratio）强相关，体现在如下公式中Φa与Ψa在不同调制格式中取值有差异。 \n(3-4) \n(3-5) \n其中为非线性噪声的功率谱密度，角标中SCI表示自相位干扰项，XCI表示互相位干扰项，Φa与Ψa为与调制格式相关的修正系数，表示待测通道功率，与是基于高斯噪声假设（发射信号服从高斯分布）与调制格式无关项，、和是由于高斯噪声假设带来的误差项，与调制格式相关。由此实现对系统给定入纤功率下的非线性代价估计。', 'id': '1143854_16'}
{'label': 'text', 'content': '数字孪生关键技术\n模型：能力的来源\n工作路径传输性能模型\n光业务端到端最常用传输性能指标是光信噪比OSNR，工作路径业务端到端OSNR性能估计重点需要分析链路通道级损耗和通道级噪声分布。 \n由于工作路径上的业务处于正常运行状态，可以采集运行数据用于对指标模型进行修正和优化，提升计算精度。通过将光性能监测（OPM，Optical Performance Monitor）板上报的实测功率谱与模型计算估计的功率谱进行对比，可以获得通道级误差反馈结果，通过闭环迭代，可以对模型参数进行修正，使误差最小化。', 'id': '1143854_17'}
{'label': 'text', 'content': '数字孪生关键技术\n模型：能力的来源\n备用路径传输性能模型\n备用路由于WSS预设衰减量通常并非最优值，OSNR无法满足业务开通/切换的需求，影响了执行成功率。在进行业务开通/切换的操作前，希望可以对备用路径光传输性能做出准确预估，依据预估结果进行操作。备用路径OSNR预估方案参见图 3-8。 \n评估光路系统在增减波时对通道功率动态变化的影响，根据端到端理论建模分析，包含的相关因素：1）EDFA对各通道的增益动态性，如短波长处的频谱烧孔SHB效应，AGC控制模式下增益竞争特性，少波时EDFA本身放大器自发辐射（ASE，Amplifier Spontaneous Emission）噪声影响等；2）光纤的动态特性，主要来源于光纤的拉曼效应，尤其在波长较多且功率偏高时，会使得短波长功率往长波长转移，且该转移量与光纤的衰减特性强相关。 \n备用路径业务未开通，缺乏实时性能状态反馈，如何提升备用路径的光性能预估准确度和可用性一直是业内难题。可采用在开局阶段向无光路径打入探针光的解决方案来提高模型精度。光层模型复用工作路径端到端方案模型，无法采集的OPM功率谱进行模型迭代，而是通过探针光所在路径上报的功率谱来校准模型参数，修正链路模型的整体偏差，解决仿真计算误差的问题。完成接收端OSNR预估后，可以指导WSS衰减量的合理化设置。', 'id': '1143854_18'}
{'label': 'text', 'content': '数字孪生关键技术\n数据：系统的基石\n数据是数字孪生光网络运行的基础，包括数据采集、上报、加工、存储等环节。实时、有效、完整的数据处理能力是数字孪生网络实现准确预测和正确决策的基础。', 'id': '1143854_19'}
{'label': 'text', 'content': '数字孪生关键技术\n数据：系统的基石\n数据采集\n数据采集是获取原始的数据过程。可改进现有采集流程，实现采集得更多、更快。 \n“更多”，扩展感知能力范围：新技术支持更多网络指标的采集和上报。通过自研DSP，可以支持更多内部处理参数的感知上报。光时域反射仪（OTDR，Optical Time-Domain Reflectometer）、光标签等新技术应用，也提升了对网络状态的感知能力。 \n“更快”，提升感知实时性：对需要快速采集的性能指标，自研芯片等底层硬件，可以支持定制化开发，支持硬件级别的快速采集和转发上报，满足快速采集的时间要求。Telemetry推送机制，也从软件层面提升了数据的时效性。 \n提升光系统数据感知和采集能力，为构建数字孪生系统打好基础。', 'id': '1143854_20'}
{'label': 'text', 'content': '数字孪生关键技术\n数据：系统的基石\n数据采集\n典型采集项\n数据采集提供历史数据和现网实时数据。利用人工智能、大数据、专家经验等技术在孪生体中进行数据的仿真验证，能主动发现问题、解决问题，可极大节省人力物力，提高网络维护效率。 \n面向传送网数字孪生的数据采集项示例如图 3-9所示。', 'id': '1143854_21'}
{'label': 'text', 'content': '数字孪生关键技术\n数据：系统的基石\n数据采集\n扩展感知能力\n随着光网络感知技术发展，现有系统可以支持越来越多的新感知能力，弥补了现有典型数据采集项的不足。\n一、光模块DSP芯片\n中兴通讯的光模块自研数字信号处理（DSP，Digital Signal Processing）芯片，支持更多光模块内部处理参数的感知上报，更好地体现光系统状态，满足光性能指标的采集的要求，对于优化光性能指标提供助力，如图 3-10。\nDSP 芯片支持快速采集和处理大量的数据，上报多种物理层的参数，如信号强度、光纤色度色散、偏振模色散、偏振相关损耗（PDL）、偏振态旋转（SOP）、激光器频偏、OSNR等等，全面体现了光性能指标的变化情况，反映业务的损伤情况和损伤来源。DSP能够快速响应光性能指标的变化，第一时间发现性能指标波动，并根据变化及时进行内部参数调整，实现调整优化的快速闭环处理。自主研发的DSP支持定制化处理，在多种业务下支持时间同步功能，支持PTP消息的处理，支持时钟频率同步功能等，并不断迭代演进，根据不同应用场景和需求灵活地调整内部实现机制。例如DSP可以根据用户需要对于信号进行加工处理，支持星座图的EVM计算，以实时误码率来呈现线路误码情况。\n二、光检测技术\nOTDR（Optical Time Domain Reflectometer，光时域反射仪），利用光的反向散射的原理来实现对于光纤相关指标特性的反映和测量，基于散射的分布式光纤传感技术中，最具代表性的主要有基于瑞利散射的相位OTDR、基于布里渊散射的BOTDR，以及基于拉曼散射的ROTDR，如表 3-1。\n表 3-1 OTDR技术\n| 光传感技术 | 感知外部扰动方式 | 排查光缆实际位置 | 光缆割接时寻缆工作 |\n|:-|:-|:-|:-|\n| pOTDR | 弯折光缆、抖动光缆 | 可以，但不推荐 | 推荐 |\n| φOTDR | 敲击地点、井盖 | 推荐 | 不可以 |\n| BOTDR | 加热、冷却光缆 | 可以，但不推荐 | 可以，但不推荐 |\n| ROTDR | 加热、冷却光缆 | 可以，但不推荐 | 可以，但不推荐 |\n凭借OTDR新技术，可以获取对于光纤施加扰动的位置和具体原因，有希望实现哑资源的精确感知，进一步实现光纤资源可视化、精细化、高效化的运营管理。解决现有光纤等哑资源的智能化管理难题。\n三、光标签\n光标签技术，通过调顶信息的加载和提取，提供随路信息通道，实现光层OAM功能，如图 3-11所示。\n基于光层OAM可以实现对光层性能和故障告警的端到端监测，扩展了现有系统的采集能力。\nOSNR检测：基于OPM的传统OSNR检测范围限制在ROADM/OTM站点之间，无法进行任意站点间或者站点内的OSNR检测。使用光标签通道功率检测，结合OA噪声模型/噪声系数定标和增益谱模型/增益谱定标，可以实现对光传输路径上任意OA噪声性能的实时动态监测，可获取任意光标签检测位置的OSNR数据。\n通道LOS检测：基于光标签的告警检测技术，可以实现单波长、毫秒级的LOS检测。\n光标签技术提供了一种板上集成的光通道层性能快速检测方法，高度集成的特点便于其在光网络中广泛部署。\n', 'id': '1143854_22'}
{'label': 'text', 'content': '数字孪生关键技术\n数据：系统的基石\n数据上报\n数据采集上报速度一直是制约实时精确感知的关键因素，可以从硬件和软件两个方面，解决上报速度的问题，满足快速采集的需要，如图 3-1。\n一、硬件转发\n配合自研芯片的硬件层面的快速采集，中间消息的封装和转发也可以通过硬件参与来提升处理效率，加快数据采集速度，如图 3-12。\n硬件采集器基于预先配置的采集模板，实现无需软件接入的数据采集，极大提升采集速度的上限。\nTelemetry上报需要按proto模板格式进行数据封装，基于硬件实现数据proto模板文件的打包处理过程，提高在数据上报格式转换环节的效率。\n二、专用软件接口\n光传送网络支持多种接口上报，典型接口包括SNMP，Netconf，Telemetry等，其中Telemetry（网络遥测）具备实时监测数据订阅推送机制，匹配快速上报的需求，优选作为数据快速上报的专用接口。\n表 3-2 典型接口收集方式进行对比\n| | SNMP | Netconf | Telemetry |\n|:-|:-|:-|:-|\n| 处理速度 | 慢 | 慢 | 快 |\n| 模式 | 查询应答 | 查询应答 | 订阅推送 |\n| 编码效率 | 低 | 低 | 高 |\n| 模型 | MIB ASN.1 | YANG 模型 | YANG模型 Proto文件 |\n| 场景 | 网络监控 | 配置管理 | 性能监测 |\n', 'id': '1143854_23'}
{'label': 'text', 'content': '数字孪生关键技术\n数据：系统的基石\n数据加工\n数据上报后，由于数据的来源不同、形式不同，无法直接使用，需要进一步的加工处理，如图 3-13。 \n数据来自各种不同的上报单元，每个上报单元对于数据格式、类型、具体的处理要求都可能不同，上报的数据也存在差异，既有形式上的差异，同一含义的数据可能以不同的数据形式存在；也有数据数据质量差异，由于数据采集设备的精度、环境因素等影响，造成数据质量的不一致；还可能存在缺失、异常等情况需要处理。 \n数据集成，收集获取不同来源的数据信息，进入统一的处理流程，针对数据进行加工处理。 \n数据清洗，针对各种原始脏数据进行处理，脏数据是指数据存在重复、缺失、错误等各种问题。数据清洗过程，就是通过去除重复项和无效项、填补缺失值、光滑噪声数据、平滑或删除离群点等方法，得到符合要求的完整数据。 \n数据转换，统一数据的表达方式、计量单位、编码方式等内容。针对计算和统计场景的要求，对数据的极值、均值、方差等参数进行控制，便于执行数据计算和数据比较。 \n数据处理，根据数据之间的关联关系进一步挖掘、分析，提取出面向不同应用需求的信息和知识。通过数据的筛选和过滤，按需选择优质数据，提升数据质量。汇总同类业务相关数据，可以更容易发现数据之间的关联规律，也可以相互进行校准修正。对于同类数据增加特征标签，可以方便的数据的访问和使用。原始数据通常只是某个特定维度的观测结果，通过不同维度的组合，可以获取更贴近真实状态的表达。对于数据进行可视化加工，更方便对于数据的全面理解。匹配不同角色、不同场景、不同限制条件下的使用要求，实现不同应用的个性化数据定制。 \n通过上述环节完成数据加工，构造完整统一数据源，保证全局唯一的数据来源的完整性和权威性，满足不同应用对于数据多次、大量、快速的使用要求。', 'id': '1143854_24'}
{'label': 'text', 'content': '数字孪生关键技术\n数据：系统的基石\n数据存储\n数据存储技术相对成熟，但针对仿真产生的海量数据，当传统存储技术无法满足要求时，需要考虑进行大数据技术的扩展。 \n大数据技术专门为海量数据的加工处理和存储的问题提供解决方案，可以按需选用多种数据存储技术，构建不同类型的数据库，分工协作，满足结构化、半结构化、非结构化网络数据的不同存储要求。分布式的系统设计还可以支持容量的动态扩展。 \n中兴通讯大数据平台集合了机器学习和数据挖掘算法，可以直接与现有管控数据进行对接，提供集成的、稳定的、反映历史变化的数据集合，为数据的处理以及进一步的分析治理提供了支撑保障，并支持分析结果可视化呈现，挖掘其中蕴含着巨大商业价值和学术价值。', 'id': '1143854_25'}
{'label': 'text', 'content': '数字孪生关键技术\n交互：信息的桥梁\n“交互”指通过接口进行的信息传递，按不同接口类型划分，南向接口连接物理网络和虚拟孪生网络，北向接口在虚拟孪生网络和网络应用之间交换信息。数字孪生系统与管控系统融合场景下，需要定义双方之间的交互接口，如图 3-14。 \n北向接口作为对外部应用提供服务的接口，基于现有北向接口扩展，方便用户将数字孪生功能集成到系统中，允许用户根据需要选择编排孪生相关功能特性。封装孪生相关服务也可以保证原始信息数据安全，内部算法与用户体验的解耦。 \n管控交互接口实现数字孪生与管控系统之间的信息交互，支持仿真功能调用和结果应用。在管控扩展数字孪生系统融合的场景，基于管控已有功能进行扩展，根据管控需要进行接口定义，满足管控和孪生系统之间功能、模型和数据互通的要求。 \n南向接口基于Telemetry协议，支持数据采集的订阅推送，可以实现亚秒级数据快速上报，提供更大的数据上报带宽。 \n接口内容可以细分Data、Model和Service三类。Data数据接口提供统一的真实数据来源，包含性能、告警、拓扑、状态等。Model模型接口体现数字孪生能力，包含从组件模型、端到端模型、业务仿真到上层应用等多个层次。Service服务接口包含仿真、分析、诊断等不同服务内容。 \n基于现有管控系统接口进行数字孪生扩展，需要考虑接口的兼容性和可扩展性。在已被广泛接受和使用的接口标准基础上，使用一致的数据格式，确保前向兼容性。面对数字孪生未来的发展空间，保留数字孪生各种预期功能场景的可扩展性。', 'id': '1143854_26'}
{'label': 'text', 'content': '数字孪生关键技术\nAI：智能提升的机遇\nAI大模型技术快速发展，是当下最热门的焦点之一，大语言模型（LLM，Large Language Model）推动AI快速进化到AGI（Artificial General Intelligence，通用人工智能）阶段，通过自主学习、自我改进，使人工智能系统能够在各种领域和任务中表现出与人类相当甚至超越人类的智能水平，为各行各业带来了大量前所未有的机遇和挑战。面对AI技术井喷的突破，如何在光传输领域应用AI技术的最新成果，是我们迫切需要研究的问题。', 'id': '1143854_27'}
{'label': 'text', 'content': '数字孪生关键技术\nAI：智能提升的机遇\n数字孪生与AI\nAI的实现需要数字化的支撑，通过数字孪生，可以完成物理网络到虚拟网络的数字化过程。数字孪生实时精确感知物理网络状态，并提供给AI分析处理，使AI对于网络状态了如指掌。数字孪生提供仿真能力和实时反馈结果给AI，实现AI与物理网络之间无障碍互动，对网络操作控制自如。通过数字孪生，打破真实光网络与AI技术之间的隔阂，实现真正融合。 \nAI也可以在数字孪生的落地过程中发挥重要作用。数字孪生中的关键技术，通过整合AI因素，可以取得更好的成果。AI可以支持更精准高效的数据采集，支持海量数据的分析处理，支持建模技术的升级演进，从而助力数字孪生技术本身的发展。通过AI不断拓展数字化的范围，提升了数字孪生的深度和广度，丰富数字孪生的内涵。 \n数字孪生与AI相互促进，相得益彰。在数字孪生光网络中，AI的特性和部署要充分考虑光网络的场景和需求。', 'id': '1143854_28'}
{'label': 'text', 'content': '数字孪生关键技术\nAI：智能提升的机遇\nAI特性\nAI可以支持针对海量数据的分析，从中发现规律并提供解决方案。 \n随着Telemetry等大数据量快速采集接口的应用，系统收集海量光网络的实时数据，如网络的状态信息、设备的运行状况、用户的行为数据等。AI可以有效地对数据进行梳理和分析，对比历史和当前数据，找出网络运行规律并发现潜在问题，为网络优化和故障预防提供保障。例如，通过分析网络故障数据，AI可以预测不同故障产生的概率，帮助网络运营商更好地定位故障解决问题，提升网络性能。 \n在光网络中，借助深度学习和模式识别，AI可以提供网络的个性化定制解决方案。例如，通过对用户行为的分析和学习，AI可以预测用户的需求和习惯，为用户提供个性化的节，如故障处理、网络优化等，AI通过学习专家知识和经验，辅助完成这些任务。 \n虽然AI具有上述优势，但是训练AI模型，需要对海量数据经过数据归一化处理，且可能需要人工进行标注，达到期望收敛，以解决特定场景的问题，但难以解决其他场景的问题。即存在训练代价大，泛化困难的问题。因此，需要对不同层次AI应用策略进行分析，发挥优势，解决问题，扬长避短。', 'id': '1143854_29'}
{'label': 'text', 'content': '数字孪生关键技术\nAI：智能提升的机遇\nAI部署\n实际系统中，可以根据需要，在不同层次灵活进行AI部署，如图 3-15，在应用层支持大语言模型的部署，模型仿真支持多种神经网络和机器学习算法来优化建模，在硬件芯片层面，针对数据处理内嵌AI技术同样是发展方向。 \n大语言模型是目前发展最快的的AI方向，通过学习大量的语料，如文本、视频、图像等数据，使用语义分割和注意力机制，支持对自然语言等的解析和处理。在光网络运维中，利用大语言模型，可以提供一个基于自然语言统一的交互界面，相比传统图形界面操作，更为简单直接，对用户更友好。利用大语言模型获取用户意图，完成用户意图识别和任务拆分，转换为内部管控系统的原子任务，结合数字孪生系统的仿真功能，完成用户的意图操作。智能运维方案选择时，大语言模型也可以根据用户需要和习惯，辅助进行方案分析和决策，选择最优的参数组合，为运维人员提供参考。大语言模型还可以通过自动化的方式，批量执行运维任务，提高运维的效率，减少人为错误，保证网络的稳定性和安全性。 \n在解析模型无法满足要求的场景下，AI可基于神经网络，辅以大数据驱动的机器学习，创建黑盒模型，或者将黑盒模型与解析模型结合，创建灰盒模型，满足建模精度、速度、适应性等多方面要求。完成模型创建后，AI同样也可以在模型训练和优化中发挥作用，利用有限数据样本模拟构建大量的数据集合，对于模型进行训练，生成符合预期的模型结果，节约资源同时大大提升了训练效率。 \n在芯片中集成AI功能，实现硬件级别的信号处理优化，完成数据筛选、加工和压缩等操作，提升数据处理的效率。对于以前需要通过复杂离线计算才能获取的系统指标，可以做到实时输出，大大提升数据采集的实时性。', 'id': '1143854_30'}
{'label': 'text', 'content': '数字孪生技术应用\n应用场景\n数字孪生技术，可以应用到光网络智能运维的全生命周期场景，包括：网络规划、网络建设、网络维护、网络优化、网络运营等不同环节。 \n在每个场景内，针对不同的网络模型，基于数字孪生技术不同的仿真能力，可以根据用户的具体要求，实现出多种多样的智能化应用。典型应用包括资源的在线仿真规划、业务快速发放、故障定位和告警溯源、健康度评估与调优、全生命周期的智能运维。 \n在不同场景之间，统一的数字孪生体支持根据不同应用进行建模，可以“打通‘规、建、维、优、营’间的设备资源信息、业务配置信息、故障及维护信息、网络优化信息间的信息墙”[2]，使不同应用场景间的信息沟通和互动成为可能，实现场景切换的全流程打通，满足全生命周期的应用要求。', 'id': '1143854_31'}
{'label': 'text', 'content': '数字孪生技术应用\n层次结构\n要满足复杂多样的应用场景，需要系统架构层面的支撑，将整个系统按层次进行划分后，可以清晰地看到各个部分之间的协作关系。从数字孪生应用整体角度观察，上面是外部应用，下面是物理网络，中间的数字孪生引擎，将用户需求与网络实体连接起来，可以进一步划分为孪生服务层、业务孪生层和网络孪生层（下文分别简称为服务层、业务层和网络层）。通过不同层次间的配合，逐层将用户应用场景，拆分为基础服务，转换为内部的业务特性，依托数字孪生体的行为模型实现仿真计算，完成功能场景的逻辑闭环。层次划分有利于功能的解耦、扩展和演进，如图 4-1所示。 \n服务层对用户提供统一的服务接口，提供分析、诊断、仿真、控制等具体服务项，符合标准化、模块化和自动化的要求，实现“数字孪生即服务”的目标，满足“规建维优营”不同环节的具体应用对数字孪生服务的需求。 \n业务层实现基本业务仿真需求，仿真引擎基于网络孪生体镜像，实现模型选择、算法加载、数据映射等功能，构建仿真模型实例，根据获取的实时数据，实现业务行为仿真，输出仿真结果。 \n网络层基于物理网络构建网络孪生体，实现物理网络的镜像映射，完成底层基础建模，满足模型相关数据采集的精度要求和速度要求，构造多层次、全维度的虚拟网络孪生体镜像。 \n三层中，服务层将外部服务需求拆分转换为具体基本业务仿真需求，网络层将业务仿真需求映射到底层物理网络。中间的业务层起到承上启下的作用，可以进一步细分为业务特性和行为模型两个层次：业务特性面向孪生服务，提供可供编排调用的业务特性，满足应用需要；行为模型对应物理网络，实现网络行为的仿真。通过中间的业务层，可以实现上下层之间的分层解耦。底层行为模型算法发生改进时，只要业务特性保持稳定，对用户来说，交互不受影响，感知上不会有大的变化。孪生应用根据用户需求发生变化时，只要业务特性保持稳定，提供相关应用的支撑，底层的实现机制可以不受影响。', 'id': '1143854_32'}
{'label': 'text', 'content': '数字孪生技术应用\n组合编排\n“孪生体管理需要支撑DTN系统多种维度、多种粒度的可视化呈现。”[3] 呈现给用户的是多层次多维度的立体的孪生体，满足各种不同的用户需求。而具体需求的实现过程中，需要进行用户需求到虚拟网络行为仿真的全程适配，可以通过针对不同层次、不同要素的编排组合来实现，如图 4-2。 \n服务层提供不同的功能应用、业务层实现多种特性、网络层中包含不同颗粒度网络对象的模型层次。将三个层次的各种要素进行组合编排，可以满足不同应用场景服务需求：在网络层中选择具体网络对象，基于业务层特性进行特定基本行为仿真，用于提供服务层的某项功能服务，组合后形成一个满足用户定制需求的解决方案。 \n组合关系可以灵活自定义，例如，健康度优化应用，在网络层选择基于端到端业务层次，从业务层选择传输仿真特性，服务层执行分析诊断，并根据结果生成控制方案并下发到物理网络。这样的组合完成了端到端业务的健康度监测和优化。又如，能耗预测应用，可以基于业务层次进行能耗仿真，也可以基于具体设备进行能耗仿真，仿真结果可以用于直接提供决策参考，也可以作为长期趋势预测的根据。 \n基于上述层次框架，基于孪生平台，可以满足不同方面的应用扩展。', 'id': '1143854_33'}
{'label': 'text', 'content': '数字孪生技术应用\n实验效果\n光性能仿真\n数字孪生相关技术在光性能仿真场景下，具备全局性和实时性的特点。全局性可以保证实现端到端链路的全局整体优化，避免局部优化，实时性可以根据网络实时动态变化做出响应，网络出现变更和波动时，保持网络的正常运行。 \n中兴通讯应用数字孪生技术，通过光性能特性仿真，面对现网运维中存在的一些问题给出解决方案，经过实验验证，获得了比较好的改进效果。下面是一些典型应用案例。 \n在线规划场景，需要针对待规划业务的光损伤状况进行评估，确定业务是否能正常开通。通过准确估计待规划业务的OSNR/光滤波代价/非线性代价值，合理预留OSNR裕量，可以提升系统传输性能1dB以上。 \n业务快速恢复场景，需要快速评估备用路由的OSNR指标，判定是否满足业务开通需求，应用数字孪生技术，减少在线评估业务OSNR误差，提升业务恢复效率。应用支持C和C+L波段、干线多跨、全场景覆盖的评估算法，可以实现备用路由OSNR估算误差小于2dB。 \n业务性能调优场景，需要针对现网业务进行功率调整效果仿真计算，满足业务优化的要求，实现目标功率优化调节，降低业务失效率。仿真结果可以提升传输性能0.5dB以上。', 'id': '1143854_34'}
{'label': 'text', 'content': '数字孪生技术应用\n实验效果\n故障根因分析\n中兴通讯网管控制系统，基于告警根因分析和故障溯源相关模型，针对光网络场景的告警和故障进行建模，挖掘和归纳出各种告警出现的内在关系和相互影响，完成告警关联及衍生规则提取，可以识别OTN 光网常见的告警和故障，实现故障的分钟级根因分析。 \n首先对历史告警以及告警相关联数据进行特征提取、清洗与聚合等处理，获得告警间的关联及衍生关系、关联告警与根因故障的映射关系，进而生成RCA（Root Cause Analysis，根因分析）规则、积累形成规则库。之后用RCA规则库对现网在指定监控周期内的批量上报告警进行规则匹配，对满足规则的告警序列，基于孪生体推理告警关联关系、显示告警关系树，并识别定位现网OTN故障根因。如图 4-3所示：', 'id': '1143854_35'}
{'label': 'text', 'content': '数字孪生管控融合系统\n关键需求\n现有网络管控系统已经成熟应用，实现数字孪生扩展最简单便捷的方法，就是基于现有管控系统架构，融合数字孪生功能，形成一个孪生管控融合系统。为了更好地使孪生系统和现有管控系统融合，有必要梳理融合架构的关键要求，如图 5-1。 \n融合系统可以针对管控功能和孪生功能统一灵活调度，实现数据同步、业务模型统一以及功能兼容。 \n孪生引擎和管控平台采用统一数据平台实现数据同步。孪生模型实例和网管管控对象采用统一业务模型，呈现一致，包含多个抽象层次的模型。融合系统提供的孪生仿真功能和网管管控功能保持兼容，统一对外提供功能调用。 \n为了实现应用层与融合系统的适配对接，考虑在应用层和系统之间增加一个功能调度层次，实现管控功能和孪生功能的混合统一调度。这样的处理机制，保证可以从现有管控系统平滑演进升级到融合系统。 \n融合系统复用现有管控接口，根据孪生需要进行扩展，无须新增独立的孪生接口，方便了用户的使用。融合系统利用已有管控业务模型对象进行仿真，无需在数字孪生系统中单独进行模型创建、编排以及与物理系统映射的工作，简化了执行仿真过程的步骤。综上，融合系统基于现有管控扩展，维持统一的风格，保留用户操作习惯，降低用户学习成本。', 'id': '1143854_36'}
{'label': 'text', 'content': '数字孪生管控融合系统\n业务架构\n为了方便实现孪生管控融合系统，我们采用了管控功能和孪生功能的对称业务架构，如图 5-2。 \n管控功能和孪生功能采用统一的业务架构，不同功能可以自由组合，实现统一调度。管控功能实现到物理实体的大业务闭环，而孪生仿真功能实现到模型实例仿真的小业务闭环。基于管控对象构造孪生系统的模型实例，保持管控对象和孪生实例定位信息结构一致，方便统一进行调度。通过服务网关实现功能适配，以及管控和孪生不同的定位访问，整合了管控功能和孪生功能，统一对外提供服务。 \n管控功能定位到现有的管控对象，包括设备模型（如网元、单板、端口、模块）和业务模型（如端到端业务对象），管控功能的实现由具体物理实体网络来完成，管控对象是物理实体的抽象映射，和物理实体之间实现双向的数据交互，配置下发和状态上报。 \n孪生功能定位到模型实例，包括模型组件和模型拓扑连接关系（简称模型拓扑），模型组件可以对应设备模型，通过模型拓扑可以对应业务模型。孪生功能由仿真功能引擎来实现。模型实例来自于对孪生体镜像的抽象映射，从孪生体镜像获取状态信息。孪生体镜像直接对应物理实体，实现对于物理实体的“复刻”。 \n管控功能和孪生功能实现从流程上是一致的，信息也是一一对应的，保持这样的对称架构，可以确保孪生功能与现有管控功能在应用层面无需区分，自由组合，统一调度。', 'id': '1143854_37'}
{'label': 'text', 'content': '数字孪生管控融合系统\n功能流程\n基于上述业务架构，进一步梳理出具体的功能流程。基于统一的数字孪生管控融合架构，实现不同的功能设计，涵盖主要的应用场景：性能调优、故障溯源、在线规划、业务开通等等，如图 5-3。 \n在系统架构统一的前提下，使用统一的业务处理流程，流经的关键节点保持一致，而关键节点处理内容有所差异，通过加载模块化、原子化的模型和算法来适配，从而实现流程与具体应用解耦。 \n用户根据提供的服务下发具体操作请求，服务网关将用户进行服务拆分，将需要仿真相关需求，传递给仿真引擎。仿真引擎实现算法和模型的加载，完成模型编排，确认需要获取的数据，执行仿真。仿真引擎返回仿真结果，传递给决策处理，返回结果呈现给用户完成交互，或者直接生成配置信息，下发到物理网络执行操作。 \n现有业务架构下，采用统一的功能流程，通过仿真引擎进行模型和算法选择以及数据来源的切换，实现整体业务的统一编排，满足不用应用场景的不同需求的实现。', 'id': '1143854_38'}
{'label': 'text', 'content': '总结与展望\n在当前算力网络和AI大模型蓬勃发展的形式下，光网络作为最基础的承载网络也迎来了新一轮的大发展机遇，各项技术快速成熟和商用，包括400G长距传输、L波段扩展、小颗粒业务承载、高维度光交叉等。光网络全生命周期运营难度进一步提升，运营智能化水平迫切需要有实质性提升，数字孪生技术将是一个有理论基础、有实施途径的有效尝试，数字孪生光网络将是高阶自智光网络必不可少的组成部分。 \n数字孪生技术中最基础也最关键的是光器件和传输性能的建模技术，建模技术在综合物理建模和数据建模的最新成果基础上已取得重大突破，建模精度已满足现网应用需求。其它各项技术，包括数据采集上报分析技术、AI和大模型应用部署技术、架构兼容演进技术、接口统一开放技术，也都有了实质性进展。 \n数字孪生光网络可以满足网络状态的实时监控呈现、故障的回放分析、操作指令的模拟验证、劣化隐患的前瞻预测、优化方案的评估反馈等一系列需求，当前在业务在线规划、业务快速发放、业务性能调优和故障分析溯源等场景已得到初步应用，显现出了相较传统运营模式的很大优势。 \n中兴通讯积极投身数字孪生光网络的研究和实践，从建模到网络架构的几大关键技术都在同步推进，并已在实验室仿真环境上初步验证现有成果，同时多途径在现网环境做试点。现有研究和实践都清晰显示数字孪生光网络技术和路径可行。 \n中兴通讯将继续加大投入，同产业界同仁一起，继续提升光网络建模精度，扩展AI和大模型应用场景，优化架构设计和接口规范定义，全面试点网络运营的各个阶段场景，让数字孪生光网络早日在现网大规模应用。', 'id': '1143854_39'}
{'label': 'text', 'content': '参考文献\n[1]. ITU-T Y.3090 《Digital twin network - Requirements and architecture》 \n[2].《中国联通光网络数字孪生技术白皮书》 \n[3]. CCSA TC3 《数字孪生网络架构及技术要求》', 'id': '1143854_40'}
{'label': 'table', 'content': '缩略语\n表 8-1 缩略语\n| 缩略语 | 术语 | 描述 |\n|:-|:-|:-|\n| AI | Artificial Intelligence | 人工智能 |\n| ASE | Amplifier Spontaneous Emission | 放大器自发辐射 |\n| ASN.1 | Abstract Syntax Notation One | 抽象语法标记1 |\n| BER | Bit Error Rate | 误码率 |\n| CCSA | China Communications Standards Association | 中国通信标准化协会 |\n| CD | Chromatic Dispersion | 色散 |\n| DGD | Different Group Delay | 差分群时延 |\n| DSP | Digital Signal Processing | 数字信号处理 |\n| DT | Digital Twins | 数字孪生 |\n| DTN | Digital Twin Network | 数字孪生网络 |\n| EDFA | Erbium-Doped Optical Fiber Amplifiers | 掺铒光纤放大器 |\n| EEPN | Equalization Enhanced Phase Noise | 均衡增强相位噪声 |\n| EGN | Enhanced Gaussian Noise | 增强高斯噪声 |\n| EVM | Error Vector Magnitude | 误差矢量幅度 |\n| FEC | Forward Error Correction | 前向纠错码 |\n| FOPA | Fiber-Optic Parametric Amplifiers | 光纤参量放大器 |\n| FRA | Fiber Raman Amplifiers | 光纤拉曼放大器 |\n| FWM | Four-Wave Mixing | 四波混频 |\n| GN | Gaussian Noise | 高斯噪声 |\n| ITU-T | ITU Telecommunication Standardization Sector | 国际电信联盟电信标准化部门 |\n| LLM | Large Language Model | 大语言模型 |\n| LOS | Loss Of Signal | 信号丢失告警 |\n| MIB | Management Information Base | 管理信息库 |\n| NETCONF | Network Configuration Protocol | 网络配置协议 |\n| OA | Optical Amplifier | 光放大器 |\n| OADM | Optical Add-Drop Multiplexer | 光分插复用器 |\n| OAM | Operation Administration and Maintenance | 操作管理维护 |\n| OPM | Optical Performance Monitor | 光性能监测 |\n| OSNR | Optical Signal Noise Ratio | 光信噪比 |\n| OTDR | Optical Time-Domain Reflectometer | 光时域反射仪 |\n| OTM | Optical Terminal Multiplexer | 光终端复用器 |\n| OTU | Opital Transport Unit | 光转发单元 |\n| PAPR | Peak-to-Average Power Ratio | 峰值平均功率比 |\n| PDFA | Praseodymium-Doped Optical Fiber Amplifiers | 镨掺杂光纤放大器 |\n| PDL | Polarization Dependent Loss | 偏振相关损耗 |\n| PMD | Polarization Mode Dispersion | 偏振模色散 |\n| QoT | Quality of transmission | 传输质量 |\n| RCA | Root Cause Analysis | 根因分析 |\n| ROADM | Reconfigurable Optical Add-Drop Multiplexer | 可重构光分插复用器 |\n| SHB | Spectral Hole Burning | 频谱烧孔效应 |\n| SNMP | Simple Network Management Protocol | 简单网络管理协议 |\n| SOA | Semiconductor Amplifier | 半导体放大器 |\n| SOP | State of Polarization | 偏振态 |\n| SPM | Self-Phase Modulation | 自相位调制 |\n| SRS | Stimulated Raman Scattering | 受激拉曼散射 |\n| TDFA | Thulium-Doped Optical Fiber Amplifiers | 铥掺杂光纤放大器 |\n| WDL | Wavelength-Dependent Loss | 波长相关损耗 |\n| WSS | Wavelength Selective Switch | 波长选择开关 |\n| XPM | Cross-Phase Modulation | 互相位调制 |\n', 'id': '1143854_41'}
