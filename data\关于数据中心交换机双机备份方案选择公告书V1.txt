{'label': 'document', 'content': '关于数据中心交换机双机备份方案选择公告书V1', 'id': '1009016'}
{'label': 'text', 'content': '选择策略公告书\n背景说明\n数据中心交换机组网中，主要三个角色：Spine（另称EOR），Leaf（另称TOR），Board-Leaf（EVPN Vxlan组网中称为DCGW），为了确保数据中心网络的安全，以上三个角色均为双机备份，目前双机备份组网技术有如下选择： \n堆叠 \n两台独立的物理设备虚拟为一台逻辑设备，即2虚1。虚拟后，系统选择其中一台设备作为主设备，负责整个虚拟逻辑设备的控制和管理。转发逻辑仍然维持两台物理设备分别转发。 \n堆叠方案的优势 \n堆叠方案在于配置简单，方案无限制。虚拟后的逻辑设备在控制管理上和单设备无区别，在转发层面还可以保持两个成员设备独立，形成备份。 \n堆叠方案的缺点 \n堆叠系统的缺点在于两台设备共用一个控制平面，控制平面的可靠性降低。日常版本升级无法支撑业务不间断需求。目前我司协议栈软件ISSU升级有一定使用约束，推广场景受限。 \nIP Route ECMP \n两台设备通过路由协议形成等价路由转发组的方式实现备份，设备独立运行，通过标准的RFC路由协议进行交互。 \nECMP的优势 \nECMP优势在于可靠性强，网络设备采用RFC标准协议，可保证互通性与兼容性。两台设备独立运行管理，控制面与转发面均独立，方便版本升级与业务倒换。 \nECMP的缺点 \nECMP缺点在于仅能运行在L3转发节点，不支持L2转发分担与保护。 \nMC-LAG \n跨设备链路聚合组（即MC-LAG），是一种实现跨设备链路聚合的机制，两台独立交换机设备通过MC-LAG功能与其他设备进行跨设备链路聚合，从而把链路可靠性从单板级提高到了设备级，组成MC-LAG系统的两台设备通过私有同步协议进行设备间的表项同步（如MAC表，ARP/ND表等）。 \nMC-LAG的优势 \nMC-LAG的优势是两台设备独立运行，可靠性强，升级运维简单，内部两台设备的同步使用私有协议，和第三方对接使用标准的LACP协议； \nMC-LAG的缺点 \nMC-LAG方案的缺点在于其部署运行在L2转发节点，或者作为L3的网关节点。第三方设备的报文转发给MC-LAG节点设备时，MC-LAG功能通过LAG hash选择的成员设备发送，无法确保协议报文均投送到同一个成员上，因此基于状态的协议无法在MC-LAG端口上运行，场景受限比较多。具体使用限制详见MC-LAG功能的使用局限性。 \n随着我司数据中心交换机的销售量增大，所承载的业务复杂多样。堆叠场景升级无法满足部分场景的业务中断时间要求（秒级）。此应用限制造成部分堆叠方案的局点版本升级困难。基于这一情况，交换机产品线和工服体系讨论后确定，决定减少堆叠方案在数据中心的开局应用。', 'id': '1009016_1'}
{'label': 'text', 'content': '选择策略公告书\n策略选择\n基于上述堆叠，IP Route ECMP，MC-LAG三个方案的功能支持情况和优缺点，本次策略选择原则是能够使用IP Route ECMP+MC-LAG开局的场景，就不再继续使用堆叠的组网方案。其他的一些必须使用堆叠方案的开局场景继续维持堆叠开局，详细策略如下：\n| 产品组网（新开局） | 9900系列 | 5960系列 | 方案推广时间点 |\n|:-|:-|:-|:-|\n| 国内核心网5GC配套（传统方案） | 去堆叠 | 去堆叠 | 已支持 |\n| 国内核心网5GC配套（SDN混合overlay方案） | 去堆叠 | 去堆叠 | 已支持 |\n| 国内核心网5GC配套（SDN硬件overlay方案） | 去堆叠 | 去堆叠 | 2021年.01 |\n| 国内核心网4G配套 | 堆叠 | 堆叠 | 尚未与核心网方面达成一致，待确认 |\n| 国际核心网传统组网（非SDN） | 堆叠 | 堆叠 | 尚未与核心网方面达成一致，待确认 |\n| 国际核心网SDN配套 | 去堆叠 | 堆叠 | 国内先行试点5960去堆叠方案，计划2021.7。 |\n| 国内运营商资源池 | DCGW仅作CE新开局点，在无新增功能要求的前提下，采用去堆叠方式开局，DCGW兼做MPLS PE局点，待完成去堆叠方案验证后再采用去堆叠方式开局。 | DCGW仅作CE新开局点，在无新增功能要求的前提下，采用去堆叠方式开局，DCGW兼做MPLS PE局点，待完成去堆叠方案验证后再采用去堆叠方式开局 | 2021.03 |\n| CDN配套 | 去堆叠 | 去堆叠 | 已支持 |\n| 城域网/政企网 | 无明确需求可维持堆叠；有需求明确且可采用5GC/运营商资源池方案选择去堆叠方案 | 无明确需求可维持堆叠；有需求明确且可采用5GC/运营商资源池方案选择去堆叠方案 | |\n| 注意：去堆叠改造过程无法支撑业务平滑过渡。当前已采用堆叠方式开局的局点可维持现状。对于具备去堆叠条件的局点，后续择期中断或切换业务后进行去堆叠改造。 | 注意：去堆叠改造过程无法支撑业务平滑过渡。当前已采用堆叠方式开局的局点可维持现状。对于具备去堆叠条件的局点，后续择期中断或切换业务后进行去堆叠改造。 | 注意：去堆叠改造过程无法支撑业务平滑过渡。当前已采用堆叠方式开局的局点可维持现状。对于具备去堆叠条件的局点，后续择期中断或切换业务后进行去堆叠改造。 | 注意：去堆叠改造过程无法支撑业务平滑过渡。当前已采用堆叠方式开局的局点可维持现状。对于具备去堆叠条件的局点，后续择期中断或切换业务后进行去堆叠改造。 |\n', 'id': '1009016_2'}
{'label': 'text', 'content': '选择策略公告书\nMC-LAG功能的使用局限性\nMC-LAG方案中两台设备独立运行，通过私有协议同步转发表项，第三方对接设备转发报文到MC-LAG设备时，基于报文中字段的HASH算法选择的LAG出端口，因此无法控制每个协议报文投递的MC-LAG成员设备，在其运行中存在一些天然的缺陷，且无法避免，在投标和开局中存在下面功能需求，则不能使用MC-LAG+ECMP方案替代堆叠方案，具体限制如下： \nMC-LAG组网方式下，无法使用MC-LAG的接口地址进行主动Ping/Trace等常规网络诊断操作，只能使用Loopback地址进行上述操作，若采用MC-LAG的接口地址，对端回应的应答报文如果上送到MC-LAG的对端设备，两台设备是独立的管理控制平面，无法正常处理； \nMC-LAG组网方式下，无法使用MC-LAG的接口地址进行有状态的协议交互，例如BGP、OSPF、ISIS等路由协议，原因同1，目前仅支持组成MC-LAG的两台设备分别采用Loopback地址与核心网网元建立BGP邻居； \nMC-LAG组网方式下，不能采用MC-LAG接口地址与核心网建立BFD，需要组成MC-LAG的两台设备和对端核心网设备开启单臂BFD的方式； \nMC-LAG组网方式下，无法支持DHCP Server、802.1X等需要维护本地数据库的协议，组成MC-LAG的两台设备间无法进行此类协议的数据库信息同步； \nMC-LAG组网方式下，无法支持端口隔离、ZESR、Loopdetect等需要操作对端设备端口状态的协议； \nMC-LAG组网方式下，无法在MC-LAG的接口上进行LDP等标签分配协议。由于组成MC-LAG的两台设备为独立的管理控制面，单台设备分发的标签无法在对端的设备上下发相应的转发表项。', 'id': '1009016_3'}
{'label': 'table', 'content': '选择策略公告书\n附：去堆叠场景的主要方案介绍\n| 应用场景 | 方案名称 | 适用范围 | 商用时间 |\n|:-|:-|:-|:-|\n| 核心网配套场景 | 国内运营商5GC NFVI传统组网方案 | 中国移动5G网络云控制面中国电信5G电信云控制面中国联通5G通信云控制面 | 当前 |\n| 核心网配套场景 | 国内运营商5GC NFVI SDN组网方案（硬件overlay） | 中国电信5G电信云控制面中国联通5G通信云控制面 | 2021.1 |\n| 核心网配套场景 | 国内运营商5GC NFVI SDN组网方案（混合overlay） | 中国移动5G网络云控制面 | 当前 |\n| 核心网配套场景 | 专U组网方案 | 中国移动5G专U中国电信5G专U中国联通5G专U | 承载方面2020.12核心网方面待确认 |\n| IT资源池场景 | SDN组网方案 | 中国移动IT资源池 | 当前 |\n| IT资源池场景 | 非SDN组网方案 | 中国电信云公司资源池 | 2021.3 |\n| CDN配套场景 | CDN配套方案 | CDN配套 | 当前 |\n', 'id': '1009016_4'}
