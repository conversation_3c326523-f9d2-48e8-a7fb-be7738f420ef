{'label': 'document', 'content': '数据中心网络解决方案技术白皮书_20200229', 'id': '895850'}
{'label': 'table', 'content': '| 版本 | 日期 | 作者 | 审核者 | 备注 |\n|:-|:-|:-|:-|:-|\n| V1.0 | 2011/10/23 | 黎英 | | 不对第三方开放 |\n| V1.1 | 2020/2/29 | 曾涛10005280 | | 更新模板 |\n| | | | | |\n| | | | | |\n', 'id': '895850_1'}
{'label': 'text', 'content': '概述\n在今天，网络已经成为组成人们工作和生活必不可少的部分，从娱乐、购物到出行这类日常生活行为，再到通信、电力和银行等等各行业的运作，背后都有一张庞大而复杂的、或透明或可见的网络在为其服务。 \n数据中心为企业和电信运营商提供了实时、高效的数据交互能力，使得网络互通和高速运算变得更为便捷、简单。但随着业务量的增加，数据中心却变得日益庞大，越来越复杂，也正面临着资源利用率低，投资量大的问题。同时，因为数据中心涉及了计算、存储、网络、应用管理等一系列的专业技术，导致了维护困难，业务部署周期长等问题。 \n如何适应企业和网络应用的高速发展，突破现有数据中心的一系列瓶颈？业界引入了云计算的概念。通过云计算方案，可有效将IDC中的计算、存储、网络、软件等资源虚拟化、资源池化。云计算概念使得IT技术从原来单台个人计算机自给自足阶段进入到计算/存储/网络/软件/服务资源按需获取的阶段 。云计算技术能够解决数据中心所面临的企业应用和电信业务迅速膨胀问题，但需要对现有的数据中心网络架构和技术进行革新，即对数据中心网络提出了新的技术要求，包括如异构网络融合、网络资源虚拟化、高带宽、高可靠性、更高级别的网络安全、网络易维性等等。 \n中兴通讯及时把握行业和技术发展趋势，提供一整套解决方案助力电信和企业级用户打造精品数据中心网络，为云计算服务保驾护航，从产品到解决方案，均体现出技术领先、统一融合、高效经济的特点。', 'id': '895850_2'}
{'label': 'text', 'content': '市场驱动力\n数据中心的发展历程\n随着技术的发展应用及企业对IT认识的深入，数据中心发生了巨大的变化，其发展经历了4个大的阶段：数据中心由集中、分散计算到再次集中，直至云计算IDC（Internet Data Center），网络也经历了从局域网到互联网数据中心再到云IDC的发展阶段。如图2-1所示。 \n第一阶段：1965-1980时期，计算机器件组成主要以电子管，晶体管为主，体积大，耗电大，主要运用于国防机构，科学研究等军事或者准军事机构。由于计算消耗的资源过大，成本过高，因此计算的各种资源集中也就是必然的选择。同时，也诞生了与之配套的第一代的数据机房。在1969年，随着ARPANET网络的出现，诞生了分组交换技术，分组交换技术是随计算机实现网络通讯而产生的，是六十年代网络发展的重要标志之一。 \n第二阶段：1980-1995时期，随着大规模集成电路的迅速发展，计算机除了向巨型机方向发展外，更多地朝着小型机和微型机方向快速演进。随着计算机技术的快速发展，出现了个人电脑，在这个时代，计算的形态总的来说是分散为主，中小型数据机房得到了爆炸式的发展。个人电脑也促进了网络技术的发展，各种局域、广域网的发展迅速，计算机生产厂商也开始开发自己的计算机网络系统。 \n第三阶段：1995-2005时期，互联网的兴起被示为计算行业从发明计算机之后的第二个里程碑，互联网的兴起本质上是对计算资源的优化与整合，而对人类社会分散计算资源的整合是计算发展本身的内在的要求与趋势，数据中心再次走向集中，随着Internet开始用于商业用途， Internet以空前的速度迅速发展，服务器的增多，连入网络的计算机数目的增多以及主干网速度的提升，都为商业的发展提供了广阔的空间，服务器农场通过Internet广泛互联到网络中。 \n第四阶段，2005年至未来，随着数据中心整合的不断演进。出现了云计算IDC，服务器有效整合，IDC通过分布式计算、虚拟化等技术进行进一步有效整合，网络的带宽、虚拟化等进一步提升。', 'id': '895850_3'}
{'label': 'text', 'content': '市场驱动力\n目前数据中心面临的挑战\n进入二十一世纪后，互联网业务、宽带业务以及企业信息化的高速发展是数据中心建设井喷式增长的最大推动力。从图2-2可以看出，通过IDC网络，可以为个人提供全方位的价值业务；而企业数据中心是联系企业内部和企业间各业务系统的纽带。同时由于互联网业务宽带化和通信网络的IP化发展，传统电信运营商可以借助IDC实现全业务运营。 \n正是互联网和商业业务的增长带动了数据中心的蓬勃发展，IDC的建设也为业务量的提升提供了有效保障。但基础建设往往滞后于业务需求，IDC的建设步伐已经远远赶不上业务量的增长步伐。传统数据中心在日益膨胀中也暴露其弊病，因为传统IDC部署是独立，对资源的分配也是独立的，业务与资源固定分配，这就导致业务部署时间长，基础资源利用率低；传统IDC前后端网络采用不同的技术导致网络维护困难，从而带来较高维护成本。如图2-3所示，IDC网络面临各种挑战和要求，包括业务应用、资源整合、降低运维成本、提升资源利用率等。这就要求数据中心够整合现有资源，适应未来业务的不断发展。', 'id': '895850_4'}
{'label': 'text', 'content': '市场驱动力\n云计算网络的引入\n云计算起源于互联网，由Google公司于2007年首先提出“云计算”，其业务特点与互联网业务或者更容易理解的公共水电应用相似，古老的公共或资源需要自行修建，而现代的公共资源或云计算资源可以按需而用， “按需而用”是云计算的核心特点。 \n云计算可以根据服务模式和部署方式划分。如图2-4所示。 \n按服务模式划分方式：云计算主要分为三种服务模式：laaS、PaaS、SaaS。其中最底层的IaaS服务（基础设施即服务），用户Internet动态申请获得计算、存储、网络资源，且用户不用关心其软件具体跑在那台服务器上，由IaaS负责资源分配；IaaS的上一层服务模式是PaaS服务（平台环境即服务），用户通过PaaS动态开发部署软件，不用关心操作系统或数据库等平台软件；最上层是SaaS（软件即服务），用户通过Internet直接使用软件，不用购买安装软件。在这三类业务中IaaS是三类业务开展的基础。 \n按部署方式划分：云计算可以划分为公有云、私有云以及混合云。大部分企业适合建设私有云，企业对基础架构的运营拥有自主权，随着公有云的逐步成熟，企业可以将部分应用建立在公有云基础上；公有云一般由运营商建设，运营商可以利用原有的IDC业务提供基础设施服务（例如机房机架空间出租、整机租用等业务），一个用户独享基础硬件资源，而开展IAAS基础设施要求资源共享，资源对用户透明。 \n云计算服务将有效整合IDC中的计算、存储、网络、软件等资源，基于云计算的IDC与传统IDC的主要区别之一即在于可以实现资源共享，动态满足最终客户突发流量或者快速增长的要求。', 'id': '895850_5'}
{'label': 'text', 'content': '云计算数据中心承载网络\n云计算对IDC网络的要求\n云计算方案可以有效将IDC中的计算、存储、网络、软件应用等资源池化，进行统一管理和调度分配，实现对资源的有效管控，实施按需分配；提高资源的利用率，降低投资成本；简化网络，降低网络维护复杂度；增强数据中心的可扩展性，资源能力规划预测性强，可快速响应企业对IT资源的需求。 \n虽然云计算可以解决IDC网络资源利用率低、部署周期长等问题，但目前的IDC网络架构并不能较好的支持云计算服务。从图3-1可以看出，当部署一个数据中心时，需要分别部署SAN网络、计算网络和LAN网络， SAN网络用于服务器后端和存储之间可靠的数据交互，常用FC（Fiber Channel）承载，FC技术具有高速率、大带宽、丢包敏感、时延不敏感的特点，但不同厂家之间设备不互通，且成本高（一些小型的SAN网络也可能采用iSCSI）；高速计算网络主要采用Infiniband技术，用于服务器和Cluster之间的信息高速传递；LAN网络用于服务器前端和外部设备之间数据交互，一般用以太网承载，具有可塑性强，技术成熟，大容量易扩展的特点。多种网络的共存导致了部署的复杂性，在这样的异构网络下，目前IDC业务部署方式是：业务是和网络资源固定捆绑的，网络资源不能动态共享；业务部署从购买网络设备开始，用户需要分别部署、维护前端LAN网络和计算、存储网络。 \n这样网络维护异常困难，而且扩展性差，随着云计算数据中心的集中化，网络规模会不断扩大，这样异构的网络维护会越来越困难。能否提供融合统一的网络，能否简化架构，成为数据中心的关注的核心问题。同时，随着业务整合，虚拟化的部署，接入带宽增大，业务部署由原来分散部署变成集中部署，云计算数据中心内部和数据中心之间的带宽需要扩展以便进行高速互联，受限于目前10G网络带宽，FC等网络现行主流带宽不到10G，核心/汇聚层对于突发的巨大流量显得越发力不从心。而云计算按需服务的特点，要求网络需要具备虚拟化特性，为不同业务、不同用户提供所需的的带宽/SLA/安全等虚拟的网络资源。可以看出，云计算IDC网络对于承载网络的要求主要包括高带宽互联技术、网络融合技术和网络虚拟化技术等。', 'id': '895850_6'}
{'label': 'text', 'content': '云计算数据中心承载网络\n云计算数据中心承载网络技术\n作为云计算数据中心承载网络，应当具备网络资源共享、统一部署、高带宽、易维护、低耗能等特点，传统IDC网络架构已无法满足云业务部署要求。以太网作为IP网络的主要承载技术，近年来取得了突飞猛进的发展，各个标准组织都致力于增强以太网技术的预研，传统以太网承载正逐步转向无损以太统一承载，使得以太网技术能够适应数据中心云业务的快速发展，以其低成本、高扩展性、等优势发展成为数据中心未来主流的承载网络技术。', 'id': '895850_7'}
{'label': 'text', 'content': '云计算数据中心承载网络\n云计算数据中心承载网络技术\n高带宽互联技术\n为了满足高带宽互联的要求，IEEE发展了40G/100G以太网（802.3ba）标准，解决了数据中心、运营商网络和其他流量密集高性能计算环境中数量越来越多的应用的宽带需求。而数据中心内部虚拟化和虚拟机数量的繁衍，以及融合网络业务、视频点播和社交网络等的需求也是推动制订该标准的幕后力量，该标准还有望推动万兆以太网的普及，可以提供更多的万兆链路汇聚。 \n在数据中心，像视频点播、10G的FCoE、以及高性能计算这样的高带宽应用，都需要万兆以太网接口。目前，万兆在大型网络的接入层上刚刚起飞，一旦扩展到客户端设备，必将引发汇聚层和网络核心层对40G/100G以太网的需要。最近两年正值数据中心高速成长的时期，再加上万兆网卡和以太网设备价格的下降，正在有越来越多的用户愿意采用万兆以太网接入技术。数据中心服务器具有集中化、高密度的特点，随着服务器和接入设备上万兆以太网的普及，普通的10GE端口已经无法满足上行汇聚的要求，因此网络汇聚层和核心层设备对100G以太网的需求越来越强烈。如图3-3所示，未来数据中心将演进至服务器10GE接入至TOR，TOR通过40G上行汇聚至汇聚/核心层，核心层之间跨数据中心的高带宽互联采用100G通道。 \n无论如何，网络流量都在继续增长，不论是企业，还是运营商，都要扩建自己的网络来应对流量的增长，来避免流量拥塞，网络的带宽升级扩容势在必行，对高带宽交换机的需求也在日益增长，100G以太网势在必行。', 'id': '895850_8'}
{'label': 'text', 'content': '云计算数据中心承载网络\n云计算数据中心承载网络技术\n网络融合相关技术\n非融合的数据中心网络物理上存在几个不同的网络系统，如局域网架构、SAN网络架构、高性能服务器网络架构、管理控制网络架构，各个网络上采用的技术不同，如局域网主要采用以太网技术，SAN网络主要采用Fiber Channel技术，一些规模较小的SAN网络也可能采用iSCSI技术。FC具有高带宽，低延时的特点，经过多年的发展，是大型存储的首选技术，FC是端到端的光纤通道，所以其QoS保障出色，转发过程中无丢包。但FC技术不普及，技术垄断性强，且不同厂商之间设备互通困难。iSCSI技术是把SCSI的指令通过TCP协议承载的一种存储传送技术，主要应用在小型的，性能要求不高的存储系统中，由于采用TCP承载，协议处理性能较低，数据传输安全性也较差。iSCSI技术也是基于以太网，但由于iSCSI并不能兼容现有的FC存储，不符合统一融合的演进思路。\n', 'id': '895850_9'}
{'label': 'table', 'content': '云计算数据中心承载网络\n云计算数据中心承载网络技术\n网络融合相关技术\n为了将存储网络和数据网络融合，同时实现现有FC-SAN网络的平滑过渡，从现有FC和以太网上发展出了FCoE（Fiber Channel over Ethernet）技术，FCoE的原理是将光纤通道映射到以太网，在以太网上运行光纤协议，进行寻址、封装、发现和转发，从而可以在以太网上传输SAN数据。实现FCoE转发的前提是支持无损以太网。它能够保护客户在现有FC-SAN上的投资的基础上，提供一种以FC存储协议为核心的I/O整合方案。表3-1对比了FC、iSCSI和FCoE技术。\n| | 技术特点 | 应用 | 发展趋势 |\n|:-|:-|:-|:-|\n| FC | 端到端的光纤通道，可靠性高，QoS能力强，能提供4Gbps的传输能力 | 服务于SAN，用于服务器后端与存储网络的高速无损传输 | FC技术应用不广泛，属于垄断地位，且高速端口发展较慢，兼容性较差，主流厂家也在向FCoE演进 |\n| iSCSI | 将SCSI封装到TCP/IP包中传输，以太网承载，可以支持1Gbps | 服务于规模小、低成本、性能要求低的SAN网络，可以基于以太网传输 | 不能兼容FC，受传统以太网络技术的限制，难以发展成为大型数据中心的主流技术 |\n| FCoE | 融合FC和以太网技术，基于以太网封装光纤通道帧，FCoE可以提供10Gbps的流量通道 | 服务于数据中心网络，SAN和LAN都通过FCoE switch与服务器互通，减少服务器端口和网络物理设备，更利于虚拟化部署 | 兼容FC，符合融合趋势，可以由10G扩展到40G/100G，结合DCB、TRILL、MACSec等技术实现高可靠、安全、低延迟的数据传输 |\n', 'id': '895850_10'}
{'label': 'text', 'content': '云计算数据中心承载网络\n云计算数据中心承载网络技术\n网络融合相关技术\n现有网络可遵循10GE->10G FCoE->40G/100G FCoE的部署步骤，由传统以太网逐步过渡到无损以太网。从网络部署来看，FCOE需要在服务器和网络上部署，部署FCOE CNA网卡后，服务器的网卡整合，对于高密度的服务器场景，FCOE简化了网络布线，降低维护难度，从而节省投资；对于支持FCOE的网络互联设备，其带宽随着以太网的带宽升级而升级，可以直接平滑升级到100G，而且具备无损及低延迟特性，满足SAN及HPC的承载需求。\nFCoE技术解决了底层承载介质一致性的问题，但由于以太网络本身缺乏无损和拥塞避免等机制，不能有效保证数据的无损传输，由此以太网制定了IEEE 802.1Qbb、IEEE 802.1Qaz、IEEE 802.1Qau等一系列DCB（Data Center Bridging）标准，提供无损以太技术，满足后端存储网络的高可靠性要求。融合网络技术以FCoE技术为主，再结合DCB技术、TRILL技术、MACSec技术，提供安全的、无环路、低延时的统一融合网络。\n表3-2列出了网络融合技术较传统以太网的优势。\n', 'id': '895850_11'}
{'label': 'table', 'content': '云计算数据中心承载网络\n云计算数据中心承载网络技术\n网络融合相关技术\n表3-2列出了网络融合技术较传统以太网的优势。\n| DCB等技术 | DCB等技术优势 | 传统以太网问题 |\n|:-|:-|:-|\n| Priority-based Flow Control (PFC) IEEE 802.1Qbb | 按流分类做流控、无损，能够满足存储网络无损需求 | 基于端口做流控，队列相互影响 |\n| CoS Based Bandwidth Management IEEE 802.1Qaz | 基于COS队列将流分类进行分组，增强转发性能，例如确保带宽 | 通常进行一级QoS调度，调度不够灵活 |\n| Congestion Management IEEE 802.1Qau | 端到端拥塞管理 | 无端到端机制 |\n| Data Center Bridging Exchange Protocol (DCBX) | 增强以太网能力的管理和自动协商协议 | 人工配置 |\n| IETF TRILL | 减少收敛时间，提高链路利用率 | STP/MSTP |\n| MACSec | 减少二层网络非法攻击，对二层帧进行加密保护 | 无二层的数据保护机制 |\n| FCoE | 以太网融合LAN/SAN/HPC | 前后端异构网络，管理复杂 |\n', 'id': '895850_12'}
{'label': 'text', 'content': '云计算数据中心承载网络\n云计算数据中心承载网络技术\n网络虚拟化技术\n云计算架构的数据中心的特点之一即服务器可以虚拟多个VM（Virtual Machine），多个VM可拥有逻辑上独立、物理上共享的计算或网络资源，不同VM之间可以像单台物理服务器一样进行数据交互。早期服务器内部VM之间虚拟交换通过服务器内部内嵌的软件交换机来完成，服务器之间的交换仍交由外部交换机处理。通过软件处理VM交换的缺点显而易见，会出现CPU处理消耗服务器资源、扩展性差、QoS部署困难等问题。因此引入了包括VEPA（Virtual Ethernet Port Aggregator）、VEB（Virtual Ethernet bridge等在内的虚拟技术，将VM的交换有服务器内部扩展到外部物理交换机中，使得虚拟网络的范围得到扩展，网络能力得到提升。通过更高效的网络虚拟化自动部署，能够对计算网络资源、存储网络资源自动进行调度，随着业务的部署、业务扩展、业务撤销，网络资源能够进行动态扩展，从而使得网络更加高效，网络更加节能。 \nVEB是虚拟网桥技术。VEB使多个虚拟终端可以共享一个网桥端口，是物理终端内的功能模块，实现虚拟终端之间或虚拟终端和外部网桥的数据转发。可以理解为物理终端内置的网桥，可以是软件也可以是内置的硬件。VEB部署在本地终端内，实现本地桥的VM之间的流量交换，如图3-4所示。在数据中心应用中，VM之间的流量交换在server内完成，外部桥设备对于VEB交换的流量与普通以太交换没有差别。 \nVEPA是虚拟以太网端口汇聚技术。VEPA是物理终端内的功能模块，它将源于终端的所有数据发送到外部邻接的网桥来完成多个虚拟终端之间或虚拟终端与外部网络数据交换。由外部网桥实现数据帧的处理和中继。VEPA是VEB的扩展，它简单且成本低，可以较好的解决VEB的不足，并可在一个物理终端内与VEB共存。VEPA适合VM之间流量较大的应用场景，所有VM之间的流量都上送到邻接的控制桥上进行交换，如图3-5所示。VEPA遵循的标准为：IEEE P802.1Qbh，IEEE P802.1Qbg。 \n虚拟化技术有助于打造高效数据中心网络，具有以下优势： \n虚拟化技术将物理资源转化为便于切分的资源池； \n虚拟化给资源以动态调配的能力，使得资源管理智能化； \n网络虚拟化提升网络可靠性； \n虚拟化提高资源利用率，降低网络能源消耗。', 'id': '895850_13'}
{'label': 'text', 'content': '中兴通讯数据中心网络解决方案\n面对数据中心的演进融合，中兴通讯结合自身深厚的研发力量，厚积薄发大力打造精品数据中心网络，基于云计算推出综合解决方案，满足运营商和企业客户业务发展和演进的网络要求，致力于为客户提升商业价值。', 'id': '895850_14'}
{'label': 'text', 'content': '中兴通讯数据中心网络解决方案\n数据中心承载网络方案\n考虑到标准化进展以及服务器、存储现有技术的成熟，同时出于对用户现有投资的保护，我司解决方案建议逐步部署融合网络，在云部署初期，主要部署服务器的整合，服务器I/O的带宽提升，由GE整合至10GE，随着云IDC单点规模的扩大及FCoE的成熟，可以逐步部署FCoE，由部分融合发展为统一Fabric融合网络。 \n如图4-1所示，云计算数据中心网络发展大致分为以下三个阶段： \n一、云计算试点阶段网络方案：\t随着业务软硬件整合，服务器具备虚拟化能力，I/O带宽由GE升级到10GE。网络虚拟化通过VEPA、VEB将VM虚拟化引入到交换机，从而扩展虚拟化规模。高密度的交换设备使得网络具备灵活可扩展性，网络利用率更高效。 \n二、云计算发展阶段网络方案：\t随着云计算业务的展开，网络规模逐步扩大，网络的异构问题越发突出，服务器的I/O进入全后端整合阶段，服务器通过CNA卡承载前后端网络，架顶交换机通过10G FCoE与服务器相连，承载前后端网络流量，同时架顶交换机通过FC端口与传统FC存储网络相连，保护用户投资。融合的架构使得规模不断扩大的云计算网络管理得到有效简化，全面降低OPEX，网络效率得到有效提升。 \n三、云计算全面部署阶段网络方案：随着融合的进一步深入，汇聚、核心设备通过40G或100G FCoE接口进行网络互联，网络从以服务器为中心的网络全面转向以交换机为中心的网络架构，网络融合同时提供自动部署虚拟化能力，使得统一融合的网络具备高带宽互联、全面自动部署网络虚拟化能力，网络维护难度进一步降低，网络得到最有效的应用，网络形成对云计算业务的完全支撑。 \n就现阶段而言，由于网络融合、网络虚拟化等技术仍处于标准制定阶段，尚不具备大规模部署和商用条件，云计算服务的部署也仍处于试点阶段，因此数据中心的网络部署对应于图4-1的发展阶段演进图仍是立足于现阶段，前端LAN网络部署是以三层树型结构为主。如图4-2所示，数据中心大致分为互联网接入层、核心层、汇聚层和接入层。 \n接入层由架顶交换机组成，连接数十台左右的服务器或者内置Blade Switch的服务器网络出口，需要提供高密度GE接入满足多台服务器直连要求，也需要支持10GE接入，连接内置Blade Switch的服务器网络出口。接入层要支持10GE上行，可以与汇聚层通过链路捆绑互联，实现扩展带宽和流量的负载分担。 \n汇聚/核心层是网络的L2/L3分割点，也可以根据网络规模只部署汇聚层。汇聚核心层由汇聚/核心交换机组成，需提供高密度的10GE端口，为接入层架顶交换机提供网络互联；通过10GE/40GE进行汇聚/核心交换机以及路由器互联；汇聚/核心层是二层网络的网关，通过部署VSC、VRRP+MSTP、OSPF+ECMP提供网关备份和流量的负荷分担，也可部署安全模块提供网络安全。 \n互联网接入层由路由器组网，路由器通过10GE/40GE WAN或10G/40G POS与骨干网互联，为LAN网络提供外部互联的出口。', 'id': '895850_15'}
{'label': 'text', 'content': '中兴通讯数据中心网络解决方案\n数据中心网络技术\n中兴通讯从虚拟化、网络互连、可靠性等多个维度着手，基于网络融合、网络虚拟化和高带宽互联等技术，构建高效稳定的数据中心网络。下面分别就这几个方面，介绍一下中兴通讯的相关技术和网络部署建议。', 'id': '895850_16'}
{'label': 'text', 'content': '中兴通讯数据中心网络解决方案\n数据中心网络技术\n虚拟交换集群技术\n网络虚拟化可以按着几个层次进行定义，横向的主要是多变一的虚拟化，传统的虚拟包括链路捆绑或虚拟的VRRP组，新的虚拟技术包括跨设备链路捆绑以及虚拟集群技术；按网络纵向划分的话，主要包括VLAN接入虚拟以及VPN的广域虚拟， L2内的VM交换由于需要网络感知VM，如3.2.3小节所述，需要IEEE P802.1Qbh和IEEE P802.1Qbg等技术的支持。从数据中心网络架构可以看出，VM外部交换的虚拟化技术更适合部署在边缘交换机上，核心/汇聚层面的网络虚拟化仍是以VRRP、链路捆、虚拟交换集群等技术为主。 \n中兴通讯自主创新研发了VSC（Virtual Switching Cluster）技术，VSC可实现设备级的虚拟化，将多台交换机设备通过VSC互联口连接起来，虚拟化整合形成一台更大容量的、虚拟的逻辑设备。VSC系统形成之后，对外表现为一台设备。相对于传统交换机堆栈技术，中兴VSC虚拟交换机集群技术能够把多台物理交换机合并成一台虚拟交换机，可以实现设备N+1备份、主控N+1备份和链路N+1备份，端口扩展更加灵活，设备冗余性提高，并且简化网络配置，具体介绍可以参考《中兴通讯VSC技术白皮书》。 \n如图4-4所示，传统IDC在汇聚层部署VRRP确保汇聚设备的可靠，通常部署主备模式，链路负载不均衡，且通常需要接入的二层网络部署MSTP等避免环路。而VSC将两台汇聚网关虚拟成一台设备，物理设备间自动完成备份功能。通过VSC技术，可以让网络更简单，无需复杂缓慢的生成树(STP)协议，无需运行VRRP协议，多台设备只需配置一次；可以让网络更可靠，支持跨设备的链路聚合，可以实现协议级和设备级的跨设备的热备份；可以让网络更有效，多台设备组成VSC系统，可以有效提高系统容量，并可实现负载分担方式更加充分利用网络带宽。所以在IDC网络中，建议汇聚/核心层部署VSC，提升核心节点可靠性和可扩展性。', 'id': '895850_17'}
{'label': 'text', 'content': '中兴通讯数据中心网络解决方案\n数据中心网络技术\n大二层互连技术\n在一个大的数据中心环境中，多数的网络架构为大二层网络，为了便于大量VM之间的数据交换以及随时可能发生的VM迁移。二层网络架构避免了设备迁移导致的IP地址变化而引起的运维负担。传统二层网络多采用STP等相关技术来解决二层以太网传输引起的环路问题。但STP等技术有其局限性，尤其运用于大规模二层网络中。STP会把整个网络形成一个树形网络，造成原本直连的各个节点之间的通讯路径可能需要绕行长距离，不能实现最优路径转发，增加了传输时延；而数量巨大的二层网络设备会使得通讯路径过长，发生故障时STP收敛较慢；网络拓扑的细微变化就会导致整个网络重新计算，使得网络振荡加大；STP缺乏有效的负载分担机制。 \nIETF引入了TRILL技术，一方面可用来解决二层网络环路问题，另一方面可以提供负载分担的多路径机制，提高链路利用率。TRILL技术运用IS-IS成熟的链路状态协议将二层网络拓扑结构从树状结构变为网状结构，用路由的最短路径等思想解决环路的问题，实现二层多路径负荷分担。TRILL技术在原有二层以太网头上再封装一层TRILL头，需要TRILL域内的网络设备能够识别和转发TRILL封装报文，利用二层网状路径实现单播、多播和广播报文的转发和分发，如图4-5所示。TRILL还引入了路由协议中常用的TTL概念，利用TTL路由逐跳递减的原则，避免形成环路时引发广播风暴。 \n目前TRILL协议本身还是处于草案阶段，尚未展开大规模的商用，但可以看到TRILL技术有助于解决目前MSTP等协议存在的问题，可以作为未来数据中心网络大二层组网的方案之一。在TRILL技术成熟前，我司仍是以结合STP、VSC等技术来实现数据中心网络的二层互连为主，同时平滑支持TRILL。', 'id': '895850_18'}
{'label': 'text', 'content': '中兴通讯数据中心网络解决方案\n数据中心网络技术\n可靠性等相关技术\n数据中心对于网络、设备、链路的都有较高的可靠性要求，以便满足业务不间断、服务永远在线的要求。中兴通讯基于对数据中心的理解和把握，为数据中心网络设计了五方面的可靠性保障机制，分别通过单机多平面、关键部件冗余、软件模块化以及可靠性的协议和进程五个方面保证网络可靠性，从这五个设计维度来提供业务端到端的可靠保障： \n单机多平面主要是指控制平面、转发平面、监查平面的分离，是数据中心网络可靠性保障的基石，多平面分离有助于隔离故障、提高设备可靠性； \n关键部件冗余主要包括主控、业务板、电源、风扇的冗余，通过部件冗余，可以实现故障快速倒换，不影响现有业务的运行； \n软件模块化使得各功能模块、组件之间左右隔开，相互独立，避免各功能模块之间相互影响，实现用户程序动态加载； \n通过NSF/GR、FRR等路由可靠性协议实现路由快速收敛，通过ZESR+、ZESS等链路层协议来实现链路负荷分担，保障业务可靠运行； \n采用进程化管理，支持NSR、ISSU和热补丁，实现设备故障或需要升级时，业务无中断。 \n数据中心现有网络设备，可以根据网络位置考虑支持上述可靠性能力，对于核心/汇聚层交换机，应当有足够的可靠性机制来保障业务不中断运行。 \n除了可靠性之外，数据中心还应该考虑安全性和易维性。对于设备和网络安全性，中兴通讯结合基于网络流量监查技术、DPI/防火墙技术、CPU保护技术等对数据中心网络进行应用层防护、非法信息防控、非法应用等多个层次安全防护。 \n网络易维性可以通过中兴通讯统一网管U31来实现数据中心网络设备的统一管理，对统一数据实现业务端到端的统一管理，提升网路整体运维能力，提高决策分析能力，达到全局资源管理能力，为高效运维提供基础支撑能力，大大减少运维人力成本。', 'id': '895850_19'}
{'label': 'text', 'content': '中兴通讯数据中心产品\n中兴通讯的ZXR10 系列交换机产品线，未来将在开放标准之上，结合以太网光纤通道（FCoE）和IEEE数据中心桥接（DCB）等协议技术，通过万兆无损以太网技术，在统一的交换网络上整合IP、存储和服务器集群网络，提升网络可靠性，简化设备管理工作，从而降低成本，实现更高效的运营，并为数据中心资源带来强大的投资保护能力。基于中兴通讯数据中心承载方案的思想，中兴通讯ZXR10 系列交换机能够整合并更有效地利用以前分散的资源，通过消除不必要的交换基础设施，减少服务器I/O适配器与线缆的数量，节省电力和冷却成本等建设和运营成本，可以高效响应不断变化的业务需求。 \n中兴通讯的ZXR10 8900E核心交换机作为100G平台交换机，可提供48端口万兆接口板，8端口40G接口板，同时可平滑支持100G以太网无阻塞交换，为数据中心提供高密度、高带宽的核心/汇聚交换能力，是业界性能最高的IDC专业交换机。同时，可支持VSC虚拟交换集群等网络虚拟技术，提供完善的全方位可靠性保障，可平滑演进支持无损以太网、FCoE、TRILL等先进特性，支持数据中心网络向云计算架构的平滑过渡。ZXR10 8900E核心交换机可满足数据中心统一交换网络解决方案的要求，有效的解决IT业务建设过程中出现的设备利用率低下，基础设施臃肿等问题，同时还能够保障IT业务的永续性，为企业的高成长保驾护航。', 'id': '895850_20'}
{'label': 'table', 'content': '缩略语\n| 英文缩写 | 英文全称 |\n|:-|:-|\n| FC | Fiber Channel |\n| FCoE | FC over Ethernet |\n| CNA | Converge Network Adapter |\n| DCB | Data Center Bridging |\n| SAN | Storage Area Network |\n| LAN | Local Area Network |\n| IEEE | Institute of Electrical and Electronics Engineers |\n| HBA | Host Bus Adapter |\n| IDC | Internet Data Center |\n| MACSec | MAC Security |\n| IAAS | Infrastructure as a service |\n| PAAS | Platform as a service |\n| SAAS | Software as a service |\n| TRILL | Transparent Interconnection of Lots of Links |\n| VM | Virtual machining |\n| VEPA | Virtual Ethernet Port Aggregator |\n| VEB | Virtual Ethernet bridge |\n', 'id': '895850_21'}
