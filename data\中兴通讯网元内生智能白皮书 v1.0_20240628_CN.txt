{'label': 'document', 'content': '中兴通讯网元内生智能白皮书 v1.0_20240628_CN', 'id': '1162997'}
{'label': 'text', 'content': '中兴通讯\n网元内生智能技术白皮书\n中兴通讯网元内生智能技术白皮书\n| 版本 | 日期 | 作者 | 备注 |\n|:-|:-|:-|:-|\n| V1.0 | 2024/06/19 | 武利明、何力 | 第一版 |\n| | | | |\n| | | | |\n| | | | |\n', 'id': '1162997_1'}
{'label': 'text', 'content': '前言\n在5G时代，通信网络复杂化，原有的静态人工管理方法不再适用，有分析显示70%的重大网络故障是由误操作等人为原因所造成；另一方面，运营商的OPEX已经是CAPEX的三倍以上，并且还在持续上升，因此运营商迫切希望在全云化网络构架基础上引入AI技术，打通从规划部署、到网络维护、到网络运营的数字化全流程；在降低运维成本的同时提升端对端业务部署效率，使能业务快速部署，推动运营商的网络走向全面智能化。 \n国内外运营商和设备厂家都在推动AI人工智能技术在通信网络中发挥巨大的价值，中国移动制定了自智网络分级标准，充分利用大数据信息，实现基于AI的智能网络管理，在2025年实现全网L4级的目标。 \n其中网元设备具备内生智能，成为实现网络全面智能化的重要环节，网元内生智能与网络智能协同，实现网络智能的整体闭环。', 'id': '1162997_2'}
{'label': 'text', 'content': '网元智能背景\nTMF、CCSA等行业组织结合AI+大数据从整体网络层面提出了自智网络概念。图1为TMF提出的自智网络架构： \n从图1自智网络架构图中可以看出，整个自智网络包含业务自智、网络自智和领域自智。网元智能位于领域自智内，具有独立自闭的内部结构，主要从运维自动化、智能化方面支撑自智网络，是自智网络的重要组成部分。从场景分析来看，网元智能需要加强自身组件和运行状态的深度感知，提升单节点高可靠性和自优化能力，并在软件部署、数据配置、网络测试等方面开发自动化操作能力，具有可感知、高可靠、自优化和可操作的特点。\n中移在自动驾驶网络白皮书中也提出“四层三闭环”的自动驾驶网络架构，主要从设备检测、资源配置、业务保障、业务优化、业务测试等维度对网元智能化提出要求。中移在光网络人工智能(AI)应用白皮书中要求网元级内生AI实现高性能分析预测、智能决策，与管控系统网络级AI进行功能互补，SPN/OTN设备能力分级表中对网元L4能力提出了明确要求。 \n如图2所示，ITUT-SG13定义了网元设备以及网络ML/AI系统架构，其中SG13架构通过定义接口，组件，参考点等概念实现ML智能化的PIPE-LINE流程。 \n参考ITUT-SG13系统架构，从数据产品定位和AI应用场景看，相对于云端的AI学习和训练，网元产品是边缘智能设备，主要实现AI推理功能。 \n2024年世界移动通信大会（MWC 2024）成立了“AI-RAN联盟”，AI-RAN指“人工智能（AI）-无线接入网（RAN）”，是一个旨在重振人工智能与无线通信融合、引领技术创新的行业协会组织，使命是提高移动网络效率、降低功耗并改造现有基础设施，为在5G和6G的推动下利用AI释放新的经济机会奠定基础。', 'id': '1162997_3'}
{'label': 'text', 'content': '网元智能整体架构\n架构设计总体\n中兴智能化网络整体由三层架构组成，包括基础设施(网元智能) ，管控系统(网络智能)和能力开放，与上层系统及应用生态进行业务协同（跨域智能）。网元智能与网络智能协同进行，网元智能主要负责高实时性和域内AI应用，网络智能主要负责网络层面大规模数据的AI应用。 \n综合当前数据产品，如路由器、SPN、OLT，交换机等网元设备的智能化需求，实现了网元智能化基础架构平台，这个平台能够适配不同网元设备软硬件环境，以及不同AI应用需求，同时满足智能化数据采集，数据管理，AI算法模型以及算力的需求。 \n网元智能基础架构作为一个公共平台，如图3所示，包括一系列智能组件，与网元其他业务功能组件协作完成AI应用需求，如下视图所示，AI基础架构包括网元数据采集，KPI管理中心，网元AI推理引擎，AI算法模型管理，智能策略动作管理等组件。 \n网元数据采集：与产品业务进程以及数据服务端协作实现样本数据的采集，包括KPI、告警、以及日志等。 \nKPI管理中心：基于网元内时间序列数据库AI SDB，实现数据样本地存储与预处理，以及KPI的异常检测与趋势预测；AI SDB为不同AI应用提供基础数据管道，AI应用不感知数据的来源，通过向AI SDB订阅机制获得样本数据，AI应用可以灵活定制感兴趣的数据类型，特征维度，以及数据推送周期等，满足不同AI应用对数据样本的特性需求。 \nAI算法模型管理：实现AI推理算法模型版本的管理，与云端协同，实现AI算法模型的运程下载与更新。 \nAI推理引擎：部署了AI推理框架,实现统一的AI算法接口，对AI应用屏蔽本地软件算法模型以及芯片AI加速推理的接口差异，同时满足高实时性AI推理以及低实时性AI推理的需求。 \n网元智能策略动作：实现对AI应用策略动作的整体监控，保证AI应用的安全部署以及执行。 \n不同的AI应用根据应用场景确定数据源，AI算法模型，以及策略动作，利用AI基础架构提供的服务完成具体的AI应用。', 'id': '1162997_4'}
{'label': 'text', 'content': '网元智能整体架构\n智能基础服务\n通信网元内数据类型众多，包括KPI/日志、告警，以及转发面的异常统计等，主要包括如下类型：\n（1）NP网络处理器统计信息：包括端口收发速率，端口不同字节长度报文速率，TCP、UDP以及ICMP等报文变化速率，NP异常丢包统计等。\n（2）QOS TM队列统计信息：包括队列深度变化统计，队列转发报文速率，队列丢弃报文速率。\n（3）流统计信息：基于ACL的流统计信息，包括TCP SYN、ACK、FIN以及RST报文变化速率。\n（4）告警日志统计：包括系统运行过程中不同业务的的告警和异常，转发表错误告警，NP芯片寄存器异常告警，协议交互异常告警以及关键事件日志。\n（5）协议报文统计信息：网元设备收到的二三层协议统计信息。\n（6）硬件相关信息：各个硬件模块相关指标。\n（7）业务定制指标：根据特定业务定制采集的数据。\n不同AI应用关注的数据类型差异较大，AI应用通常需要对不同空间和时间维度的原始特征数据进行融合处理，才能满足AI应用的数据样本需求，传统上的AI推理架构通常和具体AI应用数据强耦合，无法把数据采集管道与应用实现解耦，不利于AI应用的跨产品跨平台部署。\nKPI管理中心提供了通用的数据采集接口，将KPI数据管理与数据采集通道解耦，不同数据源采集的数据均统一上送到AI SDB进行管理，对于不同的AI应用只看到AI SDB，不与具体的数据源进行交互，有利于跨产品快速部署。\n数据采集模块面向不同的数据源，定义的接口与数据格式均不相同，如下表1定义的统一数据格式：\n| 订阅数据集 | 说明 |\n|:-|:-|\n| SDB table-id | 对应AI SDB的Table-id |\n| Date | 数据采集日期 |\n| Time | 数据采集时间，1588同步PTP时间格式 |\n| Period | 数据采样周期，支持秒，毫秒级别 |\n| Slot | 单板，槽位信息信息 |\n| SourceType | 数据源类型（NP、TM，ACL，告警日志等） |\n| Entry Id | 数据实体ID（接口，队列，ACL，告警日志ID等） |\n| feature_1 | 特征值，采用TLV格式 |\n| . . . | |\n| feature_n | 特征值，采用TLV格式 |\nAI SDB为AI应用提供如下通用功能:\n支持KPI数据的多周期历史数据的存储功能。\n支持定制的特征预处理方式,如缺失值，归一化，最大最小值等。\n支持样本数据行列统计方式设置，支持统计周期设置，支持MAX、MIN、AVG统计值。\n自动支持跨表统计数据同步和聚合。\n注册感兴趣的AI SDB数据源，支持AI应用与AI SDB数据源表多对多的管理关系。\n支持AI增量训练的前置条件，包括时间序列数据节点数量。\n配置时间序列融合方式，支持对不同周期数据融合时进行时间对齐处理。\nKPI管理中心支持通用的异常检测与预测功能，集成多种时序预测算法模型，对于不同模态采用不同数据处理方式进行整理分析,主要包含如下算法模型：\nKPI异常检测：包括时序数据的预测，关联分析（端口流量关联等），进行异常检测（端口流量、CPU利用率异常）。\n日志异常检测：对于日志加工处理，给出日志模块的分析结果，包括日志解析、日志异常检测与根因分析。\n离散事件管理中心：对于告警的处理，以及一些统计表项、flag状态等记录。\n流量基线数据库：对流量维度数据进行收集处理，支撑防攻击、QOS等APP。\nQOS服务质量监控：收集QOS相关定制化特征、队列信息，支撑服务质量感知、流量模型等APP。\n', 'id': '1162997_5'}
{'label': 'text', 'content': '网元智能整体架构\n算力架构设计\n网元智能化架构，需要满足AI应用的算力需求，算力主要对两方面有要求，即数据采集性能与通道带宽要求，以及AI芯片的计算能力要求。 \n数据采集性能与通道带宽要求 \n数据采集性能要求：单位时间内支持数据采集的吞吐量，以及数据采集精度，通过telemetry进行数据采集，需要支持10ms，100ms的采集精度。 \n采集通道带宽要求：即AI芯片和转发芯片之间的数据通道带宽，通道带宽需要满足AI应用每秒发起的推理次数（推理吞吐量）要求。 \nAI芯片的计算能力要求 \nAI推理能力：网元AI应用，通过AI推理结果做出决策行为，对于深度学习，AI推理计算量较大，直接影响AI应用的吞吐量。 \nAI训练能力：实现在线AI增量训练和学习，全量离线训练主要放在管控端。 \n网元设备硬件架构包括分布式与集中式两种架构，对于分布式架构，主控主要实现控制面的功能，线卡主要实现业务转发功能，综合当前网元智能的性能需求，如图5所示，通过在主控上增加FPGA或者AI芯片，实现高性能的AI计算，在架构设计上线卡和主控之间通过高速telemtery进行数据采集，保证通道带宽，满足高实时性的AI推理。 \n对于部分AI应用，需要实现在线增量的AI应用训练，这部分功能由AI芯片完成训练后更新AI模型，对于规模较大的离线训练，主要由管控或者云端进行训练，训练完成后将模型下载到网元。 \n对于高实时性AI应用，转发面NP芯片需要支持10ms，100ms的telemetry的采集精度，每秒需要处理的telemery数据报文数为10K-100K左右，现有的主控CPU架构无法处理这个数量级的telemetry数据，需要通过AI芯片增强数据的特征处理能力，以及集成telemetry的封装与处理能力，如图6AI芯片架构设计。 \nAI芯片与NP之间互联2个10GE接口，其中一个10GE接口用于高速telemetry数据通道，另外一个10GE端口将AI芯片的推理结果封装报文返回给NP。 \n对于部分吞吐量要求不高的AI应用（智能运维），可以通过CPU实现KPI多源数据的融合，CPU处理非实时性KPI，AI芯片负责推理以及增量AI训练和学习。 \nCPU、内存与AI芯片通过PCIE网桥互联，数据通过DMA通道完成，包括KPI性能数据交互，以及AI芯片推理数据的返回，主要读写操作都是在内存上。', 'id': '1162997_6'}
{'label': 'text', 'content': '网元智能整体架构\n智能应用监控\nAI应用根据推理的结果，调用设备内相关模块提供的接口，执行相关的策略动作，策略动作需要综合考虑AI推理结果，以及当时的系统环境，因此AI策略动作需要维护一个“动作分发矩阵”，结合推理结果、各个模块上报的状态信息和其他元数据，进一步分析才能得出实际分发的动作策略，当AI推理结果或者系统环境发生变化时，需要及时改变动作策略，进入或者退出策略动作。 \n网元智能支持智能节能，智能防攻击，智能无损，智能运维以及智能QOS，执行策略动作有如下几种类型： \n智能节能：实现对交换板SFU，业务处理单板PFU以及交换总线Serdes的关断动作。 \n智能防攻击：实现对特定攻击流的溯源与限速丢弃功能，需要联动ACL和QOS模块配置攻击过滤规则和限速资源。 \n智能无损：实现对数据中心流量的优化，达到吞吐量与时延抖动最优，需要联动QOS模块实现对指定队列的缓存深度以及ECN水线进行配置修改。 \n智能运维，实现对特定业务故障分析，需要联动管理平台给出告警，联动业务模块给出故障原因代码以及联动业务模块进行故障恢复动作。 \n智能QOS：实现对广域网流量的优化，达到吞吐量与时延抖动最优，需要联动QOS模块实现对指定队列的缓存深度或者WRED参数进行自动优化。 \n策略动作统一由AI应用调用，策略管理模块对AI应用提供统一的调用接口，调用接口包括策略动作实体ID，动作类型，动作类型执行时间，动作内容等。 \n对于多个AI应用，如果与AI基础框架在同一个进程中，可以共用同一个策略应用实体，为了向策略应用实体屏蔽具体动作下发来源，统一接口模式，策略应用通过动作适配器进行适配，由动作适配器转发至策略应用实体，整体架构如图7所示。', 'id': '1162997_7'}
{'label': 'text', 'content': '中兴网元智能实践\n中兴网元智能在多个AI应用领域进行实践，其中包括智能节能、智能运维（硬件劣化预测、故障定位、异常监控识别、业务体验）、主动安全、流量调优等智能应用场景，已经发布商用版本并在工程上得到了商用推广。', 'id': '1162997_8'}
{'label': 'text', 'content': '中兴网元智能实践\n智能节能\n5G时代，海量接入以及大带宽的需求，对于承载网设备也意味着在设计、建设以及运维方面，都要做到能耗优化和绿色节能，当前主要节能策略是通过预先设置好的流量阈值参数，或者固定时间段控制节能开启或者退出节能，无法根据业务流量负载和外部环境的变化，动态响应调整设备的节能策略。 \n网元产品设备，其功耗总类型体分为两大类，静态功耗和动态功耗。 \n静态功耗在设备开启上电后就一直存在，即使没有流量也有固定的功耗，不随负荷而变化，静态功耗的主体是网元设备的各种硬件单元和芯片，硬件上包括机框，单板，子卡，物理端口，光模块，电源，风扇，交换网等，芯片包括NP、SF、FPGA、CPU、TCAM、TM等芯片。 \n网元动态功耗则随着流量负荷增加而上升，动态节能主要策略是识别网元设备的流量变化趋势，通过AI人工智能技术，对已有流量历史数据进行训练、学习与趋势预测，区分网元设备的业务场景，对负荷较低的网络设备出合适的节能动作，动态降低网络元设备的整体功耗，大幅减少电力消耗和能源浪费。 \n从流量波形结构看，一般情况下，流量可以分解为三个主要成分成：增长趋势、周期趋势以及节假日，对于智能节能来说，主要是通过AI算法预测流量中的趋势部分和周期部分，找到流量低负荷的时间周期，从而进行相应的节能策略。 \n流量场景识别，主要是识别流量相对稳定状态，对于存在异常突发流量场景，节能操作存在可靠性问题，通常不进行节能动作。 \nAI智能节能算法模型主要是流量预测，流量预测有多种算法模型。 \n一种是基于序列特征的预测方法，包括Arima、Prophet等时间序列算法。这类算法基于时间序列进行建模和预测，这类算法是线性模型，适合短期内而且平稳时间序列的预测。 \n另外一种是回归预测方法，即采用AI等机器学习技术，包括RNN/LSTM神经网络、基于历史负荷数据建立回归模型来达到预测未来负荷的目的，适合非平稳变化，复杂的非线性网络流量。 \n节能策略的的执行需要关闭部分空闲的硬件资源，从而达到降功耗的作用，对于网元设备，如图9所示，有下几种可硬件关断操作，关闭线卡Serdes总线，关闭交换板SF，物理端口休眠，关闭线卡等，AI智能节能整体硬件动作如下图所示。 \nAI智能降功耗通过关断空闲的硬件器件来实现降功耗的目的，但在实际部署中，需要在降功耗的效果与硬件动作的安全性间取得平衡，主要有如下原则： \n不同流量场景采用不同节能策略，具有一定的流量突变的容错空间 \n全面的异常检测与识别机制，快速响应异常事件，及时退出节能，保证业务安全 \n尽量减低硬件动作的频次，降低硬件频繁关断带来的故障风险 \nAI智能降功耗的核心是流量预测，通过对网元设备长短期流量趋势的分析与预测，可以识别出各种流量场景，针对不同的流量场景的特性，判断是否满足节能准入条件，对于长期相对稳定，周期性特征比较明显的流量场景执行进行动态节能，对于流量随机波动以及突发较大的流量场景，则相对保守，不执行节能策略。 \n为了提升AI算法的准确性，并对风险进行评估，实际部署中综合使用多种时序算法进行预测，通过调整不同算法的权重来让预测趋势更加准确，以此应对流量场景的变化。此外， \n在实际部署中，对硬件的上下电动作进行监控，当检测线卡或者交换板异常上下电后，需要选择是否退出节能状态。', 'id': '1162997_9'}
{'label': 'text', 'content': '中兴网元智能实践\n智能防攻击\n内生安全是网络5.0五大核心技术之一，其中6G、天地空一体化在安全方面明确要求网络内生安全设计。中国移动将内生安全作为“三层四面”的6G主要技术特征之一，强调智能共识、智能防御、可信增强、泛在协同，在IMT-2020/CCSA开展内生安全研究和标准制订。 \n未来网络安全和6G中设定了安全的演进目标为内生安全，并将其作为网络演进的核心目标之一，要求在2025年前形成补丁+内生安全的未来网络系统。 \n在网络安全中，恶意攻击变种众多，隐蔽度高，目前防火墙通过“特征匹配”去分析和检测威胁的方式已无法有效应对，AI 技术能够解决传统防火墙静态规则引擎的弊端，从而加强威胁检测能力，并通过自动化的方式的对威胁进行处置，将AI 技术内嵌到网络设备中来增强设备主动安全能力，基于AI的主动安全的有如下技术特点： \n在线学习：能够追踪业务变化与趋势，对合法流量进行建模，评估误报风险。 \n异常检测：检测流量中的异常内容， 快速发现，即时响应。 \n自动生成规则: 针对异常请求快速生成防护规则。 \n分析攻击行为: 对攻击行为进行特征提取及建模，以应对伪装成合法请求的攻击行为。 \n根据用户使用智能效果，自动优化防护模型，一步步迭代，以提供更精准的防护策略。 \n中兴网元智能防攻击结合数据产品情况，主要面向如下两类攻击问题： \n对网元设备的路由协议进行DDOS攻击，目的是中断路由协议的正常服务，导致网络路由转发故障，尤其是核心路由器被攻击瘫痪后，会导致大范围的网络中断，后果严重。 \n对客户网络或者应用服务器发生DDOS攻击，导致应用服务器与客户网络服务中断，对特定的用户群体造成拒绝服务。 \n运营商虽然有专业的抗DDOS设备，但是一般只部署在较高的位置上，比如城域或者骨干网核心节点，无法检测到大量的接入域汇聚层的发起的网络攻击，尤其是5G业务的展开，大量的物联网设备成为网络攻击的发起端。 \n网元内嵌智能防攻击功能，能够提升网络整体的安全防护能力；接入路由器具备一定的抗DDOS攻击能力，能够离大客户最近的出口设置网络保护域，对其出口带宽进行抗DDOS保护，实现快速检测与清洗，服务提供商则可以通过定制DDOS服务带来相应的增值收益，向企业网络提供更多的安全服务。 \n中兴基于路由器产品基于实时数据采集与转发面流自学习，基于流级别建立了多维度流量基线数据库，并通过AI人工智能算法模型，对复杂的网络攻击流量特征进行分析，实现对网络攻击事件的检测，溯源以及清洗相关的技术，整体方案架构设计如图10所示： \n在路由器网元中嵌入智能防DDOS攻击功能，通过多个组件实现DDOS攻击缓解，设备支持多种攻击缓解动作，如图11所示： \n通过与外部的网络清洗设备协作完成流量的转移、清洗，以及注入 \n通过发布BGP FlowSpec路由与上游远端入口路由器联动，实现对DDOS攻击报文的限速，丢弃 \n设备在网络保护出口自动生成DDOS清洗策略，在近端实现对DDOS攻击报文的清洗。 \n网元通过多种技术方式实现动态流量基线学习，即学习正常情况下的流量模型，正常情况下流量统计特征和源IP分布特性先对稳定，当流量模型发生了变化，偏离正常基线并超出了阈值，表示发生了DDOS攻击。 \n当发生DDOS攻击后，短时间流量统计特性与IP地址的分布则会发生剧烈变化，当发生DDOS攻击后，会综合信息熵，DFI流量统计，异常流量识别多个指标进行加权判断，增加检测识别的准确性。 \n流量基线数据库在每个检测周期不断地更新的过程中，会进行四种级别的检测： \nglobal级别检测：从全局的特征角度进行检测，包括基数、熵、流数、整体流速等特征，并且综合server和dip异常信息进行综合判断。（当前周期和过去周期进行对比） \n检测结果：异常分数，ddos攻击发生标识。准确度高，误报极少。 \nserver级别检测：从单个server角度进行检测，包括基数、流数、流速等特征进行判断。（当前周期和server历史表现进行判断） \n检测结果：受害server，准确率较高，但是波动较大服务可能存在误报。 \ndip级别检测：同server，从单个dip角度进行检测，包括基数、流数、流速等特征进行判断。（当前周期和dip历史表现进行判断） \n检测结果：受害dip，准确率较高，但是波动较大服务可能存在误报。 \nflow级别检测：从流特征的角度进行判断，一般ddos攻击流第一次出现。检测策略上首个周期利用包括tcp flag和流速特征。后续周期依靠流式上升或tcp flag持续异常。 \n检测结果：异常FlowSet，误报率高，大流和正常流tcpflag被采样时出现假阳性，但是假阳性的比例不会太高，因此flowset需要整体判断。 \n当综合模块触发了全局检测后，会进行DDOS攻击类型的简单分类，根据分类来掉调用对应的溯源分类模块，最后根据溯源结果结合分类类型来触发不同的清洗动作。 \n对于协议防攻击，在流程上与转发面防攻击是统一的，主要是基于dip以及flow的级别进行监控，当去检测到协议攻击后，执行相应的清洗策略。', 'id': '1162997_10'}
{'label': 'text', 'content': '中兴网元智能实践\n智能QOS\n广域网业务复杂，运营商总体划分为2C与2B两大类业，2C业务面向用户，主要是eMBB大带宽业务，家庭宽带，移动上网，以及视频点播等业务为主；2B则面向大客户，智能化应用等场景，主要是mMTC与uRLLC,对时延抖动有确定性的要求，中兴路由器产品通过在设备内集成智能QOS功能解决广域网当前面临的如下问题： \n流量突发丢包严重：对时延抖动影响较大，微突发主要由HTTP业务，视频业务（直播、点播）业务引起。由于TCP的间断式连续发包会造成短时的突发，导致队列拥塞丢包，结果会对高清视频业务会造成花屏，卡频，跳跃等场景。突发通常会导致多个数据包从同一流中丢弃，导致流的全局同步减小流，然后是链路利用率的持续降低，从而降低总体吞吐量，因此吸收突发等同于提高吞吐量。 \n时延抖动不可控：2C/2B业务通常在转发面映射为DiffServ，不同等级的业务流量通常混跑，增大时延，存在同优先级之间，以及优先级内部不同业务流的相互干扰。 \n中兴智能QOS架构如图13所示，基于流量模型学习组件，智能优化QOS参数，满足不同场景对时延，以及吞吐等SLA指标的要求， \n智能QOS架构，实现括流量建模与QOS参数自动优化两个核心功能，其中流量建模是QOS参数自动优化的前置条件。 \n流量建模通过对QOS队列实时流量数据采集与学习，输出如下流量模型： \n微突发的检测与预测：实现流量长短期波动的检测与学习，建立流量基线，输出微突发最大规模，持续时间 \n时延检测与预测：实现队列时延变化的检测与学习，其中包括最差时延与平均时延 \n大小流分布：通过流自学习技术，实现队列大小流分布模型的学习 \n在实际运行中，通过多个周期完成流量建模后，将输出的流量模型作为QOS参数自动优化功能的输入，执行QOS自动化的参数设置策略，策略动作包括时延性能优化与吞吐性能优化两部分，动作结果反馈回流量建模，当队列吞吐和时延达到优化目标后，则优化终止，整体架构如图14所示：', 'id': '1162997_11'}
{'label': 'text', 'content': '中兴网元智能实践\n智能无损\n随着5G时代的到来，以及数据中心内以大模型，AI/ML计算，分布式存储，在线数据密集服务将成为主流应用，网络运营商维护了一个运行 RoCEv2 协议的大规模（数百到数千台服务器）、高带宽（25Gbps及以上）、低延迟（微秒）的 RDMA 网络。 \n一典型的数据中心交换机组网如图15所示，由于数据中心广泛存在的分布式计算与分布式存储的设计，会出现多个服务器同时向一个服务器传输数据的多打一现象，这使得在出口交换机处，接收数据总流量大于端口发送总流量，即会在该出口处出现报文拥塞丢包，严重降低链路的时延和吞吐性能。 \n数据中心网络采用ECN（显拥塞Notification）机制进行流量速率控制，指流量接收端感知到网络上发生拥塞后，通过协议报文通知流量发送端，使得流量发送端降低报文的发送速率，从而从早期避免拥塞而导致的丢包，实现网络性能的最大利用，有如下优势： \n所有流量发送端能够早期感知中间路径拥塞，并主动放缓发送速率，预防拥塞发生。 \n在中间交换机上转发的队列上，对于超过平均队列长度的报文进行ECN标记，并继续进行转发，不再丢弃报文。占比避免了报文的丢弃和报文重传。 \n由于减少了丢包，发送端不需要经过几秒或几十秒的重传定时器进行报文重传，提高了时延敏感应用的用户感受。 \n与没有部署ECN功能的网络相比，网络的利用率更好，不再在过载和轻载之前来回震荡。 \n数据中心服务器主要部署RDMA协议，满足高吞吐量、超低时延和低CPU开销的需求，通过DCQCN拥塞控制算法需保证数据中心环境下的网络零丢包和可靠传输，可以提供较好的公平性，实现高带宽利用率，保证低的队列缓存占用率和较少的队列缓存抖动情况。DCQCN只需要可以支持WRED和ECN的数据中心交换机（市面上大多数交换机都支持），其他的协议功能在端节点主机的网卡上实现。 \nDCQCN算法由三个部分组成： \n交换机（CP，congestion点） \nCP算法中，如果交换机发现出端口队列超出阈值，在转发报文时就会按照一定概率给报文携带ECN拥塞标记（ECN字段置为11），以标示网络中存在拥塞。&SEP9；标记的过程由加权随机早期Detection）功能完成。 \nWRED是指按照一定的丢弃策略随机丢弃队列中的报文。它可以区分报文的服务等级，为不同的业务报文设置不同的丢弃策略。WRED在丢弃策略中设置了报文丢包的高/低门限以及最大丢弃概率，（该丢弃概率就是交换机对到达报文标记ECN的概率）。并规定： \n当实际队列长度低于报文丢包的低门限值时，不丢弃报文，丢弃概率为0 \n当实际队列长度高于报文丢包的高门限值时，丢弃所有新入队列的报文，丢弃概率为100% \n当实际队列长度处于报文丢包的低门限值与高门限值之间时，随机丢弃新到来的报文。随着队列中报文长度的增加，丢弃概率线性增长，但不超过设置的最大丢弃概率。 \n接收端（NP，notification点） \n接收端NP收到报文后，发现报文中携带ecn拥塞标记（ECN字段为11），则知道网络中存在拥塞，因此向源端服务器发送CNP拥塞通知报文（Congestion通知Packets），以通知源端服务器进行流量降速。 \nNP算法说明了CNP应该什么时间以及如何产生：如果某个流的被标记数据包到达，并且在过去的N微秒的时间内没有相应CNP被发送，此时NP立刻发送一个CNP。网卡每N微秒最多处理一个被标记的数据包并为该流产生一个CNP报文。 \n发送端（RP，reaction点） \n当发送端RP收到一个CNP时，RP将减小当前速率Rc，并更新速率降低因子α，和dctcp类似，并将目标速率设为当前速率，更新速率过程如下： \nRT=Rc \nRc=Rc*（1-α/2） \nα=（1-g）*α+g \n如果RP在K微秒内没有收到CNP拥塞通知，那么将再次更新α，此时α=（1-g）*α；注意k必须大于N即K必须大于CNP产生的时间周期。进一步，RP增加它的发送速率，该过程与QCN中的RP相同。 \n中兴在数据中心交换机上实现了数据中心智能无损功能，算法模型基于网元智能化架构,基于强化学习与启发式算法,通过实时监控交换机队列的拥塞情况，通过动态调整ECN水线，实现零丢包，吞吐与时延最优，满足不断增长的大模型算力需求。 \n算法流程描述如图16所示：', 'id': '1162997_12'}
{'label': 'text', 'content': '中兴网元智能实践\n智能运维\n算法方案\n基于网元AI基础架构，实现了网元智能运维功能，提供了多维KPI根因定位，以及网元知识图谱故障RCA定位系统两个核心算法逻辑，多维KPI根因定位与知识图谱作为算法平台，可以充分支持不同业务领域故障诊断，缩短新业务的开发周期。 \n多维KPI根因定位：实时监控网元KPI，当发生异常时，实时给出故障根因分析，打分以及排名，历史原因可查看回溯 \n网元知识图谱：网元内部根据业务内部故障依赖于传播关系形成的逻辑知识图谱，更精确定位故障原因 \n算法首先对于关键KPI进行监测，关键KPI的异常触发根因分析整体流程： \n时序KPI数据被异常检测算法统一为异常事件，与告警统一到同一离散空间中，以异常事件链形式存在。 \n异常事件链基于改进TF-IDF算法计算异常事件的惊奇度。 \n异常事件的惊奇度作为分数填充知识图谱，并利用随机游走算法定位到根因上。 \n同时端到端的分类模型直接对根因进行分类，集成增强根因分析效果。 \n多维KPI根因定位算法模型模型，提出了KPI“惊奇度”的概念，主要思想是，通过计算KPI的惊奇度指标实现对KPI异常时故障根因定位，算法逻辑，需要从历史窗口中的多个异常中找到最重要的异常事件，这与文档中关键词抽取的思想高度相似，把异常事件类比为单词，把异常时间窗口类比为文档，过去多个时间窗口对应语料库。 \n知识图谱成为最优的选择，即能增强网元内的业务领域故障诊断知识，又能基于动态寻优算法适应不断变化的故障类型。 \n故障知识图谱的方案，根据专家知识或历史经验，将可观测指标对应到真正诊断的根因之中，知识图谱表示KPI之间，以及KPI与RC之间的故障传递关系，在知识图谱中，根据节点类型分为了可观测指标层、隐变量层和根因层。 \n可观测指标层：中表示异常事件之间发生时的因果关系，上游节点的异常会可能会导致下游节点的异常发生。 \n隐变量层：可以辅助指标层和根因层链接之间的关系。 \n根因层:表示最终输出的根因结果，可以是人工定义的RC事件, 此外KPI本身也可以是根因，如图可观测指标层中部分KPI出现自环，代表本身可能成为根因的异常事件。 \n在算法逻辑上，使用惊奇度指标作为可观测层节点分数，利用专家知识定义边节点，利用随机游走的思想给根因层节点进行赋分，越接近真实根因的异常指标越多、越集中（由边权重反映），同时异常指标重要度越大对应搜索的出的根因分数越高（由节点权重反映）。 \n网元内嵌知识图谱是基于历史故障经验总结出的专家知识，在设计上采用了分布式架构，将设备的故障知识图谱划分为不同的领域，不同领域之间建立故障根因传递关系，当发生故障时，可以在领域内以及领域间并进行动态寻优算法，定位故障根因。', 'id': '1162997_13'}
{'label': 'text', 'content': '中兴网元智能实践\n智能运维\n故障诊断应用\n转发面丢包故障诊断\n设备在转发过程中，存在各种丢包原因，基于多维KPI根因定位和故障知识图谱，可以有效识别出转发丢包的具体原因，通过监测网元端口流量的异常变化（突升和突降）进行故障定位，可以识别出本网元的丢包根因，也可以一定程度上识别出其他网元的故障。 \n转发面丢包点分析： \n对于分布式设备，业务在转发过程中会经过NP（PPU），TM，SA/SF等多个芯片，其丢包可以分为入向NP丢包，入向QOS丢包，交换网丢包，出向NP丢包，出向TM丢包几个环节。 \n转发面丢包，具体原因有可能是控制面异常导致的转发丢包，比如表项下发一致性问题，协议异常断链，串表索引不一致问题等，结合知识图谱识别更深层次的原因 \n通过监测网元端口流量的异常变化，结合多维KPI根因定位和故障知识图谱，可以有效识别出具体转发丢包的原因，以如下组网图为例，当Router-2的端口流量发生异常后，可以识别出本网元的故障根因，也可以识别出其他网元的故障根因。', 'id': '1162997_14'}
{'label': 'text', 'content': '中兴网元智能实践\n智能运维\n故障诊断应用\n光模块与光纤故障诊断\n光模块故障在网络故障中占比高，故障根因多，故障排查耗时费力，通过光路故障诊断，快速准确定界，提升运维效率。出现光路故障时，获取故障发生前后的光功率数据，结合算法模型，给出故障发生的原因或位置。 \n网络实时监测光功率，一旦光功率低于预设阈值，就会触发失光告警，并且自动保存告警前后的光功率数据。 \n然后，这些告警信息和数据会被上报至KPI管理中心，用于后续的故障诊断与分析。接收到数据后，KPI管理中心会对其进行预处理，例如填补缺失值，进行数据归一化等，然后进行特征工程，包括增强实际信号传输过程的特征和提取有助于故障诊断的各种特征等。 \n光模块故障诊断AI应用订阅光功率告警和特征数据，这些特征被用于训练机器学习多分类模型，如果精度满足要求，则输出离线模型。后续可通过离线模型推理故障类型，包括尾纤松动、尾纤中断、光缆单芯中断等情况。 \n网元将设备内内的故障诊断结果上送到管控，管控根据网元的推理结果和网络拓扑信息再综合判断，例如，如果多个设备报告同一光缆单芯中断，系统会判定为光缆发生了中断。 \n具体流程如下： \n1、实时采集：基于AI基础框架实现光模块数据的定时采集（10ms），采集光功率以及特征数据 \n2、故障分类推理：网元离线完成光模块数据的算法模型训练并加载到设备中，当检测到光模块LOS后，进行在线推理实现故障定位定界。', 'id': '1162997_15'}
{'label': 'text', 'content': '中兴网元智能实践\n智能运维\n故障诊断应用\n1588时钟故障定位定界\n数据产品1588时钟同步网络连接大量通信设备，并且和各个软硬件模块密切相关。当网元发生时钟同步故障时，需要投入大量人力和时间进行故障定界，时钟故障主要涉及网元定界与故障模块定界。 \n网元定界：故障发生时通过日志数据提取时序特征，送入分类模型判断当前故障属于本网元还是上下游网元。 \n故障模块定界：当确定网元侧发生故障时，收集时钟日志数据，利用智能化架构进行解析，送入分类模型，判断故障模块。 \n时钟故障定位定界，主要基于网元智能化架构，包括实时数据分析与在线推理分类，以及故障模块定界。 \n实时数据分析：1588故障诊断应用向KPI管理中心订阅网元时钟运行日志，利用日志解析算法从中提取大量多组时间戳序列。 \n在线推理：利用时序分析 算法从时间戳序列中提取能表征时钟同步业务状态的多维指标，利用 聚类算法对相似信息进行聚合，通过 SVM 等分类算法学习异常值边界，判断时戳是否异常。 \n故障模块定界：结合知识图谱确定可能的交换芯片异常、线卡逻辑异常、主控逻辑异常、线卡硬件异常，上游设备异常等，通过和业务模块交互确定更细粒度的故障原因。', 'id': '1162997_16'}
{'label': 'text', 'content': '中兴网元智能实践\n智能运维\n故障诊断应用\nBRAS保活失败故障诊断\nBRAS业务用户上线成功后，如果长时间没有用户流量后，需要进行会话保活，实际运行过程中会话保活可能失败，但是保护失败的原因有可能并不是用户下线，而是设备原因或者网络原因导致会话协议报文交互流程无法正常进行。但是从业务角度，用户并没有下线，因此需要在保活失败的情况下，进行准确根因分析，如果是设备自身或者网络故障导致的保护失败，则尽量保持转发面的会话表不老化，避免影响用户业务。 \nBRAS保活失败的原因通常会在KPI异常、日志异常以及告警中体现出来，比如保活报文攻击，网络故障等，通常会在多个KPI指标上反映出来，并结合和BRAS故障相关的知识图谱，可以快速定位到具体的故障原因。 \nBRAS用户侧网络故障发生时，BRAS与OLT/交换机侧没有发生链路相关故障，但是和USER_1相关的端口流量急剧下降，BRAS无法接受USER_1的保护协议报文，而BRAS网元不存在协议丢包异常，因此经过KPI异常打分，以及BRAS故障知识图谱，确定用户侧网络中断是最有可能的故障原因。', 'id': '1162997_17'}
{'label': 'text', 'content': '总结\n网元内生智能是网络智能化的重要组成部分，随着大模型以及高算力需求，算网一体对数据产品在各个业务领域都有迫切的智能化落地需求；运营商可以通过网元智能化可以带来新的AI业务运营模式，提高运营效率，并为移动以及数据中心用户提供新服务，实现新的营收增长机会。 \n中兴数据产品在网元智能化方向进行了深入研究，基于网元的软硬件特性实现了创新的智能化架构，提供了一系列提升网络运维，以及服务质量的智能化应用，并已经发布商用版本，在现网得到了应用。', 'id': '1162997_18'}
{'label': 'table', 'content': '缩略语\n| 缩略语 | 术语 | 描述 |\n|:-|:-|:-|\n| KPI | Key Performance Indicator | 设备关键性能指标。 |\n| SDB | Series DataBase | 基于时间序列的KPI数据库 |\n| QOS | Quality of Service | 服务质量，用户是实现对业务流量的区分调度，满足不同业务吞吐，时延等性能要求 |\n| ECN | Explicit Congestion Notification | 显式拥塞通知,是一种用于在IP网络中检测和管理拥塞的机制，它是一种基于IP协议的拥塞控制机制,它允许路由器在网络中 |\n| DDOS | Distributed Denial of Service | 分布式拒绝服务攻击 |\n| AIOps | Artificial Intelligence for IT Operations | 基于机器学习的智能运维（AIOps），将人工智能/ML或其他高级分析技术应用于业务和运维工作。 |\n| RCA | Root Cause Analysis | 故障根因分析 |\n', 'id': '1162997_19'}
