{'label': 'document', 'content': '中兴通讯承载网管控系统ZENIC ONE产品安全 V1.2_20211118_CN', 'id': '1002127'}
{'label': 'table', 'content': '在ZENIC ONE中，| 版本 | 日期 | 作者 | 审核者 | 备注 |\n|:-|:-|:-|:-|:-|\n| V1.0 | 2020/03/31 | 吕顺10240335 | 张晓冬10033733 | 首次发布 |\n| V1.1 | 2020/10/31 | 吕顺10240335 | 张晓冬10033733 | 增加市场关注内容更新 |\n| V1.2 | 2021/10/31 | 吕顺10240335 | 张晓冬10033733 | 基于市场需求更新内容 |\n', 'id': '1002127_1'}
{'label': 'text', 'content': '概述\nZENIC ONE产品架构\n在ZENIC ONE中，ZENIC ONE是中兴通讯承载网统一管控平台，实现IP/IPRAN、OTN、PTN/SPN设备的统一网络管理、以及网络控制。 \nZENIC ONE产品架构可分为三个层面：基础设施层，PaaS层，以及应用服务层（即SaaS层）。基础设施层提供底层部署环境，ZENIC ONE支持部署在虚拟机（VMWare/TECS/OpenStack）和裸金属环境中，操作系统则采用ZTE自研CGSL系统。PaaS层以及应用服务层的内容构成了ZENIC ONE软件本身（如下图红框所示），其中PaaS层主要部署基础组件，而应用服务层则涉及到ZENIC ONE的业务核心组件等。', 'id': '1002127_2'}
{'label': 'text', 'content': '概述\nZENIC ONE产品安全设计原则\n在ZENIC ONE中，管控系统在电信网络中处于核心位置，它几乎与所有网元设备存在信息交互，并与很多其他系统、运营商企业网络等连接在一起。管控系统负责网元的监控、管理和控制、向关联系统提供数据和接收命令等诸多任务。管控系统的安全如若受到威胁，则影响的范围较广，并会造成比较严重的后果，因而管控产品的安全性显得极其重要且有价值。ZENIC ONE部署最新更新的安全补丁来应对不断出现的安全威胁。当ZENIC ONE需要升级软件，或更新安全补丁时，中兴通讯将在14个工作日内发送更新版本的系统文档。 \nZENIC ONE产品的安全设计原则为： \n能防止误操作和抵御非专业攻击者的攻击， 在专业攻击者的攻击下能尽量发现并争取到足够的反应时间。 \n2）基于安全设计和不同的技术选择，满足运营商在承载网多场景下较高的安全需求。 \n3）运作和管理上无已知和明显可利用的漏洞，有持续完善安全的机制，并建立完整的符合国际标准的安全管控体系和流程规范。 \n基于上述目标，本文将从系统安全、应用安全、数据安全、传输安全等维度来阐述ZENIC ONE产品的安全性。', 'id': '1002127_3'}
{'label': 'text', 'content': '通用公共安全\n安全红线要求\n在ZENIC ONE中，产品安全红线规定中兴通讯业务过程、产品或服务的最基本安全要求，是各业务单位在开展工作过程中必须达到的标准。 \n中兴通讯产品安全红线管理规定： \n禁止产品中存在后门 \n禁止交付的产品中存在中高危安全漏洞 \n禁止违规处理个人数据 \n禁止未经客户授权情况下访问客户网络和数据 \n禁止瞒报、拖延或忽视安全事件； \n中兴通讯规定：“任何产品/项目触犯产品安全红线时必须及时叫停，整改完成后方可走下一流程。如不满足要求，提交产品安全委员会审批同意后方可放行，否则属违规行为，违反流程的责任单位和个人将受到相应处罚”。', 'id': '1002127_4'}
{'label': 'text', 'content': '通用公共安全\n安全证书\n在ZENIC ONE中，中兴通讯致力于产品安全，在安全认证方面获得以下一些证书： \nISO 27001 信息系统管理安全证书，用于证明公司整体的信息安全能力，有能力保护客户信息在公司的安全流转； \nISO 28000 供应链安全证书，用于证明公司在供应链安全方面的能力 \nCC：通用准则认证，是国际上比较公认的安全功能认证。中兴通讯承载网各个产品在2011年获得过认证。 \nFIPS 140-2：美国和加拿大推出的关于加密算法自身安全性的认证书。我司统一平台证书号1586，UEP平台证书号1589，系统产品和网管产品分别继承了统一平台和UEP平台。 \nCCRC：中国网络安全审查技术与认证中心，证明中兴通讯在安全方案集成方面的能力和资质。', 'id': '1002127_5'}
{'label': 'text', 'content': '通用公共安全\n遵循的安全规范\n在ZENIC ONE中，基于统一的安全设计框架构建解决方案安全的同时，中兴通讯的产品与解决方案还需要遵从一系列的安全设计原则，以提高安全体验的一致性。 \n规范顺从：支持3GPP\\ITU\\IETF\\...等规范中要求的基本安全框架与安全能力；满足特定地区、国家公开性的行业性的网络与业务安全规范需求；结合特定运营商或者企业客户自身的网络及业务安全规范要求进行定制。 \n使用最佳实践：鼓励采用业界最佳安全实践与多种方法论模型，进行威胁识别、建模与安全生命周期管理，例如：X.805、DevSecOps、SDL、PybD等。 \n遵循数据最小化使用原则：使用用户许可、匿名化、数据访问控制和加密保护等多种手段对包括用户隐私在内的客户的数据资产进行保护，符合GDPR等数据管制要求。 \n安全的第三方开放性与兼容性：可以根据客户自身业务安全的需要，在解决方案中支持客户认可的第三方安全组件与服务。采用开放性协议与公开的密码学算法。 \n合理划分安全域：结合安全设计框架，根据不同的平面与服务层次的特点，结合解决方案的具体场景，合理划分安全域，进行安全边界的管理。 \n端口保护：对各种网络端口、服务端口进行保护，不得预留各种调试以及远程控制的后门。 \nZENIC ONE作为软件系统应该遵循安全的编码规范，如Web应用开发规范Open Web Application Security Project (OWASP)， CERT 安全编码, SDL（Microsoft Security Development Lifecycle ）等。', 'id': '1002127_6'}
{'label': 'text', 'content': '系统安全\n系统高可用性\n本地高可用性\n在ZENIC ONE中，ZENIC ONE支持集群部署方式，如下图。\n根据ZENIC ONE部署时节点的不同角色，主要分为主节点（Master node）和从节点（Minion node）两大类型，如下表所示。系统内外部流量通过网关服务节点（MSB）进行互通，从而实现系统内部与外部的隔离。系统内Master/Minon节点之间通过内部网络平面进行相互通讯，系统内部组件之间不存在隔离。对于部署了网关服务的Minion节点，可以配置与外部网络平面互通。\n表1 集群部署节点角色\n| 名称 | 部署内容 | 节点数量 | 高可用能力 |\n|:-|:-|:-|:-|\n| Master集群 | 主要部署Kubernates Master、PaaS Controller、SoftwareRepository等系统集群管控相关的功能组件。 | Master node的数量最小为3个节点。管理规模更大时，可按不同的组件（Kubernates Master、PaaS Controller、SoftwareRepository）独占节点方式，部署3*N个节点（其中N是分拆独立部署的管理组件数量）。 | 对于按最小3节点集群部署的场景，可允许最大1个节点故障。对于按功能组件独占节点部署的方式，每个组件集群内最大运行1个节点故障。如果有N个独立部署的管理组件，则可以允许最大N个节点故障。 |\n| Minion集群 | 主要用于部署各种服务/微服务。微服务部署时以POD为最小部署和运行单位。每个minion node 里面可运行若干个POD。每个节点内部署哪些POD，由PaaS主控根据各个服务/微服务的POD节点亲和性要求、CPU/内存资源要求等进行统一调度。 | 根据管理规模不同，Minion节点数量会有较大差异，具体数量可以按管理资源规模不同无上限扩展。 | 节点内不同的服务有各自不同的高可用处理方式。 |\nMaster节点高可用：集群中leader节点故障以后，剩余两个节点会触发重新选举，产生新的leader节点。新的leader节点会接管以以前leader节点的能力。以前的leader节点故障恢复以后，可以重新加入集群，但节点状态会变更为follower。\nMinion节点高可用：Master节点会监控Minion节点以及Minion节点内部署的POD健康状态，并根据相应状态判断是否需要触发高可用机制。\nMinion节点故障：Master节点会根据故障Minion节点内部署的POD信息，依据该Minion节点各个POD的节点亲和性要求，将各个POD迁移到符合要求且有空闲资源的其它Minion节点下。故障节点POD可能被迁移到不同的Minion节点。\nPOD故障：Master节点会尝试在当前Minion节点下重生该POD，如果该POD在当前Minion节点下无法重生，则会根据POD的节点亲和性要求，将故障POD迁移到符合要求且有空闲资源的其它Minion节点下。具体来看，Kubernates Master节点对POD实例状态进行监控，使得POD异常时能够自动化恢复。POD的故障恢复分为3种场景：1.POD包含的容器实例异常，此时POD实例本身还存在，因而POD只会在本节点重建容器实例；2.POD实例异常或被删除，此时Kubernates会重新进行POD调度，会在任意一个节点重生POD实例；3.节点异常，如果POD实例所在节点发生异常或重启，则优先等待节点恢复后在本节点重生POD，5分钟后才考虑在其它节点重生POD。此外，ZENIC ONE还提供了探针机制，可以对POD自身业务逻辑进行监测：1.Readiness探针，当探针API检查失败后中断POD实例网络通信；2.Liveness探针：当探针API检查失败后重生POD。\n集群部署还具有以下特点：\n服务高可靠：用户浏览器可通过统一的IP地址和端口访问集群中的服务。当集群中任意一个或多个服务器故障，集群服务 IP地址、端口不会发生变化，服务仍然可用。\n弹性扩缩容：在系统冗余范围内，增加或删除集群内服务器，业务无感知，集群服务 IP地址、端口不会发生变化，服务仍然可用。\n用户不感知：外部访问采用浮动IP，IP地址与端口号不变。\n', 'id': '1002127_7'}
{'label': 'text', 'content': '系统安全\n系统高可用性\n系统弹性伸缩及负载均衡机制\n在ZENIC ONE中，ZENIC ONE基于微服务架构研发，各应用组件以微服务的形式部署在容器中，同时各应用微服务在POD中运行，一个POD可以承载一个或者多个相关的容器，并通过Kubernates实现对POD的统一管理。ZENIC ONE支持节点与容器的弹性扩展，实现了网络管理规模的弹性伸缩。系统内部由弹性伸缩控制组件EPS（ElasticProcessSystem）负责监控应用服务的各个POD实例的CPU、内存等资源使用情况。EPS基于设定的弹性伸缩策略，根据当前资源使用情况触发弹性伸缩机制，通知Kubernates组件实现POD的弹缩。目前ZENIC ONE支持自动弹缩、手动弹缩、计划弹缩三种方式。 \nZENIC ONE应用服务分为无状态服务和有状态服务。无状态服务是指服务中的POD不含与命令状态、上下文相关信息，对该服务任意的命令请求都可以由该服务的任意POD处理。而有状态服务是指服务中的POD含有与命令状态、上下文相关的信息，对该服务的命令请求需要根据某些关键属性来判断具体由哪个POD来执行。 \n无状态服务弹性伸缩及负载均衡机制 \nZENIC ONE中的服务默认按无状态服务方式进行开发和实现。无状态服务的负载均衡、高可用和弹缩机制是一致的。 \n1、负载均衡：外部应用调用系统内部接口时，均需要通过网关服务MSB（MicroServiceBus）。MSB会根据调用的接口找到负责实现此接口的应用微服务的各个POD。然后以预定负载均衡策略将外部调用转发到对应微服务的某个POD地址上，再由对应POD完成接口处理。转发策略包括轮询、容器加权等策略。 \n2、弹性伸缩：当现有POD的资源使用到达预设阈值上限以后，EPS（ElasticProcessSystem）按服务预定策略通知Kubernates Master节点对该服务弹出（scale-out）一个或多个新的POD。后续MSB在流量转发时会将之均衡到新的实例上。反之，当POD资源使用达到预设阈值下限时，就会收缩删除(scale-in)部分已有容器实例。上述弹缩过程也可以在中兴通讯PaaS运维管理界面(OpenPalette)中手工配置执行。 \n3、高可用：当应用服务中某个POD故障以后，网关服务会检测到对应POD失效，就会将其从负载转发实例中去掉。后续该服务的命令请求就仅会发给存活的POD，不会再发给失效POD。 \n有状态服务弹性伸缩及负载均衡机制 \n有状态服务其负载均衡、弹性伸缩实现方式，根据不同服务自身特性设计，各有不同。ZENIC ONE中绝大多数公共服务，以及产品应用的PCE等服务都是有状态服务。现以ZENIC ONE内消息中间件Kafka为例。 \n1、负载均衡：Kafka通过分区机制进行消息流量的负载均衡。Kafka内每个主题（topic）都可以设置若干分区。发送方发送消息时，Kafka会自动根据消息头中的key值，将其自动负载均衡在每个分区中。某个微服务需要订阅消息时，该微服务的所有容器实例都处于同一个kafka消费组中。此时Kafka通过分布式协调，会自动负载均衡的将消费组中每个容器实例与分区进行映射，从而保证每个容器处理的消息量均等。Kafka主题的分区数量在对应主题创建时进行设置。每个主题中消息订阅处理的最大并发数量等于该主题分区的数量。如果消费组中容器数量多于分区数量，多出的容器将无法订阅消息。 \n2、高可用：Kafka主题消息分区一般都会有三副本，其中一个副本为主副本，其它为备副本。Kafka POD部署时，会自动将每个分区的副本均匀分布在每个POD上。并且尽量保证POD中的主副本数量均衡。正常运行时，每个分区只有主副本对外提供服务，其它两个副本从主副本同步数据。 \n如下图所示，当POD2发生故障时，Kafka会自动检测到该POD中的分区2是主副本，会自动触发分区2剩下两个副本的选举动作。如果此时选举到POD3下的分区2为新主副本，则会将POD3中的分区2副本状态切换为主状态，对外提供服务。', 'id': '1002127_8'}
{'label': 'text', 'content': '系统安全\n系统高可用性\n异地容灾\n在ZENIC ONE中，ZENIC ONE支持异地容灾，主用和备用的网管服务器系统配置完全相同，均采用RAID10镜像的方式实现数据和系统的冗余备份，如下图所示。其中，MBR：Middle-software Backup Recover(中间件备份恢复)；GR：Geographic Redundancy(地理容灾)；PG:Postgre(PG关系数据库)；ES：Elasticsearch（ES数据库）；DSM：Data Sync Manager(数据同步管理)。 \n对于主ZENIC ONE系统，其所有应用保持运行状态。在主系统正常工作状态下，备用系统各应用并不运行以节省资源消耗。MBR中间件对各组件容灾备份进行调度，ZENIC ONE主要有PG和ES数据库需要进行数据备份，其中PG数据库主要存储配置、当前告警等数据，ES数据库主要存储历史告警、历史性能等数据。对于PG数据库，ZENIC ONE内部通过消息通道与备用系统进行实时备份。对于ES数据库，其数据通过容灾软件以每5分钟为周期进行备份。 \nZENIC ONE主备系统的GR中间件时刻保持通信，当主系统出现故障时，GR中间件会触发倒换动作，此时备系统启动各应用服务，实现主备倒换，整个主备倒换能够在15分钟内完成，保证网络安全。', 'id': '1002127_9'}
{'label': 'text', 'content': '系统安全\n系统高可用性\n软件仓库\n在ZENIC ONE中，ZENIC ONE基于ZTE自研PaaS平台（OpenPalette）研发，OpenPalette软件仓库基于业界主流的开源项目Docker Registry进行商业增强，为平台组件和容器化业务提供高可用的软件版本和容器镜像的存储和访问服务，满足用户对各类格式软件的整个生命周期管理的需求。 \nOpenPalette软件仓库系统架构如下图所示： \nDataBase：负责软件版本索引数据的存储； \nSWR：负责整体对外接口和服务逻辑的实现； \nDocker Registry：负责容器镜像生命周期管理相关接口和功能； \nSyncthing：负责主备实例存储层的数据同步。 \nOpenPalette软件仓库基于主备HA机制实现服务的高可用性，为用户提供以下关键服务： \n基于租户的资源隔离管理。 \n容器镜像的全生命周期管理（新建/删除/更新/查找/导入/导出等）。 \n对接远端仓库，同步容器镜像。 \n容器镜像安全扫描（可选）。 \n备份恢复。 \n基于开源项目Docker Registry实现的ZTE OpenPalette软件仓库服务，对容器镜像的整生命周期管理操作完全符合OCI标准规范，并支持通过蓝图的方式编排和配置容器化业务，为用户提供安装就绪的镜像和启动配置。 \n以容器镜像下载为例，其软件仓库业务流程如下图所示：', 'id': '1002127_10'}
{'label': 'table', 'content': '系统安全\n公共服务安全\n在ZENIC ONE中，ZENIC ONE公共服务主要包含数据库、消息中间件等服务，如下表所示。ZENIC ONE不对外用户暴露数据库，且对应用组件访问数据库采用最小访问权限原则，以保证数据库数据安全。同时，防止外部对ZENIC ONE系统进行SQL注入攻击，也是重要的安全手段。\n表2 公共服务\n| 名称 | 描述 | 部署方式 | 节点亲和性 | 高可用 |\n|:-|:-|:-|:-|:-|\n| PostgreSQL数据库 | 关系型数据库，存放系统网元配置、端到端业务配置、当前告警等数据。 | 根据管理规模部署1个或多个PostgreSQL数据库集群。每个集群一般由3个PG POD 组成，1主两备。 | 按节点反亲和部署。 | 每个集群有各自独立的高可用能力。单个集群内不超过1个节点故障，系统不会停服。 |\n| ElasticSearch数据库 | 文本型数据库，主要存放历史告警、审计日志、调测日志等数据 | 整个系统仅部署一个集群（一套ES），集群内根据管理规模部署2N+1个POD。 | 同上 | 不超过N个节点故障，系统不会停服 |\n| Kafka | 消息中间件，用于系统内应用微服务之间通知消息处理 | 同上 | 同上 | 同上 |\n| Zookeeper | 分布式协调器，为分布式应用提供一致性服务，包括选主、分布式锁等。 | 同上 | 同上 | 同上 |\n', 'id': '1002127_11'}
{'label': 'text', 'content': '系统安全\n公共服务安全\nPostgreSQL数据库安全\n在ZENIC ONE中，PostgreSQL数据库安全配置满足口令、认证授权、日志等方面要求。 \n口令 \n支持密码有效期配置，非程序使用的账户（主要使用者为用户）的账户口令生存期不长于90天。 \n创建和修改密码时，强制限制密码复杂度，如必须包含数字、字母、大小写、特殊字符，密码长度等。同时排除暴力破解字典中的字符串。 \n认证授权 \nPostgreSQL的trust认证方式只允许在本地使用，同时对访问IP、用户和数据库进行严格设置。 \n在数据库权限配置能力内，根据用户的业务需求，配置其所需最小权限。 \n日志 \n配置日志功能，对用户登录进行记录，记录内容包括用户登录使用的账号，登录是否成功，登录时间，以及远程登录时用户使用的IP地址。 \n合理配置数据库日志文件权限，审计日志文件权限最小化。 \n其它 \n修改监听IP。默认只有本地主机能够访问，设置绑定的IP地址使客户端只能使用固定的IP才能访问PostgreSQL，其它IP地址无法访问。 \n设置默认监听端口为5432。 \n根据机器性能及业务需求，设置最大连接数。 \n根据业务实际情况设置完成客户端的最长时间，避免出现问题的客户端无限制占据连接资源。', 'id': '1002127_12'}
{'label': 'text', 'content': '系统安全\n公共服务安全\nElasticSearch数据库安全\n在ZENIC ONE中，ElasticSearch数据库安全配置满足账号、口令、认证授权等方面要求。 \n账号 \n按照不同用户分配不同账号，避免不同用户之间共享账号。 \n认证授权 \n开启xpack安全功能及配置密码认证。 \n最小权限账号运行ElasticSearch服务。 \n为ElasticSearch的目录分配合理的读写权限，避免使用共享文件系统。确保只有ElasticSearch的启动用户才有权访问目录。 \n其它 \n绑定访问源IP。 \n修改默认端口。节点间交互的TCP端口为非9300端口，对外服务的http端口为非9200端口。 \n可设置关闭Http访问，停止对外提供服务。', 'id': '1002127_13'}
{'label': 'text', 'content': '系统安全\n自监控\n在ZENIC ONE中，ZENIC ONE系统自监控自身各模块和功能点的运行状态，并提供系统管理相关功能，当出现问题时上报告警给用户。监控对象包括：\n系统服务状态、应用程序进程/服务/微服务等。\n系统物理设备的状态信息和使用情况，如服务器、数据库、存储的运行和使用情况。\n表3 自监控\n| 监控类型 | 功能说明 |\n|:-|:-|\n| 服务器监控 | 提供针对服务器逻辑CPU、内存、物理磁盘、文件系统等信息进行统计，统计信息包括使用率、空闲率等 |\n| 数据库监控 | 提供对数据库表、大小、连接数等信息进行统计。 |\n| 软件服务监控 | 可按模块/微服务提供CPU使用情况、内存使用情况、存储使用情况、网络速率等信息的统计。 |\n', 'id': '1002127_14'}
{'label': 'text', 'content': '系统安全\n物理安全\n在ZENIC ONE中，ZENIC ONE支持虚机、物理机部署方式，物理安全主要包括电源冗余，存储冗余，端口冗余等。ZENIC ONE部署方案包含了如RAID技术、存储冗余、冗余电源+UPS、冗余端口等。', 'id': '1002127_15'}
{'label': 'text', 'content': '系统安全\n物理安全\n物理端口冗余\n在ZENIC ONE中，物理端口冗余采用端口bond+交换机堆叠技术实现。首先ZENIC ONE上通过不同网卡两个不同端口绑定为bond口，实现网卡和端口的冗余。其次，一个bond口的两个网口分别连接到两个交换机上，实现交换机的冗余。最后交换机之间通过VRRP协议进行冗余保护和切换。', 'id': '1002127_16'}
{'label': 'text', 'content': '系统安全\n物理安全\n储存RAID 1+0技术\n在ZENIC ONE中，ZENIC ONE 采用RAID（Redundant Array Of Independent Disk，独立磁盘冗余阵列）,可以提供比普通磁盘更快的速度、更高的安全性。 \nRAID 0（String 条带模式）其优点是数据分散存储于不同磁盘上，在读写时可以实现并发，是所有RAID级别中存储性能最高的；磁盘利用率100%。但RAID 0没有容错功能，一旦其中一块磁盘挂掉全部数据将都会损坏。 \nRAID 1（Mirroring 镜像卷）其优点是用硬盘镜像数据实现数据冗余并保证数据的安全性。两块硬盘互为备份，当原始数据繁忙时，可以直接从镜像备份中读取数据。但是RAID 1是RAID级别中单位成本最高的，当其中一块磁盘挂掉时，系统直接切换到镜像盘上读写，而不需要重组失效的数据，磁盘利用率50%。 \n在物理机部署方案中，ZENIC ONE采用RAID 1+0储存方式。即先创建RAID 1，在RAID 1的基础上创建RAID 0。RAID 1+0储存方式结合了 RAID 0 性能和 RAID 1 容错的优点，能够提供良好的数据保护能力，只要不是一个RAID 1上的2个硬盘同时损坏，都不影响数据的正常读写，数据恢复快。', 'id': '1002127_17'}
{'label': 'text', 'content': '应用安全\n登录认证\n在ZENIC ONE中，用户登录认证方式可以分为集中认证模式，本地认证模式。对于集中认证模式，ZENIC ONE与安全管理系统对接，安全管控系统负责ZENIC ONE系统的统一认证和单点登录，ZENIC ONE仅负责自身用户账号管理。同时，ZENIC ONE也支持本地认证模式，ZENIC ONE自身对接入用户的合法性进行认证和鉴权。 \n在ZENIC ONE的安全模块中有认证配置功能对认证模式进行设置。ZENICONE可以与第三方产品（如Raduis/LDAP/AD）互连。 \n目前ZENIC ONE主要支持的可选认证模式有： \n本地认证 \nRadius认证 \nLDAP/LDAPS认证。对于LDAP认证，支持 TLS、StartTLS \nLDAP/LDAPS认证+本地认证 \n4A票据认证 \n用户根据需求在ZENIC ONE上配置用户的登录策略以及账户规则, 如设置账户有效期，锁定账户，同时在线用户数，用户闲置退出时间等等。同时，ZENIC ONE可以对用户的IP范围进行配置使得用户只能在指定的IP范围内使用用户名和口令进行登录。 \n此外，ZENIC ONE还支持： \n多因子认证方式（MFA），如用户可以设置是否开启短信验证，图像验证码进行双验证。 \nGUI、SSH方式接入，可以根据不同用户接入方式来限定某些用户只能在指定的接入方式上接入。这样可以限定不同场景的用户职责是分离的，有效缩小对系统造成的影响或者破坏。 \n通过设定用户的工作时间来限定用户使用时间，指定不同用户的访问时间段。一方面可以避免时间相冲突的业务操作，另一方面增强了用户接入的安全性。 \n支持配置超出指定时间没有任何操作，系统自动注销会话。同时支持短期离开的手动注销。 \n如果发现某个用户操作有风险，ZENIC ONE支持立即禁用该用户，这样该用户会被立即踢出系统，避免风险的进一步扩散，当管理员由于重要操作或者识别出非法用户时，同样可以踢出已经登录系统的用户。 \n不允许时间，来源，登录方式等等各种访问控制条件不满足的用户登录系统建立会话。 \n防止暴力破解，ZENIC ONE可以对登录验证次数进行限制，这样在登录时，若出现多次账户密码错误则会锁定该账户，从而防止出现对账户进行暴力攻击的情况。同时，为了避免用户在多个地方登录，信息泄露，系统支持配置一个账户的登录并发数，超出并发登录数时，该用户将无法再登录。 \n用户登录系统出错时，不会明确地提示“用户名错误”或者“口令错误”，而是提示用户“用户名或者口令错误”，避免用户通过登录错误信息猜测其它用户信息。', 'id': '1002127_18'}
{'label': 'text', 'content': '应用安全\n用户/账户管理\n在ZENIC ONE中，ZENIC ONE提供集中式用户管理，对新用户创建、用户数量、当前用户在线数、用户类型、用户状态、用户操作权限等等进行管理。在创建新用户时可对密码设置（依据设置的密码规则）、登录数（设置是否单账户可以多处同时登录）等等进行管理。在新用户创建时，用户可以根据自身需求对用户名进行任意设置，避免有指向用户权限的用户名出现，如“admin”、“guest”，需要注意的是，系统存在默认管理账户“admin”，该账户名不可更改。超级管理员可以对系统帐户进行管理，包含对系统账户密码更改，新建/删除系统账户，系统账户权限修改等等。 \n对于6个月内未激活账户，可取消该账户，除非对该账户进行专门授权。 \n对于用户长期未登陆、不使用的账号（默认为180天）进行停用清理，并记录安全日志。可设置用户账户时间，实现过期账户自动停用、禁用。', 'id': '1002127_19'}
{'label': 'text', 'content': '应用安全\n安全管理\n分权分域\n在ZENIC ONE中，网络设备多、组网复杂，因而使用管控系统的用户角色也多样，因此ZENIC ONE提供分权分域的能力。ZENIC ONE支持用户角色管理，按照最小授权原则，赋予用户角色不同的权限(分权)，以及不同的设备管理范围(分域)。不同用户角色赋予了不同操作权限或设备管理范围，当某个用户角色没有某个功能/设备的操作/管理权限时，ZENIC ONE界面上会隐藏该功能或设备。\nZENIC ONE系统默认的角色和对应的操作集，如下表所示。\n表4 用户角色\n| 角色 | 权限 | 操作集 |\n|:-|:-|:-|\n| 超级管理员 | 拥有全部权限，分配其他角色 | 角色管理 |\n| 安全管理员 | 进行安全数据维护、调整资源所属组织，没有业务相关权限。 | 安全管理员权限 |\n| 系统管理员 | 不具备维护安全信息的权限，除此之外对管理系统及被管网络有不受限制的完全访问权 | 管理员权限 |\n| 系统维护员 | 不具备维护安全信息及管理系统的权限，除此之外具有被管网络的所有权限 | 系统维护权限 |\n| 系统操作员 | 可进行一般的配置修改，不能对管理系统本身进行备份恢复等维护工作，不能修改敏感的资源配置信息 | 操作权限 |\n| 系统监控员 | 可对网络信息进行浏览 | 查看权限 |\n此外，ZENIC ONE支持对用户角色以及操作集的自定义，通过灵活分配用户的操作权限(分权)和设备管理范围(分域)，实现用户不同需求，并支持导入或导出自定义操作集。\n在实际网络维护过程中，用户可能出现误操作的情况，从而导致网络业务受损的情况发生。为了尽可能减少误操作对网络产生的危害，ZENIC ONE提供二次授权能力（金库模式）。在对某些危险操作（如：删除L3VPN业务）进行二次授权后，这些危险操作只有在具有“二次授权”权限的人员的授权确认后才会生效，从而防止误操作的发生。\n', 'id': '1002127_20'}
{'label': 'text', 'content': '应用安全\n安全管理\n密码学支持\n在ZENIC ONE中，ZENIC ONE提供对用户账户密码的设置，用户可根据需求设置密码规则，并将密码规则绑定给指定用户。 \n密码规则有： \n设置用户账户首次登录时，强制修改密码。 \n指定管控系统用户的密码长度范围。 \n指定不能与最近x次历史密码相同 \n设置密码时进行弱密码检测。包含： \n密码必须包含数字、字母、其它字符。 \n密码不能包含用户名。 \n密码不能包含用户名的逆序 \n密码不能与用户电话号码相同。 \n不能是用户电话号码的逆序。 \n密码字典，即密码不能是常用词（可导入/导出常用词） \n连续x字符序列检测（不能为相同序列、递增/递减序列、间隔的递增递减序列、键盘序列等） \n连续x字符不能重复x次（如：123ab#123,123三个连续字符即重复2次） \n用户名与密码最大x连续字符相同 \n密码的有效期设置（默认60天），以及密码过期提前提示。 \nZENIC ONE对全部静态密码进行加密存储，如数据库密码、FTP/SFTP密码、管控系统账号密码等等。对于需要加密的数据而言，除标准协议规定外，ZENIC ONE不使用不安全的密码算法（比如MD5， SHA1），而是采用高安全等级的密码算法，如：AES192、DH1024、DSA1024、ECDSA192、RSA2048、ECC192、SHA256、HMAC-SHA256。', 'id': '1002127_21'}
{'label': 'text', 'content': '应用安全\nLicense\n在ZENIC ONE中，ZENIC ONE提供成熟的License公共组件，支持通过限制管理的设备数、功能集等方法进行License授权控制。\n表5 License条目\n| License类型 | 条目 | 说明 |\n|:-|:-|:-|\n| 网络规模 | 软件管理规模(管理等效网元<=3000) | 提供不同网络规模，包括小型/中型/大型三个标准规模，以及超大型网络规模叠加（例：管理30000等效网元，则需1个“软件管理规模(管理等效网元<=16000)”license加上2个“软件管理规模(管理等效8000）”license）。 |\n| 网络规模 | 软件管理规模(管理等效网元<=8000) | 提供不同网络规模，包括小型/中型/大型三个标准规模，以及超大型网络规模叠加（例：管理30000等效网元，则需1个“软件管理规模(管理等效网元<=16000)”license加上2个“软件管理规模(管理等效8000）”license）。 |\n| 网络规模 | 软件管理规模(管理等效网元<=16000) | 提供不同网络规模，包括小型/中型/大型三个标准规模，以及超大型网络规模叠加（例：管理30000等效网元，则需1个“软件管理规模(管理等效网元<=16000)”license加上2个“软件管理规模(管理等效8000）”license）。 |\n| 网络规模 | 软件管理规模(管理等效8000) | 提供不同网络规模，包括小型/中型/大型三个标准规模，以及超大型网络规模叠加（例：管理30000等效网元，则需1个“软件管理规模(管理等效网元<=16000)”license加上2个“软件管理规模(管理等效8000）”license）。 |\n| 业务功能 | 网络管理 | 按照OTN/PTN/IP/IPRAN等不同的业务领域方向划分 |\n| 业务功能 | 网络控制 | 按照OTN/PTN/IP/IPRAN等不同的业务领域方向划分 |\n| 业务功能 | 网络编排 | 为单域控制器场景提供域内SDN编排 |\n| 业务功能 | 网络分析 | 提供智能故障诊断、RCA根因分析、网络资源分析 |\n| 业务功能 | 智能运维 | 提供便捷开通、批量运维、业务割接、智能扩缩容、业务迁移等 |\n| 南北向接口 | 北向接口 | 采用SNMP/FTP/Restful/XML等不同接口，提供告警/资产/性能服务 |\n| 南北向接口 | 南向网元接入 | 所有支持管理的设备都有单独的license |\n', 'id': '1002127_22'}
{'label': 'text', 'content': '应用安全\n日志管理\n在ZENIC ONE中，ZENICONE具有全面的审计能力，满足对所有的关键性事件进行日志记录。日志主要包括操作日志、安全日志和系统日志。操作日志主要是用于记录用户从界面或外部系统发起的一些业务操作动作日志，用于审计用户的操作过程和风险跟踪；安全日志主要记录系统中发生的与安全相关的事件日志，用于安全审计和风险识别，主要是登陆，注销，密码修改，权限变化，非法入侵与安全相关的内容；系统日志主要记录系统内部后台服务运行的一些关键事件日志，比如自动备份任务，清理任务，用于审计跟踪系统运行的关键任务的状态。 ZENIC ONE将默认保留所有事件的审核跟踪180天，日志存储周期可由用户决定。日志以通用的格式进行存储，比如CEF（common event format）, CLFS(Common log file system)格式。\n', 'id': '1002127_23'}
{'label': 'table', 'content': '应用安全\n日志管理\n在ZENIC ONE中，操作日志、安全日志和系统日志都存储于ES数据库中。操作日志包含的详细字段如下表。\n表6 操作日志字段\n| 名称 | 字段名 | 描述 |\n|:-|:-|:-|\n| 主键 | id | 原则上id不重复。 |\n| 操作 | operation | 操作名称 |\n| 用户名 | userName | 发起操作的登陆系统用户名 |\n| 级别 | rank | 日志的重要程度，重要等级从上到下依次升高，包含：operlog_rank_normal：表示【普通】等级operlog_rank_notice：表示【注意】等级operlog_rank_important： 表示【重要】等级operlog_rank_veryimportant：表示【非常重要】等级 |\n| 操作对象 | operateResource | 操作对应的资源对象 |\n| 对象地址 | resourceIP | 即操作对象的IP地址 |\n| 操作开始时间 | logStartDate | 操作的开始时间，精确到年月日时分秒。 |\n| 操作结束时间 | logEndDate | 操作的结束时间，精确到年月日时分秒。 |\n| 操作结果 | operateResult | 操作是否成功，包含log_success：成功；log_fail：失败 |\n| 客户端地址 | hostname | 客户端IP地址或主机名 |\n| 接入方式 | connectMode | 客户端执行操作时的接入模式。常见的有：“WEB”，"GUI"，“TELNET”、“SSH”等。 |\n| 描述信息 | descriptionInfo | 操作的描述信息。 |\n| 详细信息 | detail | 操作日志的详细信息，包含关键信息（如操作时的参数）。 |\n| 失败原因 | failReason | 操作执行失败的原因 |\n| 操作功能模块 | appModule | 操作归类的模块功能，如告警，性能，安全，拓扑，日志,配置,备份恢复，系统等 |\n| 操作类型 | operateType | 操作类型，如增加、删除、更新、查询等 |\n| 日志来源标识 | logSource | 用于记录日志产生的来源，目前记录为微服务的名称。 |\n| 客户端MAC地址 | mac | 客户端MAC地址。 |\n| 关联日志id | linkId | 关联日志id。 |\n| 显示过滤 | isDisplay | 决定是否显示在界面上 1：显示；0：隐藏。 |\n| traceid字段 | traceid1 | 预留 |\n| traceid字段 | Traceid2 | 预留 |\n| 日志来源系统实例ID | systemid | |\n| 自定义字段 | - | 自定义字段，便于扩展日志信息。 |\n', 'id': '1002127_24'}
{'label': 'table', 'content': '应用安全\n日志管理\n在ZENIC ONE中，安全日志包含的详细字段如下表。\n表7 安全日志字段\n| 名称 | 字段名 | 描述 |\n|:-|:-|:-|\n| 主键 | id | 系统自动生成 |\n| 用户名 | userName | 发起操作的登陆系统用户名 |\n| 客户端主机地址 | hostname | 客户端IP地址 |\n| 客户端MAC地址 | mac | 客户端MAC地址 |\n| 日志的名称 | logName | 安全日志的名称 |\n| 详细信息 | detail | 此操作日志的详细信息 |\n| 操作时间 | logDate | 操作发起时间，精确到年月日时分秒 |\n| 接入类型 | connectMode | 此条操作日志的接入模式类型 |\n| 系统实例ID | systemid | |\n| 自定义字段 | - | 自定义字段，便于扩展日志信息， |\n| 操作结果 | operateResult | 安全操作结果，如登录成功/失败 |\n| 操作类型 | operateType | 安全操作类型，如安全登入/登出, |\n| 日志级别 | rank | 日志的重要程度 |\n| 日志分类 | facility | |\n', 'id': '1002127_25'}
{'label': 'table', 'content': '应用安全\n日志管理\n在ZENIC ONE中，系统日志包含的详细字段如下表。\n表8 系统日志字段\n| 名称 | 字段名 | 描述 |\n|:-|:-|:-|\n| 主键 | id | 系统自动生成 |\n| 日志级别 | rank | 日志级别: |\n| 微服务名 | source | 微服务名 |\n| 微服务ID | sourceID | 微服务容器实例标识符 |\n| 日志名称 | logName | 系统日志的名称 |\n| 详细信息 | detail | 此条日志的详细信息 |\n| IP地址 | hostname | 微服务IP地址 |\n| 开始时间 | logStartDate | 开始时间，精确到年月日时分秒 |\n| 结束时间 | logEndDate | 结束时间，精确到年月日时分秒 |\n| 显示过滤 | isDisplay | 是否在界面显示 |\n| 关联日志id | linkId | 关联日志id |\n| traceid字段 | traceid1 | 预留 |\n| traceid字段 | Traceid2 | 预留 |\n| 系统实例ID | systemid | - |\n| 自定义字段 | | 自定义字段 |\n', 'id': '1002127_26'}
{'label': 'text', 'content': '应用安全\n日志管理\n在ZENIC ONE中，此外，ZENIC ONE还支持：\n将日志导出为EXCEL、TXT、CSV等文件格式或者输出到外部的FTP、SYSLOG服务器。\n支持对日志的容量（日志保留时间、日志占用空间）等参数进行设置，保证一定时间内日志数据的完整性，当存储介质满时最新记录只覆盖最旧的数据。\n支持日志手工备份或定期自动备份，当日志容量达到设定门限时，支持自动备份或产生相应告警提醒操作人员进行手动备份。\n日志数据文件的访问需要权限设置，只有特定的用户角色才能读、写文件。\n日志管理界面分为日志查询、日志统计、日志输出。其中日志查询界面如下图，包含对操作日志、安全日志、系统日志的查询，用户可以设置查询条件来对日志进行过滤。\n日志统计界面则对用户登录数、单个用户访问日志Top5、客户端地址来源、用户进行的日志操作类型等等进行统计，并以图表的形式直观展示。\n日志输出界面则包含输出到外部的FTP、Syslog服务器两种类型。当输出到外部FTP服务器时，需要设置需要输出的日志类型、输出周期、以及FTP服务器相关信息，在设置完毕激活后，即可将日志输出到FTP服务器。导出到Syslog服务器形式同样类似，用户需要设置导出的字符集格式等信息。此外，也支持将日志输出到两个Syslog外部服务器上。\n', 'id': '1002127_27'}
{'label': 'table', 'content': '数据安全\n数据完整性\n在ZENIC ONE中，ZENIC ONE保证数据的完整性，数据不会因升级或任何维护工作而丢失。当存储介质满时最新记录只覆盖最旧的数据。各数据存储周期最大值由当前系统存储容量、存储数据容量决定，用户可以手工设置数据存储周期，默认存储周期如下表。\n表9 数据存储周期\n| 名称 | 周期（天） |\n|:-|:-|\n| 配置数据 | 长期储存 |\n| 历史告警数据 | 180 |\n| 历史性能数据 | 180 |\n| 操作日志数据 | 180 |\n', 'id': '1002127_28'}
{'label': 'text', 'content': '数据安全\n数据备份与恢复\n在ZENIC ONE中，ZENIC ONE提供数据备份恢复能力。 \n提供手动/自动备份网管数据功能，备份数据不中断业务，仅针对管控中的配置、日志、告警、性能等业务数据进行备份。（请见3.1.3 异地容灾） \n提供报警/性能数据的增量备份 \n支持设置自动数据备份执行时间 \n支持定期备份到外部存储设备 \n支持对数据库数据手动恢复。单机/集群部署恢复功能一致，特别注意，集群部署时，需要先把集群管控在各个服务器上完成安装，然后再进行数据恢复，保证业务数据与备份时完全一致。', 'id': '1002127_29'}
{'label': 'text', 'content': '数据安全\n个人数据保护\n在ZENIC ONE中，ZENIC ONE保证敏感数据的存储、传输和处理安全，并遵从适用国家和地区的法律和法规要求。敏感数据包括但不限于口令、密码等，个人数据包括但不限于用户姓名、帐号、电话号码、通信记录、话单、通信时间、定位数据等。ZENIC ONE所有涉及个人数据的采集/处理的功能须经过用户同意，并提供安全保护机制（如认证、权限控制、日志记录等），并向客户公开。 \nGDPR是欧盟为了应对大数据背景下的个人数据公开化及全球化的要求而制定，影响范围广。中兴通讯承诺严格遵循欧盟GDPR标准。 \n所有功能只涉及到必要的、最少范围的个人数据读取，并在使用用户个人数据前，明确地、清晰地提示用户知晓个人信息的将被使用情况、并取得用户的同意。 \n用户可以查询、修改和删除其个人数据。 \n系统使用、处理用户数据的过程应该有完整的日志记录。 \n个人数据在存储和传输中予以加密。 \n当数据被导出转移出欧盟区域时，保证其中不具备个人数据或者匿名化处理。', 'id': '1002127_30'}
{'label': 'text', 'content': '传输安全\n在ZENIC ONE中，基于安全设计原则，ZENIC ONE和外部接口必须是传输安全的。ZENIC ONE和外部的接口包括但不限于如下： \nZENIC ONE和北向的接口。 \nZENIC ONE和南向的接口。 \nZENIC ONE和其它系统对接的接口，如安全LDAP、Radius、Syslog对接。 \nZENIC ONE和浏览器的接口，包括OpenAPI的接口。 \nZENIC ONE的MML(CLI)对外提供的接口。 \nZENICONE为命令行界面（CLI）和图形用户界面（GUI）接口的所有管理、操作员和客户访问实施安全的加密协议。', 'id': '1002127_31'}
{'label': 'text', 'content': '传输安全\n南北向接口\n在ZENIC ONE中，ZENIC ONE对外的接口主要包含北向接口与南向接口，系统内部组件间采用Rest接口进行通信。ZENIC ONE通过TCP机制保证南北向数据传输完整性。其支持的北向接口如下表。\n表10 NBI\n| 名称 | 配置 | 告警 | 性能 | 资产 | 采用安全协议 |\n|:-|:-|:-|:-|:-|:-|\n| XML | √ | √ | √ | √ | Https |\n| SNMP | | √ | | | SNMPv3 |\n| FTP/SFTP(FILE) | | | √ | √ | SFTP |\n| Rest | √ | | | | Https |\nZENIC ONE支持的南向接口如下。\n表11 SBI\n| 名称 | 说明 |\n|:-|:-|\n| Qx | Qx接口是基于ITU-T的Q3接口规范简化而来的一种私有通讯接口格式，是基于标准TCP/IP包的管理协议。支持在包括带内DCC/ECC或带外等通讯介质上传送。具有开销少、结构规范、效率高等特点。ZENIC ONE能自动适配不同协议类型。 |\n| SNMP | SNMP是基于TCP/IP的应用层网络管理协议，它使用UDP协议作为传输层协议，能管理支持代理进程的网络设备。ZENIC ONE支持标准的SNMP v1/v2/v3接口，通过SNMP接口可以实现ZENIC ONE同设备连接。用于发现网络设备，实现业务配置数据同步、故障管理和性能管理等基本管理功能。 |\n| Telnet/STelnet | Telnet接口和STelnet（SSH Telnet）接口是管理网络设备的基本接口之一，用于远程登录和管理设备。通过Telnet/Stelnet接口弥补通过SNMP接口管理的不足，并增加部分额外的管理功能。Telnet用于从管控启动命令窗口访问网元，直接使用CLI命令行对网元进行维护配置操作。Telnet是基于TCP/IP的应用层网络管理协议，它使用TCP协议作为传输层协议，给网络通信提供服务。但Telnet采用明文传输通信数据，存在安全隐患。STelnet是一种安全的Telnet服务，建立在SSH连接的基础之上。SSH可以利用加密和强大的认证功能提供安全保障，保护网元不受诸如IP地址欺诈等攻击。 |\n| FTP/SFTP | FTP/SFTP是基于TCP/IP的应用层网络管理协议，FTP依赖于UDP协议，SFTP依赖于SSH over TCP协议。FTP（File Transfer Protocol）是用于在网络上进行文件传输的一套标准协议。密码和文件内容都使用明文传输。SFTP（SSH FTP）通过SSH协议提供安全的文件传输和处理功能。使用SFTP方式备份时，指令与数据在传输过程中都是经过加密的。 |\n| Syslog | Syslog接口作为管控系统接收设备系统日志的接口，用于网元日志管理功能。 |\n| Netconf | Netconf是一种提供网络数据设备配置管理的协议。它是为弥补简单网络管理协议（SNMP）和Telnet协议在网络配置方面的功能不足所设计的。Netconf协议提供了安装，操作和删除网络设备配置的机制。它采用基于数据编码的可扩展标记语言配置数据以及协议信息。在自动化网络配置系统中Netconf起着关键性的作用。系统可采用基于YANG模型的netconf下发设备命令。支持SSHv2安全协议。 |\n| PCEP | 当控制器基于L3拓扑等信息计算出TE路径后，通过此协议下发LSP路径给网络设备。采用KeyChain的安全传输通道。 |\n| BGP-LS | 通过此接口获取网络L3拓扑信息。采用KeyChain的安全传输通道。 |\n| BGP-FS | 通过此接口对流量进行精确匹配和过滤，实现流量的细颗粒度管理。 |\n| Telemetry | 搜集网络状态信息、性能数据、流量信息。采用TLS安全协议。 |\n| OF-config | 用于对SDN交换机进行远程配置和管理 |\n| gRPC | 一个高性能、开源和通用的RPC框架，用于客户端与远程服务器端之间的通讯 |\n', 'id': '1002127_32'}
{'label': 'text', 'content': '传输安全\n传输安全\n传输安全\n在ZENIC ONE中，对于管控系统的数据传输，可以分为管里面、控制面、以及转发面，在进行组网设计时，可以将管理/控制/转发平面数据进行隔离。\n对于需要加密传输的机密数据，如认证信息（用户、密码）等，整个系统中都是以加密后的密文形式存在，包括：从数据输入，到数据传输，到最后的数据保存等，确保数据在网络传输过程中难以被窃取和篡改。对所有ZENIC ONE API的访问都需要在访问控制和身份验证后，才能获得批准，如：\n外部系统进行API调用时，会统一通过建立用户，采取oAuth2.0用户凭证模式进行调用。\n认证客户端：各个微服务都要调用认证接口进行认证，通过认证代理模块，完成用户认证和访问令牌的获取。\nZENIC ONE系统对协议支持情况如下表：\n表12 安全传输协议\n| 名称 | 说明 |\n|:-|:-|\n| SSL | 采用3.0或更高版本，X.509证书认证 |\n| SSH | 采用2.0或更高版本; |\n| TLS | 采用1.2或更高版本 |\n| SNMP | 支持SNMP v1/v2/v3，SNMP v1/v2代理可禁用 |\n| HTTPS | 1.使用get方法时加密URL中的参数2.支持2048位私钥进行部署3.仅启用TLS版本1.2及更高版本，并禁用所有较低版本启用具有128位身份验证和加密功能的密码套件或更强的密码套件4.禁用不安全的重新协商5.禁用TLS压缩-CRIME攻击6.禁用RC4 密码套件7.所有的Cookie都设置“Secure”标志及“Http only”标记，保证Cookie不采用不安全Http协议进行传输。 |\n| SFTP | 支持禁用 |\n| NTP | 支持NTP协议的安全机制。支持MD5进行验证。支持NTP V4的Auto key 技术。 |\n| IPSec/IPv6 | 支持基于IPSec的IPv6协议。 |\n', 'id': '1002127_33'}
{'label': 'text', 'content': '传输安全\n传输安全\n端口列表\n在ZENIC ONE中，在系统南北向间部署防火墙时，需要打开的端口列表如下。', 'id': '1002127_34'}
{'label': 'text', 'content': '传输安全\n传输安全\n网络地址转换\n在ZENIC ONE中，ZENIC ONE对外仅暴露网关IP地址，如下图所示，外部应用访问ZENIC ONE 时，目的IP输入网关IP地址*********即可。 \n网关收到请求以后，对于HTTP/Restful调用请求中的URL，会解析出由哪个应用服务相应对应请求。如下图所示由MeLcm服务响应对应的restful请求。MeLcm服务中有三个POD，其在内部网络平面上的IP分别是**********-13。MSB选中其中POD2作为本次请求的目标，按DNAT转换方式，将请求报文中的目的IP替换为POD2 的内网IP地址**********，转发指POD2。 \nPOD2处理完毕后，应答请求时，按外部应用IP *********作为目的IP发送出去。内部网络平面上没有对应的IP可以接收这个IP报文，因此会被默认发送到网关上。网关此时按SNAT方式，将应答报文中的源IP替换为网关IP，然后将其发送到外部网络平面上。', 'id': '1002127_35'}
{'label': 'text', 'content': '传输安全\n传输安全\nWeb安全\n在ZENIC ONE中，XSS（Cross Site Scripting）攻击会让恶意攻击者在web页面中会插入一些恶意的script代码。当用户浏览该页面的时候，那么嵌入到web页面中script代码会执行，因此会达到恶意攻击用户的目的。ZE跨站请求攻击，简称CSRF（Cross-site request forgery）或XSRF，可以让攻击者通过一些技术手段欺骗用户的浏览器去访问一个曾经认证过的网站并执行操作，从而造成客户的会话被利用等一系列影响。ZENIC ONE能够防止CSRF攻击并已通过渗透性测试。 \nNIC ONE能够防止XSS攻击并已通过渗透性测试。', 'id': '1002127_36'}
{'label': 'text', 'content': '操作系统安全\n在ZENIC ONE中，CGSL是一款基于Linux的安全操作系统，遵从GPL V2开源协议。在安全功能上如访问控制、网络安全、数据安全、安全认证、易用的安全策略配置管理上提供可持续性的安全保障，防止关键数据被篡改被窃取，系统免受攻击；并提供针对特定应用的安全策略定制和系统定制；兼容主流的软硬件，包括数据库系统、中间件和用户应用等，保障运行于其上的关键应用能够安全、可控和稳定对外提供服务。CGSL为用户提供全方位的操作系统和应用安全保护。CGSL主要获得认证有： \n国际Linux基金会的电信级Linux操作系统CGL 5.0认证。查询地址：https://wiki.linuxfoundation.org/cgl/registered-distributions \n由中国公安部所负责检验《GBT 20272-2006 信息安全技术 操作系统安全技术要求》四级结构化保护级认证，并获得销售许可证。 \n国际Nessus安全漏洞扫描软件认证并收录数据。查询地址: https://www.tenable.com/plugins/search?q=NewStart&sort=&page=1 \n此外，CGSL还提供在线的CVE和SA查询平台，帮助用户快速的查询安全风险并获得支持。查询地址: http://security.gd-linux.com/ \nZENIC ONE CGSL操作系统同时支持rSyslog，rSyslog与syslog-ng类似，增加TLS加密，因而能够提供比传统syslog服务更高级别的安全性。', 'id': '1002127_37'}
{'label': 'text', 'content': '漏洞扫描及安全认证\n漏洞扫描及渗透测试\n在ZENIC ONE中，漏洞扫描的主要目的为模拟真实的入侵行为，对ZENIC ONE进行安全漏洞扫描测试，评估是否存在可以被攻击者真实利用的漏洞以及由此引起的风险大小，为制定相应的安全措施与解决方案提供实际的依据。目前ZENIC ONE进行的漏洞扫描主要有： \n绿盟扫描 \nAWVS（Acunetix Web Vulnerability Scanner）安全漏洞扫描 \nNessus安全漏洞扫描 \nKlocWork工具代码级白盒扫描，保证代码无缺陷漏洞，漏洞全部治理。 \nCoverity工具代码级白盒扫描，保证代码无缺陷漏洞，漏洞全部治理。 \nDefensics模糊测试工具扫描。 \nPeach模糊测试工具扫描 \nAppScan web项目安全测试工具。 \nBurp Suite 应用安全测试工具。', 'id': '1002127_38'}
{'label': 'text', 'content': '漏洞扫描及安全认证\n安全认证\n在ZENIC ONE中，ZENIC ONE基于ZTE自研PaaS平台 OpenPalette研发，OpenPalette平台已于2019年通过CSA STAR Tech PaaS平台安全能力增强级认证，产品整体成熟度及安全性处于世界领先水平。CSA STAR Tech认证由英国标准协会（BSI）和国际云安全联盟CSA（Cloud Security Alliance）联合推出，分为基础级和增强级，是云计算领域最权威的统一安全能力级别认证标准。 \nCIS（Center for Internet Security）也是目前业内主流的安全认证组织，ZTE正在积极推动ZENIC ONE 对于CIS的相关认证。目前ZENIC ONE多个组件已遵循CIS benchmark（https://www.cisecurity.org/cis-benchmarks/），如操作系统CGSL、PaaS平台 Openpalette、Docker组件、Kubernates、应用组件等。CIS安全认证主要对操作系统、中间件、应用组件等依据benchmark进行扫描评分，目前ZENIC ONE各组件整体已经达到CIS安全加固90%得分。', 'id': '1002127_39'}
{'label': 'text', 'content': '安全加固\n在ZENIC ONE中，ZENIC ONE进行的安全加固操作详情如下：', 'id': '1002127_40'}
{'label': 'text', 'content': '开源软件\nZENIC ONE开源软件及漏洞扫描\n在ZENIC ONE中，ZENIC ONE开发过程中用到了许多开源组件，软件组件及漏洞扫描结果如下表。ZENIC ONE能够定期对用到的开源组件进行安全治理，保证不存在风险。', 'id': '1002127_41'}
{'label': 'text', 'content': '开源软件\n操作系统开源软件\n在ZENIC ONE中，CGSL构建自开源社区，遵从GPL V2开源协议。操作系统CGSL开源组件，如下表。', 'id': '1002127_42'}
