{'label': 'document', 'content': '网络仿真及数字孪生', 'id': '1027063'}
{'label': 'text', 'content': '网络仿真及数字孪生\<EMAIL> 2021.04 \n汇报提纲CONTENTS', 'id': '1027063_1'}
{'label': 'text', 'content': '1 数字孪生，迈向自动驾驶网络\n2 ZTE数字孪生网络创新方案 \n3 数字孪生网络合作建议', 'id': '1027063_2'}
{'label': 'text', 'content': '数字化实现云网融合\n构建数字化平台，可拆解各层中台能力与运营商中台形成合力，根据云+网+行业应用一体化运营要求， 拉通云网，更好满足客户的要求', 'id': '1027063_3'}
{'label': 'text', 'content': '工业4.0及数字孪生的应用\n仿真即生产，生产即数据，数据即产品和服务。。。', 'id': '1027063_4'}
{'label': 'text', 'content': '网络数字孪生，迈向自动驾驶网络\n引入云网业务锚点=云POP + 云GW，配置模块化部署，支撑企业上云、分支互联、多云互联 1，从物理网络空间，通过南向接口实时 采集现网拓扑、流量及状态等数据，并完 成网络数字空间的建模及数据存储； 2，在网络数字空间完成仿真分析，例如 故障仿真、流量仿真、变更仿真、worst case分析等，分析预测物理网络存在的潜 在风险及瓶颈，并告知用户提前扩容； 3，用户基于仿真分析结果，对网络进行 变更，例如增加节点、增加链路、FlexE 带宽调整等； 4，当新设备、新链路上线，则自动下发 相关配置到设备，完成网络的变更调整； 5，物理网络形成新的平衡，例如流量基 于新的拓扑重优化，之前出现的拥塞消失 或减轻； 6，物理网络空间的状态通过telemetry 实时同步给数字孪生体，和前期方案进行 验证及方案优化；数字孪生基于时序记录 网络的历史状态，实现历史回溯及未来预 测；最终，通过数字孪生，实现网络的自 动驾驶。', 'id': '1027063_5'}
{'label': 'text', 'content': '网络数字孪生的客户价值\n数字孪生网络的目的是帮助我们在网络部署前在数字空间环境中发现潜在在问题，仿真和虚拟测试，激 发创新思维，提供最优部署方案；在网络部署后，也可通过仿真，发现运行中及未来存在的潜在问题， 并反馈到前期的方案设计进行验证，不断追求优化和进步，降低OPEX和CAPEX； \n<table><tr><th colspan="2">规划设计人</th><th>工程开通人员 案 配置方案</th><th>运维人员 配置方案 优化方案</th><th>运维人员 优化方案 割接方案</th><th>运维人员 割接方案</th></tr><tr><th colspan="2">网络规划</th><th>网络建设</th><th>网络维护</th><th>网络优化</th><th>割接迁移</th></tr><tr><th></th><th></th><th></th><th></th><th></th><th></th></tr><tr><th>阶段1 阶段2</th><th>阶段3</th><th>阶段4</th><th>阶段5</th><th></th><th></th></tr></table> \n汇报提纲CONTENTS \n1 数字孪生，迈向自动驾驶网络 \n2 ZTE数字孪生网络创新方案 \n3 数字孪生网络合作建议', 'id': '1027063_6'}
{'label': 'text', 'content': '总体架构\n北向接口 网络规划 流量仿真 网络拓扑 网络流量 网络历史 故障仿真 应 用 层 网络规划和仿真 网络可视化', 'id': '1027063_7'}
{'label': 'text', 'content': '网络数字孪生体\n网 络 孪 生 体 网络仿真 网络预测 网络部署 网络模型（节点、链路、拓扑（物理/逻辑/切片）、流量、路径、TE等） DB 镜 像 DB 实 时 虚 实 交 互', 'id': '1027063_8'}
{'label': 'text', 'content': '在线网络感知\n1，网络发现及流量采集（在线/离线） • 网络资源和拓扑发现，通过snmp/netconf and bgp-ls南向接口； • 采集器实时采集端口/链路/接口/LSP流量统计信息； 2，网络仿真及分析 • 对流量进行建模，进行流量仿真 • 在业务流量源宿端口导入流量需求； • 基于业务流量矩阵及PCE算法，建立E2E LSP路径； • 变更仿真，包括拓扑、Metric等，输出风险评估报告； • 故障仿真，输出业务风险评估报告. • 流量仿真，导入业务流量，输出对网络的影响分析报告； • Worst Case最坏情况仿真，输出网络风险评估报告； 3，网络编程 • 网络调整/变更，包括拓扑变更、业务部署等；', 'id': '1027063_9'}
{'label': 'text', 'content': '主要功能及亮点\n• 应用场景：IP/IPRAN的SR网络 • 流量分析及预测 • 网络可视化 • 拓扑 • 流量 • 历史回溯 • 仿真能力 \n• 网络建模，在线构建/离线构建 \n• 网络仿真 • 故障仿真及what-if分析 • 流量仿真及容量规划 • 流量优化 • 网络编程 \n• 拓扑变更 • 新业务引入 • 维护升级及扩缩环', 'id': '1027063_10'}
{'label': 'text', 'content': 'Use  Case  1：故障仿真及影响评估，提前发现潜在风险\n1，故障仿真前链路利用率 2，故障仿真后链路利用率 3，网络风险评估报告', 'id': '1027063_11'}
{'label': 'text', 'content': 'Use  Case2：流量仿真，提前评估资源瓶颈\n1，流量增加前的链路利用率 2，流量增加30%的链路利用率 3，网络风险评估报告', 'id': '1027063_12'}
{'label': 'text', 'content': 'Use  Case3：拓扑仿真，网络容量精准规划\n1，链路拥塞，需要在KLG 和BAT之间增加链路 2，增加链路后，拥塞减轻 \n注：也可应用于节点的升级维护 /维护窗/扩缩环/增量部署等场景； \n3，网络风险评估报告', 'id': '1027063_13'}
{'label': 'text', 'content': 'Use  Case4：业务仿真，提前评估新业务引入对网络的影响\n1，新业务增加前 的链路利用率 2，新业务增加后 的链路利用率 3，网络风险评估报告', 'id': '1027063_14'}
{'label': 'text', 'content': 'Use  Case5：Worst  Case最坏情况分析，提前预测和规避风险\n2，全网端口及链路利用率 1，多次故障的WC分析 \n3，全网LSP带宽利用率', 'id': '1027063_15'}
{'label': 'text', 'content': 'Use  Case  6：网络编程/变更，基于最优方案的科学决策\n2，基于最优方案，完成网络的配置变更 1，在仿真环境试错，得出最优方案 \n3，设备/链路上线，一键下发 \n2021.04', 'id': '1027063_16'}
