{'label': 'document', 'content': '承载网络智能化解决方案_解说词_20231204_CN', 'id': '1110052'}
{'label': 'text', 'content': 'P4: \n根据2021Omida分析报告支持，运营商们正在将网络向SDN和网络自动化演进。趋势该趋势的主要因素是来自业务快速发放，运营效率提升和降低资本输出。 \nP5: \n自智网络是网络技术与数字技术的交叉点，自智网络2019年由TM Forum联合产业伙伴共同提出，旨在引领网络基础设施和运营体系的自动化、智能化转型。 \nP6： \n自治网络实现的4要素。在通过完全自动化、智能化的网络和 ICT 基础设施、敏捷运营和全场景服务，为垂直行业客户和消费者提供零等待、零接触、零故障的极致体验，利用前沿技术，将简单带给用户，将复杂留给供应商 \nP7： \n自治网络的五级演进路线 \nP6: \n通过“意图驱动、自动化控制、网络感知”三大引擎驱动，提供网络规划设计-业务发放-网络部署-网络感知-网络优化全流程的自动化能力，以实现业务自愈及网络自愈. \n基于云原生的架构体系： \n基于微服务化的云原生架构平台让系统可灵活扩展和弹性伸缩，能快速对接第三方提供新应用、新网络设备和新业务的建设，新设备及新业务的开发周期从以往的数月缩短到数天。有效降低客户的硬件投入和软件开发投入成本。 \n开放的网络能力及管理能力的API，让服务可开放、网络可编程、业务可定制，致力于为用户构筑完整的端到端运营解决方案，同时配合DevOps工具，支持在线开发、部署、运维托管、合作运营，支撑创新应用的快速定制和快速持续交付。 \n认知型的意图控制引擎： \n通过意图控制的闭环系统，可准确转换为网络行为并自动部署，快速达成商业目的；通过用户意图深入挖掘商业机会点，结合企业的数字业务需求和网络管道，为用户数字化业务提供连续、敏捷、安全和增长保障，提升网络价值。 \nP7: \n自动化系统演化有三个阶段。第一阶段是可编程网络阶段，最传统阶段NMS直接连接OSS/BSS，没有控制器，如果网络不复杂，则有意义。但是，随着SDN/NFV技术的发展，需要管理的NE出现了爆炸性增长，因此在SDN阶段，我们采用控制器和NMS共同管理网络，服务协调器协调网络。这给我们的合作伙伴带来了很多好处，但仍然存在一个大问题，即控制器和NMS是分开的。为了解决这些问题，控制器和管理融合是演化的趋势，这就是第二阶段，主要采用为云原生、微服务、AI技术。未来，SDN 将会演进到IBN阶段，主要特点就是更加智能，用户可以基于自身意图实现网络的管理。 \nP9: \n智能化分为三个层次，转发层简化最重要就是引入SR技术。SR技术能够与SDN完美契合，是实现自动化的关键底层技术。而管控层主要就是形成规划-建设-维护-优化的全生命周期闭环。业务层最核心关键在于开放的API，通过OPEN API，客户实现软件模块定制化开发。 \nP10: \n转发层对传统的MP-BGP，BGP-LU,LDP/RSVP-TE协议进行了简化，引入EVPN及SR技术，配合SDN控制器实现整个网络的简化。 \nP11: \n对于管控层，ZTE支持从网络构建和仿真、网络服务部署、网络感知和保证到网络优化的5G全生命周期。 \n在网络建设和仿真方面，ZENICONE支持典型功能增强型快速服务提供、故障模拟、流量模拟。 \n在网络部署方面，支持EVPN调配、SR-TE隧道、SRPCE路径计算、网络切片、5G服务迁移等特征。 \n在网络保障方面，包括以下关键功能、网络切片监控、遥测、避免路径共享风险、基于机器学习的RCA \n对于网络优化，包括SR堆栈深度优化、流量分析和授权、基于不同策略的流量调整。 \nP12: \nAthena包括意图、自动化、感知三大引擎，其中意图引擎是ZENIC ONE的核心。 \n意图引擎包括意图翻译、意图感知、意图保障三大组件。意图引擎对外支持WEB界面和第三方北向接口，WEB界面中的意图管理（意图输入）组件负责用户意图的输入，意图管理（意图状态）组件负责意图生命周期内的状态呈现。意图翻译组件负责与意图管理组件交互，当用户通过语音、文字等多种输入方式表达其意图后，意图翻译组件与之交互确认，确保意图表达的完整性、去模糊性，构建起用户与机器之间的桥梁。意图翻译组件还实现了一致的意图实现流程，即不管是用户输入的意图，还是网络内部的修正/优化意图，都会转化为统一的网络意图表达模型，并经历方案设计、网络编排和提前验证流程，然后生成配置信息下发给自动化引擎。意图感知组件将感知引起上报过来的网络数据针对具体意图进行分析后，将意图分析结果同时发给意图管理（意图状态）组件和意图保障组件。意图保障组件根据该意图既定的保障策略进行对应的保障处理，比如带宽调整、路径调整、保护恢复等，确保用户意图的持久质量。 \n自动化引擎包含网络控制、网络管理服务。网络控制服务可支持IP、IPRAN、SPN/PTN、OTN等网络的控制，并与网络管理服务紧密配合实现对各类网络的统一管控；自动化引擎接收意图引擎下发的配置信息，经内部处理后下发到实际设备上，完成意图所对应业务的创建。如果涉及跨专业网或跨域的场景，自动化引擎可能有多个实例以管理多个专业网或多个域。 \n感知引擎采集全网的海量数据、存储，并对海量数据使用机器学习算法进行关联分析、深度挖掘、预测，然后将网络分析结果发送给意图引擎中的意图感知组件。 \n第三方北向服务对接运营商上层系统，与上层系统一起实现用户业务自动发放，支持客户的平滑演进及对新业务新需求的快速满足。 \nP13: \n（BIGDNA属于增值功能，需要额外报价，具体口径与策略请与市场接口人沟通） \nAthena解决方案主要产品:ZENIC ONE作为管控系统实现对网络的管控及分析。产品南北向如下： \nZENIC ONE： \n北向：Rest/SNMP/FTP/XML/Corba/WebSocket \n南向：NetConf/SNMP/TELNET/SSH/Qx/PECP \nP14: \n云原生架构的开放能力： \n基于微服务化的云原生架构平台让系统可灵活扩展和弹性伸缩，能快速对接第三方提供新应用、新网络设备和新业务的建设，新设备及新业务的开发周期从以往的数月缩短到数天。有效降低客户的硬件投入和软件开发投入成本。 \n开放的网络能力及管理能力的API，让服务可开放、网络可编程、业务可定制，致力于为用户构筑完整的端到端运营解决方案，同时配合DevOps工具，支持在线开发、部署、运维托管、合作运营，支撑创新应用的快速定制和快速持续交付。 \n认知型的意图控制引擎： \n通过意图控制的闭环系统，可准确转换为网络行为并自动部署，快速达成商业目的；通过用户意图深入挖掘商业机会点，结合企业的数字业务需求和网络管道，为用户数字化业务提供连续、敏捷、安全和增长保障，提升网络价值。 \n智能级的自动化： \n构建一个规划-建设-维护-优化的全生命周期闭环自动化。 \nP16: \n方案基于ZTE自研PaaS平台，采用容器化部署，支持管理ZTE及第三方厂家网络。 \n采用网络操作系统、APP理念，用户安装运营商/客户可以通过安装方式选择所需功能APP进行安装/删除/升级 \n系统提供DevOps环境，为用户提供开放的开发环境，用户可给予系统自行研发功能APP。厂家APP和用户APP可通过系统APP商店推送平台，推送给用户进行选择性安装。 \nP17: \n1、智能脚本：系统提供Python SDK开发脚本功能，用户可通过Python语言自行开发功能脚本、Web应用脚本、增MML脚本等脚本，通过接口加载脚本以实现用户开发功能 \n2、Paas微服物化开发定制：基于Paas平台提供开发接口，用户可基于平台开发新功能，并通过厂家提供的平台进行新功能推送 \n3、OpenAPI：系统内部功能模板提供开放API接口，用户可通过API接口开发新功能。 \n4、北向接口能力：提供多种北向接口类型 \nP18: \n对外提供OpenAPI的能力，因此，基于DevOps快速开发能够实现功能组件快速上线，用户可能基于Athena产品提供的开放平台进行快速定制化开发组件。 \nP20: \n智能级自动化方案主要从规建维优四个方面来实现全生命周期闭环管理。对于每一个阶段，都存在关键的智能化功能。 \n规划： \n网络规划： \n自顶向下规划网络架构 \n定义网络AS域与路由协议 \n定义设备角色与基础特性 \n设计网络拓扑结构 \n定义IP等基础配置规则 \n个性化调整 \n仿真分析 \n结合用户意图，构建一个平行世界来验证用户意图 \n在仿真环境下设计新业务，提供规范的业务模板 \n规模化模拟业务和流量，提前验证网络容量 \n准实时同步现网，逼真仿真网络行为 \n流量视频展示过去与未来 \n交互式与静默式运行 \n网络快速构建 \n无需下站，DCN实现网元自动发现 \n设备便捷开通，无需下站，ZTP实现设备自动纳管，版本自动下载，基础配置自动化 \n建设： \n业务自动发放 \n业务自动化，模型驱动，实现业务的快速开通，自动基于业务SLA建立路径，并基于业务性能自动调整和优化 \n跨域跨层自动化，通过分层控制器实现跨域协同和跨层协同，实现端到端的网络自动化 \n维护： \n网络感知 \n网络状态实时收集 \n实时网络告警分析 \n网络流量分析和预测 \n优化： \n网络优化 \n网络自动优化 \n生存性分析和建议 \n拥塞调整 \nP21: \n按网络规划划分的ZeroTouch网络部署 \n基于e2e服务流设计的可编程服务 \n用户点击应用程序实现触发零接触服务 \n网络瓶颈时规划工具优化的实现 \nP22: \n基于流量预测结果，再进行仿真规划。实现了对网络瓶颈的提前识别，使得用户能更好设计后续网络。 \n流量预测可以分为多个场景来进行，从而使得预测准确率更高。 \n1.按地域按业务流量增长预测, 简称:流量增长预测 \n2.按地域忙闲时流量平均/峰值预测, 对忙闲时流量进行建模,用于估算忙时流量和闲时流量占比关系, 时间关系, 简称: 忙闲时预测 \n3.按天,周,月平均/峰值流量预测, 对平均流量和峰值流量关系进行建模, 用于估计峰值流量是平均流量的倍数关系,简称: 峰值预测 \n4.重点区域峰值流量预估, 对重点区域的峰值进行保障, 简称:重点区域流量保障 \n5.重点节日峰值流量预估, 对节假日流量峰值与平均流量进行建模, 用于估计平均流量为x情况下, 节假日峰值流量的关系, 简称:重点节日流量保障 \nP23: \n5G网络庞大且复杂，因而提前对潜在故障风险进行识别、优化，对于网络质量提升极具价值。网络仿真对实际网络进行模拟，还原网络真实拓扑与协议，构建一个虚拟网络，通过在虚拟网络场景中研究网络运行规律和响应机制，分析在外界正常和异常变化情况下网络的处理能力和网络质量的改变，从而评估网络调整对现网产生各种影响，为网络的规划、建设、维护、优化提供决策依据。依据多次网络仿真“沙盘推演”的结果进行网络优化，避免了对现网进行频繁调整，在保证稳定性前提下，有效提升网络运维效率。 \n中兴通讯承载网网络仿真方案涉及IP/IPRAN、PTN/SPN、OTN等多种场景，并包含故障仿真、流量仿真、质量仿真、协议仿真等多种类型。总体上看，网络仿真的典型流程一般分为三步：一，镜像网络：接收管控系统的输入，基于现网拓扑、配置、流量信息构造网络镜像模型，通过镜像网络反映现网的真实状况；二，网络变更模拟：围绕镜像网络进行各种网络变更，以模拟现实网络中可能会发生的内外部环境变化并输出结果；三，仿真分析：基于仿真结果 分析各种网络变更对网络的影响，识别出风险点和风险水平并直观展示，辅助运维人员做出最佳决策（见图1）。网络仿真这种假设分析的评估方法，让运维人员对故障范围、流量变化影响、网络质量状况有了更加清晰的认识，具有广泛的应用价值及商用前景。 \n镜像网络 \n镜像网络为仿真操作提供了一个实时同步现网状态的模拟网络，使得仿真更加“真实”，从而达到在线仿真、实时仿真的效果。依据仿真的需求，网络镜像主要由三部分组成：一，物理管道：即设备和链路构成的基础物理网络，其他一切工作都基于物理管道之上开展；二，逻辑管道：在物理管道基础上通过各种隧道技术、VPN或路由等技术划分的动态、静态路径；三，流量镜像：即在物理和逻辑管道模型基础上模拟数据在逻辑管道中的流动规律，从而构建完整的网络仿真试验环境。网络仿真动作以相应的流量镜像为基础，同时，研究流量的变化趋势也是网络仿真的主要目标。 \n网络变更模拟典型类型 \n网络镜像构建好以后，围绕网络镜像的三个基本特征（物理管道、逻辑管道和流量镜像），通过触发一定的事件（网络变更）来模拟镜像网络发生变更，即进行仿真操作。一般来说网络变更分为故障模拟、流量/质量模拟、物理管道模拟（物理拓扑规划）、逻辑管道模拟（协议仿真）等类型。 \n故障模拟，顾名思义即模拟网络中发生一个或多个故障后的情形，如IP网络L3链路/TE隧道断路，单板/芯片故障，OTN链路断纤等。通过网络自我调整处理的结果，分析故障发生前后的流量分布变化和网络质量劣化情况。流量/质量模拟，即模拟网络受外部事件或预期流量事件影响而发生流量变更，并将其叠加到流量镜像中，通过观察网络流量的转发分布及其变化情况，来发现网络出现拥塞的可能性和时间、位置。此外，对网络质量指标系数（如时延、抖动等）进行模拟变更来分析网络的质量状况。物理管道模拟，即模拟物理拓扑改变（如扩缩容）来触发变更仿真计算，通过网络自身处理的结果（如隧道、业务路径的变化等），进而分析网络流量/质量变化情况。逻辑管道模拟则不改变网络物理拓扑，仅通过调整设备协议参数（如带宽、保护路径、优先级等）达到影响流量转发规则的目的。并基于网络自处理的结果来分析协议变更前后的网络流量/质量变化情况。 \n以链路的故障模拟为例，在构建的网络镜像中，系统通过分析发现当模拟链路a出现断路故障时，会造成模拟链路b、c出现拥塞的风险。因此系统会输出可行性报告，建议用户增加链路b、c的带宽，从而达到规避风险的目的（见图2）。在现网正常的情况下，提前进行网络优化。 \n用户可以依据不同需求进行相应的网络变更模拟操作，各类型变更模拟也可以相互配合，进行叠加操作，从而更好地识别网络中潜在的风险。 \n仿真分析 \n仿真的目的是为了提升网络的质量，从而让网络更好地服务于用户。仿真分析对网络变更模拟操作结果进行判断，分析网络变更对网络局部或全局的影响，分析网络现有的流量/质量瓶颈，并为网络优化提供指导意见，极具价值。 \n网络变更影响可以从业务级、隧道级、链路级、网络级等多个维度与层面进行分析。从业务层面看，网络发生变更可能会触发重路由等动作，从而对L2VPN、L3VPN等业务造成影响，因而业务层面的仿真分析主要针对业务状态、路径的变化，业务依附隧道的变化，业务流量的变更进行分析。同理，针对隧道层面的分析则主要涉及隧道状态、路径的变化，隧道承载的业务变化，以及隧道的流量变更。网络变更对链路层面的影响则主要体现在汇总流量的改变，仿真模块可以通过分析物理链路上叠加的隧道流量累计值情况来判断网络瓶颈。而网络级分析则是从全网的角度对网络的质量进行评估，通过构建全局性的网络质量指标，或者可以由客户自定义的网络指标来分析判断网络仿真结果对全网的影响。此外，生存性分析（健壮性分析）则是仿真分析的另一种方式。生存性分析主要对网络的抗打击能力进行研究，即模拟网络发生了一次故障、二次故障甚至多次故障的情况下，进行网络健康程度评估。同时通过生存性分析找到超过风险阈值的节点和链路，从而实现潜在风险的快速定位。 \nP24: \n传统DCN开通需要用户下站，现在采用DCN便捷开通功能，管控系统提供多个模板，用户只需选择所需模板进行配置下发即可。极大简化配置参数的数量，提升了开通效率 \nDCN便捷开通的优点： \n免进站，免配置 \n设备硬件安装后，Athena自动发现，无需软件工程师进站 \n网元、单板、链路自动发现，网络拓扑自动生成 \n可规划，可设计 \n支持管理面参数设计，包括DCN、管理通道、VLAN及三层接口参数设计 \n支持控制面参数设计，包括路由协议、MPLS协议参数设计 \n支持Excel表格规划，无缝对接系统 \n模板灵活定制，支持多场景 \n模板可定制，满足定制化场景要求 \n预置模板，典型配置一键式开局 \n开通范围灵活控制，可指定全网、接入环或逐跳 \nP25: \n开通业务是用户在网络运维中遇到的最频繁且最重要的操作之一。传统业务开通方式，用户需要进入管控系统对应的功能窗口，选择繁多的业务参数进行逐点配置，才能完成业务的开通，此外还往往容易发生因用户操作失误而造成业务开通失败情况。因此，业务开通面临着人力投入大、人员要求高、开通时间长、开通成功率低的问题。业务自动开通服务则有效解决了上述问题，在大幅降低用户技能要求的同时，将业务开通时间缩短80%。 \nP26: \n传统SNMP方式采集周期为分钟级，对网络的感知能力差。Telemetry技术的引入具备如下优点： \n性能卓越：根据支持秒级性能上报，通过PUSH方式，相比SNMP等方式，性能无法比拟 \n灵活部署：根据用户需要，对关注的业务、隧道进行订阅，方便灵活 \n增量上报：stream data 连续增量方式发送 \n格式统一：设备data遵从相同的数据模式 \n安全高效：高效、安全的传输协议（gPRC） \nBigData基石：是进行网络大数据实时分析的基础 \n基于Telemetry秒级性能采集实现对网络的实时感知。此外，基于AI技术的进步，来实现智能RCA能够快速从海量告警中发现根因告警，同样提升了对网络状态实时处理。 \n在基于ML的RCA功能中，首先进行实时数据采集和数据挖掘，然后根据人工智能算法进行报警智能分析和大数据分析，形成知识库，当发现RCA后，可以将结果存储到知识库中。这样整个算法会越发智能，准确率越高。 \nP27: \n故障排除是O&M中一项极其重要的工作，传统故障定位基本依靠人工经验，逐步定位，往往耗费数周时间。对于总结的故障定位经验，无法传承，导致对运维人员不友好。在这个新功能中，引入了规则的概念，规则是判断故障情况的最小子集，通过对多个规则流进行组合、编排，用户可以实现故障快速排除工作。这种组合、编排的方式大大简化了故障定位时间，用户可以基于现网情况进行规则的编排来进行故障定位。此外用户还可以自己编写规则，编写的自定义规则可以于规则库，从而实现专家经验的传承。 \n亮点：客户可以人工写规则，通过规则的组合形成流程库，实现经验可传承。 \nP28: \n场景1：多节点/链路故障，服务自动恢复 \n分析服务受故障影响 \n智能恢复路径计算与资源优化 \n恢复服务 \n通知服务已恢复正常 \n场景2：网络退化分析，风险预案 \n退化预警和性能退化分析 \n判断网络风险 \n必要时制定故障计划 \n为服务添加保护或预设路由 \n基于计划的故障恢复服务 \nP29: \n流量预测可以分为多个场景来进行，从而使得预测准确率更高。 \n1.按地域按业务流量增长预测, 简称:流量增长预测 \n2.按地域忙闲时流量平均/峰值预测, 对忙闲时流量进行建模,用于估算忙时流量和闲时流量占比关系, 时间关系, 简称: 忙闲时预测 \n3.按天,周,月平均/峰值流量预测, 对平均流量和峰值流量关系进行建模, 用于估计峰值流量是平均流量的倍数关系,简称: 峰值预测 \n4.重点区域峰值流量预估, 对重点区域的峰值进行保障, 简称:重点区域流量保障 \n5.重点节日峰值流量预估, 对节假日流量峰值与平均流量进行建模, 用于估计平均流量为x情况下, 节假日峰值流量的关系, 简称:重点节日流量保障 \nP30: \n整个快速自愈的优化步骤如下： \n1.控制引擎收集网络拓扑结构。 \n2.分析引擎从控制引擎导入网络资源和拓扑信息。 \n3.将调优模式设置为自动调优并人手动确认。 \n4.统一收集器收集网络数据。 \n5.分析引擎订阅统一收集器的网络数据，分析和发现网络问题，确定优化目标，生成调整策略和报告。 \n6.分析引擎将优化目标、策略、网络流量数据和其他信息上报管控系统进行呈现。 \n7.根据网络流量状况其他信息，确认调优目标和调优策略是合理的，并给出了验证结果。 \n8.如果结果不成功，将其回滚。如果成功，将它们传递到设备。 \nP32: \nZENIC ONE系统是中兴通讯面向5G时代最新研发的业内首个基于云原生概念的承载网络智能管理-控制-分析一体化SDN系统，通过将AI、大数据等新技术应用于网络运维中，从而构建能力逐级进化、价值持续叠加的自主进化网络，从而更好的为5G垂直行业应用提供实时网络保障。具备如下优势： \n1．技术： \n架构领先：业内首个基于云原生概念的智能网络管理-控制-编排-保障-分析一体化SDN系统，在中兴自研PaaS平台上，基于微服务架构开发，实现30万+等效网元管理规模，提供云计算IaaS/PaaS/SaaS全套解决方案。 \n智能运维：网络资源集中控制、统一调度，对5G垂直行业应用提供实时网络保障，应用AI+大数据提供全生命周期自动化运维，具备新业务快速上线、业务快速开通、网络故障智能分析与预测等自动化能力，解决传统网络自动化程度低、业务审批慢、运维复杂的短板。 \n能力开放：提供标准开放API，可根据不同业务需求做定制化开发，提升5G网络运维效率，快速满足垂直行业用户需求。 \n2．创新： \n参与ONF、ONAP、IETF、MEF、ETSI等标准化组织，并与公司、大学合作。中兴通讯是LinuxFundationAcumos项目的TSC成员（该项目专注AI和ML）；ETSIZSM创始成员，参与网络自动化架构定义；中国人工智能产业联盟（AIIA）副主席单位等等。三年内，ZENIC ONE共获得94项专利，提出67份标准草案，发布1份技术白皮书，并与高校、运营商合作开展10项、20+技术创新课题。GlobalData SDN控制器评级“Very Strong”。 \n3．应用： \n截止2019年8月，ZENIC ONE与全球运营商及垂直行业组织进行30+项联合验证和商用部署，降低网络25% CAPEX和50% OPEX，通过自动化网络与行业应用的合作建立一个更加开放的生态系统。 \nP33: \n（BIGDNA属于增值功能，需要额外报价，具体口径与策略请与市场接口人沟通） \nZENIC ONE引入SDN、虚拟化和容器技术，实现基于PaaS微服务架构的弹性伸缩，采用集中式SDN控制架构，简化设备层复杂的协议以实现设备轻载，真正实现承载网控制和转发功能分离。提供网络规划、部署、保障和分析的全生命周期运维，包括IBN网络设计、敏捷网络规划与设计、业务快速上线、一键业务部署、智能网络故障排查、网络质量分析和保证、网络自愈、流量调优、流量预测等。全面支持FlexE隧道、SR技术和高精度时间时钟等5G关键技术，并通过提供端到端的网络切片来差异化保障eMBB业务、mMTC和uRLLC等不同业务场景的差异化网络指标需求。采用PaaS构建开放的平台，通过DevOps实现持续集成、持续交付，通过OpenAPI建立一个能力开放的生态系统。', 'id': '1110052_1'}
