{'label': 'document', 'content': '中兴通讯基于数据库的开放资源服务网络白皮书_20230316_CN', 'id': '1108115'}
{'label': 'text', 'content': 'IP网络创新技术白皮书\n基于数据库的开放资源服务（DB-ORS）网络\nIP网络创新技术白皮书-基于数据库的开放资源服务（DB-ORS）网络\n| 版本 | 日期 | 作者 | 备注 |\n|:-|:-|:-|:-|\n| v1.0 | 2023.3.16 | 周芬林、钱小聪、袁冬宇 | 第一版 |\n| | | | |\n| | | | |\n| | | | |\n© 2023 ZTE Corporation. All rights reserved.\n2023版权所有 中兴通讯股份有限公司 保留所有权利\n版权声明：\n本文档著作权由中兴通讯股份有限公司享有。文中涉及中兴通讯股份有限公司的专有信息，未经中兴通讯股份有限公司书面许可，任何单位和个人不得使用和泄漏该文档以及该文档包含的任何图片、表格、数据及其他信息。\n本文档中的信息随着中兴通讯股份有限公司产品和技术的进步将不断更新，中兴通讯股份有限公司不再通知此类信息的更新。\n目录\n一、 背景\t1\n1.1云网融合趋势下的网络能力GAP分析\t1\n1.2云网融合趋势下的技术展望\t1\n二、 技术方案\t2\n2.1开放资源服务\t2\n2.2 DB-ORS新型架构\t3\n2.3 DB-ORS关键技术\t4\n2.3.1 原子服务能力抽象\t4\n2.3.2 服务能力定向发布\t6\n2.3.3 强一致性分布式数据库\t7\n2.3.4 云网一体化编排\t8\n2.3.5 DB-ORS安全\t8\n2.4 小结\t8\n三、 应用场景\t10\n3.1 网络为访问云应用的终端提供服务\t10\n3.1.1场景描述\t10\n3.1.2 DB-ORS解决方案\t11\n3.2 网络为多云互联提供服务\t13\n3.2.1场景描述\t13\n3.2.2 DB-ORS解决方案\t13\n3.3 小结\t15\n四、 总结展望\t16\n附录1 缩略语\t17\n附录2 参考文献\t18\n', 'id': '1108115_1'}
{'label': 'text', 'content': '背景\n1.1云网融合趋势下的网络能力GAP分析\n在网络升级、智能制造、融合创新等强国战略引领下，企业数字化改造、行业智慧化发展提档加速，国内数字基础设施的使用已从全面上网走向全面入云。泛在、海量、多样化的应用，早已不满足于传统IP互联网以尽力而为方式提供的数据传输服务，而是呼唤着个性化、差异化、定制化的数据传输服务，例如更低的时延和抖动、更高的稳定性、更好的安全性，等等。 \n为了满足个性化需求，目前运营商基于带宽的互联网流量工程技术为云端应用提供专属服务。但是，基于资源独占的流量工程是种复杂而且奢侈的网络资源分配方式，基于优先级的差异化流量工程又难以同时满足海量应用的差分服务需求。究其原因，长期以来运营商所运营的承载网络是个能力封闭的实体。该封闭性源自传统互联网遵循端到端的设计原则，简化IP层，将复杂性留给端侧，这样导致了业务和网络分离，IP层并不感知上层信息。由于网络能力的自我封闭，承载网没法区分业务，没法实现应用感知，网络能力只能被动地去满足应用需求，资源分配灵活性受到束缚，精准度缺乏指引，利用率难以提升。即使运营商积极探索云网融合的道路，但如果网络对云仍然是一个“黑盒”，那么云侧的流量调度仍难以实时感知网络拥塞情况，仍难以及时获取网络的富余资源能力，从而导致很多业务难以获得精准的SLA保障，现有网络能力也难以得到最优的使用。', 'id': '1108115_2'}
{'label': 'text', 'content': '背景\n1.2云网融合趋势下的技术展望\n如果要彻底解决现有网络技术的不足，满足全面入云新时代的应用需求，我们不妨打破网络能力封闭的现状。 \n我们期望，未来的网络能面向云端业务提供细粒度的资源配置，能面向核心客户按需定制出精细化的服务。我们建议，未来的网络对应用不再是“黑盒”，而是一种授信后的“白盒”——网络把精细规划后的资源以能力形式向特定云客户开放，为云端提供细粒度的应需网络服务。', 'id': '1108115_3'}
{'label': 'text', 'content': '技术方案\n2.1开放资源服务\n本白皮书提供一种网络按需开放资源服务的思想。 \n网络开放资源服务的过程，由需求表达（云侧作为需求方，向网络表达个性化的承载需求）、需求响应（网络基于云侧最新需求，精准分配资源并以服务能力方式向外提供）、响应传递（把网络按需提供的服务能力开放给对应的云侧需求方）、需求满足（云侧获取并使用网络开放的资源服务，实现业务需求）四个相互衔接的环节构成，如图2-1所示。 \n在响应传递环节，本白皮书采用具有强一致性的分布式数据库作为开放资源服务的“中转站”，以支撑网络侧所定制的服务能力向云侧定向投送，实现面向应用的网络资源精准配置和细粒度服务保障。在“中转站”里供给的服务目录里，可以有链路或隧道，可以有低时延、低抖动、高可靠性等QoS能力，还可以有安全能力等等，使得网络服务“白盒化”。 \n需要说明的是，“中转站”服务目录里的是网络所提供的原子服务能力（面向云应用的最小服务单元），这不同于现有的网络切片技术。用铁路交通网作个比方，以前购买一张去往某目的地的火车票，只能在线下的售票窗口询问有没有该目的地的直达或者中转的火车票，购买到的火车票类似于网络的一个固定切片；现在线上的售票系统将全国的列车资源和运输编排通过12306网站开放给所有用户，出行的时间成本、价格成本甚至订餐等增值服务都完全可视，用户能根据自己的出行计划和喜好自主选择符合要求的火车票，例如一等座还是二等座，直达还是中转，是否订餐，等等。服务化网络思想与之类似，将网络的服务能力开放给应用（例如云应用），使得应用可以根据自己差异化的诉求对网络提供的原子服务进行积木式组装或二次编排，从而赋予云端更主动更方便的网络使用模式。结合SRv6编程[1]，订阅网络服务的应用甚至能像编写程序一样去编排网络，业务所需的精细化服务质量可以得到保障；而对网络而言，由于网络资源和业务需求精准匹配，资源利用率大为提升。', 'id': '1108115_4'}
{'label': 'text', 'content': '技术方案\n2.2 DB-ORS新型架构\n2.1节中提出的网络开放资源服务思想，其网络资源能力和业务需求的交互是通过数据库进行的，本白皮书称之为基于数据库的开放资源服务（DB-ORS）。 \nDB-ORS的愿景，是在可控的授信范围内，实现应用与网之间需求和能力相互可见。使得网络可以为业务提供细粒度的服务，可以为核心客户按需提供定制化服务。对于云内业务而言，网络服务随用随取，一点接入[2]。可以说，它是一种灵活、易部署的云网融合轻量级方案。 \nDB-ORS的技术路线，是将网络对业务的承载和保障能力进行抽象，提取得到原子服务能力，通过数据库技术选择性发布服务能力，实现上层应用对网络服务的按需取用。DB-ORS的总体架构涉及服务供给层、服务中间层、服务消费层[3]，如图2-2所示，解释如下： \n服务供给层是承载网，具体包括承担数据转发功能的基础网络和承担网络控制功能的控制器。 \n服务中间层是为云网之间需求和供给信息提供传递、发布、通知等对接服务，及受委托提供云网综合计算和编排服务的实体，可以形象地称为“算网大脑”，具体包括分布式数据库和云网编排器。 \n服务消费层是网络所服务的对象，及该对象的服务对象，具体包括不同的云、云的不同数据中心、云上承载的不同云应用、云应用所关联的客户终端或CPE设备。', 'id': '1108115_5'}
{'label': 'text', 'content': '技术方案\n2.3 DB-ORS关键技术\n2.3.1 原子服务能力抽象\n云及云应用是网络的重要服务对象。为了同时满足不同云应用对网络的定制化需求，网络将其承载能力分拆和聚类，抽象出链路服务、资源服务、SLA服务等大类的原子服务。\n其中，链路服务是最易被抽象且得到规格化描述的能力。承载网需要为不同云应用分别预留链路资源或三层拓扑资源，并使得对应的云应用可以独享为其分配的资源。对于上述预留的资源，可以抽象为下述原子性的服务能力：\n（1）虚拟直连链路(简称为vdlink)。Vdlink由直连链路或三层物理链路抽象而得。每条vdlink会被分配一个唯一性的logic id，并带有本地节点标识、对端节点标识、本地接口地址、对端接口地址等字段，以及其他用以描述其资源能力的属性，包括且不限于最小单向链路时延、最大单向链路时延、单向链路丢包率、最大预留带宽、IGP metric、TE metric、END.X SID等等。其中，最大预留带宽是在物理链路为vdlink预留的最大带宽，原物理链路需要相应进行带宽预减。\n（2）虚拟隧道链路（简称为vtlink）。Vtlink为虚拟SRv6 policy隧道链路，通常由SRv6 policy隧道抽象而来。每条vtlink也会被分配一个唯一性的logic id，区别于普通物理链路和SRv6 policy，vtlink的属性包括本地节点标识、对端节点标识、最小单向链路时延、最大单向链路时延、单向链路丢包率、最大预留带宽、IGP metric、TE metric、Binding SID等等。其中，最大预留带宽取SRv6 policy配置的带宽约束，虚链路生成后，需要在原来物理链路上进行扣减。\n虚拟链路的抽象粒度可粗可细，使用灵活。该灵活性一方面体现在抽象过程中为vdlink、vtlink按需指定链路带宽，并在网络侧完成相应带宽资源的预留；一方面体现在vtlink中segment list可长可短，即可以精细规定路径途径的全部节点，也可以只指定路径必经的关键节点。\n虚拟链路在抽象时，相关链路SLA属性以上界方式提供，例如metric值和时延值取的是原物理链路的最大值；而带宽这种链路共享属性，已在原有物理链路上进行扣减。这样的好处是，云及云应用认为vdlink和vtlink是为其专享的已得到资源保证和metric保证的链路，可以直接使用和编排。\n表2-1 网络为云甲和云乙抽象出的专属服务能力\n| 服务对象 | 类型 | 关键属性 | 来源 |\n|:-|:-|:-|:-|\n| 云甲 | vdlink | cost:10; delay:10; bw:3 | A-B直连链路int1 |\n| 云甲 | vdlink | cost:10; delay:10; bw:3 | B-D直连链路int3 |\n| 云甲 | vtlink | cost:20; delay:20; bw:3 | A-D、A-C-D构成的SR policy隧道 |\n| 云乙 | vdlink | cost:10; delay:10; bw:5 | A-B直连链路int2 |\n| 云乙 | vdlink | cost:10; delay:10; bw:5 | B-D直连链路int4 |\n| 云乙 | vtlink | cost:20; delay:20; bw:3 | A-D路径构成的SR policy隧道 |\n', 'id': '1108115_6'}
{'label': 'text', 'content': '技术方案\n2.3 DB-ORS关键技术\n2.3.2 服务能力定向发布\n网络将抽象而得的原子服务能力采用Key-Value（键值对，简称KV）形式建模，写入到Key-Value数据库（简称KV数据库）中。由于网络设备可能来自于不同生产商，云应用也可能是不同的云商，为了让不同的设备产商和云商之间能够互通，需要对网络原子服务以统一的模板进行描述。KV数据库采用发布/订阅机制，进行网络原子服务的发布。以上过程如图2-4所示。 \n数据库中存储并向外发布的网络链路资源包括且不限于： \n虚拟链路。所有为服务对象（服务对象编号为cloud id）提供的vdlink和vtlink，将以Key-Value格式写入到KV数据库中。KV数据库中除了存储有vdlink、vtlink的所有属性，还将维系一张（cloud id，logic id）映射表，以保持云及云应用与虚拟链路的关联关系。 \n跨域链路。对于连接云PE和云网关的跨AS链路，网络也将其关键属性（包括且不限于跨域链路标识，源端路由器ID，源AS，目的端路由器ID，目的AS，PeerNode SID，PeerAdj SID，本地接口地址，远端接口地址，时延，带宽，丢包率，TE metric）以Key-Value格式写入到KV数据库中。 \n实际三层链路的头尾节点信息。网络将它们也写入到数据库中，包括IGP 路由器ID、AS号等。 \n此外，网络为云及云应用定向提供的资源服务、SLA服务也可以将抽象后信息以Key-Value格式写入到KV数据库中，并和其服务对象（cloud id）关联，面向订阅其的服务对象进行发布。', 'id': '1108115_7'}
{'label': 'text', 'content': '技术方案\n2.3 DB-ORS关键技术\n2.3.3 强一致性分布式数据库\nDB-ORS需要接纳多个服务对象的需求，对多个服务对象分别提供相互隔离的链路、资源、SLA等服务，并通过统一的格式进行能力发布以供调用。基于Key-Value映射和统一的服务描述模板，KV数据库成为能力发布的核心枢纽。云应用、云数据中心等服务消费者通常就近接入网络，且可以存在多个接入点，而云应用终端的入云位置更是可以分布在网络边缘的任意节点，因此KV数据库需要分布式部署，且各分布式实体之间保持强一致性。 \nKV数据库应具备的关键性能有： \n1）高可用性：分布式部署，每个节点都具有完整存档，不惧单点故障或网络问题； \n2）强一致性：通过分布式强一致性的算法实现各节点的存档保持一致； \n3）使用方便：面向用户提供明晰易用的API； \nKV数据库应提供的业务能力主要有： \nKey-Value格式的数据存储； \n2）对数据进行版本号管理； \n3）对外提供统一的高可用的服务存储目录； \n4）标准化的数据读写接口； \n5）服务的注册和发现； \n6）数据的发布以及订阅。', 'id': '1108115_8'}
{'label': 'text', 'content': '技术方案\n2.3 DB-ORS关键技术\n2.3.4 云网一体化编排\n基于数据库开放出来的网络原子服务，可以在云网融合层面进行一体化的业务编排和算路。 \n云网一体化编排可以运行在云应用中。当云控制器从数据库订阅到分配给它的承载网虚链路等服务，可以在其原已掌握的云类信息上叠加来自网侧的信息，进行联合编排，例如业务绑定、再算路。 \n云网一体化编排还可以运行在拥有算网大脑的服务中间层。算网大脑中运行有性能强大的云网编排器，拥有对KV数据库的高级读写权限，掌握有承载网所开放的能力、云应用的需求和算网能力，因此可以进行云网综合编排。例如，为来自终端的入云业务流进行策略算路，并指挥网络将此策略把流传输到云。', 'id': '1108115_9'}
{'label': 'text', 'content': '技术方案\n2.3 DB-ORS关键技术\n2.3.5 DB-ORS安全\nDB-ORS的安全是一个不可回避的问题。为了能力的开放，网络向外批露了部分链路、资源和性能信息，外界藉此可以推算网络的拓扑、载荷、资源利用等敏感信息；如果服务对象之间隔离不够或者被越权访问，还会带来客户敏感信息被泄露的问题。因此，KV数据库的访问需要经过严格鉴权，访问权限需要精心的最小化设计，需要强化数据库入侵防御和安全审计；数据库所在的网络节点需要强化网络安全保护措施，并具备抵御拒绝服务攻击的能力。 \n例如，服务能力发布时，数据库通过权限控制使得不同用户只能访问分配给其的服务能力，不同用户所看到的资源视图是隔离的。权限控制通常以角色为对象，为每个角色专门设定数据库访问权限（由只读、只写、读写组成）。每个角色在位置权限时，还指定了对应的Key、Key范围。用户通过绑定角色而达到针对特点Key的权限控制。', 'id': '1108115_10'}
{'label': 'text', 'content': '技术方案\n2.4 小结\n基于数据库构建的网络开放资源服务，将网络对应用的需求感知和资源调配与基于云网一体化视图提供业务编排和流量策略贯穿起来，其关键技术包括网络原子服务能力的抽象技术、服务能力定向发布技术、强一致性分布式数据库技术、云网一体化编排技术和安全技术，如图2-5所示。 \n基于上述技术，实现网络对业务的细粒度感知和面向业务的网络资源精细化利用，支持业务的对网络服务随愿取用，一体化编排，一点接入。', 'id': '1108115_11'}
{'label': 'text', 'content': '应用场景\n3.1 网络为访问云应用的终端提供服务\n3.1.1场景描述\n当前运行在云上的业务越来越多，例如图3-1所示的智能制造场景中，基于云计算或云边协同计算的云化PLC（工业可编程逻辑控制器）、云化AGV（无人运输车）以及部署在车间里的各类传感器、智能摄像头需要将采集到的数据信息传输到云端的生产集中管控中心，并接收云端集中管控中心下达的控制指令或调度策略。这里，云化PLC的远程控制需要网络服务的低时延、低抖动、极低丢包和高稳定性，基于云端算力的AGV、机器视觉则对网络服务的带宽、时延、稳定性有较为严格的要求。 \n又如对于移动终端上加载的云游戏、虚拟现实、增强现实等新潮应用，终端希望能随时随刻地访问云服务器上的应用，而且需要画面流畅、人机交互迅捷的高质量入云体验，这对网络提出快速接入、低时延、低抖动、低丢包等SLA要求。再如远程听诊、远程治疗，求医侧（患者）和给医侧（医生）需要在云应用进行严格同步的数据同步、流畅的画面交互，要求网络服务的带宽、时延、抖动、稳定性都必须有一系列严格的保证。类似的具体场景还有车联网、能源设施远程监控等等。上述场景都可以归纳为网络为访问云应用的终端提供服务（如图3-2所示），他们的共同特点为：服务消费者位于端侧，网络提供“一点即入”的端-云互联服务接口。', 'id': '1108115_12'}
{'label': 'text', 'content': '应用场景\n3.1 网络为访问云应用的终端提供服务\n3.1.2 DB-ORS解决方案\n本节以软件即服务（SaaS）的终端入云为例，介绍网络为访问云应用的终端提供服务的DB-ORS解决方案。 \nSaaS是一类最常见的应用层云服务，其形式为用户通过与位于云上的服务端程序建立数据连接而使用具体应用。SaaS应用中，终端用户一来需要能够随时随地访问云端应用，二来希望应用服务体验优良，譬如连接快，交互迅捷，音视频流畅，等等。这些诉求提交给网络后，将转化为如下需求： \n网络的任意边缘节点可为终端用户提供接入，经过承载网的路由转发，送入云应用服务端。 \n网络需满足应用服务体验相关的SLA。 \n网络为用户入云制定承载方案时，会将SLA具体化为一系列服务质量（QoS）性能指标或特殊的功能性指标，如最大时延、最小带宽、最大丢包率等等，然后为用户入云规划满足其SLA要求的网络资源，并定制入云的路由策略，最后引导用户入云的流量，且在流量的生命周期内实行必要的监护。 \n云控制器将应用需求和算力信息通告给算网编排器。承载网将可以向云提供的专属资源，经Key-Value抽象形成基础原子服务能力，调用KV数据库的编程接口API（或经由数据库代理）写入云网编排器的KV数据库中。 \n云网编排器基于掌握的云、网两侧资源，为云应用的终端入云进行路径计算，得到SRv6 policy，同时绑定一个具有唯一性的业务标识（例如采用服务感知网络SAN-ID[4]）。SRv6 Policy和业务标识以Key-Value格式写入数据库。 \n接入云应用的终端通过订阅方式获取业务标识。承载网控制器通过订阅方式获取到policy和业务标识，并通告给网内所有为云终端提供接入的边缘路由器。 \n终端发向云侧的业务流量在CPE封装上携带业务标识，承载网的边缘路由器接收到流量后根据业务标识匹配policy，根据policy进行转发到对应云的云网关。 \n需补充说明的是，不失一般性，图3-3的云可以是公有云也可以是私有云，可以是中心云也可以是边缘云。', 'id': '1108115_13'}
{'label': 'text', 'content': '应用场景\n3.2 网络为多云互联提供服务\n3.2.1场景描述\n大型的互联网云计算服务提供商，为了向用户保证服务的高可用性，及为了优质用户体验而缩短用户访问点到服务器的距离，往往采用异地多活的云计算服务架构，即在不同城市分别建立数据中心，数据中心通过承载网互联。异地多活机制下，每个数据中心都有承担用户读写流量的能力；任意一个数据中心出问题的时候，其他中心都可以快速接管用户的流量，为此位于不同地理位置的多个数据中心之间需要保持数据的同步。异地数据中心的互联需要网络的低时延、低抖动和高稳定性。 \n类似的场景还有若干，例如网格计算中算力节点间的协同，在多个节点间实现计算任务的分担和调配，同样需要网络的低时延、低抖动和高稳定性。上述场景都可以归纳为网络为多云互联提供服务（如图3-4所示），特点为：服务消费者位于云侧，网络提供“点-点”、“点-多点”或者“多点-多点”的云-云互联服务接口。', 'id': '1108115_14'}
{'label': 'text', 'content': '应用场景\n3.2 网络为多云互联提供服务\n3.2.2 DB-ORS解决方案\n本节以大型云服务商的数据中心互联为例，介绍网络为多云互联提供服务的DB-ORS解决方案。 \n为了服务的高可用，大型云服务商有必要对其多个数据中心采取异地多活架构。此时，数据中心之间存在的大量数据同步，对网络提出了传输低时延、低抖动和高可靠性的需求。通常，承载网运营商会向云服务商提供连接多地数据中心的MPLS专线。但是MPLS专线成本高，部署复杂；而且其固定带宽的特点使得若按业务峰值流量配置专线则资源浪费明显，若按业务均值流量配置专线则在流量高发期会遭受大的时延或丢包。而且，云内的业务种类繁多，对网络质量要求各异，根据五元组来识别业务及区分业务服务质量，粒度不够精细。 \nDB-ORS则是一种推荐的替代方案。承载网把为云数据中心规划的原子资源服务写入分布式KV数据库，云内控制器通过订阅获取之后，基于整个数据中心互联可用的网络拓扑（包含来自承载网内部的路径及其带宽、时延等属性），可以根据云内不同的业务类型和要求，重新编排并绑定网络路径，实现网络精细的差分服务和提升整体网络的资源利用率。 \n（1）承载网基于对云数据中心1和云数据中心2的需求，分配出给云数据中心1 、云数据中心2的专属资源，并经Key-Value抽象形成基础原子服务能力，调用KV数据库的编程接口API（或经由数据库代理）写入数据库。 \n（2）云数据中心1控制器、云数据中心2控制器订阅与他们相关的信息，及时获知并持续关注其增删改等变化。 \n（3）云数据中心1、云数据中心2通过标准化的模板文件进行Key-Value解析，获取到最新的网络提供的原子服务能力。基于承载网开放的虚拟链路和云数据中心内部链路构成的云网一体拓扑视图，云侧完成数据中心1与数据中心2互联的二次编排和算路。 \n显然，通过这样的精准规划和定向开放，提高了网络资源的利用率。而且经过云网间需求交互，网络可以动态调整资源以匹配云端最新需求，支持数据中心间进行随愿互联。', 'id': '1108115_15'}
{'label': 'text', 'content': '应用场景\n3.3 小结\n通过DB-ORS架构，承载网基于其客户的服务需求，从富余资源中进行精准的定向分配并封装为服务能力，经分布式数据库对外定向发布，以满足云及云上用户的需求。3.1、3.2节概述了两类基础场景，除了3.1.2节SaaS终端入云和3.2.2节数据中心互联这两种典型场景外，其实还有更多种场景，比如承载网为位于一个云内具有不同需求的两个应用提供满足各自SLA的定制服务，又比如承载网为以主备方式部署到两个云上的一个应用提供终端优选入云策略及性能降级时自动切换服务，再比如承载网为在部署在两个云上的不同应用提供既满足各自SLA又具备隔离度的定制化入云路径服务。概言之，DB-ORS可以满足绝大部分云网融合应用场景，实现对多样化业务的高效承载、精细服务，并具有灵活、智能等特性。', 'id': '1108115_16'}
{'label': 'text', 'content': '总结展望\n网络和云计算的深度融合，需要网络能根据云业务特点提供细粒度的、按需定制的服务。这对基于尽力而为转发的IP承载网是很大的挑战。为此，本白皮书提出一种轻量级的云网融合方案，它采用DB-ORS网络能力开放架构，通过对网络的原子服务能力进行抽象，经强一致性分步式KV数据库进行服务发布，实现云侧对网络侧服务随用随取，云网联合编排，业务一点接入。 \n本白皮书给出了网络链路资源向云开放的应用场景，以满足当前云网融合中的常见需求。面向未来，网络基于对业务的感知和DB-ORS架构，将进一步构建确定性QoS服务、安全资源服务等类型的原子能力并对外定向开放，促进云网深度融合，为业务带来前所未有的多维度性能保障和美好的使用体验。', 'id': '1108115_17'}
{'label': 'table', 'content': '附录1 缩略语\n| 英文缩略语 | 英文完整语 | 中文 |\n|:-|:-|:-|\n| AS | Autonomous System | 自治系统 |\n| BGP | Border Gateway Protocol | 边界网关协议 |\n| CPE | Customer Premise Equipment | 用户前置设备 |\n| DB | Database | 数据库 |\n| DB-ORS | Database-based Open Resource Service | 基于数据库的开放资源服务 |\n| DSCP | Differentiated Services Code Point | 差分服务代码点 |\n| ID | Identifier | 标识 |\n| KV | Key-Value | 键值 |\n| MPLS | Multi-Protocol Label Switching | 多协议标签交换 |\n| PE | Provider Edge | 骨干网边缘节点 |\n| QoS | Quality of Service | 服务质量 |\n| SaaS | Software as a service | 软件即服务 |\n| SD-WAN | Software Defined Wide Area Network | 软件定义广域网 |\n| SID | Segment Identifier | 段标识 |\n| SLA | Service Level Agreement | 服务等级协议 |\n| SR | Segment Routing | 段路由 |\n| SRH | SRv6 Header | SRv6报头 |\n| SRv6 | Segment Routing with IPv6 Data plane | 基于IPv6数据平面的段路由 |\n| TE | Traffic Engineering | 流量工程 |\n| VLAN | Virtual Local Area Network | 虚拟局域网 |\n', 'id': '1108115_18'}
{'label': 'text', 'content': '附录2 参考文献\nCharence Filsfils, Pablo Camarillo, John Leddy, et al., "Segment Routing over IPv6 (SRv6) Network Programming", RFC 8986, https://www.rfc-editor.org/info/rfc8986 \nFenlin Zhou, Sheng Wang, Dongyu Yuan, “Requirements and Use Cases of DB-ORS”, https://datatracker.ietf.org/doc/draft-zhou-alto-dbors-requirement-usecase/ \nFenlin Zhou, Xiaocong Qian, Dongyu Yuan, “Database-based Open Resource Service Framework”, https://datatracker.ietf.org/doc/draft-zhou-alto-dbors-framework/ \nLiwei Ma, Fenlin Zhou, Hesong Li, et al.,“Service Identification Header of Service Aware Network”, https://datatracker.ietf.org/doc/draft-ma-intarea-identification-header-of-san/', 'id': '1108115_19'}
