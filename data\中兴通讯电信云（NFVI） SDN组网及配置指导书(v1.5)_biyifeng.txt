{'label': 'document', 'content': '中兴通讯电信云（NFVI） SDN组网及配置指导书(v1.5)_biyifeng', 'id': '675737'}
{'label': 'table', 'content': '| 版本 | 日期 | 作者 | 审核者 | 备注 |\n|:-|:-|:-|:-|:-|\n| V0.1 | 20190507 | 汪军 | | 搭建框架 |\n| V1.0 | 20190517 | 毕以峰 | 汪军 | 完成第一版。协同作者，邵会勇，顾红芳，王承锋，宋波，于星。 |\n| V1.3 | 20190713 | 毕以峰 | 李旅，李宏飚 | 更新对DC GW的选型，以及对VNF的BGP和BFD的的要求。 |\n| | | | | |\n', 'id': '675737_1'}
{'label': 'text', 'content': '背景和需求\n文档目标\n该文档主要目标是供给MKT同事参考，用作在国际电信云市场投标SDN时进行方案选择、设备选型的配置参考指导。 \n注：对于国内电信云项目，一般根据运营商行标和具体要求已经确定了组网和配置，而且国内电信云项目的互相复制可能不大，因此该文档的内容对国内电信云的规范和配置暂不涉及太多。', 'id': '675737_2'}
{'label': 'text', 'content': '背景和需求\n电信云（电信云）关键需求\n电信云分为两类，一类是标准的数据中心，一般是城市以及之上的承载NFV的DC，其网络的基本需求和一般IT云区别不大，主要在逻辑功能层面有一定的差异，后面会单独介绍；另一类是边缘DC，其规模相对较小(几台到十几台服务器)，一般需要采用更为紧凑的组网：对其而言，可能就一对交换机接入服务器，不存在Spine-Leaf架构，而且独立的云平台、控制器开销都过高，需要轻量化的解决方案。', 'id': '675737_3'}
{'label': 'text', 'content': '背景和需求\n电信云（电信云）关键需求\n标准电信云对网络的需求\n大流量、高密度组网，支持高密度40GE/25GE端口接入，预计到2020年预计将有部分100GE服务器接入。 \n高可用网络，无单点故障。尤其是不中断网络升级，需要业务接入交换机优先考虑采用堆叠方式组网。 \n支持SR-IOV方式的虚机接入网络。在SDN方式下要支持SR-IOV网络自动化，ToR作为VTEP，并且需要修改社区原生OpenStack的Neutron实现，支持VxLAN组网下创建SR-IOV端口（TECS 6.0版本已支持，其它Openstack版本需要额外开发）。 \nSDN组网下，Overlay层面支持ECMP，以满足VNF的组网需求 \n\uf06e流量监测有特定要求。传统的电信网络有合法监听的要求，电信网络虚拟化之后继承了这一要求；另外，电信云为了实现自动化的运维、网络分析和故障预判，需要对网络中的信令和数据能够实时采集和分析。鉴于以上两个方面的驱动，在电信云网络中，需要具备精准的、自动的数据布控和采集能力，特别是电信网元虚机终端在迁移和重生的时候，流量采集的策略要实现自动化跟随调整。 \n路由协议要求多样。支持和VNF建立BGP邻居，并与BFD联动。当检测到虚机故障时，路由能够快速收敛。在大型网络中，需要支持1K以上的BGP邻居和BFD会话，并且虚机热迁时BGP、BFD会话保持不中断。在存在多企业APN和业务链情况下，还要支持MPLS，QinQ等协议。', 'id': '675737_4'}
{'label': 'table', 'content': '背景和需求\n电信云（电信云）关键需求\n电信云 SDN和一般IT云SDN差别对比\n| 对比项 | IT云 | 电信云 | 电信云延伸需求 |\n|:-|:-|:-|:-|\n| 业务类型 | 主机型业务 | 普通主机型业务主备主机型业务路由型VNF | 普通主机型业务使用虚机vport地址通信；主备虚机型业务使用floating IP地址通信路由型VNF使用loopback地址实现VNF的自身通信；根据静态路由或者动态路由条目指导被路由的数据报文的转发。虚机需要和网络建立动态路由协议，并实现自动化编排 |\n| 流量密度 | 中低 | 根据VNF类型不同，差别巨大：信令面网元流量密度小；用户面网元流量密度大 | 高流量网络需要SR-IOV接入或者采用智能网卡卸载，提升网络性能 |\n| 故障收敛时间 | 分钟 | 百毫秒～秒级 | 需要自动化网络和VNF之间的BFD快速检测协议 |\n| 业务部署位置 | 集中 | 根据VNF类型不同，差别巨大：控制面的VNF多集中式部署；用户面网元多采用多级DC部署，在提供边缘分布式部署的同时，还要监管中心DC的用户面VNF的支持能力 | 同时考虑中心DC和在边缘实现轻量级的小D整合，根据业务模型合理配置 |\n| 合法监听及镜像 | 可选，一般在DC出口做分光 | 必选，需要监听VNF之间的接口 | TAPaaS流量自动化 |\n', 'id': '675737_5'}
{'label': 'text', 'content': '背景和需求\n市场格局及需求差异\n国内市场\n国内三大运营商技术实力都比较强，目前都在自己主导制订最符合自身业务需求的电信云SDN网络方案，下面分别简单进行介绍。', 'id': '675737_6'}
{'label': 'text', 'content': '背景和需求\n市场格局及需求差异\n国内市场\n中国移动\n国内运营商中，中国移动的TIC项目率先进行电信云SDN化的研究和规范制定，试点与商用都走在了国内三大运营商的前列；目前基本确定了如下原则： \n混合Overlay方案，SDN控制器既要控制SR-IOV VNF的ToR VTEP，也需要控制普通虚机的vSwitch，硬件交换机采用EVPN组网，控制器用NetConf进行硬件交换机的配置自动化；采用OpenFlow控制vSwitch。 \n采用路由器作为网关，网关双活部署，网关和VNF之间建立BGP邻居关系，接受路由发布，同时联动BFD，实现路径的快速切换。', 'id': '675737_7'}
{'label': 'text', 'content': '背景和需求\n市场格局及需求差异\n国内市场\n中国电信\n中国电信的电信云规范目前正在讨论制订阶段，估计也会受中国移动的影响；', 'id': '675737_8'}
{'label': 'text', 'content': '背景和需求\n市场格局及需求差异\n国内市场\n中国联通\n中国联通电信云（联通也称为通信云）网络解决方案目前主要采用交换机作为网关，SDN控制器不接管vSwitch的纯硬件EVPN层次化端口方案，与移动存在较大差异。', 'id': '675737_9'}
{'label': 'text', 'content': '背景和需求\n市场格局及需求差异\n国际市场\n德电、沃达丰、TEF等Tier 1运营商电信云的网络方案基本采用的是Juniper、Nokia的SDN方案，上层云平台是Redhat等IT厂商，底层硬件交换机也存在异构场景；另外，第一批的时间窗已经错过，我司机会较小。 \n对于其它Tier 2以及以下的厂商，部分跟随Tier 1运营商的架构，要求SDN和底层网络解耦，由于我司的方案还是控制器和转发设备耦合方案，因此我司方案还有一定差距；大部分电信云+VIM采用单厂商交付方式，该方式下组网方案可以由厂商决定，我司一般用路由器（M6000）作为网关，网关双活部署，控制器不接管vSwitch的纯硬件EVPN+层次化端口方案。 \n本指导书中针对的国际市场主推基于硬件交换机的Overlay方案，方案简单、稳定，成熟度高。', 'id': '675737_10'}
{'label': 'text', 'content': '组网方案\n技术及原理\n本文档是组网和配置指导手册，不涉及太多的技术细节。如果需要了解技术背景，请参见《中兴通讯电信云 SDN技术白皮书》。', 'id': '675737_11'}
{'label': 'text', 'content': '组网方案\nDC内组网\nUnderlay组网原则\nUnderlay网络采用三层路由组网方案，各Leaf和Spine之间全部采用路由端口互联，避免二层环路风险。 \n每台Leaf到Spine的上行端口配置为路由端口模式。对于远端Leaf，形成L3 ECMP等价路由，业务流量通过Hash分担到不同转发路径上，当其中一条转发路径故障时，业务流量快速切换到剩下的转发路径上。 \nUnderlay网络启用OSPF \n如上图所示，SPINE、LEAF，DCGW同属于一个OSPF area，公告自己的underlay路由信息。当有两个SPINE节点时，在LEAF节点、DCGW节点上需要创建2个OSPF邻居， SPINE节点上需要创建与LEAF节点、DCGW节点数量相同的OSPF邻居。 \n注： \n为节省DR/BDR选举时间，加速路由的快速收敛，需将OSPF邻居配置为P2P模式。 \nUnderlay优选推OSPF，如果客户要求ISIS，也可以采用ISIS。Underlay采用BGP协议一般不推荐，太复杂。', 'id': '675737_12'}
{'label': 'text', 'content': '组网方案\nDC内组网\nUnderlay组网原则\n带外控制和管理原则\nSDN控制器和硬件交换机、vSwitch之间采用带外组网，保证管理/控制网络和业务网络严格隔离，即使出现SDN控制器自身的错误指令、配置也不会导致控制网络不可访问。 \n此处带外管理对于硬件交换机/服务器而言，是指其管理口，对于服务器来说，是指其上的IPMI口，一般推荐都加入PIM-NET作为带外管理网络平面。', 'id': '675737_13'}
{'label': 'text', 'content': '组网方案\nDC内组网\nUnderlay组网原则\n业务、存储、管理三网分离原则\n整体原则： \n服务器上业务、存储、管理采用不同的网络接口，每类接口采用一对网卡，也上连不同的LEAF交换机：业务LEAF，管理LEAF和存储LEAF。 \n在海外电信云的项目中，一般DC规模有限，严格的采用三网隔离的方案会造成成本急剧增长，为了缓解成本压力，在三网分离的整体原则下，可以接受以下方案变种： \n管理和存储LEAF交换机可以合一，业务LEAF单独隔离。 \nSPINE可以1）管理存储合一，业务单独隔离，或者2）三网SPINE合一，2）的前提是管理和存储可以使用三层组网。 \n如果客户标书中明确要求三网的LEAF合一，该情况需要转递到产品线讨论输出对策。 \n另外，为了降低承载设备的成本，还建议考虑如下的配置方式： \n服务器的管理网建议采用2*GE接口，用千兆低端交换机组网，成本会大大降低。 \n对于采用管理面作为虚机迁移存储复制的云平台(主要是我司TECS)，其要求管理网是10GE组网，该种情况下如果要求管理网单独10GE组网，成本增加较多，因此针对此种情况可以采用存储和管理网合设的方案。 \n注： \n管理网交换网卡在以往项目中都是单独配置的2*10GE网卡，与存储网卡分离，因此管理交换机也需要采用万兆交换机与之对接，该组网方式后续应该逐渐放弃，改用上述的两种方式之一，以节省成本。 \n考虑到交换机送风出风方向等问题，用于管理网的千兆交换机一般选择5960-52TM。其他的千兆交换机一般不用做推荐。', 'id': '675737_14'}
{'label': 'text', 'content': '组网方案\nDC内组网\nUnderlay组网原则\nSpine-Leaf L3 Fabric架构组网\nSpine-Leaf两层架构，采用L3组网，Spine交换机根据组网规模选择2~4台设备，不堆叠，Underlay推荐配置OSPF协议。VxLAN GW也可以认为是一种特殊的Leaf：Border-Leaf。 \n方式1：采用盒式交换机作为Spine。 \n方式2：Spine和GW合一，直接充当出口。', 'id': '675737_15'}
{'label': 'text', 'content': '组网方案\nDC内组网\nUnderlay组网原则\n无单点故障原则\n无论是Leaf、Spine还是GW，无论是业务网、管理网或存储网的设备，都是成对部署（带外管理交换机除外）。对于ToR节点，采用堆叠部署，Spine独立L3部署，后续支持完全不中断业务升级，网关配置M6000-S，采用双活部署。', 'id': '675737_16'}
{'label': 'text', 'content': '组网方案\nDC内组网\nUnderlay组网原则\n跨架保护原则\n如果既要实现三网的完全隔离（设备层），又要实现尽最大可能的节省交换机成本，建议采用跨架组网的方案。如上图。 \n一般交换机的端口管理一架服务器有节余，能力上完全能够对接两架服务器端口。因此建议两架服务器组成一个单元，为其配置一对业务LEAF、一对管理LEAF和一对存储LEAF。同类型的LEAF跨架组件堆叠或者双活；服务器上的1+1的网卡，分别连接本架LEAF和邻架LEAF，同时提供的跨架的保护机制。 \n这样，交换机的端口使用接近饱满而无浪费，有实现的三网的最大可能的物理隔离。', 'id': '675737_17'}
{'label': 'text', 'content': '组网方案\nDC内组网\nUnderlay组网原则\n安全等级分区隔离原则\n理论上电信云组网对安全性有严格的要求，但是因为各种原因，比如现网有防火墙，或者为了省钱等，有的运营商不要求配置防火墙（一般为海外的Tier1之外的运营商），有的仅仅要求在出口配置防火墙。但也有少数的大T运营商，对安全等级隔离提出了比较系统的要求。例如，系统按照不同的安全风险等级，需要将设备划归到不同的安全域中，不同的安全域边界如果需要互访采用防火墙进行隔离。以下举例仅供参考： \n暴露域：EPC、P-CSCF、SBC、vBRAS-U、vPE \n非暴露域：一般部署和Internet公网无连接的控制面网元，包括S/I-CSCF、vBRAS-C \n核心域：一般部署有核心用户数据的网元，HSS/UDM、MANO、VNFM、EMS \n管理域：VIM、SDN控制器、网管。 \n建议原则： \n第一原则是参考客户的标书要求。 \n在满足客户要求的前提下，尽量简配，保证成本优势。 \n控制器只纳管并自动化配置跨暴露域-非暴露域-核心管控域的东西向防火墙； DC出入流量的南北向防火墙和负责管理域的管理面防火墙采用手动配置，不由控制器纳管。', 'id': '675737_18'}
{'label': 'text', 'content': '组网方案\nDC内组网\nOverlay组网原则\nOverlay组网方案选项\n电信云网络方案有两种，方案1：硬件EVPN方案和方案2：混合方案。 \n海外电信云环境首推方案1；特殊情况要求使用方案2的，需要与产品线市场代表、规划SE沟通确认方案；国内的电信云根据运营商的实际要求选用方案，例如中国移动指定要求vSwitch做VTEP的情况，采用方案2。 \n方案1：硬件EVPN方案 \n方案特点： \n采用层次化端口组网，所有的VTEP都落在硬件LEAF交换机上，Vx GW落在硬件DC GW上 \nvSwitch仅作二层转发，基于VLAN隔离。 \n控制器只对硬件交换机进行控制，当前采用netconf协议，控制器不控制vSwitch虚拟交换机。 \n控制器不参与eVPN协议对接，无需启用PSSC组件。 \n动态路由，静态路由，BFD全部由DC GW提供。控制器从2019Q4后只接管配置下发，不参与实际业务交互。 \n安全组只在vSwitch场景下启用，由VIM控制vSwitch实现，控制器不参与。 \n方案2：混合方案 \n方案特点： \n控制器启用PSSC，通过netconf和eVPN控制网关，通过db和Openflow控制vSwitch做VTEP，通过netconf控制LEAF交换机做VTEP \n支持SRIOV和虚机，支持裸金属服务器。 \n服务于VNF的动态路由，静态路由，BFD等功能，由DC GW提供，控制器接管配置（2019Q4） \nvSwitch的安全组由控制器控制实现。 \n提供集中式路由和分布式路由', 'id': '675737_19'}
{'label': 'text', 'content': '组网方案\nDC内组网\nOverlay组网原则\nEVPN RR部署\nEVPN RR在我司提供Spine交换机的场景下部署在Spine交换机上；如果SPINE设备为第三方或者无SPINE的场景下，RR部署在DCGW/Border LEAF上', 'id': '675737_20'}
{'label': 'text', 'content': '组网方案\nDC内组网\nOverlay组网原则\n虚拟网络自动化模型\nOverlay组网均采用NFVO的NSD描述，由VIM+SDN实现逻辑网络的自动化，遵循如下原则： \n一个或多个VNF的相同网络平面使用一个OpenStack Router对象，对应到SDN网络的一个VRF，不同的VRF之间是完全隔离，系统支持地址重叠。 \n注：虽然系统支持跨VRF的IP地址重叠，但是在电信VNF部署时，一般不推荐使用地址重叠进行部署。 \nVNF内部的L3通信可以选择分布式路由，或者集中式路由，SDN控制器支持每个逻辑Router的灵活配置，通过OpenStack的DVR属性指示是否启用分布式路由，一般而言信令平面采用集中式路由，方便在网关上做集中的镜像；媒体面的南北向通信采用集中式路由；东西向通信（一般对应业务链场景）采用分布式路由，避免流量的迂回，降低成本。 \n共享网络的使用原则：对于多个位于同一安全等级的VNF之间需要互通，无安全隔离要求的网络平面，可以创建共享网络平面进行通信；其他场景默认采用独立网络平面，以保证通信安全。 \n电信云的SDN网络原则上不限制VNF的VM的部署形式，但是根据工程经验，推荐两种部署方式： \n同一个VNF的多个VM均部署在一个Network的subnet中。即VNF的内部VM可以二层互通；对外通信采用三层通信； \n同一个VNF的多个VM分别占用不同的Network，通过Network进行层隔离。 \n注：当前某些局点采用的另外一种方式：同一个VNF的多个VM使用相同的network，但是划归为不同的Subnet的方式，也可工作，但是存在弊端：①二层广播报文穿越Subnet，会对同network的虚机造成干扰 ②在使用VLAN transparent功能时，因为DC GW的VLAN子接口的MAC地址空间受限，会造成MAC地址漂移的问题。 后续建议逐渐废弃该部署方案。', 'id': '675737_21'}
{'label': 'text', 'content': '组网方案\nDC内组网\nSDN 提供的云网络服务（基于openstack模型的服务）\n虚拟路由器，采用VRF概念实现，默认打开分布式路由。一个租户可以申请多个虚拟路由器，跨路由器的通信需要借助外网手动连通。 \n虚拟网络，对应控制器的VNET模型，在转发设备上实例化为VNI，一个虚拟路由器下可以挂接多个虚拟网络 \n子网，对应DHCP的IP Address Pool，一个虚拟网络可以挂接多个子网 \nPort，对应网络的一个接口或VLAN子接口，也是VM的一个虚拟网卡； \n安全组，进行主机/VM的分组隔离，提供更细颗粒度的安全隔离和控制机制；默认同一个安全组的VM可以互访，不同安全组VM不可以互访，租户管理员可以修改此策略；支持安全组同组可入、有状态会话特性 \n纯硬件的eVPN SDN方案（国际市场默认方案），SDN不提供安全组，安全组由TESC接管 \n混合SDN方案（特定电信云环境，比如中国移动5GC），通过控制器控制DVS实现安全组 \nFWaaS，LBaaS，少数电信云网络中提供', 'id': '675737_22'}
{'label': 'text', 'content': '组网方案\nDC内组网\nSDN 提供的云网络服务（BFD）\n网络侧： \nBFD部署在配对路由器（GW）上，指向VNF网元的静态路由需关联BFD session。BFD检测到故障时，直接在路由器上触发路由的快速切换。默认场景路由器需要支持单臂BFD，并由控制器自动配置。 \n路由器双活部署时，同一个VRF（vRouter）在两台路由器上分别用不同的本端IP与VNF网元建单臂BFD。 \n相应地，VNF网元需支持单臂BFD的回弹。路由器收到VNF网元回弹的BFD报文后，能识别出对应的会话。 \n网元VNF侧： \n硬件交换机TOR作为VTEP场景，如果网元VNF侧需要BFD，可以使用单臂BFD，向SDN网络发送单臂BFD，SDN网络支持单臂BFD的回弹。', 'id': '675737_23'}
{'label': 'text', 'content': '组网方案\n全解决方案组网方案\n安全设计原则\n东西向流量：微分段保护 \n跨安全域访问：安全等级划分及防火墙 \n南北向流量：防火墙 \n电信网元的安全性要求极高，从物理组网层面到虚拟网络层面都有严苛的要求。一般来说，物理组网要进行严格的组网隔离，部分运营商要求多层级的隔离。例如第一层级，实现业务网络、存储网络和管理网络的隔离，并使用防火墙对跨不同的网络通信的防护。第二层级，在业务网络内部，划分“对外暴露区域”“隔离区域”和“核心管控区域”，不同的的区域之间通过不同类型的防火墙实现等级保护；管理域和存储域又要划分不同的网络平面，不同的网络平面的互访严格禁止。', 'id': '675737_24'}
{'label': 'text', 'content': '组网方案\n全解决方案组网方案\n安全组网方案\n东西向流量安全采用分布式防火墙(安全组)进行隔离。同一个安全组的VM可以互访，不同安全组VM缺省是不能够互相访问。安全组是有状态的，租户可以设置某个VM可以主动访问其它外网资源，但是拒绝外部的主动访问。 \n南北向流量安全防护，采用外置硬件防火墙进行安全隔离。 \n安全域间流量，部署东西向防火墙挂接在网关上，跨安全域流量经过防火墙进行互通。', 'id': '675737_25'}
{'label': 'text', 'content': '组网方案\n流量采集组网方案\n物理分光组网\n对于用户面流量，采用在DC GW和外部路由之间的物理光纤上分光的方式进行流量采集。 \n被分光的流量，或者直接送往分析仪，或者再部署物理交换机进行汇聚后送往分析仪。 \n分光光纤的带宽要根据分光流量的大小进行对应配置。', 'id': '675737_26'}
{'label': 'text', 'content': '组网方案\n流量采集组网方案\nTAAS流镜像组网\nTAAS流镜像方案一般用于信令采集。 \n需要配置镜像汇聚交换机MAS，MAS交换机根据可靠性要求选择配置单台或者是VSC主备。默认方式配置单机。 \nMAS与LEAF交换机通过物理光纤直连，带宽大小依据被采集的流量的大小，一般默认不超过10Gbps。堆叠的两台LEAF均需要出10GE光纤连接到MAS。 \nMAS和分析仪/探针服务器之间的链路大小取决与整体DC被采集的流量的大小和分析仪的端口能力。 \n该方案只采集业务LEAF上的流量，不采集管理LEAF和存储LEAF上的流量。', 'id': '675737_27'}
{'label': 'text', 'content': '组网方案\n多DC组网\nDCI组网\n大部分场景下，一般优选每个DC一套云平台加一套SDN控制器，多个DC采用无耦合DCI的架构，如下图所示。数据中心的网关之间直接运行EVPN协议，数据面封装采用VxLAN，穿越WAN网络，但WAN的SDN编排不是必须的，自动化编排完全由云平台和SDN完成。', 'id': '675737_28'}
{'label': 'text', 'content': '组网方案\n多DC组网\n边缘DC组网\n在单个DC规模较小且距离较近的情况下，可以采用单个云平台+SDN控制器来管理、控制多个分布式DC的方案一般而言建议DC之间的距离不超过100KM(同城数据中心)，最远不超过500KM；多个DC的服务器规模之和不超过1000台服务器。一般来说，单控制器多DC的场景尽量用于运营商超小型边缘DC的整合(每个DC少于50台服务器)。 \n边缘DC拉远接入中心DC \n该方案通过一套云平台和SDN控制器，纳管多个物理DC的交换机和服务器资源，实现将多边缘小型DC组建为一个大型vDC的效果。云平台和控制器，以及DC GW和汇聚交换机都选择在一个地理位置相对中心的DC，其他的边缘DC只部署TOR/LEAF交换机和服务器资源。结构上看，是将LEAF和服务器拉远伸入到边缘DC。从逻辑功能上讲，跟同一个DC内部的SDN方案没有本质区别。通过该方案将多个地理位置上接近的小型DC整合成一个逻辑的资源池，资源池内业务可以统一调度部署，支持虚机弹缩、热迁等特性。 \n边缘DC拉远接入中心DC \n对于存在大量小型化边缘计算的EDC，且EDC的流量需要直接Offload到Internet进行通信，各个DC间没有互联互通要求的场景，通过部署一套云环境和SDN控制器，将多个遍布全国或者全球的小型DC以POD的方案进行纳管，中心DC的云系统为运营商提供统一的运维管控界面。如上图所示。', 'id': '675737_29'}
{'label': 'text', 'content': '组网方案\n统一网管方案\n统一网管\nNetNumenTM U31(ICT) 网管 是中兴通讯WEB化的网络管理系统，实现了物理网络资源、SDN控制器、虚拟网络资源统一管理和集中的操作维护。NetNumenTM U31(ICT) 网管系统支持对中兴通讯公司的路由器、交换机等数据通信设备以及各类IT设备的统一管理。 \nNetNumenTM U31(ICT)网管系统能够统一管理Underlay网络，Overlay网络，L4-L7层服务，如下图所示。对于Overlay网络，通过对接ZENIC控制器完成对Overlay网络的运维。ZENIC控制内置LCT 管理系统，能够完成对控制器范围内的网元进行配置和诊断。 \nNetNumenTM U31(ICT)网管系统 提供北向接口，支持与 OSS/上层系统进行对接，向上开放系统的管理能力。', 'id': '675737_30'}
{'label': 'text', 'content': '组网方案\n统一网管方案\n部署方式\nNetNumenTM U31(ICT)网管系统采用支持集中部署，与DC POD基础设施和ZENIC VDC控制器是一对多的关系。如下图所示，NetNumenTM U31(ICT)网管系统部署在中心机房，分别连接各个DC，需要和各个DC内的underlay网络联通，也需要和各个DC VDC 控制器（ZENIC VDC）联通。 \nNetNumenTM U31(ICT)网管系统支持高可用和容灾部署方式，支持1+1本地HA双机，1+1异地双机，2+1异地容灾部署。 \n如图所示，NetNumenTM U31(ICT)网管系统采用的是最常用的1+1异地双机方式。主用的服务器和备用的服务器分别部署在两个数据中心机房。每个站点都有独立的数据库，软件系统和数据。两个服务器采用心跳线进行互相检测，并通过数据同步通道进行两个站点间的数据同步和复制。 \n当心跳系统检测到心跳异常后，系统会倒换到备用系统上，继续提供服务。', 'id': '675737_31'}
{'label': 'text', 'content': '产品组合及选型说明\n产品组合选型原则\n根据客户组网的基本需求选择：\n服务器网卡，有10G、40GE、25GE以及100GE，LEAF提供对应网口负责接入。\n', 'id': '675737_32'}
{'label': 'table', 'content': '产品组合及选型说明\n产品组合选型原则\n| 服务器网卡 | ToR交换机型号 | 说明 |\n|:-|:-|:-|\n| 10GE | 5960-72DL-H/5960-72NL-H | 48*10GE+6*40GE |\n| 40GE | 5960-32LC-H | 32*40GE |\n| 40GE | 5960-4M-HC | 支持8*100GE、24*25GE、24*25GE+2*100GE、16*40GE（2020年3月底发布）子卡，最大四个子卡 |\n| 25GE | 5960-56QU-HC | 48*25GE+8*100GE |\n| 25GE | 5960-4M-HC | 支持8*100GE、24*25GE、24*25GE+2*100GE、16*40GE（2020年3月底发布）子卡，最大四个子卡 |\n| 100GE | 5960-4M-HC | 支持8*100GE子卡，最大四个子卡 |\n', 'id': '675737_33'}
{'label': 'text', 'content': '产品组合及选型说明\n产品组合选型原则\n注：4M-HC设备只有在特殊情况下才考虑选用做LEAF，一般由5960-32LC-H（40GE）或者5960-56QU-HC（25GE）都能满足项目需求。\nSpine汇聚链路带宽的要求，流量较小的场景一般还是维持40GE汇聚，新的电信云项目一般要求100GE汇聚链路。\n推荐使用9904-S/9908-S设备，对于小型DC或边缘DC，可采用5960-4M-HC产品\n', 'id': '675737_34'}
{'label': 'table', 'content': '产品组合及选型说明\n产品组合选型原则\n| 汇聚链路要求 | 上行链路带宽要求 | 汇聚交换型号 | 说明 |\n|:-|:-|:-|:-|\n| 40GE | ≤64个互联端口端口 | 5960-4M-HC（当前采用8*100G子卡，100G端口降速到40G的方式，2020年Q1，支持16*40G子卡） | 国内运营商存在集采入围型号限制，只能按照集采规格配置，未必是最优场景。注：第二列的端口数是按照一对SPINE交换机来计算的。 |\n| 40GE | (64,288]个互联端口计算方法与上面类似 | 9904-S | 国内运营商存在集采入围型号限制，只能按照集采规格配置，未必是最优场景。注：第二列的端口数是按照一对SPINE交换机来计算的。 |\n| 40GE | ＞288个互联端口 | 9908-S | 国内运营商存在集采入围型号限制，只能按照集采规格配置，未必是最优场景。注：第二列的端口数是按照一对SPINE交换机来计算的。 |\n| 100GE | ≤64个互联端口 | 5960-4M-HC | 国内运营商存在集采入围型号限制，只能按照集采规格配置，未必是最优场景。注：第二列的端口数是按照一对SPINE交换机来计算的。 |\n| 100GE | (64,144]个互联端口 | 9904-s | 国内运营商存在集采入围型号限制，只能按照集采规格配置，未必是最优场景。注：第二列的端口数是按照一对SPINE交换机来计算的。 |\n| 100GE | ＞144个互联端口 | 9908-S/9908/9916 | |\n', 'id': '675737_35'}
{'label': 'text', 'content': '产品组合及选型说明\n产品组合选型原则\nSPINE9900系列交换机板卡支持能力。\n', 'id': '675737_36'}
{'label': 'table', 'content': '产品组合及选型说明\n产品组合选型原则\n| 单板代号 | 描述 | 9916 | 9908 | 9908-S | 9904-S |\n|:-|:-|:-|:-|:-|:-|\n| 单板代号 | 描述 | 9916SFUB | 9908SFUB | 9908-SSFUBA | 9904-SSFUB |\n| 9900XF48E2A | 48口10GE SFP+ | 支持 | 支持 | 支持 | 支持 |\n| 9900LQ36E2AU18 | 18口40G+18口40/100G（100G端口可降速到40GE使用，即单板最大可支持36个40GE端口） | 支持 | 支持 | 支持 | 支持 |\n| 9900UQ36E2A | 36口100G QSFP28 | 支持 | 支持 | 不支持 | 不支持 |\n', 'id': '675737_37'}
{'label': 'table', 'content': '产品组合及选型说明\n网关选型\nDC GW的选型参见下表：\n| 设备角色 | 可选产品及型号 | 使用场景 | 说明 |\n|:-|:-|:-|:-|\n| DC GW/Border LEAF | M6000-S系列 | 电信云 SDN网络主推M6000-S路由器作为网关，去堆叠双活部署 | 某一系列的产品的具体型号需要根据具体的功能、性能要求进行选择。使用场景仅供参考，具体选配还要考虑局方的要求，例如，某些运营商要求必须是集采设备。 |\n| DC GW/Border LEAF | 9900-S系列 | 当客户指定要求交换机作为网关时，可配置9900系列交换机作为DC GW | 某一系列的产品的具体型号需要根据具体的功能、性能要求进行选择。使用场景仅供参考，具体选配还要考虑局方的要求，例如，某些运营商要求必须是集采设备。 |\n', 'id': '675737_38'}
{'label': 'text', 'content': '产品组合及选型说明\n网关选型\nM6000-S\n电信云 SDN网络主推M6000-S系列路由器做网关，默认双活部署。M6000-S的具体型号，需要考虑如下： \n端口数量 \nVRC功能 \n单槽板卡处理能力 \nM6KS系列路由支持DC GW的设备型号有：M6000-3S或者M6000-3SPlus（支持1T线卡）、M6000-5S、M6000-8SPlus、M6000-18S。 \nM6KS系列路由器硬件板卡通用，软件版本通用，其中，M6000-3S不支持VRC，其余型号支持VRC；单槽板卡>400G，选择M6000-8SPlus，M6000-18S； \nM6KS在DC内的主要功能： \nEVPN VxLAN集中式路由GW+集中式转发/分布式转发，分布式路由GW+分布式转发； \n基于VRF+用户业务的引流功能； \nNAT功能； \nBFD支持双向（多跳/单跳）检测，也支持echo BFD（单跳/多跳）； \n注意事项： \n启用IRB后会导致性能降低一半； \n引流功能跟NAT功能不共存，二选一； \nVRC模式单机故障场景BFD会中断，业务切换时间较长；切换时延高要求高的场景 不推荐使用VRC模式，而是采用双活方式。', 'id': '675737_39'}
{'label': 'text', 'content': '产品组合及选型说明\n网关选型\n9900-S\n当客户指定要求交换机作为网关时，可以考虑9900做DC GW，两台9900交换机堆叠部署。但方案需要提交核心网与承载产品线共同评审，评审通过后才可配置。\n9900-S的具体型号，需要考虑如下两个参数：\n端口数量\nBFD性能\n9904-S和9908-S的端口数量和接口版本形态参见《ZXR10 9900 Series Datacenter Switch Product Description》。\nS系列交换机支持的BFD性能如下：\n| BFD心跳保活间隔 | 超时倍数 | 最大支持session数量 |\n|:-|-:|-:|\n| 200ms | 5 | 200 |\n| 300ms | 5 | 300 |\n| 400ms | 5 | 400 |\n| 500ms | 5 | 500 |\n| 1000ms | 5 | 1000 |\n', 'id': '675737_40'}
{'label': 'text', 'content': '产品组合及选型说明\n承载网带宽配置原则\n承载网带宽需要根据流量模型进行计算，一般而言根据业务流量型计算接入、汇聚、出口的带宽，最终获得网络各个层面的带宽需求。因此，带宽计算之前需要首选获取到该DC内将要部署的VNF的流量大小、部署模式和特定功能，才能估算各个设备的带宽需求。 \n流量大小。 \n部署模式。一般指的是哪些VNF采用集中式路由，哪些网元采用分布式路由； \n特定功能。一般指业务链，以及FW和LB功能，因此这几个功能会对引入流量迂回，对带宽消耗翻倍。 \n南北向流量主要分为两大类： \n用户面网元，包括vEPC、5G UPF、vPE、vBRAS-U，此部分流量占据电信云南北向流量的绝大部分。一般VNF系统给出流量模型中流量大小是X Gbps，指的是上行X/5 Gbps, 下行4X/5 Gbps。 \n信令流量，包括GTP-C、SIP、Diameter等信令，一般为Gbps到10Gbps级别。 \n东西向流量主要看用户面南北向流量的放大倍数(存储流量另外计算)，放大倍数主要取决于业务链的跳数。业务链每增加一跳，对ToR-Spine之间的带宽需求增加200%。 \n比如总计200Gbps用户流量，业务链平均3跳（经过DC中的三个节点，例如xGW-VFW-CGN），则总的总流量为200Gbps*3=600Gbps，考虑1+1冗余，意味着用户面VNF的ToR和Spine之间的总带宽要≥1.2Tbps。 \n注：vEPC系统的流量大小一般是上行+下行（现对于手机用户来说）的，比例是1：4，也即可以理解为单向流量，单位是Gbps；承载网的带宽一般是按照10GE，100GE等来计算的，是双向的。需要注意的一点是，由于vEPC系统的流量从DC一进一出，会耗费两倍的带宽，同时，上行和下行流量又是互补关系。因此，流量的Gbps和承载设备的GE带宽最终是1：1的关系，如果考虑承载网络因为构建虚拟网络带来的隧道和VLAN的封装占用的部分带宽，那就是略大于流量即可，举例：vePC系统的流量大小为95Gbps，DC GW（单设备）的对外网的带宽配置100GE，对Spine的带宽配置100GE，就能满足要求。', 'id': '675737_41'}
{'label': 'text', 'content': '产品组合及选型说明\n承载网带宽配置原则\nDC GW与业务SPINE交换机之间的带宽配置\n纯分布式路由场景下，根据南北向的流量进行配置网关和Spine交换机带宽，例如vEPC系统的流量是150Gbps， 那么，单DC GW和单SPINE之间需要配置至少2*100GE光纤来支撑业务，再考虑到1+1的冗余，双活的DC GW和SPINE之间需要配置4*100GE的光纤。 \n存在集中式路由场景下，除了考虑南北向流量外，还要考虑经过DC GW路由的东西向流量的大小。例如，南北向流量150Gbps，经过DC 迂回的东西向流量为80Gbps，DC GW和Spine之间的带宽配置需要按照150+80=230（Gbps）进行计算，单设备需要配置4*100GE（需要保持偶数），主备设备共需要配置8*100GE。', 'id': '675737_42'}
{'label': 'text', 'content': '产品组合及选型说明\n承载网带宽配置原则\nDC GW与外网路由器的对接带宽\nDC GW与外网路由器的对接，除了考虑局方现有设备的光纤支持类型外，还要考虑流量模型所需的带宽。此处主要以南北向流量带宽为参考，保证以1+1主备方式支撑实际流量大小。例如，局方设备可以支持100GE光纤，实际南北向流量为150gbps，那么需要单设备配置2*100GE光纤，主备设备共需要配置4*100GE带宽。', 'id': '675737_43'}
{'label': 'text', 'content': '产品组合及选型说明\n承载网带宽配置原则\nDC GW的互联带宽\n两个DC GW之间的互联带宽需要按照“DC GW与Spine之间带宽”的25%~50%进行配置。例如，单独的DC GW和Spine之间的带宽配置为400G，那么DC GW之间的互联口要至100G~200G。 \n另外，堆叠设备还需要配置单独的MAD口，具体支持哪种形态的MAD口，需要根设备确认。在启用MC-LAG的时候，不需要MAD口。', 'id': '675737_44'}
{'label': 'text', 'content': '产品组合及选型说明\n承载网带宽配置原则\nLEAF交换机的下联带宽\n这里的LEAF交换机包括业务LEAF，存储LEAF和管理LEAF，其下联带宽根据服务器的实际端口对应配备。', 'id': '675737_45'}
{'label': 'text', 'content': '产品组合及选型说明\n承载网带宽配置原则\nLEAF交换机的上联带宽\n业务LEAF的上联带宽有两种带宽配置模式：1）根据业务的实际需求；2）根据经验收敛比。如果部署的VNF能给出LEAF交换机上行的准确业务流量，那么就根据业务流量基础上考虑1+1冗余来配置上联带宽。如果没有明确业务流量模型，一般会参照上联：下联收敛比为[1/3, 1/1]的范围来配置。例如，下联为160GE，上联可以配置60GE~150GE。 \n存储LEAF一般采用1/2~1/1收敛比配置上联带宽。 \n管理LEAF的上联带宽一般配置2*10GE就可以。', 'id': '675737_46'}
{'label': 'text', 'content': '产品组合及选型说明\n承载网带宽配置原则\nLEAF交换机的互联带宽\n业务和存储LEAF的互联带宽一般配置上联带宽的25%~50%； \n管理LEAF的互联带宽一般配置为2*10GE就可以。', 'id': '675737_47'}
{'label': 'text', 'content': '产品组合及选型说明\n承载网带宽配置原则\n业务Spine交换机带宽\nSPINE交换机也分为业务SPINE，管理SPINE和存储SPINE。 \n业务Spine交换机上联带宽对齐DC GW的下联带宽；下联带宽对齐业务域LEAF交换机的上联总带宽； \n存储Spine交换机一般无上联带宽；下联带宽对齐存储域LEAF交换机的上联总带宽； \n管理Spine交换机上联带宽连接DC GW，一般能够配置的最小带宽即可，例如2*10GE；下联带宽对齐管理域LEAF交换机的上联总带宽； \n如果Spine交换机是上述几类SPINE的融合体，那么将根据上述带宽分别配置即可。 \nSpine交换机互联口，无需互联带宽。', 'id': '675737_48'}
{'label': 'text', 'content': '产品组合及选型说明\n控制器选型\n控制器根据组网类型有三种部署模式： \n纯EVPN方案，控制器1+1主备（集群）模式；推荐物理服务器部署，特殊情况下可以采用云外的虚机进行部署； \n混合方案。控制器1+1主备（集群）模式+PSSC组件。控制器本身推荐物理服务器部署，PSSC组件推荐云外的虚机进行部署。 \n控制器部署的资源需求参见《ZENIC vDC控制器产品配置指导书》。', 'id': '675737_49'}
{'label': 'text', 'content': '产品组合及选型说明\n统一网管选型\n网管选型为 NetNumen U31 ICT R10。 \n数据库采用 Ebase数据库 \n操作系统推荐为 CGSL \n双机软件为 NewStart HA \n该网管系统，支持在物理机上部署，也支持在VM上部署（支持VMware 和 TECS Openstack）。 \n推荐采用本地双机部署方式支持高可用；也能支持异地双机进行容灾部署。方案需要采用Newstart HA双机系统。 \n该网管系统支持集中部署，就是部署一套网管系统能够管理多个POD中的DC Fabric网络。为此，网管系统的管理通道网络需要能够与各POD中的Underlay交换机互联，和各个ZENIC VDC控制器联通。建议采用带外方式进行组网。 \n该网管支持通过我司核心网的MANO进行自动化部署。MANO自动化部署时，最多支持6个网络平面，其中EBase数据库单列一个平面。目前支持自动化部署网管系统，包括单机和双机系统，也能够检测系统的可用性，系统失效时，会自动重新部署。 \n网管系统的部署资源需求参见《NetNumen U31(ICT) R10配置指导书(VDC场景) 》。 \n注：2019-2020Q4，会持续使用U31 R10 ICT。后续UME可测试了再更新到文档中。', 'id': '675737_50'}
{'label': 'text', 'content': '产品组合及选型说明\n光模块及光纤\n服务器和ToR之间的10GE/25GE采用多模光纤及光模块，封装形式为SFP+或SFP28。如果有100GE接口也采用QSFP28接口。 \n对于ToR-Spine之间的连接，中小型DC也采用多模光纤、光模块；对于大型DC，或者两个网络平面划分在不同的机房中的DC设计，采用单模短距光模块，一般为2KM短距。40GE/100GE的光模块封装形式为QSFP和QSFP28。 \n注： \n1.如果不考虑成本，推荐单模2KM，稳定性比多模的好；如果成本控制较严，则推荐多模光纤。 \n2. 100GE R4（MPO）需要手动开启FEC', 'id': '675737_51'}
{'label': 'text', 'content': '产品组合及选型说明\n防火墙选型\n在电信云层面，防火墙的用途主要用于安全域边界的隔离，包括： \nDC业务网和外部网络之间的隔离，一般使用南北向防火墙 \nDC内不同安全域之间互访的隔离，一般使用跨域东西向防火墙 \nDC同安全域内部虚机间的互访，一般不用采用防火墙保护，或者使用SG保护。 \n系统管理网(VIM、SDN控制器)和云内的VNFM、MANO之间的隔离，一般成为管理面防火墙，该墙不受SDN纳管，且由核心网选配。 \n注： \nNFV中的一类应用防火墙极容易与上述的第1种防火墙混淆，需要分清。主要区别在于保护对象不同：应用防火墙适用于保护VNF所服务的service的，第1种防火墙是用于保护组成VNF的VM的安全的。举例，vEPC系统的Gi口上的FW，应该划入应用防火墙，用于保护手机中断的数据安全。 \n上述第1种防火墙，SDN已经可以实现自动化；第2种防火墙是在扩展OpenStack的接口基础上实现自动化的，主要和中移大云平台对接支持，当前SDN，TECS和MANO均不能完全支持，预计2019Q4之后能提供测试。 \n注：以上两种防火墙不能同时纳管。 \n如果客户要求提供租户级的FWaaS服务，则防火墙必须采用中兴已经测试通过的防火墙，包括： \n山石X/E系列硬件防火墙，包括X7180(40-240Gbps档次)、E6160(40Gbps)、E5660（20Gbps）、E3960(10Gbps)等产品；部署在网关之外，进行南北向流量或租户间东西向流量防护；同时支持IPS功能。 \n迪普FW1000系列硬件防火墙，FW-1000-TE-17N等； \n防护墙选型首先考虑从ZTE对接过的型号中选择，之外，还需要考虑另外的几条标准： \n客户指定品牌类型 \n考虑等级保护的原则，防火墙选择异厂家品牌 \n根据实际流量选择防火墙的具体型号 \n根据能力（比如NAT，IPS等功能）选择防火墙具体型号。', 'id': '675737_52'}
{'label': 'text', 'content': '产品组合及选型说明\n镜像汇聚及网关设备选型\n根据不同的端口速率要求，可选用5960 72DL-H、32LC-H、56QU-HC设备。', 'id': '675737_53'}
{'label': 'text', 'content': '产品组合及选型说明\n网络出口设计\n一般情况下电信云数据中心的网络出口都汇聚在网关上出口上，但是存在多个出口方向： \nInternet方向，如果存防火墙接管要求，串接防火墙，实现南北向流量安全防护，并且实现云平台所定义的SNAT及Floating IP功能。 \nDCI网络，无需经过防火墙，由网关直接和互联DC的网关建立VXLAN隧道进行通信。 \n客户专线接入，如果已经有CE/PE路由器终结MPLS转发，则转换成VLAN后，从DCGW设备上接入，将VLAN映射成VXLAN接入云网络；否则需要新增MPLS路由器，再接入DC GW。', 'id': '675737_54'}
{'label': 'table', 'content': '附录：缩略语\n表 缩略语一览表题注\n| 英文缩写 | 英文全称 | 中文全称 |\n|:-|:-|:-|\n| eVPN | Ethernet VPN | 以太VPN协议，一种通告以太网主机路由信息的协议 |\n| FW | Firewall | 防火墙 |\n| GW | VXLAN Gateway | VXLAN网关 |\n| K8S | Kubernetes | Google发起的容器编排平台，目前为业内主流的容器编排平台 |\n| LB | Loadbalancer | 负载均衡器 |\n| NFV | Network Function Virtualization | 网络功能虚拟化，ETSI 制订的电信网络虚拟化规范 |\n| NFVI | NFV Infrastructure | 网络功能虚拟化基础设施，指支撑NFV的计算/存储/网络基础设施 |\n| VTEP | VXLAN Tunnel End Point | VXLAN隧道终结点 |\n| VDC | Virtualized DataCenter | 虚拟化数据中心 |\n| VXLAN | Virtual eXtensible LAN | 虚拟可扩展局域网 |\n| ZENIC | Zte Elastic Network Intelligent Controller | 中兴弹性网络智能控制器 |\n', 'id': '675737_55'}
