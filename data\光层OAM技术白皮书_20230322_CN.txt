{'label': 'document', 'content': '光层OAM技术白皮书_20230322_CN', 'id': '1109133'}
{'label': 'text', 'content': '光层OAM技术白皮书\n光层OAM技术白皮书\n| 版本 | 日期 | 作者 | 审核者 | 备注 |\n|:-|:-|:-|:-|:-|\n| V1.0 | 2023/03/22 | 吴琼 | 陈勇 | 第一版 |\n| | | | | |\n| | | | | |\n目 录\n1. 趋势和挑战\t3\n2. 关键技术\t4\n2.1 光层开销技术\t4\n2.1.1 光层调顶技术\t4\n2.1.2 光层随路开销\t5\n2.1.3 光层连接管理\t6\n2.2 光层性能检测技术\t7\n2.2.1 频谱检测\t7\n2.2.2 光功率检测\t8\n2.2.3 在线OSNR检测\t8\n2.3 光层性能自动优化技术\t9\n2.3.1 通道内频谱自动均衡\t9\n2.3.2 通道功率与OSNR自动优化\t10\n3. 应用场景\t12\n3.1 超大容量、灵活业务快速开通部署\t12\n3.2 强业务自愈能力的智能在线运维\t12\n4. 展望\t13\n5. 缩略语\t15\n', 'id': '1109133_1'}
{'label': 'text', 'content': '趋势和挑战\n在全光网络大行其道的背景下，当前OTN网络在光层缺乏如电层一样成熟的OAM技术，导致骨干网面临升级扩容后运维难度不断增加的局面，尤其是在光层通道性能检测、故障定位以及业务调度上，常常需要借助繁琐的人工性能采集和复杂的定位分析方法，难以适应智能化的发展趋势。 \n具体而言，光层OAM技术挑战主要分为两个方面： \n1）光层开销 \n对于光层开销,目前标准只定义了标识与告警指示的定义、承载方式，没有对帧结构进行详细定义。在实际的系统运行过程中,主要使用电层开销,光层开销机制未被有效利用，只有将业务下路到电层才能读取开销信息,在全光传输过程中随路开销就无法发挥作用。 \n与上述通道级性能监测需求相匹配，光层OAM需要更加完善与更具针对性的光层开销管理机制，实现足够精细的光层管理颗粒度，弥补传统DCN信令网在光层性能检测、故障定位以及业务调度上的不足，共同保障光层OAM的高效可靠运行。 \n2）光层性能检测 \n传统的通道级光层性能检测主要通过相干光模块和光谱扫描器件实现，前者实时性好，但只能覆盖端到端检测，颗粒度太大，后者颗粒度上更细，可精确到每个站点，但时延较大（至少s量级），且部署成本高、集成度差。 \n与传统的光层性能检测技术相比较，光层OAM需要颗粒度更细（精准到波长与连纤粒度）与时延更低（支持保护倒换的ms量级）的光层性能检测手段，以保障光层的快速精准感知能力。', 'id': '1109133_2'}
{'label': 'text', 'content': '关键技术\n光层开销技术\n2.1.1 光层调顶技术\n光标签是一种最常见的光层调顶技术，通过在高速（GHz以上）的光波长通道加载低频（MHz量级）调制（Pilot Tone），因为使用的调制频率较正常业务频率很低，因此标签信号不会影响正常的业务信号，并且可以在系统的各个位置检测，实现对单波道的监控。光标签作为一种光层随路开销，既可以实时监测光层通道的性能，又可以携带光层业务标识与路径信息，甚至可以承载少量通道信令开销，是实现光层OAM达成光层智能化的关键。 \n光标签的实现原理如图1所示，收发端处理流程如下： \n调顶信息加载：内置DSP或光模块器件实现载频在MHz量级的频分复用调制，调制信息包含全网唯一通道标识以及光层OAM开销信息； \n调顶信息提取：在光放大与合分波单板内集成调顶信息检测与处理单元，检测硬件实现弱光信号的光电转换、电放大与AD转换，调顶数字信号处理包括快速傅里叶变换、调顶信息恢复、光层OAM信息处理与通道性能检测等。 \n因为标签的加载和传递在一个光波长通道中完成。通过对光波长通道上调制的低频标签信号进行频谱分析可以得到该业务波长的通道功率和OSNR等性能信息，同时，低频标签通道还可用于承载其他附加信息，从而可以实现更多波长通道相关的功能。有效设计低频频率范围、频段划分关系和调制深度，可以在不经过波长解复用的情况下，直接对已经合波的多波长信号进行多波长标签信号分析，提取各波长通道的性能及其他附加信息。 \n具体到波分设备，则可以在OTU的输出位置进行标签的加载，在波长路径经过的合分波板和光放大板等位置进行标签的检测。如下图所示：', 'id': '1109133_3'}
{'label': 'text', 'content': '关键技术\n光层开销技术\n2.1.2 光层随路开销\nG.709中详细定义了电层开销的类型及帧结构，但对光层开销只给出标识与告警指示的定义，承载方式（OSC），缺乏帧结构的详细定义。 \n常见的光层随路开销承载方式有光层调顶和OSC两种，OSC一般部署在OTS段，主要用于承载传统DCN的管理开销，只能处理OTS或OMS粒度的光层开销，无法处理OCH粒度的光层开销，而且在OSC单纤双向部署场景下，没有OSC的业务方向就会出现随路开销缺失的情况。 \n借助光层调顶技术，可以构建完善的光层随路开销体系，在OCH层使用调顶，就可以构建OCH层随路开销通道，同样的，在OTS/OMS层使用调顶，也可以构建OTS/OMS层随路开销通道。OCH层调顶与OTS/OMS层调顶结合，或者OCH层调顶与OSC结合，都可以构建完善的光层随路开销体系，实现足够精细的光层管理颗粒度，弥补传统DCN信令网在光层性能检测、故障定位以及业务调度上的不足，全方位提升光层OAM能力。 \n光层随路开销最大的优势是不需要借助复杂的IP路由关系即可实现随路开销与业务的绑定，从而实时感知光层的连接状态与物理性能，甚至可以借助光层随路开销管道来承载光层OAM信令，这让整个光层的感知、分析和调控都可以集中在物理层甚至是统一的通用光电芯片上实现，为光层智能化的实现创造了可能。', 'id': '1109133_4'}
{'label': 'text', 'content': '关键技术\n光层开销技术\n2.1.3 光层连接管理\n除了实现极致的通道性能检测与管理之外，光标签技术还会给所有业务波长打上全网唯一通道标识并随主光业务一起传输，在每一个检测点均可以提取出经过的所有业务通道标识，综合全网所有检测点的业务信息可以实现开局扩容或者业务新建、拆除与重路由场景下的拓扑发现、波长路径追踪以及错连检测等简化运维功能。 \n站点间的拓扑连接关系发现可以通过OSC监控光实现，但是站点内无OSC监控光覆盖，而光标签技术恰好可以弥补这一短板。对于ROADM/OTM站点间的拓扑连接发现，只需要控制每一个站点入站与出站的业务不完全一致保证不出现全部波长业务直通即可（可以通过上路或者下路带标签的测试波长实现），而对于站点内的拓扑连接发现，除了连接的两检测点之间业务标识一致外，还可以结合通道功率平坦度变化信息来进一步校验前面识别的连接关系，比如入站的OPA和WSS或者出站的WSS和OBA，两单板之间的连纤并不会引起功率平坦度的变化，而WSS之间的连接关系可以在前述基础上结合端口配置信息进一步推导出来。 \n在已知拓扑的基础上，可以从业务发起点开始沿着已知的拓扑路径查询当前业务可能经过的所有检测点的通道标识信息，基于光标签的全网唯一通道标识的查询还可以实现业务波长的真实传输路径检测，而这正是传统OPM的痛点之一，只能从光谱上区分波长，无法区分同波长的不同业务，而且随着波道间隔的压缩，光谱检测技术甚至都难以分辨有效波长通道。 \n在已知业务规划路径的基础上，可以查询规划路径上所有检测点是否检测到期望的业务标识开销，从而实现业务路径的校验，业务路径校验是业务路径检测的功能简化版本。在已知的期望连纤关系基础上，可以查询任意期望连纤两端检测点的所有通道标识与通道功率并做比对，增加通道功率信息是为了增加检验的准确度，根据比对的结果是否匹配来检验业务路径是否正确。', 'id': '1109133_5'}
{'label': 'text', 'content': '关键技术\n2.2 光层性能检测技术\n2.2.1 频谱检测\n常用的频谱检测方法一般有两种： \n1）光域频谱检测方法，指在光域上利用光滤波器（如体相位光栅）将待测信号的不同频率分量分离检测的方法，光域检测的颗粒度较粗、频谱范围较大，一般使用商用的OCM模块实现，主要部署于光网络关键节点，用以感知光传输链路上的多波长频谱均衡状态，辅助控制器完成通道间与通道内频谱不均衡损伤的优化； \n2）电域频谱检测方法，指在电域上利用电滤波器（如数字域带通滤波器组）将待测信号的不同频率分量分离检测的方法，一般使用DSP芯片实现，主要部署于光层业务终结位置，即线路接收的光模块内，用以感知经过完整光路传输之后的单波长频谱均衡状态，辅助控制器完成通道内频谱不均衡损伤的优化。 \n综上所述，频谱检测器可以感知光路上的频谱均衡状态，辅助控制器完成频谱不均衡损伤的优化，结合光层开销技术，可以实现全光层的自动优化。此外，高分辨率的频谱检测器还可用于感知ASE噪声引起的谱形变化，从而检测OSNR。', 'id': '1109133_6'}
{'label': 'text', 'content': '关键技术\n2.2 光层性能检测技术\n2.2.2 光功率检测\n光功率检测技术是光层性能检测技术中最基础，应用和部署最广泛的技术，按检测颗粒度，通常分为总光功率检测和通道光功率检测两种： \n1）总光功率检测，一般使用PD+电路放大+AD采集的组合直接检测光路上所有波长（含ASE底噪）的总光功率，总光功率检测点在光路上所有板卡中均有部署，可以用来感知光路板卡之间的连接损耗，值得一提的是，在光放大的输入和输出端部署总光功率检测点，可以辅助光放大器完成自动增益控制； \n2）通道光功率检测，常见的通道光功率检测方法有三种：OCM模块扫描频谱积分法、相干光模块内相干混频检测法与调顶检测法，前两种方法相对成熟，但受限于成本和集成度，难以大规模大范围部署，而调顶检测法可以将通道光功率检测跟总光功率检测的部署统一，大大简化光层的功率检测方案，此外，基于调顶的光功率检测时延在ms量级，可以实时感知OCH粒度的连接损耗，极大地提升光层的功率管理效率，同时也满足快速通道LOS检测要求。', 'id': '1109133_7'}
{'label': 'text', 'content': '关键技术\n2.2 光层性能检测技术\n2.2.3 在线OSNR检测\n在线OSNR检测，指的是在不中断业务的情况下完成该业务OSNR的测量，早期的波分复用系统（通常指10G系统）中，由于分配的频谱栅格远大于信号带宽，相邻的通道之间不存在频谱交叠，而且底噪也不受滤波影响，因此通常使用带外插值的方法完成噪底和OSNR估计。后期随着40G和100G系统的商用部署，受ROADM站点滤波效应和相邻通道串扰的影响，带外底噪再也无法代表通道内真实的噪声水平，传统的带外插值估计方法完全失效，因而在线OSNR检测成为光层性能检测里面的一大难点。 \nOSNR检测的本质在于信号光与ASE噪声的定量分离，而根据两者的光学差异可以实现分离：如信号光是相干光源，ASE噪声则是非相干光源，采用延迟干涉甚至是受激布里渊效应等非线性的方法即可实现分离；再比如信号光由两路正交偏振态的信号组成，而ASE噪声的偏振态则是随机的，采用偏振归零法也可以实现分离。然而，以上光学类方法普遍受环境的影响很大，如干涉效应对环境温度与细微振动很敏感，而信号偏振态也受环境影响而快速变换，因此，该类方法目前很难商用部署。另外一类方法基于数值与统计的差异实现信号与噪声的分离：如DSP类方法，通过解调信号或者利用信号相关噪声不相关的统计特性实现分离；如谱比较类方法，通过比较噪声引起的频谱数值差异实现分离。该类方法已经广泛部署于现有网络中，DSP类方法已内置于大部分主流相干光模块中，只能用于端到端OSNR的检测，且对非线性和滤波效应等ASE噪声以外的损伤很敏感，而谱比较类方法已通过商用OCM模块部署于光网络各节点中，可以检测任意节点之间的OSNR，能够弥补DSP类方法检测覆盖范围的不足，但检测精度严重依赖于OCM的频谱分辨率。 \n基于光纤链路的参数估计方法，即根据光纤链路中检测到的光功率和提前标定的光纤链路参数等信息，结合光纤和光放大器的物理模型，可以估算出光纤链路中任意位置处的OSNR，该方法在功率检测的基础上无额外硬件部署要求，检测覆盖范围大且无死角，与基于调顶的光功率检测方法结合还能极大地提升OSNR检测效率，将光层的感知能力提升到一个全新的层次。', 'id': '1109133_8'}
{'label': 'text', 'content': '关键技术\n2.3 光层性能自动优化技术\n2.3.1 通道内频谱自动均衡\n利用对发对收的两个OTU之间构建的波长标签所使用的物理信道可以实现光层OAM开销信息的两端交互，如下图所示，在光层OAM开销信息中写入谱形特征以及频谱均衡（含预加重和分片整形等）等参数，可以不借助传统信令网，完成收发端频谱不均衡损伤感知与端到端穿通优化联动，从而实现全光层业务频谱的自动均衡，解决动态可重构光网络中因滤波损伤和通道内功率不平坦引起的光层业务传输性能的劣化问题。 \n频谱自动均衡系统及装置如图7所示，除了核心的主光业务收发单元外，在传统的光收发机内需额外集成波长标签收发单元、光层OAM协议代理和频谱自动均衡控制单元，其中波长标签收发单元负责物理上随路波长标签信号的发送与接收，光层OAM协议代理负责波长标签数据中的OAM开销生成和解析，频谱自动均衡控制单元负责频谱不均衡损伤的分析、均衡调节参数的计算和调节指令的下发。 \n波长标签收发单元与主光业务收发单元之间为波长标签信号的物理层发送和接收接口，光层OAM协议代理和波长标签收发单元之间为波长标签信号的数据链路层发送和接收接口，光层OAM协议代理和频谱自动均衡控制单元之间为频谱自动均衡功能相关的光层OAM开销发送和接收接口，频谱自动均衡控制单元与主光业务收发单元之间为发端预加重参数配置与收发端谱形特征读取接口，频谱自动均衡控制单元需同时具备软件配置接口，为上层软件应用提供可配置途径。', 'id': '1109133_9'}
{'label': 'text', 'content': '关键技术\n2.3 光层性能自动优化技术\n2.3.2 通道功率与OSNR自动优化\n利用光层OAM随路开销或者传统的DCN连接实现上下游通道功率与OSNR的交互，如果以ms级的低时延性能开销实现，就可以让光路上每一个OTS/OMS段都具备独立的实时输入输出性能感知能力，每一个OTS/OMS段都可以根据输入输出的通道功率与OSNR的变化做出快速调整与优化，从而为全光层的通道功率与OSNR自动优化创造了可能。 \n通道功率与OSNR自动优化一般有四种可选的策略： \n目标功率与功率平坦度优先策略，给各站点的发送端和接收端设定固定的目标功率，优先将业务波长功率调节至设定目标功率，同时不能超过目标功率的最大偏差和功率平坦度指标约束； \nOSNR余量与复用段OSNR平坦度优先策略，每个复用段均以OSNR平坦度最优为调节目标，同时需满足每条业务的OSNR余量不低于最低门限要求； \nOSNR余量与单位比特能耗优先策略，每条业务在满足OSNR余量要求的前提下，以能耗最低为优化目标，可通过调节收发端光模块参数和光路功率来降低设备功耗； \n多目标混合优化策略，将多种优化策略中的优化目标按不同权重叠加到一起，形成一个新的混合目标作为优化方向，能够达到多目标平衡的效果。', 'id': '1109133_10'}
{'label': 'text', 'content': '应用场景\n3.1 超大容量、灵活业务快速开通部署\n在超大容量、灵活业务的开通部署阶段，由于大容量的光交叉节点连纤关系复杂，人工操作很容易出现失误，因而需要连纤关系自动发现方法来确认连纤关系和质量，借助光层调顶技术，既可以发现并检验连纤关系，还可以通过调顶检测的光功率来计算连接损耗，可以大大简化开局光纤连接阶段的人工操作与检验环节。 \n在完成连纤自动发现需要新建业务时，可以通过光层随路开销来辅助完成光路连通性校验，同时使用光层性能自动优化技术实现光层业务传输性能的快速优化，如可以通过通道内频谱自动均衡技术解决因滤波损伤和通道内功率不平坦引起的光层业务传输性能的劣化问题，通过通道功率与OSNR自动优化技术将光层业务的通道功率与OSNR调制最优状态，如可以选择功率平坦度最优、OSNR平坦度最优或者单位比特能耗最优等优化策略。全光层的性能快速感知与自动优化技术，可以减少跨层协议与信令交互，能够显著提升业务开通部署效率。', 'id': '1109133_11'}
{'label': 'text', 'content': '应用场景\n3.2 强业务自愈能力的智能在线运维\n智能在线运维要求在不需要人力干预的情况下，系统能够处理网络突发状况，保持客户业务的稳定性。 \n当发生光缆老化、弯折、挤压、振动等光层性能劣化事件时，光路损耗发生变化，从而导致光层业务性能劣化，此时，基于光层OAM的快速性能感知单元能够及时发现这一功率和衰减量变化并完成自动优化，维持在线业务的功率水平，保证客户业务的可靠传输。当发生WSS通道波长指派、衰减量设置等光层业务动态调整事件时，光路频谱不均衡损伤发生变化，从而导致光层业务性能劣化，此时，基于光层OAM的快速性能感知单元能够及时发现这一频谱不均衡损伤的变化并完成频谱自动均衡，维持在线业务的频谱均衡水平，保证客户业务的可靠传输。 \n当光层性能劣化到无法达成在线优化目标时，如断纤故障，需要触发业务恢复动作，首先得完成恢复路由的计算，在计算恢复路由时，需要合理避开有性能风险或故障的链路，而光层OAM感知单元在实时感知业务风险或故障的同时，能够快速定位出风险或故障链路位置，刚好能够辅助提升算路的效率和光网络资源的利用率，在业务重路由之后，也能基于光层OAM的快速性能感知和优化机制完成业务的快速恢复，提升客户业务的服务质量。', 'id': '1109133_12'}
{'label': 'text', 'content': '展望\n调顶技术的随路特性与极致的通道性能检测能力，使其在光层OAM技术的演进中扮演着越来越重要的角色。基于调顶的光层OAM技术当前需要解决的问题主要分为两个方面： \n一、物理层技术难题 \n主要体现在调顶载频等关键物理层参数的选取上，载频选取过高，受非线性SRS影响，调顶信号会对不同波长形成较大串扰，长距传输场景应用受限，载频选取过低，受色散引起的功率衰落影响，调顶信号经过长距离传输后接收灵敏度与功率检测精度均会劣化，需要选取合适的载频，将非线性SRS和色散功率衰落的影响降到最低，并且能同时满足以下应用要求： \n能满足G.652、G.654和G.655等色散与非线性差异较大的光纤传输场景，需在不同光纤传输场景下达到非线性SRS与色散功率衰落的平衡； \n能满足200G QPSK、400G QPSK等大波特率业务的长距离传输场景，需解决大波特率业务色散功率衰落严重的问题； \n能满足C+L扩容需求，需预留足够多的频谱资源用于调顶载频的分配，并保证多载频检测时的高接收灵敏度要求，通常-30dBm以下。 \n二、当前基于调顶技术实现的光层随路开销最大的缺陷主要有两点： \n中间节点只能读取调顶信息，不具备改写能力，因而暂时无法自成体系，进而独立实现光纤链路自动发现与快速故障定位等光层OAM基础需求； \n在保证不影响主光业务性能的前提下，调顶的速率最多只能做到Kbps量级，因而调顶通道无法承载大带宽低时延类型开销，其应用范围严重受限。 \n为了发挥出调顶随路开销在光层OAM中的最大作用，需将其与电层开销、光监控信道(Optical Supervisory Channel,OSC)等传统开销机制协作，取长补短，而多级开销协同涉及到不同技术特点的多级开销接口的定义及规范，对传统DCN信令网架构也会形成较大冲击。当前，我们仍然需要挖掘更多光层OAM开销协同技术的应用，其应用价值与实现代价需要进一步探讨。 \n三、光层OAM技术将来会朝三个方向演进： \n1、构建更加完善的光层开销体系，光标签技术提供了一种光通道层随路开销传输渠道，为光层开销体系的完善开辟了新的局面，新的光通道层开销的定义将会引入新的光层OAM功能，随着光层开销的不断完善，光层OAM的功能也会日趋成熟； \n2、构建更加快捷的光层感知网络，光标签技术提供了一种板上集成的光通道层性能快速检测方法，高度集成的特点便于其在光网络中广泛部署，随着基于光标签功能的光层感知网络的全面部署，光通道层性能的感知将会变得无时无刻无处不在，海量光层性能数据的采集将会为光层智能化引擎提供更为实时可靠的决策依据； \n3、构建全连接的光层一体化OAM平台，OSC随路开销与光标签随路开销的结合，OSC+光标签+OPM+OTDR等光检测与随路开销功能的融合，并最终出现硬件集成与芯片定制化的产品形态，整个光层的所有感知与开销功能均统一集成于同一光层OAM功能单元内，而光路上所有的光层OAM功能单元均通过统一的光层随路开销连接到一起，互相之间能够进行实时性能等信息的共享和管控信令的传递，就像一个整体一样，全连接的光层一体化OAM平台必定会将光层智能化提升到一个新的层次。 \n综上所述，光层OAM技术是光网络运维的核心，是光层智能化的基础，具有广阔的发展与应用前景。', 'id': '1109133_13'}
{'label': 'table', 'content': '缩略语\n| 缩略语 | 英文原文 | 中文含义 |\n|:-|:-|:-|\n| OTN | Optical Transport Network | 光传送网，网络的一种类型，是指在光域内实现业务的传送、复用、路由选择、监控，并且保证其性能指标和生存性的传送网络 |\n| OAM | Operation，Administration，Maintenance | 运营，管理，维护 |\n| SNR | Signal to Noise Ratio | 信噪比 |\n| BER | Bit Error Rate | 误码率 |\n| WSS | Wavelength Selective Switch | 波长选择开关，实现ROADM的一种器件 |\n| OTU | Optical Transponder Unit | 光收发单元；光波长转换器 |\n| SRS | Stimulated Raman Scattering | 受激拉曼散射，光纤中的一种非线性效应 |\n| OSNR | Optical Signal to Noise Ratio | 光信噪比，表征光信号质量的参数 |\n| OSC | Optical Supervisory Channel | 光监控信道，承载DCN网络的光层随路开销通道 |\n| OCM | Optical Channel Monitor | 光通道监测仪，用于波分系统中检测光通道性能的器件 |\n| DCN | Data Communication Network | 数据通信网络，是指为传送平面、控制平面和管理平面的内部以及三者之间的管理信息和控制信息提供传送通路 |\n| ASE | Amplified Spontaneous Emission | 放大自发辐射，一般指光放大过程中产生的自发辐射现象 |\n| DSP | Digital Signal Processing | 数字信号处理，一般指专业的数字信号处理芯片 |\n| OTS | Optical Transmission Segment | 光传输段，一般指光纤跨段 |\n| OMS | Optical Mutiplexing Segment | 光复用段，一般指合波之后的光传输段 |\n| ROADM | Reconfigurable Optical Add-Drop Multiplexer | 可重构光分插复用器，是一种使用在密集波分复用系统中的器件或设备，其作用是通过远程的重新配置，可以动态上路或下路业务波长 |\n| QPSK | Quadature Phase Shift Keying | 正交相移键控，是一种四相位调制方式，具有良好的抗噪特性和频带利用率，可以应用在相干光通信系统中 |\n| WDM | Wavelength Division Multiplexing | 是将两种或多种不同波长的光载波信号在发送端经复用器汇合在一起，并耦合到光线路的同一根光纤中进行传输的技术 |\n| OTDR | Optical Time Domain Reflectometer | 光时域反射仪，是一种检测光纤光缆连接质量的器件 |\n', 'id': '1109133_14'}
