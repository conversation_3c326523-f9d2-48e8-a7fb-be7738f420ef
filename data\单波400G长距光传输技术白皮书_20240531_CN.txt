{'label': 'document', 'content': '单波400G长距光传输技术白皮书_20240531_CN', 'id': '1105451'}
{'label': 'text', 'content': '单波400G长距光传输\n技术白皮书\n单波400G长距光传输技术白皮书\n| 版本 | 日期 | 作者 | 审核者 | 备注 |\n|:-|:-|:-|:-|:-|\n| V1.0 | 2023/05/21 | 冯振华、陈欢 | 陈勇 | 第一版 |\n| V2.0 | 2024/5/30 | 冯振华、陈欢 | | 第二版，修改统一调制格式命名规范，DP-MQAM统一成PM-MQAM，并声明DP与PM含义等同。 |\n| | | | | |\n| | | | | |\n目录\n1. 光网络演进逻辑与规律\t5\n1.1. 网络流量持续增长，承载接入面临带宽和时延压力\t5\n1.2. 传输距离不变速率容量翻番，持续降低单比特成本\t7\n1.3. 性能尺寸成本兼顾，可插拔低功耗光模块成主流\t9\n1.4. 短距应用先行，新一代模块提升上一代长距传输能力\t12\n1.5. 架构持续创新，推动光网智能化提升运维体验\t13\n2. 光网络演进挑战\t16\n2.1. 光系统损伤对速率敏感\t17\n2.2. 高阶码型传输距离受限\t18\n2.3. 光电器件带宽受限需新材料新封装\t19\n2.4. 带宽扩展引入新难题\t21\n3. 长距400G光传输关键技术\t22\n3.1. 高速相干光模块\t22\n3.1.1. 先进DSP芯片与算法\t23\n3.1.2. 高速相干光器件\t25\n3.2. 扩展C+L波段光系统\t26\n3.2.1. L波段波长分配\t27\n3.2.2. L波段光器件\t28\n3.2.3. C+L光传输系统架构\t29\n3.2.4. C+L光功率管理\t34\n3.2.5. 400G C+L光传输性能评估与对比\t35\n4. 长距400G技术进展与应用建议\t38\n4.1. 400G相关标准与产业链进展\t38\n4.2. 单波400G长距传输相关试点和验证\t40\n4.3. 典型应用场景分析与建议\t44\n5. 总结与展望\t45\n6. 参考文献\t47\n7. 缩略语\t48\n', 'id': '1105451_1'}
{'label': 'text', 'content': '光网络演进逻辑与规律\n长期以来光网络作为网络流量承载的基础管道网络，体现在“大容量、高速率、长距离”刚性管道能力提供方面；然而近期随着4k视频、云网融合、“东数西算”等应用和需求的普及和发展，光网络正向高品质综合业务网演进，特色为“智能化、小颗粒、精细化”灵活业务处理方面。伴随通信网络几乎每10年一代的行业周期，如图1.1所示，从3G、4G到5G甚至6G时代，光网络也经历了一系列的转变和演进，本章节将分析总结其演进升级的内在逻辑与基本规律。', 'id': '1105451_2'}
{'label': 'text', 'content': '光网络演进逻辑与规律\n网络流量持续增长，承载接入面临带宽和时延压力\n网络流量的快速增长给承载接入带来的巨大带宽压力，始终是光网络提速扩容的最根本动力。根据Cisco预测2017-2022年全球IP流量复合年增长速率 (CAGR) 为26%，其中到2022年视频类业务占比达71%[1]。相比于固网，移动网络流量年增速更快达到46%，而平均移动接入带宽年增速仅27%，Omdia预测2019-2024年网络流量预测结果[2]也表明近几年流量增速CAGR接近30%，ITU更是预测在2020-2030年移动流量的复合年增长速率高达55%[3]，如图1所示，可见未来几年网络带宽增长需求十分强劲。网络流量的激增主要得益于宽带中国、FTTx、东数西算、数字经济等国家战略或重大工程驱使下，以大视频、多用户、高突发为主要特征的新应用和新业务需求大幅增长，当前主要以云计算、物联网、高清视频、工业互联等应用为主，面向未来6G，网络应用将进一步关注3D沉浸式体验，以扩展现实（XR）、智慧交互、全息通信、数字孪生等为代表的新应用将成主流[3]。\n新的应用和业务，在进一步丰富我们生活、方便我们工作的同时，反过来也对网络的带宽，时延，可靠性等指标提出了更高的要求。 研究表明[4]，这些面向6G的新应用对显示、带宽、时延和可靠性方面的要求如表1.1所示。海量数据、宽带连接是未来应用的基本属性，网络应该提供大容量传输，同时确保低时延和高可靠，保证业务实时性和安全性，这些指标需要新的通信技术、网络架构、运维系统支持，因此网络升级演进是必然的。\n表1.1.\xa0新应用对网络性能需求\n| 应用 | 云化XR | 全息通信 | 智慧交互 |\n|:-|:-|:-|:-|\n| 显示 | 8K | 16K | - |\n| 带宽 | 1Gb | 1~10Gb | >10Gb |\n| 时延 | 5~20ms | 1~5ms | <1ms |\n| 可靠性 | - | 99.999% | 99.99999% |\n', 'id': '1105451_3'}
{'label': 'table', 'content': '光网络演进逻辑与规律\n传输距离不变速率容量翻番，持续降低单比特成本\n光传送、接入网络因采用高频光载波调制信号可在低损耗光纤信道中多路同时传输，具有带宽高、容量大、抗干扰强、绿色节能、无电中继距离长等优点，作为通信网络和信息社会的基石，在数据信息承载和传送方面发挥着越来越重要的作用。回顾光通信技术、产业发展里程，如表1.2所示，长距光通信系统基本遵循着4-5年一代的发展规律，新一代系统相比于上一代在单波速率、单纤容量上都有2-4倍的提升。在100G以前，光模块主要依赖强度调制直接探测技术，线路上采用色散补偿模块或色散补偿光纤来克服色散的影响实现长距传输。自100G开始，QAM调制结合数字相干探测成为业界主流趋势，利用强大的数字信号处理（DSP）技术补偿信号在光纤传输及模块收发信道中的各种线性损伤，如色散、PMD、 带宽限制、skew等，这使得单波高速信号长距传输成为可能，并且通过扩展波特率和调制码型阶使得单波速率还能持续演进。\n表1.2.\xa0长距光通信技术演进及关键系统特征\n| 时间 | 1998 | 2002 | 2007 | 2013 | 2018 | 2018 | 2023 | 2023 | 2028 |\n|:-|:-|:-|:-|:-|:-|:-|:-|:-|:-|\n| 单波速率 | 2.5G | 10G | 40G | 100G | 200G | 200G | 400G | 400G | 800G |\n| 系统容量 | 0.2T | 0.8T | 3.2T | 8T | 16T | 16T | 32T | 32T | 64T |\n| 波特率(Gbd) | 2.5 | 10 | 20 | 32 | 64 | 64 | 128 | 128 | 192/256 |\n| 波段 | C4T | C4T | C4T | C4T | C6T | C6T | C+L12T | C+L12T | S+C+L18T |\n| 光纤 | G652.D | G652.D | G652.D | G652.D | G652.D | G652.D | G652.D/G654E等 | G652.D/G654E等 | G652.D/G654E等 |\n| 调制解调 | 强度调制直接检测/DCM | 强度调制直接检测/DCM | 强度调制直接检测/DCM | 高阶调制相干检测/无DCM | 高阶调制相干检测/无DCM | 高阶调制相干检测/无DCM | 高阶调制相干检测/无DCM | 高阶调制相干检测/无DCM | 高阶调制相干检测/无DCM |\n| 交叉维度与设备形态 | 2-4维，FOADM | 2-4维，FOADM | 2-4维，FOADM | 9-20维，ROADM | 9-20维，ROADM | 20-32维，OXC | 20-32维，OXC | 32维及以上，OXC，OXC级联 | 32维及以上，OXC，OXC级联 |\n', 'id': '1105451_4'}
{'label': 'text', 'content': '光网络演进逻辑与规律\n性能尺寸成本兼顾，可插拔低功耗光模块成主流\n光网络区别于数据通信很重要的因素是光网络需要兼顾性能尺寸功耗和成本，这主要体现在光传输系统相干光模块的形态上。100G时代早期，相干光模块主要是嵌入式MSA形态，如5x7寸，后来到4x5寸，主要采用分立相干光器件，如调制器，接收机，Driver, TIA,ITLA，相干DSP芯片等组装而成，尺寸较大，功耗较高。后来随着芯片、器件集成和封装技术的突破和发展，相干光器件集成度显著提高，支持热插拔的CFP甚至CFP2相干光模块应运而生，此时相干光模块主要由三件套（CDM+ICR+ITLA)结合相干DSP芯片构成。进一步地，随着InP,SiP集成平台的成熟，CDM+ICR可封装成ICRM(一般是SiP路线)，CDM+ICR+ITLA封装成TROSA(一般是InP路线)，器件/组件集成度进一步提升，成本功耗也得以下降。最近两年，2.5D/3D封装工艺的成熟，ICRM+DSP共封装成MCM也已成为可能，有效改善光模块有效带宽10%以上，目前已在200G,400G相干产品中规模应用。与此同时，作为相干光模块的核心之一，DSP芯片的性能、功耗、面积也随着CMOS芯片工艺的进步而显著改善，如表1.3所示。每一代新的CMOS工艺都会在PPA(性能，功耗，面积）方面带来明显的改善[6]，如7nm DSP ASIC芯片，相比于其上一代16nm工艺，性能改善约30%，功耗降低60%，面积降低70%，这不仅使得一些更先进的DSP整形或均衡算法能在芯片中实现，增加DSP的性能或功能，而且使得整体功耗降低，从而使得尺寸更小的可插拔模块成为可能[7]，如OSFP, QSFP-DD等，如表1.4所示。\n表1.3. CMOS工艺节点演进及性能改善\n| | 2011年40nm->28nm | 2015年28nm->16nm | 2018年16nm->7nm | 2020年7nm->5nm | 2022年5nm->3nm |\n|:-|-:|-:|-:|-:|-:|\n| 性能(%) | 50 | 60 | 30 | 15 | 10 |\n| 功耗(%) | -40 | -60 | -60 | -30 | -20 |\n| 面积(%) | -26 | -50 | -70 | -45 | -42 |\n尽管经过10余年的发展，相干光模块仍然保持高性能和低功耗两种形态继续演进，前者一般是对应MSA固定封装形态，后者对应可插拔形态，但当前运营商在节能减排、低碳环保的压力下，对低功耗模块需求更加迫切，一些交换机，路由器等设备受限于面板槽位及端口，对模块尺寸也有更明确的要求。据统计在国内近年来发货相干光模块中，可插拔形态的模块占比高达90%以上。当然，OTN电信市场与传统数通市场不同，电信市场对传输距离有明确要求，因此模块在尺寸功耗降低的同时还得保证性能，这主要是通过先进制程DSP芯片来保证。尽管步入5nm后CMOS工艺的红利在逐步减小[6]，基于IEEE国际器件与系统路标（IRDS)，芯片制程工艺仍然可持续进步，如2025年有望实现2.1nm，2028年进入1.5nm,2031年进入1nm。无疑这将继续支持相干DSP芯片持续提速、降功耗，为B400G相干光模块保持小型化可插拔形态奠定可能性。\n表1.4. 相干光模块形态基本参数\n| 模块形态 | 尺寸(长宽高/mm) | 功耗限制(W) | 图片示例 |\n|:-|:-|-:|:-|\n| MSA(5x7’) | 177.8*127*33 | 90 | |\n| MSA(4x5’) | 127*101.8*25 | 45 | |\n| CFP | 144.8*82*13.6 | 32 | |\n| CFP2 | 107.5*41.5*12.4 | 24 | |\n| QSFP-DD | 89.4*18.3*8.5 | 15 | |\n| OSFP | 100.4*22.9*13.0 | 30 | |\n', 'id': '1105451_5'}
{'label': 'table', 'content': '光网络演进逻辑与规律\n短距应用先行，新一代模块提升上一代长距传输能力\n同一相干光模块支持多速率、多调制格式，可支持不同应用场景也是光传输系统演进的重要特点。针对城域、干线等不同应用场景，400G传输系统采用不同实现技术，可以实现传输性能、频谱效率和成本的平衡，图1.4中总结了单波400G光传输系统的主要特征与能力。400G技术可分为三代，第一代是波特率为64Gbd,支持PM-16QAM城域传输，已经商用；第二代是波特率约为90Gbd,支持PS-16QAM中长距传输，小规模试点；第三代波特率达到130Gbd, 支持PM-QPSK超长距干线传输，即将具备商用能力。值得注意的是，正是由于目前相干光模块普遍具备多速率、多制式可编程的特征，因此不同速率相干光模块一般是指满足OTN实用距离的最高调制速率不同，如100G,200G，400G，800G等，一般还具备向下兼容低速率调制模式的能力，如表1.5所示，总结了不同速率相干光模块支持的主要调制格式、应用场景及封装形态。可以看出，每一代高速率光模块都增加高bit带宽的中短距码型，解决上一代模块长距传输能力，如400G光模块主要搭载400G 16QAM(中短距)和200G QPSK(长距)，这主要受三个因素影响。1）需求侧，随着CDN、云业务部署，数据中心离用户越来越近，大部分网络流量不需要经过长距干线被终结在中短距城域网及数据中心互连范围内，目前城域网流量已经超过干线，并呈现快速增长态势，因此城域应用比长距干线更早面临容量和带宽压力，扩容升级需求更迫切；2）能力上，早期光器件、DSP芯片（DA/AD)在带宽能力上略显不足，无法支持高波特率码型应用；3）产业链方面，新一代速率短距/城域应用和上一代速率长距共产业链，实现产业链归一。如400G光模块主要采用64Gbd器件，既实现了400G城域和DCI应用，又解决了上一代200G长距传输能力不足问题。又如800G光模块将主要采用130Gbd器件，即解决800G城域传输，又能实现400G长距干线应用。另外，一般早期是MSA固定模块，后期逐步被CFP2可插拔替代，如当前90Gbd 800G和130Gbd 1.2T光模块均为MSA形态，预计1-2年后随着130Gbd产业链的进一步成熟，130Gbd相干光模块有望演进为CFP2可插拔形态，并支持向下降速，兼容支持90Gbd 400G和800G调制模式应用。\n表1.5. 不同速率相干光模块主要调制格式、应用场景与封装形态\n| | 主要调制模式 | 应用场景 | 封装类型 |\n|:-|:-|:-|:-|\n| 200G光模块 | 200G\xa016QAM/8QAM/PS-16QAM；\xa0100G\xa0QPSK | 100G干线长距，200G城域 | MSA/CFP2 |\n| 400G光模块 | 400G\xa016QAM;200G\xa0QPSK/8QAM/PS-16QAM | 200G干线长距、城域，400G城域/DCI | MSA/CFP2 |\n| 800G光模块 | 800G\xa0PS-64QAM;\xa0400G\xa0PS-16QAM | 400G省内干线，800G\xa0DCI | MSA |\n| 800G光模块 | 800G 16QAM/PS-16QAM; 400G QPSK/16QAM/PS-16QAM，200G QPSK | 200G干线长距，400G干线长距，省内干线，800G城域 | CFP2 |\n| 1.2T光模块 | 1.2T\xa064QAM；\xa01T\xa0PS-64QAM；800G\xa016QAM/PS-16QAM；\xa0400G\xa0QPSK/PS-16QAM | 200G干线长距，400G干线长距，省内干线，800G城域，1T/1.2T\xa0DCI | MSA |\n', 'id': '1105451_6'}
{'label': 'text', 'content': '光网络演进逻辑与规律\n架构持续创新，推动光网智能化提升运维体验\n光网络由基础承载管道向品质业务网转型，网络组网规模、服务能力、运维效率等方面都发生着显著的变化，这就需要光网络设备商、运营商及产业链同行一首通过架构创新和技术突破来构建大容量、高带宽、低延时、高可靠、智能化、易运维的精品网络。大容量、高带宽主要通过单波提速、波段扩展及新型多芯少模、空芯光纤等技术路线来实现，考虑工程实际，未来3-5年内，基于C+L波段扩展的光系统架构配合单波400G、800G速率将是商用主流路线，面向未来中长期应用，新型光纤和放大技术提升带宽、传输距离也将变得更加重要。低时延主要是通过光网络组网规划、站点架构简化来实现。规划上随着算力光网建设的驱动，通过更加合理的光层和电层路径规划，使得电节点间光程最短、端到端尽量光层穿通一跳直达。站点架构上主要是通过技术创新和架构极简，如图1.5所示，用高集成、更智能的OXC替代ROADM，实现设备小型化、站内免连纤、网元连接自动发现和校验、一板卡一方向使用和维护更加方便快捷，极大缩减业务调测和开通时间。光网络可靠性主要依赖W/ASON分别在光、电两层提供快速、可靠的恢复和保护来实现可抗多次断纤，自动重路由功能。这其中可融合引入基于AI算法、数字孪生建模、光传输性能（QoT)预测等技术进一步增强业务可靠性，从被动运维向主动甚至预测式运维转变。 \n智能化、易运维是下一代光网络的关键必备属性。国内外运维商、设备商都在积极讨论并制定自智光网络相关分级、关键功能特性、应用场景相关的标准[8,9]，主要研究进展如图1.6所示。目前从网络意图、感知、分析、决策、执行等维度智能化、自动化程度可将自智网络分为L0-L5个等级，最终网络演进的目标是1）从用户体验视角，实现“三零”：零接触、零等待、零故障的便捷操作使用体验；2）从网络运维视角，实现“三自”：自配置、自修复、自优化的省心网络运维体验。其中不同等级智能的定义如下： \nL0 – 人工运维： 系统提供辅助监控能力，所有动态任务都需要人工执行； \nL1 – 辅助运维： 系统可根据预先配置，执行特定的重复性子任务，以提高执行效率。 \nL2 – 部分自智网络： 在特定外部环境中，系统可以根据预定义的规则/策略， 面向特定单元使能自动化闭环运维。 \nL3 – 条件自智网络： 在L2的基础上，系统可以实时感知环境变化，并在特定网络专业中进行自优化和自调整，以适应外部环境。 \nL4 – 高度自智网络： 在L3的基础上，系统可在更复杂的跨多网络领域环境中，实现业务和客户体验驱动网络的预测式或主动式的闭环管理，从 \n而进行分析并做出决策。 \nL5 – 完全自智网络： 这个等级是电信网络演进的终极目标，系统具备面向多业务、多领域、全生命周期的全场景闭环自治能力。 \n根据上述定义，当前的光网络基本具备L3级自智能力，预计24年可满足L4级高度自智水平。在光网络智能化能力提升过程中，基于调顶的数字光标签、基于光传感的同路由检测、基于AI的光器件/光缆故障/系统软故障预测、基于数字孪生的在线优化、闭环控制等技术将发挥越来越重要的作用，不断提升光网络在规划、建设、维护、优化及运营全生命周期的自动化、智能化程度。', 'id': '1105451_7'}
{'label': 'text', 'content': '光网络演进挑战\n流量增长驱动光网络代际演进，主要体现为单波速率、系统带宽容量提升，网络运维简化和智能化两方面，这无疑给光通信产业带宽了巨大的技术研究和商业应用机会，但同时各方面挑战也随之而来。首先是部分光系统损伤对速率敏感，速率越高，代价越灵敏，如色散，skew等；其次，高阶码型的欧氏距离显著下降，抗噪声和干扰能力弱，难以支持长距传输；另一方面，新型空分复用、空芯光纤，低噪声分布式拉曼放大等有望实现更大容量、更长距离传输的新技术，受限于对既有光纤光缆、站点等基础设施投资的保护和利旧，商用落地应用较艰难；光器件带宽步入100Gbd以后，进一步提升带宽和性能需要借助于新材料平台和新封装工艺，如薄膜铌酸锂材料和2.5D/3D共封装工艺；带宽扩展到多波段传输后，一方面器件带宽需要拓展，这需要开发并引入新放大器、WSS、光模块等，另一方面光纤中存在强烈的受激拉曼散射效应，导致光功率从短波向长波转移，引入系统性能难以均衡的问题。', 'id': '1105451_8'}
{'label': 'text', 'content': '光网络演进挑战\n光系统损伤对速率敏感\n光传输系统及其中主要损伤如图2.1所示。从系统组成上讲，光传输系统包括OTU（相干光模块）,（R）OADM站点，线路放大站点，及光纤，其中的损伤主要包括ASE噪声，光纤线性损伤（色散/PMD，SOP变化等），光纤非线性干扰（SRS,FWM,XPM,SPM等），光器件不理想损伤（带宽，分辨率限制，DSP算法实现代价，FEC编解码与理论差距等） 以及其通道的干扰与不均衡（如串扰，imbalance, skew等）。即使保持调制格式不变，\xa0随着单波速率的提升，有些信道损伤也将变得更加敏感。 \n光纤色散对信号的影响与波特率成二次方关系，因此当波特率从64Gbd提升到90Gbd甚至130Gbd时，400G信号的色散容限/色散补偿将面临新的挑战，需要新的均衡补偿算法架构。在光纤非线性代价方面，尽管波特率提升后可以容许更大的入纤功率，但高速信号的OSNR容限变大后，非线性干扰造成的性能代价对高速信号更大。光模块内部，由于核心电芯片及光器件带宽限制，高速信号会经历更大的滤波损伤，进而引入强烈的码间干扰(ISI)，需要更强的均衡算法来补偿；另一方面，高速信号对偏振相关损耗/增益（PDL/PDG)，四个通道间的幅度相位失衡（imbalance)，延时(skew)等损伤的容忍度更低。再者，高速信号因占用更宽频谱，其对多通道串扰更加敏感。', 'id': '1105451_9'}
{'label': 'text', 'content': '光网络演进挑战\n高阶码型传输距离受限\n保持波特率和器件带宽不变，高阶调制码型是提升单波速率的重要途径。但是从QPSK向8QAM,16QAM,甚至更高阶QAM如64QAM等码型演进时，从星座图上很容量看出调制阶数越高，星座图越密集，符号间的最小欧式距离也不断减少，这样在有噪声和干扰的信道下符号判决出错的概率也会大大增加，OSNR容限更大，极大地限制传输距离。另一方面，高阶QAM对ADDA的有效分辨率（ENOB)、抖动（Jitter)等指标、ITLA线宽/频偏、调制器及光纤非线性等损伤更敏感，进一步缩短传输距离。为改善高阶调制的OSNR容限，业界一般采用概率星座整形（PCS)或几何整形(GS)算法配合优化的FEC编解码算法来提供1-2dB的OSNR整形增益。尽管该技术已经成功商用，但相比于标准QAM调制解调，这会增加信号的峰均功率比(PAPR)，对非线性更敏感，引入开销增加波特率和功耗，整体上的性能仍然不如更高波特率配合低阶调制信号。 \n采用超低损大有效面积光纤（G654.E）光纤来替换现有的G652光纤，或者缩小现有的跨段距离来降低跨段损耗、提升入纤功率有望将高阶码型的传输距离改善50%以上。另外，采用分布式拉曼放大（DRA）来替换现有的EDFA，减小光纤链路上累积的ASE噪声也能将传输距离提升50%以上，有助于高阶信号实现长距传输。甚至理论上，由于空芯光纤中的非线性折射率比普通石英光纤低3-4个数量级，并且同时具备低损耗的特性，空芯光纤技术也是实现高阶调制长距传输的重要备选方案。但是现网光纤、放大器类型的约束，限制了新技术的应用，如拉曼放大、新光纤、新站点，制约了容量、传输输距离的提升。主要表现或影响为，设计新网络或升级现有网络需最大化保护基础设施投资，控制成本，对新技术/方案引入有限制。这使得EDFA+G652光纤是现有最典型配置，提高波特率，采用低阶调制格式如QPSK才是400G长距干线的最佳实际解决方案，而G654E、DRA、空芯光纤等技术引入仍面临成本高、维护难、技术成熟度低的现实问题。', 'id': '1105451_10'}
{'label': 'text', 'content': '光网络演进挑战\n光电器件带宽受限需新材料新封装\n长期以来，对于相干光器件行业主要采用两种主流技术，既传统InP和新型硅光（SiP)技术平台。InP的优先在于带宽相对较大，本质上属于直接带隙材料能方便地集成有源层，可集成SOA和激光器等，但其晶圆尺寸小，产量低，需要气密性封装，成本高；而硅基光器件则与传统CMOS半导体工艺兼容，在尺寸、成本、功耗方面具有优势，但带宽在超过130Gbd后存在技术瓶颈。为改善400G传输距离，支撑单波速率继续提升，光器件需要借助新的材料、工艺和平台，如图2.2示出了不同材料体系的工作带宽潜力，面向未来，薄膜铌酸锂(TF-LN)因具有大的线性电光系数、高折射率差、低波导损耗、与硅平台能兼容等特征最有可能在近期实现规模商用；长期看，有机聚合物和等离子体材料的突破则有望支撑光电器件带宽持续演进。 \n除了带宽限制以外，相干光器件的功耗、封装尺寸、工作谱宽等也成为了新的挑战。一方面，随着相干光模块速率越来越高，相干光模块的封装形式越来越紧凑，电信市场应用需求CFP2相干光模块将是主流。相干光模块的体积缩小对相干光器件的集成程度和带宽提出了更高的要求，这就需要更先进的器件封装技术来减小高速信号的传输长度与衰减，提升器件带宽降低功耗，如图2.3所示。SiP体系在130Gbd阶段可以采用光电共封装技术，集成DSP、CDM、ICR等器件成为紧凑封装的MCM（多芯片组件，将多个裸芯片和其它元器件组装在同一块多层互连基板上的组件）器件；InP材料体系可以采用微光学气密封装集成ITLA、CDM、ICR等器件成为TROSA器件。以SiP技术路线为例，图2.3(a)为传统2D封装示意图，传统的2D封装是将光子集成芯片(PIC)和驱动电芯片水平放置，并通过打线的方式将驱动器和PIC封装在一起，之后再与DSP在模块PCB上进行互联。在这一封装形式下，高速模拟信号需要沿着DAC->PCB走线->CDM基板走线->打线->驱动器->打线->PIC这一路径从DSP传输到PIC，如图中红线所示，这一传输路径长、中断连接点多，对高速信号产生的损耗和反射已经很难满足光器件面对的高速传输场景。为了进一步减少信号传输路径长度，减少信号损耗，硅基相干光器件引入了MCM封装形式，如图2.3(b)所示。首先驱动器以倒装焊接的方式直接贴装在PIC上，同时DSP裸片与PIC也封装在同一个基板上，将光芯片和电芯片进行混合封装，如图2.3(c)，尽可能减小高速信号传输长度，保证了器件的带宽，同时这一封装形式也大大减少了器件体积，并且降低了器件封装成本。 \n另一方面，单波提速后，光信号波特率变大需要更大的波道间隔，为保证系统容量与速率同时倍增，光传输系统需要拓宽现有的光谱波段范围，当前相干光通信正向C+L波段扩展，相干光器件也需要支持C+L一体化设计，波段的扩展对器件波长相关性提出了更高的要求，此时发端TF-LN结合收端SiP较有应用前景。', 'id': '1105451_11'}
{'label': 'text', 'content': '光网络演进挑战\n带宽扩展引入新难题\n长距离传输需求、单波速率提升、最大化程度兼容现有基础设施三个前提下，光系统向更宽波段扩展是近期扩容升级必经之路。首先在必要性方面，长距高速传输，如长距干线400G需要130Gbd，波道间隔必须扩展到150GHz左右，80波系统理想光带宽约12THz；其实在系统架构兼容与演进可行性方面， 若采用如图2.4所示的多波段分立组网架构，不同波段光传输系统的光信号是独立并行放大和交叉的，每个波段分别使用一个光模块、光放大器和WSS，一方面可以较小的代价兼容当前的C波段系统，另一方面，由于可复用更多的波段，如S,U等，可支撑向未来更大容量、更多波段传输演进。 \n然而，带宽/波段扩展、架构升级也将引入一系列新的难题。1）分立式的系统架构，几乎需要成倍地增加光系统器件，进行增加系统复杂度和成本，并且由于波段间还需要保护间隔，导致频谱效率下降，实际占用波长范围更大，增加光器件制造难度，波段分合波引入的插损也会导致系统性能劣化；2）宽波段系统中光纤的受激拉曼散射效应（SRS）显著增强，光功率从短波向长波转移更明显，系统需要设计更加强大的自动光功率管理/调节算法，支持系统快速调测与可靠恢复；3）多波段复杂光系统伴随强烈的SRS效应导致传输性能评估与优化难，更宽波段不同波长性能一致性需要特殊设计来保障；4）新波段需要新的器件支撑，如L波段的相干光模块（调制器、接收机及ITLA)，L波段光放大器，L波段WSS，C/L合分波器，L波段OPM等。其中对于L波段光放大器，主要是EDFA，由于L6T波段范围相比于传统L波段更宽，EDFA中铒纤效率比C波段下降明显，对温度也更敏感，长波处增益下降陡峭，需要新的掺杂工艺和EDFA设计来克服。', 'id': '1105451_12'}
{'label': 'text', 'content': '长距400G光传输关键技术\n对于上述2.4中多波段引入的挑战，主要通过以下形式解决。1）中的分立式架构问题将通过持续的架构升级演进来克服，如采用C+L一体化架构，所有的波长均可用一个宽谱光模块，光大器，WSS来支持，系统外部接口仍然保持像传统C波段那样一体上下路，一体调度和交叉，将在3.2.2中传输系统架构中论述；对于2）中的SRS引入的问题，主要是采用填充波技术和自动光功率优化算法(APO)来管理C+L系统光功率，保证快速调测和可靠恢复，对应于3.2.1和3.2.3章节内容；对于3）中的系统性能评估难的问题将在3.2.4中体现并给出解决方案；4）中的L波段光器件能力及进展将在3.2.1中介绍。应对以上挑战，其实从根本上讲也就是要掌握长距400G光传输的关键技术，包括高速相干光模块（含DSP芯片算法及光器件），C+L波段系统架构（含关键光路器件，填充波与OXC方案，光功率管理及性能评估算法）。', 'id': '1105451_13'}
{'label': 'text', 'content': '长距400G光传输关键技术\n高速相干光模块\n相干光模块作为长距传输OTN系统的核心，其功能和特性决定了系统的传输能力。为保障传输性能，400G长距相干光模块需要具备以下能力：a)最高波特率高达130Gbd以上，多档甚至连续可调；b）多种线路速率和调制码型可调，支持200G QPSK, 400G 16QAM, 400G PS-16QAM, 400G QPSK, 800G PS-16QAM, 800G 16QAM等常用调制模式；c) 模块出光满足至少-9dBm以上，工作波段覆盖扩展C（C++）和扩展L（L++）波段；d) 400G QPSK码型的背靠背OSNR容限相比于当前90Gbd方案至少好1dB。这些性能和特性主要由先进的DSP算法、芯片及高速相干光器件来保障。', 'id': '1105451_14'}
{'label': 'text', 'content': '长距400G光传输关键技术\n高速相干光模块\n先进DSP芯片与算法\n相干DSP芯片由硬IP和软IP构成，软IP主要包括前向纠错FEC、相干调制解调、星座整形等算法，也集成Framer功能；硬IP包括DA/AD和高速SERDES。对于130Gbd系统，DA、AD的采样率高达170Gsa/s，需要采用多路复用或时间交织技术架构来提高带宽，并采用5nm CMOS工艺来降低功耗。 而SERDES则最多需要16路SERDES，其中至少有12路支持106/112G PAM4。 \n显然相干DSP芯片技术是相干光模块，乃至OTN系统的核心技术。为更有效实现应用聚焦，DSP演变为高性能和低功耗两类：低功耗DSP集成标准FEC和简化均衡算法，实现线路侧互联互通，更有效应对集成度、功耗敏感的网络层次应用，城域网为主要应用场景；高性能DSP从性能角度可覆盖全部应用，但由于体积大、功耗大、成本高，主要应用于超长干线和大容量短距场景。 \n如图3.1所示，随着CMOS技术的进步，每一代DSP芯片性能、功耗、体积都持续改进，不仅提高了光模块的最高工作速率、延长400G/800G信号的传输距离，而且随着晶体管尺寸的降低，单位面积和功耗限制下可实现的逻辑门的数量可急剧提升，从而新的DSP芯片可允许一些创新型、较复杂的DSP算法快速落地应用。两个典型的应用是混合调制技术(TDHM)与概率星座整形（PCS)近年来在16nm/7nm相干DSP中实现后可极大地提高光模块/光系统配置的灵活性，根据需要优化码型、波特率来满足不同场景下对背靠背容限、ROADM穿通及传输距离的需求。对于5nm及3nm DSP芯片，一些更先进的算法如高性能私有FEC（LDPC,TPC,MLC等），高维/编码调制，超Nyquist技术（FTN)，多电子载波（DSCM), 非线性补偿算法，神经网络算法等在高性能DSP芯片中有逐步落地应用的机会，以期进一步改善400G/800G的传输性能。 \n中兴通迅凭借卓越的供应链管理和技术创新能力，充分发挥先进DSP芯片能力，从28nm到16nm到7nm和5nm，一路伴随产业上下游伙伴共同成长，持续降低模块、板卡、设备功耗，让光传输更快速、更集约、更绿色。即将面世的130Gbd 400G长距光传输解决方案将采用业界领先的5nm工艺，相比于行业当前水平性能提升40%，功耗降低30%，不断深入践行提速降价、绿色双碳的行业发展理念。自研芯片的核心能力也不断加强，100G芯片已成功验证，400G芯片开发中，800G芯片及算法预研中。面向未来，超强的DSP算法、3nm工艺和自研芯片能力的应用将为高速光系统带来更大提升和差异化竞争点，值得期待。', 'id': '1105451_15'}
{'label': 'text', 'content': '长距400G光传输关键技术\n高速相干光模块\n高速相干光器件\n伴随相干光模块向小型化、低功耗发展的同时，相干光器件必须更加集成化。如图2所示，目前两个技术路线来持续助力器件小型化、集成化。一类是硅光技术为主体，可将调制、接收、驱动放大封装成ICRM器件，甚至还能把DSP die共封装进去成为MCM，这样只用外部注入光源，再加上放大后基本就可构成光收发模块。这种MCM集成封装不仅可以让器件小型化，而且缩短了高速信号走线，器件带宽提升10%以上，很大程度上弥补了SiP技术在带宽上限的不足，同时也能一定程度上降低封装成本。64Gbd 硅光MCM已经广泛商用，130Gbd MCM技术即将成熟，预计1-2年内商用。当波特率持续提升到192Gbd甚至256Gbd时，SiP调制带宽不足的问题预计将会暴露，发端需要借助TF-LN技术，目前已经有一些基于TF-LN的调制芯片或CDM器件样品，带宽高达110GHz，展现了具大的应用潜力。TF-LN的材料特性还表明，使用TF-LN不仅可部分兼容SiP工艺，还有望继续保持非气密性封装，这进一步增加了其商用机会。另一类走InP路线，采用III-V族化合物材料制作调制和接收器件，可发挥片上集成有源的优势，光源及SOA放大实现起来比SiP更容易，因此最终可实现ITLA+调制接收一体的TROSA，外部配合DSP芯片来完成相干收发的功能。理论上InP调制器的带宽要比SiP更高，模块出光功率也更大，传输能力更强，但实际上到400G时代，特别是130Gbd时，模块的性能不仅取决于器件带宽，而且还高度依赖于DSP芯片算法的补偿和配合，光器件与电芯片的协同设计和优化也十分重要，这加速了光芯片、器件、电芯片、DSP芯片产业链的垂直整合。 \n中兴通讯于2021年发布了基于MCM器件以及TROSA器件的400G可插拔相干光模块，实现了紧凑的CFP2封装形式。针对130G波特率的相干光模块，也将继续探索SiP MCM和InP两种主流技术路线，并持续优化性能、功耗和成本。新的TF-LN材料体系、加工平台及先进封装技术的应用可以提升器件带宽，减少器件体积和成本，在长距400G及未来B400G光传输系统中应用前景广阔，中兴通讯也同行业伙伴一道在积极布局推进研发中。', 'id': '1105451_16'}
{'label': 'table', 'content': '长距400G光传输关键技术\n扩展C+L波段光系统\n单波速率提升和容量倍增的需求迫使光系统带宽必须扩展，而且这已成为行业共识。自200G长距方案（QPSK码型）商用已来，传统的C波段带宽就得从4.8THz扩展到C++波段6THz。进入Real 400G时代（QPSK码型），C++波段已经不足以让单纤容量倍增（保持80波），需要进一步将频谱资源扩展到C+L波段。首先从整体上对C++与C+L波段系统进行对比，如表3.1所示，C+L系统与C波段无论在系统架构还是传输能力上都有明显差异，后续将详细介绍C+L系统的关键技术，如L波段波长分配，光器件成熟度，传输系统架构，功率管理算法，传输性能评估等。\n表3.1. 扩展C波段与C+L波段系统的对比分析\n| 比较项目 | C++波段 | C +L波段 |\n|:-|:-|:-|\n| 可用光频谱带宽 | 6THz | 12THz |\n| 传输性能（与CE系统相比） | 性能劣化0.5dB~1dB，优化后劣化有望降到0.5dB以下 | 经过优化后，性能劣化有望控制在1.5dB左右 |\n| 光层架构 | 1套 | 2套，设备形态上可以做成1套 |\n| 光收发单元 | 1套 | 2套，分别支持C和L波段；长期看有望演进到1套C+L一体化系统。 |\n| 运维复杂度 | 与CE相当 | 比CE略复杂 |\n', 'id': '1105451_17'}
{'label': 'text', 'content': '长距400G光传输关键技术\n扩展C+L波段光系统\nL波段波长分配\n工作波长范围统一是光系统必须优先解决的问题，否则会造成供应链分散、加重研发成本问题。在扩展波段的波长范围方面，C++波段波长范围目前业界已经达到统一：边缘波长范围1524.3nm-1572.27nm，如图3.3所示。L波段波长范围目前还在规划讨论中，目前国际上更倾向于CE+LE扩展，对于国内则不满足于LE4.8THz带宽，而且也C++也有重叠，希望在C++基础上扩展L波段，目前L波段5THz技术已经基本成熟，但面向长距400G应用仍面临带宽不足的问题，希望在此基础上进一步拓展到L波段6THz。对于L波段6THz主要有争议点在于C++和L++中间的保护间隔是2.9nm还是2.1nm，即图3.3中Type I还是Type II。目前考虑到业界的供应链情况，并尽量拟制C和L波段间串扰，业界更倾向于将扩展L波段的波长范围收敛到1576.16nm到1626.43nm，即Type I方案。', 'id': '1105451_18'}
{'label': 'table', 'content': '长距400G光传输关键技术\n扩展C+L波段光系统\nL波段光器件\n在波段扩展技术商用方面，业界正在积极推动C波段向C+L波段的演进升级。目前C++波段和L++波段相关产业链的发展情况如表3.2所示，L6T的光电器件、组件基本都已具备样品，目前主要难点在于L++波段的EDFA增益和噪声系数的优化，系统设备预计将在2023年上半年具备商用测试能力，可见扩展的C+L波段光系统有望在2023年下半年实现商用，这将为长距单波400G及城域单波800Gb/s及更高速率的传输系统应用奠定基础。\n表3.2. C+L波段波分复用系统关键组件产业链进展\n| | C6T | L6T | 技术难点 |\n|:-|:-|:-|:-|\n| ITLA | 已商用 | 样品已具备 | 重新设计增益区和选频光腔 |\n| 光调制接收器件 | 与C4T基本相同 | 与C6T基本相同 | 关注偏置点和响应度波长相关性 |\n| oDSP | 与C4T相同 | 与C6T几乎相同 | L波段色散略大，不同波段器件差异补偿 |\n| EDFA | 已商用 | L5T已有产品，L6T样品已具备，性能优化中 | 优化铒纤掺杂配比改善增益带宽；提升饱和功率和噪声系数；控制EDFA模块尺寸和功耗 |\n| DRA | 已商用 | 增加长波泵浦激光器 | 解决与OTDR的波长冲突问题 |\n| WSS | 已商用 | 样品已具备，技术难度不大,C+L一体10THz已产品化 | 更换衍射光栅和空间光路设计，高隔离度端口设计 |\n| AWG | 已商用 | 难度不大，技术准备就绪 | 无 |\n| OPM | 已商用 | 难度不大，技术准备就绪 | 无 |\n| OTDR/OSC | 与C4T相同 | 难度不大，技术准备就绪，待确定波长 | 无 |\n', 'id': '1105451_19'}
{'label': 'text', 'content': '长距400G光传输关键技术\n扩展C+L波段光系统\nC+L光传输系统架构\n为克服波段扩展带来的挑战，C+L光传输系统需要新的架构、功率管理算法及性能评估手段，而系统架构显然是关键，理论上讲，C+L系统可分为分立式和一体化两大类，如下图所示。其中分立式架构是指C和L波段的信号独立产生、放大和路由，而一体化架构则根据光放大器是否支持C+L宽谱放大分为两种，一种是OA独立，WSS和OTU一体化，另一种是包含OA的全一体化架构。很显然，全一体化架构比较理想，部署和运维起来都跟传统C波段无异，设备成本和集成度也有望极大改善，但其可行性与否及何时部署完全取决于关键器件的成熟度和商用时间。\n表3.3总结了三种系统架构在不同方面的对比情况。目前来看，分立式C+L系统架构最成熟，将最早具备商用能力，也能够支持现网C波段向C+L波段平滑演进（少量站点改造工作），但主要问题在于设备集成度低、成本高，基本是C和L设备各一套，运营维度更复杂。一体化-分立OA架构能否及何时商用的核心在于一体化WSS及一体化OTU的技术成熟度和产品化时间，理论上要实现WSS，OTU在1524-1626nm范围内工作都有相应的解决思路，但产品实现上可能面临挑战，如一体化WSS需要更高分辨率的LCoS芯片，更大色散能力的光栅；宽谱ITLA可能需要2个芯片加光开关切换来实现，这会增加功耗体积和成本；相干光器件在100nm宽范围内工作可以对材料和设计有要求，如采用硅光/TF-LN技术的同时还需要注意MMI的设计，降低器件插损波长相关性。对于一体化-宽谱OA架构则是在一体化-分立OA架构基础上进一步将C/L波段的放大器也合一设计，进一步提升集成度，但技术挑战也最大，虽然C+L一体化的EDFA放大技术近年来在学术上有所探索研究，但具体技术路线不明确，性能竞争力也不强。得益于其在传输性能、性能成本、波长路由调度、运维复杂度、设备集成度上的突出优势，长期来看，宽谱OA虽然技术挑战大，但全一体化的C+L系统架构仍然值得期待。\n表3.3总结了三种系统架构在不同方面的对比情况。目前来看，分立式C+L系统架构最成熟，将最早具备商用能力，也能够支持现网C波段向C+L波段平滑演进（少量站点改造工作），但主要问题在于设备集成度低、成本高，基本是C和L设备各一套，运营维度更复杂。一体化-分立OA架构能否及何时商用的核心在于一体化WSS及一体化OTU的技术成熟度和产品化时间，理论上要实现WSS，OTU在1524-1626nm范围内工作都有相应的解决思路，但产品实现上可能面临挑战，如一体化WSS需要更高分辨率的LCoS芯片，更大色散能力的光栅；宽谱ITLA可能需要2个芯片加光开关切换来实现，这会增加功耗体积和成本；相干光器件在100nm宽范围内工作可以对材料和设计有要求，如采用硅光/TF-LN技术的同时还需要注意MMI的设计，降低器件插损波长相关性。对于一体化-宽谱OA架构则是在一体化-分立OA架构基础上进一步将C/L波段的放大器也合一设计，进一步提升集成度，但技术挑战也最大，虽然C+L一体化的EDFA放大技术近年来在学术上有所探索研究，但具体技术路线不明确，性能竞争力也不强。得益于其在传输性能、性能成本、波长路由调度、运维复杂度、设备集成度上的突出优势，长期来看，宽谱OA虽然技术挑战大，但全一体化的C+L系统架构仍然值得期待。\n表3.3. 不同C+L系统架构情况对比\n| | 分立式C+L | 一体化-分立OA | 一体化-宽谱OA |\n|:-|:-|:-|:-|\n| 技术可行性 | 高 | 中 | 暂不明确 |\n| 成熟度 | 高 | 低 | 较低 |\n| 核心器件 | L++ OA | 一体化WSS及OTU | 一体化宽谱OA |\n| 传输性能 | 中 | 中 | 优 |\n| 系统成本 | >2x C波段系统 | 1.x C波段系统 | 与C波段相当 |\n| 波长路由 | C、L独立配置和调度 | C、L一体调度 | C、L一体调度 |\n| 运维 | 2个光层，复杂 | 接近一个光层，中等 | 与C波段一样，简单 |\n| 槽位占用/集成度 | C波段的2倍，低 | 1.x C波段，中 | 与C波段一样，高 |\n| 兼容性 | 兼容现有C波段 | 无法兼容 | 无法兼容 |\n', 'id': '1105451_20'}
{'label': 'text', 'content': '长距400G光传输关键技术\n扩展C+L波段光系统\nC+L光传输系统架构\n填充波\nC+L系统占用频谱带宽高达12THz以上，光纤中强烈的SRS效应导致的短波长功率向长波转移的问题不可忽略。另外，如果考虑C+L系统中动态的波长上下路，系统波道的频繁增减变化，业务信号受到光纤SRS效应影响产生强烈的功率/OSNR波动，引起性能劣化甚至业务中断。如图3.5所示，当C+L满配状态，传输5跨段G652光纤（每跨段光纤75km），稳定状态下系统中的OSNR如下图中蓝线所示（假设C波段单波平均功率~6.5dBm，L波段单波平均功率~4.8dBm，C波段与L波段EDFA的噪声系数都为6dB）。当C波段的最短波分别掉5波、10波、15波时，L波段最长波处的OSNR劣化量分别为1.7dB、3.5dB、5.2dB，显然这已经超过系统OSNR余量，将导致业务中断。 \n为克服上述影响，理论上可以自动检测加减波的数量和位置，准确计算出SRS导致的功率斜率变化，动态调整OA增益及斜率来均衡每个波道的功率和OSNR，避免业务受损，但这不仅对OA配置响应速度提出了严格的要求，而且还可能长时间让系统处于动态调节状态，威胁系统稳定性。作为替代方案，我们提出在C+L系统中采用“真假波替换”思想，即采用填充波道作为“假波”让系统始终保持满配状态，开局通过功率管理算法将满配系统功率调节到目标状态，业务波道新建或删除时，只用通过ROADM/OXC站在控制，采用“真波”和“假波”相互替换即可实现功率稳定和波道间性能均衡。避免动态增减波时复用段和通道级频繁的功率调节过程，使系统更快达到稳定。值得一提的是，C+L系统中的填充波可以有不同的实现方式，主要包括EDFA或其它类型放大器产生的宽谱ASE噪声配合WSS进行波道成型切割和通道波长选择，如图3.6所示，或者采用直流可调谐激光器/激光器阵列填充来作为dummy light。C+L系统中填充波的引入，需要关注其对业务波道性能的影响（串扰/非线性），并采取相应方案进行抑制。填充波除了基于功率均衡、调节，让系统保持稳定的作用外，还具备其它检测功能，如作为光探针，检测链路端到端的光层性能等。', 'id': '1105451_21'}
{'label': 'text', 'content': '长距400G光传输关键技术\n扩展C+L波段光系统\nC+L光传输系统架构\nOXC\n填充波的引入和控制都依赖于ROADM/OXC站点，其作用关键。传统的ROADM采用WSS、OA、Coupler/光开关/OTDR/OPM等组件，通过搭积木的方式根据需求灵活组建ROADM站点。随着维度增加，ROADM站内连纤数快速增加，对开通和运维造成了极大压力。为解决传统ROADM的问题，OXC设备应运而生，采用全光背板解决纷繁复杂的内部连纤；同时采用高集成板卡集成多种功能，一板卡一方向，插板即连纤，极大的提升了易用性。很显然，对于C+L系统，一般伴随大容量传输和交叉的需求，通常交叉维度较高，超过20维，此时OXC必不可少。受器件限制，目前的C+L OXC采用分立式架构，即C++ band和L++ band各采用一套OXC设备/子架，再结合OBM板卡来实现C/L波段合分波，所图3.7所示。未来随着一体化WSS和一体化/小型化OA器件的成熟，有望一个板卡上支持C和L两个波段，即可在一个OXC子架内完成C+Lband的光交叉功能，提升设备集成度。', 'id': '1105451_22'}
{'label': 'text', 'content': '长距400G光传输关键技术\n扩展C+L波段光系统\nC+L光功率管理\n前面已多次提到，宽谱C+L系统(12THz）中存在着强烈的SRS效应，光功率从短波长向长波长转移非常显著。并且SRS转移具有累积效应，经历多个跨段的传输后，接收端短波长处功率明显低于长波，OSNR平坦度显著劣化，难以满足系统应用，特别是C波段短波将成为系统性能瓶颈。以单OMS段（包含5个G652光纤跨段）的功率演进仿真为例进行说明，系统波道配置为80x150GHz，假设C波段与L波段均按单波5.5dBm功率入纤，光纤跨段损耗约22dB，EDFA工作在默增益和斜率下，经过一个OMS段后输出的功率分布如图3.8蓝色曲线所示。显然扩展到L++波段后，由于强烈的SRS功率转移效应，系统收端功率分布极不平坦。1）从波段间来看，C波段功率向L波段转移，使得L波段平均功率明显高于C波段；2）从波段内来看，C波段影响更大，功率不平坦度达18.5dB，L波段内功率不平坦度约3dB。 \n因此为了抑制SRS效应、均衡系统波道间性能，智能的功率管理算法十分必要。基于C+L系统中填充波配置和“真假波替换”原则，我们设计了自动功率优化（APO）算法来保障C+L系统开局调测快人一步、稳态性能优人一等的目标，APO算法主要包括： \n复用段级功率调节：1）补偿波段间的功率不平坦，迭代调节C波段与L波段OA增益；2）补偿波段内的功率不平坦，迭代调节C波段与L波段OA斜率；和通道级功率调节：3）补偿残余的功率不平坦，调节C波段与L波段WSS通道衰减。 \n经过APO算法调节后，OMS段输出功率如图3.8红色曲线所示，经过APO算法对系统功率进行有效管理后，功率平坦度显著提升，C波段功率不平坦度约1.5dB，L波段功率不平坦度约0.5dB。', 'id': '1105451_23'}
{'label': 'text', 'content': '长距400G光传输关键技术\n扩展C+L波段光系统\n400G C+L光传输性能评估与对比\n我们针对400G长距干线的两种待选码型（PS-16QAM和QPSK)在C+L系统不同波道间隔和频谱宽度下的传输性能进行评估分析和对比。传输性能评估原则如下：\n非线性代价原则: 在满足OSNR余量与非线性要求（非线性代价1dB），优化每个波段单波入纤功率最大化传输距离。\n余量原则：净余量=平均OSNR- 不平坦度 - 非线性代价 - 滤波代价, 保证净余量大于2dB，同时优化C波段与L波段间的功率，使得C波段与L波段传输后的净余量基本相当。\n可见单波400G C+L波段光传输系统中性能的评估主要包括：不同码型非线性代价评估，链路平均OSNR评估，OSNR不平坦度评估，滤波代价评估等。其中链路平均OSNR及OSNR不平坦度的评估、滤波代价的评估与C波段系统评估方法类似，只是在计算链路OSNR的时候需要注意L波段的光放大器的噪声系数、增益波动、C+L系统在不同实际入纤功率下由于SRS效应导致的附加损耗等。对于C+L波段非线性代价的评估，区别主要在于两点，一是考虑概率整形及多电子载波的调制码型，需要将EGN模型中的PAPR相关项系数及非线性干扰项的积分区间进行重构；二是考虑C+L宽谱系统中SRS的影响计算每个波道等效衰减系数和有效作用距离。我们已经在CIOP会议论文连续发表系列论文进行了理论分析和实验验证，400G及800G C+L系统中非线性代价估计精度优于0.3 dB。\n在上述评估原则和方法的指引下，我们针对不同谱宽、通道间隔下不同码型/波特率400G传输解决方案，利用仿真评估的实际可达的传输距离极限。在仿真中，基本参数设置如下，各码型背靠背OSNR容限为实际模块典型值，C++ 波段的EDFA 噪声系数（NF）与CE波段相当，L++波段EDFA NF比C++ NF波段劣化1.6 dB，比LE劣化0.9 dB；每个复用段（OMS）包含5-7个光纤跨段，光纤采用常规G652光纤，每跨段75km，损耗为22dB。评估过程中考虑了EDFA的增益不平坦特性及实际的自动功率均衡调节算法，即APO，对于C++波段只考虑增益调节，对于C+L系统同时考虑增益和增益斜率的调节。\n', 'id': '1105451_24'}
{'label': 'table', 'content': '长距400G光传输关键技术\n扩展C+L波段光系统\n400G C+L光传输性能评估与对比\n表3.4. 不同400G技术方案传输性能对比\n| 调制码型 | 波道数量 | 波道间隔(GHz) | 系统波段 | 最佳单波入纤 (C/L)(dBm) | 最大传输距离G652光纤(km) | 单纤容量(Tb/s) |\n|:-|:-|:-|:-|:-|:-|:-|\n| 64Gbd QPSK | 80 | 75 | C6T | 3.5 | 34*75=2550 | 16 |\n| 128Gbd QPSK | 40 | 150 | C6T | 5.8 | 37*75=2775 | 16 |\n| 128Gbd QPSK | 80 | 150 | C6T+L6T | 6.6/4.2 | 24*75=1800 | 32 |\n| 107Gbd PS-16QAM | 80 | 125 | C5T+L5T | 5.2/3.4 | 19*75=1425 | 32 |\n| 95Gbd PS-16QAM | 80 | 112.5 | C4.8T+L4.8T | 5.1/3.6 | 17*75=1275 | 32 |\n| 91.6Gbd PS-16QAM | 120 | 100 | C6T+L6T | 5.5/2.3 | 11*75=825 | 48 |\n| 注：64Gbd QPSK是单波200G，此处用作对比参考。 | 注：64Gbd QPSK是单波200G，此处用作对比参考。 | 注：64Gbd QPSK是单波200G，此处用作对比参考。 | 注：64Gbd QPSK是单波200G，此处用作对比参考。 | 注：64Gbd QPSK是单波200G，此处用作对比参考。 | 注：64Gbd QPSK是单波200G，此处用作对比参考。 | 注：64Gbd QPSK是单波200G，此处用作对比参考。 |\n', 'id': '1105451_25'}
{'label': 'text', 'content': '长距400G光传输关键技术\n扩展C+L波段光系统\n400G C+L光传输性能评估与对比\n不同400G技术方案传输性能仿真结果如表3.4所示，其中C++波段200G QPSK方案作为比较的基准。可以得出如下结论：\n对于C6T波段单波400G QPSK方案，相比于200G QPSK，传输能力有所提升（约10%），主要是得益于400G QPSK方案滤波代价的改善。\n相比于C6T波段传输，C6T+L6T系统的传输距离有所下降（约37%），主要是SRS影响C波段短波长OSNR。\n对于G652光纤及EDFA放大实际光纤链路，400G QPSK配合C6T+L6T是实现1500+km长距传输的最佳，也是唯一解决方案,相比于95Gbd PS-16QAM在112.5GHz间隔CE+LE系统传输距离提升约40%; 相比于107Gbd PS-16QAM在125GHz间隔下传输距离提升约26%。\n91.6Gbd PS-16QAM配合100GHz间隔C6T+L6T光系统，具有更高的频谱效率，可作为400G城域传输高性价比的解决方案，容量提升50%，但滤波代价可能会限制其ROADM穿通站点数量。\n', 'id': '1105451_26'}
{'label': 'text', 'content': '长距400G技术进展与应用建议\n400G相关标准与产业链进展\n400G及以上相干光模块及传输系统相关国际标准主要由国际电信联盟电信标准化部门（ITU-T）、IEEE 802.3、OIF 等标准组织，以及800G Pluggable MSA, IPEC, OpenROADM, Open ZR+等厂商发起的多源协议（MSA)组织共同制定，单波400G和800G光模块是目前业界研发应用和标准化研究热点。国内高速光传输模块及系统的标准化工作主要由中国通信标准化协会（CCSA）传送网与接入网工作委员会（TC6）完成。大部分行业标准以国外先进标准为基础，结合国内应用需求制定而成，整体发展速度与国际标准基本同步[10,11]。 \n在400G短距应用方面，光互联论坛（OIF）早在2020年3月就发布了400ZR实施协议，定义两大应用场景，一是单波长无光放大点到点功率预算受限系统，传输距离小于40km, 二是DWDM有光放大点到点OSNR受限系统，传输距离小于120km。该标准规范了PM-16QAM调制码型、开销为14.8%的CFEC以及帧结构等光层互通参数。IEEE 802.3cw 也在制定80 km DWDM 400 Gbit/s 标准，主要技术方案与OIF 400ZR相同，预计将增加75GHz波道间隔的参数规划。在此基础上，业内多个MSA也相继发布400G相关技术标准，如OpenROADM / OpenZR+发布的100～400 Gbit/s相干光模块规范，在400ZR帧结构的基础上增加100/200 Gbit/s QPSK、300 Gbit/s 8QAM等调制模式，并采用oFEC替代CFEC,光模块形态可支持CFP2-DCO和QSFP-DD/OSFP封装，传输距离覆盖450 km级城域400 Gbit/s应用。国际电信联盟第15研究组（ITU-T SG15）开展了200 Gbit/s/400 Gbit/s接口的物理层规范研究，将16QAM作为400 Gbit/s城域应用的标准码型，推动了开放前向纠错编码（oFEC）的标准化进程。面向更高单波速率，OIF目前正在讨论800G ZR规范，确定采用PM-16QAM码型、OFEC编码和150GHz波道间隔来满足80-120km单跨放大DWDM链路，甚至在更短的10km 800G互连场景中也确定将采用固定波长相干方案。IEEE 802.3 B400G研究小组也紧随其后热烈讨论基于相干技术的800G LR/ER标准。 \n国内方面CCSA相关标准制订工作包括：100 Gbit/s及以下速率的光传输和模块标准制订已完成，200 Gbit/s报批稿主要选择200 Gbit/s QPSK、8QAM、16QAM码型，400 Gbit/s城域标准实质上采用的是单波200 Gbit/s双载波方案。《N×400 Gbit/s长距离增强型光波分复用（WDM）系统技术要求研究》等面向更高速率应用的标准课题研究正在开展。波段扩展方面，扩展C波段(C++)CCSA行标目前已完成送审稿评审，附录中增加96Gbd PS-16QAM码型作为400G\xa01000km以内应用场景的可选方案。扩展C+L、单波长超400G及增强型N x 400G长距传输相关课题正在研究中，预计在一到两年内会启动行标立项，预计将增加128 Gbd 400G QPSK, 800G 16QAM等新速率和码型，以及扩展C+L宽波段的规范。整体来看，用于短距互联的400GZR,ZR+及城域等标准已经发布应用，400G长距及800G ZR / ZR+仍在进一步研究讨论中，同时注意到未来单波超400G时代相干下沉到10km及以下短距应用的趋势明显。 \n产业进展方面，目前主要设备厂家都具备96Gbd的400G PS-16QAM及800G PS-64QAM的传输能力，CE及C++波段可商用,L波段具备样品，128Gbd的相干DSP芯片预计在23年初开始陆续送样，配合C++,L++波段的ITLA,ICRM等光器件，23年下半年具备400G QPSK商用能力，并且可支持向下兼容96Gbd PS-16QAM等码型。作为光系统核心器件，光放大器和波长选择开关最为关键。目前支持C++波段的EDFA和WSS均已规模商用，带宽支持6THz。支持L波段6THz的EDFA也在开发中，但受限于铒纤在长波处的放大效率，L波段EDFA的噪声指数比C波段劣化0.5 dB以上，模块成本和尺寸也更大。支持L波段6THz,甚至C+L一体10THz的WSS已经具备商用能力，下一步将攻克C+L一体12THz宽带WSS的技术难点。 \n这些产业现状和标准进展标志着400G甚至超400G光传输时代加速到来，高速相干光模块和新型宽谱光器件将持续推动400G及以上速率长距传输能力提升和产业进步。', 'id': '1105451_27'}
{'label': 'text', 'content': '长距400G技术进展与应用建议\n单波400G长距传输相关试点和验证\n中兴通讯在400G长距传输领域始终与国内外运营商客户长期保持深度合作并取得了显著成果。早在2019年，凭借业界领先的调制技术、强大的FEC算法、先进的高性能DSP、独有的光域均衡技术，中兴通讯在中国移动现网测试中同时取得了传输距离最长、支持码型最多两项最优成绩。在干线首次完成多种码型，如16QAM,64QAM等单载波400G OTN现网传输，传输容量32T，现网传输超过600公里，刷新业界400G现网最远传输纪录。另外，支持100Gb/s QPSK、200Gb/s 16QAM、400Gb/s 16QAM、400Gb/s 64QAM等差异化的速率和码型，丰富多彩的技术方案满足多种应用场景需求。 \n2021年，中兴通讯携手辽宁移动独家完成业界首个C+L系统单波400G长距1000km的现网试点。如图4.1(a)所示，选取沈阳-大连线路1300km的传输路径，采用PS-16QAM码型实现无电中继大容量400G长距离现网传输，系统带宽达11THz，传输容量32T，潜在容量44T，凸显中兴通讯在大带宽、长距离方面的技术能力进步，该创新应用标志着400G长距传输方案加速完善中。同年，中兴通讯配合中国电信积极推进400G的现网试验，如图4.1(b)所示，在上海－广州间国内首条全G．654E陆地干线光缆上实现单波400G WDM传输现网实验，无电中继传输距离超过1900公里。现网测试结果表明，G.654E光缆的应用可以使得系统OSNR相较G.652D纤芯环境提升3.5dB，起到减少电中继数量和节能降耗等实际效果，对未来单波超400G高速传输系统的发展演进提供有力支撑。 \n2022年，基于96Gbd PS-16QAM结合C+L 11THz光系统，中兴通讯联合中国联通在实验室完成单波400G C+L系统光传输多个应用模型研究型测试，为400G规模商用部署奠定基础增强业界信心。随着数字经济的蓬勃发展及国家“东数西算”工程的启动，我国算力网络基础资源建设展开全面布局。光网络是数算时代的基石，因此光网络宽带化成为全产业一致共识，运营商骨干网升级到400G速率势在必行。2022年7月，中兴通讯联合中国移动在实验室模拟现网光纤长度、损耗和维护余量，采用准实时原型机进行400G QPSK传输验证。验证结果显示，搭载2.5D/3D光电芯片融合封装的128Gbd 400G QPSK光模块，凭借更优的背靠背OSNR容限和更高的入纤功率，在EDFA放大链路中传输距离达到2018km。进一步地，采用新型PID增益控制算法降低系统OSNR波动，并在部分跨段使用混合拉曼放大技术，如图4.2所示，最终可在G.652.D光纤中实现49跨段无电中继3038km的传输距离，可满足中国移动海量大带宽业务超长距传输需求，推动中国移动光网络发展进程，为全球数智光网络发展奠定基础。该项目是全球首个400G QPSK准现网传输验证，此次验证成功对整个光网络传输领域具有重要意义，对400G长距应用调制制式的选择提供了重要技术依据，有力证明了400G QPSK技术是下一代骨干网长距传输的最佳选择。在海外，中兴通讯助力土耳其移动Turkcell部署全球首个商用12THz超宽频谱波分系统，为其提供基于1T背板带宽的强大OTN平台，大幅提升网络传输容量。同时，中兴通讯在该城域WDM网络中预置C＋L波段耦合器，可支持土耳其移动快速、平滑地向多波段演进，相比标准80波C波段可提升两倍系统容量，充分满足其未来数据网络对带宽的需求，这次超大容量超宽频谱光传输系统解决方案即将拉开400G及C+L系统商用的帷幕。 \n进一步地，为验证单波400G QPSK OTU板卡的实际传输性能，中兴通讯联合中国移动在宁波-贵阳长距光纤网络上基于商用系统传输设备进行单波400G实时传输现网验证。网络环境包含45段光纤，平均设计损耗22 dB,单向传输距离2800km。凭借超强的5nm DSP芯片和独有的flexshaping 2.0算法族，在现网G652光纤C波段6THz频谱上实现单向 2800km 无误码传输，OSNR余量大于3dB；为进一步压缩系统余量，挑战传输距离极限，采用宽带DRA低噪声放大技术改善链路OSNR及自适应补偿算法增强抗非线性损伤能力，最终实现5600km超长距无误码传输。这是业界首次基于120+Gbd商用芯片的real 400G方案的实时传输测试，现网验证结果不仅证实了400G QPSK产业链的逐步成熟，而且展现了QPSK比PS-16QAM调制码型具有更好的传输性能，无疑400G QPSK所具备的极致传输能力更适合构筑长距离、大容量、高速率、低时延的算力网全光底座。 \n此外，中兴通讯在单波800G大容量、长距离实时传输，相干DSP算法设计、优化和FPGA验证等方面也取得不错进展。基于95Gbd PS-64QAM技术，与中国电信合作在实现室实现单波800G C+L波段G652光纤EDFA放大链路300km传输，创造单纤实时传输容量纪录88T，与中国移动合作成功演示800G WDM信号在G654E光纤DRA放大链路上传输2000km，打破长距传输纪录；基于140Gbd相干光器件在业内首次实现G652光纤EDFA放大链路1050km准实时传输。这些实验和试点研究表明130Gbd QPSK是长距400G干线传输最佳，也是唯一选择，可利旧G652光纤资源及EDFA站点，仅需配合L波段设备部署和功率管理算法即可；对于单波800G长距传输，130Gbd 16QAM需要借助于新型低损耗大有效面积G654E光纤、低噪声放大技术或缩小跨段损耗等手段来实现1500km以上干线传输。 \n作为100G/超100G创新技术领域的引领者，中兴通讯一直致力于前沿技术的研究，在超100G传输方面有着深厚的技术积累。多年来在国际核心期刊发表了大量超100G相关文献，多次刷新了超100G的传输纪录，为光网络技术发展持续做出贡献。未来，中兴通讯将继续向下扎根，深入光网络方面技术研究与实践探索，携手全球运营商共筑数智光网络，共促数智经济新发展。', 'id': '1105451_28'}
{'label': 'text', 'content': '长距400G技术进展与应用建议\n典型应用场景分析与建议\n基于前面的分析和判断，对于400G不同调制技术的传输能力和应用场景可总结如下。 \n对于400G短距DCI建议采用64Gbd 16QAM可插拔CFP2光模块，配合CE或C++波段实现64波或80波配置；对于400G城域主要采用两种技术，1）性能要求不高场景(600km以下），建议采用64Gbd 16QAM CFP2光模块，C++波段即可容纳75GHz间隔80波；2）性能要求较高场景（600~1000km)，建议采用90Gbd PS-16QAM MSA或CFP2光模块，C++波段可配置100GHz间隔60波，C+L 12THz频谱可配置120波；对于400G长距干线传输场景（1000~1500km），优先推荐采用130Gbd QPSK技术，配合C+L 12THz频谱可实现80波，32T容量。对于部分新建G654E链路，也可配置最多120波90Gbd PS-16QAM，提高频谱效率、降低单bit成本。另外，工程中大跨损光纤段落还可选配混合拉曼放大以改善传输性能。 \n对于800G短距DCI应用，目前可选仅有90Gbd PS-64QAM，但现有模块是MSA封装功耗尺寸较大，预计一两年后130G CFP2光模块将成熟商用，届时可用130Gbd 16QAM或降速至90Gbd PS-64QAM使用；对于800G城域传输，小于600km传输场景，130Gbd PS/16QAM是合适方案，配置C+L 12THz实现80波满配，600~1000km传输场景，130Gbd PS/16QAM还需要配合G654E新型光纤和低噪声拉曼放大器等技术；对于800G长距干线传输，建议铺设新型G654E光纤，并缩小站点间距控制跨段损耗至20dB以内，采用130Gbd技术升级800G，后期也可采用180Gbd甚至256Gbd相干光模块来摆脱对新型光纤和放大器的强烈依赖。 \n对于单波1.2T，目前130Gbd技术仅能支持100km级单跨传输，满足DCI短距互连应用，但1~2年内可实现可插拔模块，预计功耗方面仍有挑战。', 'id': '1105451_29'}
{'label': 'text', 'content': '总结与展望\n不断增长的网络流量驱动光网络容量提升、单波提速。长距光传输系统100G已规模商用，200G批量部署，400G时代加速到来。随着大带宽相干器件、2.5D/3D MCM封装、先进DSP芯片、L++ EDFA等技术的成熟，130Gbd的400G QPSK技术预计在一年内具备商用能力，在性能上也可有效弥补90Gbd PS-16QAM方案在长距传输能力上的不足及模块形态功耗方面的短板，形成完整的400G产品解决方案，覆盖全部应用场景。 \n单波400G预计是光网络演进中关键代际，具有长达10年以上的生命周期，持续优化传输性能，提升设备集成度，降低每bit成本是设备厂家的不懈动力，也是整体光通信行业的伟大使命。为实现这一目标，首先需要在400G C+L系统架构上持续迭代。依托产业链及自研创新能力，预计两到三年有望实现C+L一体化系统架构，这将在设备集成度、调度效率、维护便捷性方面有显著改善。其次是光系统底层器件和技术的创新，如高带宽低插损的TF-LN调制器，小尺寸大带宽高功率SOA，宽谱连续放大铒纤，低成本OPM等，进一步优化系统性能。第三在智能化程度方面，随着QoT模型精度和实用性的改善，底层设备关键参数的快速获取，AI和ML高级智能算法的引入，数字孪生光网络将成为现实，形成评估-分析-决策-执行的自动闭环控制和性能优化，丰富的性能监测、预测手段也将使得光网络的可靠性大幅提升，如基于分布式光纤传感的光缆同路由检测技术大大降低主备业务同时中断风险，光探针和GPA算法可有效解决组大网场景下备用路径性能未知，倒换恢复慢，业务恢复时间长的问题。基于光标签的光层OAM和光性能监测技术将助力全光网迈向更智能的光电联动组网新阶段。 \n随着 400G 技术日益成熟并在传输市场的广泛应用， 800G将是光网络演进的下一个重要里程碑。中兴通讯将持续夯实大容量长距离光传送产品和技术优势，大力推进高性能800G长距传输方案产品化，如180Gbd及以上高速相干光器件，DSP芯片及相干光模块。同时还将加大对新波段扩展的研究力度和深度，挖掘S，U波段的应用潜力，攻克核心器件技术和系统算法难题。对于具有高价值或颠覆性的前沿技术，中兴通讯也保持密切关注，积极推动低复杂度光纤非线性补偿算法、空芯光纤、超表面材料等技术商用落地。 \n面向未来，坚持核心技术创新，从系统到器件再到芯片和材料，逐层强化。中兴通讯将始终积极与业内合作伙伴、产业链、高校院所、友商同行一道积极探索、研究并应用各类新材料、新器件与新技术，开发高效、灵活、紧凑、可靠、智能的光传输系统设备，助力全球客户实现算网全光底座持续提速扩容、性能和成本优化，共同为行业数字化转型提供充足运力，让连接无处不在，算力无处不达。', 'id': '1105451_30'}
{'label': 'text', 'content': '参考文献\nCisco Visual Networking Index: Forecast and Trends, 2017–2022. \nhttp://www.199it.com/archives/1080883.html \nITU-R, 研究报告，IMT traffic estimates for the year 2020 to 2030, M.2370-0, 2015. \nIMT-2030（6G推进组）,6G总体愿景与潜在关键技术白皮书 \nhttps://blog.huawei.com/2020/05/06/approaching-shannons-limit-the-way-forward-for-optical-transport/) \nhttps://www.infinera.com/blog/the-three-vectors-of-coherent-evolution/tag/optical/ \nhttps://effectphotonics.com/points-of-view/integrating-line-card-performance-into-a-pluggable \nTM Forum, 自智网络白皮书3.0（中文版），2021 \n中国移动，自动驾驶网络白皮书，2021 \n汤瑞，吴冰冰，赵文玉，张海懿，单载波800Gbit/s光传输关键技术及标准化进展，光通信研究，48(3), pp. 17-23, 2022. \n吴冰冰，赵文玉，张海懿，高速光模块关键技术方案及标准化进展，电信科学，38(9), pp. 105-115, 2022.', 'id': '1105451_31'}
{'label': 'table', 'content': '缩略语\n| 缩略语 | 术语 | 描述 |\n|:-|:-|:-|\n| DSP | Digital signal processing | 数字信号处理器，是一种通信专用的信号处理芯片，有着强大的数字信号处理能力。 |\n| MCM | Multi-Chip Module | 多芯片组件，是一种将多个裸芯片和其它元器件组装在同一块多层互连基板上，然后进行封装，从而形成高密度和高可靠性的微电子组件。 |\n| ICT | Information and communications technology | 信息与通信技术，覆盖了所有通信设备或应用软件以及与之相关的各种服务和应用软件。 |\n| DWDM | Dense Wavelength Division Multiplexing | 密集型光波复用，是能组合一组光波长用一根光纤进行传送的技术。在一根指定的光纤中，多路复用单个光纤载波的光谱间距非常紧密，以便可以达到更高带宽的传输性能。 |\n| DCI | Data Center Interconnect | 数据中心间通信，指两个及以上数据中心的连接通信。 |\n| QPSK | Quadrature Phase Shift Keying | 正交相移键控，是一种四相位调制方式，具有良好的抗噪特性和频带利用率，可以应用在相干光通信系统中。 |\n| M-QAM | M-Quadrature Amplitude Modulation | M阶正交幅度调制，也是相干光通信系统中常用的调制方式 |\n| OTN | Optical transport network | 网络的一种类型，是指在光域内实现业务信号的传送、复用、路由选择、监控，并且保证其性能指标和生存性的传送网络 |\n| ROADM | Reconfigurable Optical Add-Drop Multiplexer | 可重构光分插复用器，是一种使用在密集波分复用（DWDM）系统中的器件或设备，其作用是通过远程的重新配置，可以动态上路或下路业务波长。 |\n| MZM | Mach-Zehnder Modulator | 马赫曾德调制器，一种利用双臂干涉进行光调制的器件。 |\n| CDM | Coherent Driver Modulator | 相干驱动调制器，一种集成了驱动器与调制器的相干光器件，用于对激光进行信号调制与加载。 |\n| ICR | Integrated Coherent Receiver | 集成相干接收机，一种集成了接收机与跨阻放大器的相干光器件，用于对相干光信号进行解调。 |\n| ITLA | Integrated\u2002Tunable\u2002Laser\u2002Assembly | 集成可调谐激光器组件，一种波长可调谐的激光器，用于相干光通信光源。 |\n| TROSA | Integrated coherent transmitter and receiver optical subassembly | 集成相干收发光学组件，一种集成了可调谐激光器、相干驱动调制器和相干接收机的相干光器件，用于相干光通信。 |\n| PIC | Photonic integrated circuit | 光子集成电路，以介质波导为中心集成光器件的光波导型集成回路。 |\n| PCB | Printed Circuit Board | 印制电路板，由绝缘底板、连接导线和装配焊接电子元件的焊盘组成，具有导电线路和绝缘底板的双重作用，可以代替复杂的布线，实现电路中各元件之间的电气连接。 |\n| TEC | Thermo Electric Cooler | 半导体制冷器，是利用半导体材料的珀尔帖效应制成的制冷器件，主要用于温度控制。 |\n| TIA | trans-impedance amplifier | 跨阻放大器，一种放大器，可以有效地抑制噪声信号的放大，广泛用于相干光通信。 |\n| PS | Probabilistic constellation shaping | 概率星座整形，通过改变各星座点的概率分布获得整形增益的技术 |\n| GPA | Global Power Algorithm | 全局功率算法，用于计算工作和备用路径单波功率及OSNR，是快速可靠恢复关键算法。 |\n| MSA | Multi-source agreement | 多源协议，厂家主导的标准化组织，也是一种固定封装的模块类型 |\n', 'id': '1105451_32'}
