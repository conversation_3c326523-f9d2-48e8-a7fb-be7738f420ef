{'label': 'document', 'content': '可编程交换机测试白皮书_20220930_CN', 'id': '1089209'}
{'label': 'text', 'content': '1 从传统交换机到可编程交换机\n随着云计算、大数据应用以及大规模机器学习的持续发展，互联网应用范围不断扩大，用 户对数据中心交换设备不断提出新的功能要求。传统交换机软硬件全部由单一设备厂商提供， 私有网络操作系统只能运行在私有硬件平台上，系统开放度很低，无法适应新功能快速开发部 署的灵活性需求。 \nSDN 技术的出现，虽然实现了网络控制平面与转发平面的分离，增强了控制平面的集中可 编程能力，但是转发平面依然面临编程能力不足的问题。传统交换机的转发平面使用了不可编 程的ASIC 芯片，转发逻辑完全固化到芯片中。芯片一经发布，用户根本无法修改芯片逻辑， 导致传统交换机设备的转发平面功能完全受制于交换机ASIC 提供的功能。用户增加任何新功 能，只要芯片不支持，就必须升级硬件。硬件更新周期往往按年计算，导致用户部署了SDN， 但由于转发平面编程能力不足，依然无法满足数据中心应用创新需求。因此，传统功能固化的 交换机设备面临严峻挑战，基于可编程芯片的新型交换机应运而生。 \n可编程交换机芯片由通用的逻辑单元和流水线组成，能够通过编程实现各种自定义的数据 包处理。利用可编程交换机芯片，用户能够在不升级设备的情况下，通过更新软件就能提供新 型网络服务，满足新业务快速部署需求。 \n2016 年，Barefoot 公司首次发布Tofino 可编程交换机芯片。该芯片转发能力6.5Tbps， 具备12 级pipeline 流水，每级pipeline 能够根据用户实际需求，灵活分配功能。这是第一款 完全具备可编程能力的交换机ASIC 芯片，该芯片也正式宣告可编程交换机时代真正来临。', 'id': '1089209_1'}
{'label': 'text', 'content': '2 可编程交换机芯片架构\n芯片可编程能力可以从以下四个维度进行评估： \n1. 报文字段解析可编程： 可编程芯片能够灵活定义关键字段在报文中的位置和字段长度， 芯片基于用户设定的字段特征灵活提取报文头的关键信息。基于该特性，可编程芯片 能够支持新协议的报文头处理。 2. 转发表可编程：在可编程芯片中，表项不是预先设定的。在不同场景下，用户可以自 定义key 值，也可以灵活调整转发表项的容量。 3. 转发行为可编程：同一张转发表，可编程芯片能够基于用户需求，定义多种转发行为， 比如用户可以自行修改报文内容，添加或删除隧道信息、设置报文丢弃、报文镜像等 转发行为。 4. 业务流水可编程：可编程芯片能够灵活编排业务流程，不同转发表之间的先后查找关 系可以灵活定义。 \n根据芯片架构的不同， 可编程芯片可分为传统增强型可编程架构、 基于RTC 的可编程架构、 基于MASA 的可编程架构等。', 'id': '1089209_2'}
{'label': 'text', 'content': '2 可编程交换机芯片架构\n2.1 传统增强型可编程架构\n传统增强型可编程架构，是在原有不可编程芯片的基础上，保持核心转发功能不变，仅向 用户开放部分可编程接口。通过这些接口，用户能够根据自己需要开发新功能。这类芯片突出 特点是：芯片功能绝大部分固化，少量内容可编程，用户受限比较大，仅能完成部分新功能开 发。例如可以调整部分表项的容量；可以定义部分报文的关键字段并进行解析；也可以实现增 强Telemetry 功能，包括按报文打上时间戳等。 \n图1 传统增强型可编程架构 传统增强型可编程的优势在于最大程度保持传统芯片功能，实现平滑演进，适用于可编程 能力要求不高的场景。 \n传统增强型可编程的劣势在于可编程能力有限， 无法适配DC 等应用需求快速变化的场景。', 'id': '1089209_3'}
{'label': 'text', 'content': '2 可编程交换机芯片架构\n2.2 RTC 架构可编程\nRTC 架构可编程也可以称为NP 架构的可编程。NP 是专门为处理数据包而设计的可编程 处理器，能够直接完成网络数据处理的一般性任务。硬件体系结构大多采用高速的接口技术和 总线规范，具有较高的I/O 能力，包处理能力得到了很大提升。网络报文处理器的体系结构和 指令集做了专门优化，用户编写微处理器代码，编译后输出可执行文件在NP 上运行，实现网 络流量快速处理。 \n网络处理器内部由多个微处理器和多个硬件协处理器组成，采用流水线方式处理报文的接 收、关键字段的解析、表项查找、报文内容修改等步骤，在减少指令总数、缩短处理时间的同 时，能够简化指令和引擎设计，提升执行效率。此外，多个微处理器能够同时并行处理报文， 充分发挥所有处理器多线程的能力，隐藏I/O 访问时延，提升NP 的报文处理能力。对于一些 复杂的标准化功能（比如路由查找、层次化流量调度、限速功能等）则采用硬件协处理器提升 性能。 \nRTC 架构可编程的优势是具有最强的可编程能力，可以完全基于用户的需求来定义处理逻 辑。 \nRTC 架构可编程的劣势是流处理性能不足，尤其是在复杂转发逻辑下需要更多的ME 来串 行处理，会大大降低整体性能。', 'id': '1089209_4'}
{'label': 'text', 'content': '2 可编程交换机芯片架构\n2.3 MASA 架构可编程\n基于Match-Actions 可编程芯片的系统架构如下图所示。 \n图3 基于Match-Actions 可编程芯片的系统架构 在Match-Actions 可编程芯片中报文解析模块、流表的key 值和容量、每级PPC 的处理 逻辑，用户都是可以根据需要进行灵活修改。整个系统核心由可编程报文解析模块、N 级业务 \n流水级处理模块、报文组装模块组成。一个报文到达芯片入口后，报文解析模块根据用户预下 发的解析编程代码逻辑，对收到的业务报文进行解析和匹配，解析报文完成后，放入medata， 后续每个PPC 依次对解析后的medata 数据进行查表匹配， 并执行相应的动作。 PPC 查表动作 由用户下发的编程代码决定，查询的表项由控制平面下发到芯片，匹配的动作包括ALUS 的标 准布尔和算术运算、报文修改、计数、统计功能。每个PPC 结构是一样的，到最后一级PPC 处理完成后，由报文组装模块根据报文处理结果进行报文的组装，组装完成后，向对应的端口 发出报文。 \n每级PPC 的处理流程如下：从寄存器读入数据，进行match-action 的KEY 选择，如果 存在不查表的指令，直接送到指令执行模块进行执行，如果需要进行表项查询，则经过MUX 统一进行表项查找，查找后的结果经过result_set 处理，然后送交给指令执行模块，指令执行 模块将结果送到结果寄存器，这样完成一个PPC 模块的处理流程。每个PPC 的处理逻辑包括 match-action 的定义，由用户编程完成，下发给芯片的PPC 逻辑处理模块。 \nMASA 架构可编程的优势在于很好平衡了可编程能力和转发性能。 \nMASA 架构可编程的劣势在于和传统增强型可编程芯片相比，有额外的成本提升与功耗提 升。', 'id': '1089209_5'}
{'label': 'text', 'content': '3 可编程交换机的应用场景\n3.1 DC 内增强Telemetry 和OAM\n3.1.1 应用场景\n伴随着企业数字化转型的加速，数据中心规模与日俱增，通过引入计算、存储、网络资源 池化技术以及云平台的统一编排，云数据中心可实现资源的自服务化，给用户带来便利性；但 云数据中心网络的规模化和自动化也给网络管理和维护带来挑战，其中面临的主要问题是基于 业务原始报文的路径还原和丢包点检测困难。 \n由于可靠性的原因，数据中心内的网络存在大量的ECMP 路径，其选路主要基于报文携带 的五元组信息Hash 值来确定某个特定业务的ECMP 转发路径。传统的OAM 检测机制，例如 Y.1731 等，都是构造特定的探测报文来做链路质量检测。由于ECMP 的存在，这种探测报文 无法精确覆盖特定业务的真实转发路径，从而也无法模拟检测出特定业务转发链路的质量。 \n针对这个需求， 产生了随路报文检测技术 （InbandOAM） ， 其主要支持的应用场景有三个： \n1. 指定业务的流量路径可视。 要求对于某条特定的业务流， 在管控平台可以精确绘制出报 文的转发路径，包括经过的设备和入、出物理端口。 \n2. 指定业务的丢包检测。对于某个特定业务的报文进行监控、计数。可以在各个转发节点 检测该业务是否产生了丢包。 \n3. 指定业务的时延检测。对于某条特定的业务报文，可以探测其端到端的链路时延及设备 内转发时延。', 'id': '1089209_6'}
{'label': 'text', 'content': '3 可编程交换机的应用场景\n3.1 DC 内增强Telemetry 和OAM\n3.1.2 技术功能\n随流检测技术是一种将检测信息直接封装在要检测的业务报文头部，转发节点及尾节点基 于检测信息进行统计、 计算， 从而得到相关检测信息的技术。 目前有染色、 In-situ OAM、 INT、 IFA 等多个方案。其中染色方案由于资源消耗少，成为相对理想的方案。但染色方案因为推出 晚，协议还在变化期，从实现方案看，基于可编程芯片的技术方案更为可行。 \n主流的染色方案基于RFC 8321 标准， 主要特点是只在入节点对检测报文添加染色报文头， 中间转发节点不对报文做修改处理， 出节点将添加的染色头解封装。 支持的功能包括丢包检测、 时延检测、路径探测等，如下图所示。 \n图4 染色技术报文转发示意图 丢包统计原理： \n图5 丢包统计\n- 发送端：按照一定周期对被检测的业务流标记字段交替染色，同时统计本周期发送的业务 流性能，并通过上报给管控系统。 \n- 接收端：按照发送端相同的周期，统计本周期被检测业务流特征字段为染色的性能，并上 报给管控平面。接收端统计的时间应在1~2 个周期之间，保证乱序报文可被正确统计。 \n- 管控平面根据发送端和接收端上报的被检测业务流的信息，计算周期i 业务流的丢包数： PacketLoss[i] = Tx[i]–Rx[i]。 \n时延检测原理： \n图6 时延检测\n- 发送端：每个检测周期对本周期内被检测业务流的其中一个报文进行时延染色，记录该报 文的入口时戳t1/t3，并上报给管控平面。 \n- 接收端： 按照发送端相同的周期， 记录本周期被检测业务流时延染色报文的出口时戳t2/t4， 并上报给管控平面。 \n- 管控平面根据发送端和接收端上报的信息，计算业务流周期i 的两个方向的单向时延： \nDelay[i](in) = t2-t1，Delay[i](out) = t4-t3，单向时延要求发送和接收端部署1588v2 时 间同步。 \n- 对于被检测业务流双向同路径的场景下，管控平面根据发送端和接收端上报的信息，计算 业务流周期i 的双向时延：Delay[i](double) = (t2-t1)+(t4-t3)。 \n路径探测原理： 图7 路径探测\n- 在入节点采用五元组为IP 业务流分配Flow ID，对特定报文进行染色。 \n- 每个转发设备匹配flow ID，收集各个报文对应的转发信息，基于Telemetry 上送管控系 统。上送信息包括报文特征、设备信息、接收和发送物理的端口等。与其它技术不同的是， 需要收集的信息（option）是在各个设备上通过配置指定，并不携带在报文内部。 \n- 管控平台静态订阅相关信息并处理，给出统计结果。 \nTelemetry 上报统计： 设备采用Telemetry 方式将统计信息上报管控平台，上报信息包括： \n- Flow ID \n- 时间戳表示方式（NTP 或1588） \n- 入方向接口 \n- 入方向包数统计 \n- 入方向字节数统计 \n- 入方向时间戳 \n- 出方向接口 \n- 出方向包数统计 \n- 出方向字节数统计 \n- 出方向时间戳 \nTelemetry 可以采用gRPC 或者UDP 封装。 gRPC 方案基于TCP 连接， 对资源占用较多。 UDP 可以看做是一种轻量级的方案，适用于大容量的信息上报。', 'id': '1089209_7'}
{'label': 'text', 'content': '3 可编程交换机的应用场景\n3.1 DC 内增强Telemetry 和OAM\n3.1.3 应用价值\n\uf0d8 染色检测技术可以精确检测特定业务流的业务质量， 实现对重点业务的实时精确监控。 \uf0d8 在发生故障时，染色技术可以快速定位丢包设备和丢包位置（入侧，出侧），实现故 障的定界。 \uf0d8 新增染色信息长度小，对带宽占用影响小。', 'id': '1089209_8'}
{'label': 'text', 'content': '3 可编程交换机的应用场景\n3.2 DC 网关间建立SRv6 隧道\n3.2.1 应用场景\n移动互联网及云业务的快速发展，要求承载网络具备智能管控、可信的差分化服务保障等 多种要求。传统MPLS、SFC 技术往往仅能支撑路径或者业务独立部署，SRv6 独特的 Locator+Function SID 设计能够同时支持路径和业务的规划能力， 在网络和业务编排器的支撑 下，为云网融合、端到端业务定义提供了更好的技术选择。 \nSRv6 技术能够简化网络协议。 在隧道/Underlay 层面，IPv6 报文的扩展替代了隧道功能， 从而取消了原有的LDP 和RSVP-TE 等MPLS 隧道技术。 SRv6 只需要通过IGP 和BGP 扩展 就可以完成Underlay 功能和隧道功能，简化了信令协议。在业务/Overlay 层面，通过EVPN 整合了原来网络中L2VPN VPWS/L2VPN VPLS/L3VPN 技术。业务层面可以通过SRv6 SID 来标识各种各样的业务，也降低了技术复杂度。SRv6 技术通过其简洁的技术实现已经成为下 一代广域网建设的主要技术标准，在大型广域网、数据中心DCI 互联、5G 分片及NFV 网络业 务链调度等应用场景，会有更广泛的应用。 \nSRv6 网络中用Segment 表示网络路径和网络服务，通过在IPv6 报文中携带Segment List，可以为报文提供定制的转发路径和服务。SRv6 Segment 代表网络指令，它的标识简称 为SRv6 SID，是一个128 位比特的IPv6 地址，总体分为三部分内容：Locator、Function 和 Argument，如下图所示。 \n图8 SRv6 SID 格式 \nLocator：标识SID 所在的节点，IGP 向其他节点通告locator，使其他节点形成locator 网段路由，实现SID 的可达性。Locator 的长度可根据网络规模进行设计。 \nFunction：节点上的网络功能标识。 \nArgument：网络功能需要的参数。 \n此外，SRv6 扩展了RFC 2460 中的Routing Header 定义，新增一种Segment Routing Header(SRH)，以包含Segment List。如下图，IPv6 Header 中的Next Header 取值为43， 表示下层头为Routing Extension Header； Routing Extension Header 中的Routing Type 为 4，表示该Routing Extension Header 是一个Segment Routing Header (SRH) 。 \n图9 SRH 格式 SRv6 网络节点分为三种类型： SRv6 Source Node: 封装SRH，按照SID List 中的第一个SRv6 SID 指示转发报文。 \nTransit Node： SID List 中没有指定的节点， 进行Native IPv6 转发， 不感知SRH 的存在， 无需支持SRv6。 \nSRv6 Segment Endpoint Node：SID list 中指定的节点，按照SID 表示的指令进行报文 处理。Endpoint 节点处理完当前SID 后，继续取SID List 中的下一个SID 作为IPv6 报文目 的地址，将报文发往下一个SID 所在的节点，执行下一个SID 所表示的网络功能。 \n下图示例SID List <R3, R4>指导报文的转发过程，以及三种角色的节点执行的转发行为。 \n图10 SRv6 报文转发原理', 'id': '1089209_9'}
{'label': 'text', 'content': '3 可编程交换机的应用场景\n3.2 DC 网关间建立SRv6 隧道\n3.2.2 技术功能\n数据中心内采用Spine-Leaf 网络架构，服务器之间通过VXLAN 进行通讯，实现多租户 之间通讯。数据中心之间建立SRv6 隧道，用户能够基于控制器部署不同策略，使不同租户的 报文走不同隧道，实现租户间信息的差异化传输。 \n其方案及业务流程如下图： \n图11 基于SRv6 隧道的业务流程 \nDC1 服务器访问DC2 服务器： \n1）DC1 服务器发送普通IP 报文。 \n2）该报文到达DC1 leaf 交换机节点，DC1 leaf 交换机封装VXLAN 隧道。 \n3）该报文在DC1 内，根据目的IP 正常转发，到达DC1 GW 节点。 \n4）可编程交换机作为DC1 GW，具备三个功能： \na) 终结VXLAN 报文。 b) 结合VXLAN 隧道标识和DC1 控制器下发的策略，将VXLAN 隧道映射到不同 SRv6 隧道中。 c) 封装SRv6 隧道。 \n5）报文在骨干网上，按照DC1 确定的SRv6 隧道进行逐条传输。 \n6）报文到达DC2 GW 节点。 \n7）可编程交换机作为DC2 GW，具备三个功能： a) 终结SRv6 报文。 b) 结合SRv6 隧道标识和DC2 控制器下发的策略， 将SRv6 隧道映射到不同VXLAN 隧道中。 c) 封装VXLAN 隧道。 \n8）该报文在DC2 内正常转发，到达DC2 leaf 节点。 \n9）DC2 leaf 交换机终结VXLAN 隧道，根据目的IP 转发报文。 \n10）报文转发到正确的服务器上。 \n在上述方案中， DC GW 作为方案核心， 需要完成VXLAN 隧道和SRv6 隧道映射及转发功 \n能，对可编程能力提出很高要求： \n1. 报文字段解析模块必须具备高可编程性。SRv6 标准中，SRH 长度不定，能够包含任 意多个Segment list，在实际应用中，segment list 甚至可能超过十层。同时，SRH 必须根据不同用户要求，支持多种SRH 压缩方案。这些都对DC GW 设备的报文字段 解析及修改模块提出很高的可编程要求。 \n2. 转发表容量支持灵活配置。SRv6 作为未来承载网的基础性技术，包含各种应用场景， 对L2 表项、L3 表项、VPN 表项容量有不同的需求，用户需要根据不同场景，灵活调 整不同转发表的容量。 \n3. 转发行为必须能够灵活修改。SRv6 协议多个字段支持用户自定义。SRv6 SID 的 128bit 地址中，function 和Arguement 字段可以灵活定义，同时结合可选TLV，能 够在转发平面中封装特殊转发规则， 最终实现用户定制化转发需求。 这就要求DC GW 交换机能够灵活修改转发行为。', 'id': '1089209_10'}
{'label': 'text', 'content': '3 可编程交换机的应用场景\n3.2 DC 网关间建立SRv6 隧道\n3.2.3 应用价值\n数据中心出口网关采用可编程交换机，能够满足本场景用户需求。可编程交换机提供： \n\uf0d8 灵活定义协议字段的能力。可编程交换机能够灵活定义关键字段在报文中的位置和字 段长度，能够基于用户设定的字段特征灵活提取报文头的关键信息。 \uf0d8 灵活的定义表容量的能力。基于可编程交换机，用户不仅能够自定义表项，还能灵活 调整转发表项的容量，满足多种场景下DC GW 转发要求。 \uf0d8 灵活的定义表项转发行为。用户使用可编程交换机能够基于不同租户、不同应用定义 多种转发行为，实现差异化转发。', 'id': '1089209_11'}
{'label': 'text', 'content': '3 可编程交换机的应用场景\n3.3 5GC UPF、MEC 增强\n3.3.1 应用场景\n伴随着5GC 的持续推进，多种业务的部署，边缘云MEC、5GC UPF 逐渐延伸到网络各个 层次位置，对于边缘云网关提出了更多的需求。 \n\uf0d8 边缘云MEC 资源池通常会采用Spine-Leaf 架构，主要由边缘云GW、EOR、TOR、 防火墙、服务器等设备组成。当边缘云MEC 下沉的位置较低的时候，可能会存在部 署空间受限、能耗受限等问题。因此，需要边缘云网关能够承担更多功能，如：接入/ 汇聚/网关合一、提供虚拟负载均衡等，减少实体设备的数量，给服务器的部署预留更 多空间，降低建网成本。 \n\uf0d8 根据业务场景的需要，边缘云MEC 有可能部署在地市级这样较高位置，也可能部署 在园区等较低的位置。当边缘云MEC 部署下沉得越低，其5GC 的U 面虚拟化网元 UPF 所连接的基站数量会越少， 基于基站IP 的负载分担的均衡性也会越困难， 因此需 要网关GW 提供更灵活的分担策略， 来满足UPF 网元或者vDPI 系统与网关GW 之间 多条物理链路流量均衡的需求。', 'id': '1089209_12'}
{'label': 'text', 'content': '3 可编程交换机的应用场景\n3.3 5GC UPF、MEC 增强\n3.3.2 技术功能\n5GC 的UPF 与基站（或者RAN 侧）的通信接口为N3 接口，采用3GPP 组织定义的移动 网络协议GTP 进行封装传输。该协议位于TCP/IP 或UDP/IP 协议之上，是对UE 地址进行一 次隧道封装。即：采用GTP 传输的数据报文，外层通信地址为基站地址和UPF 地址，中间添 加一层GTP 封装消息头，最后为用户地址与应用地址组成的内层地址，以及数据体内容。 \n通常作为有线承载领域的设备不会解析移动领域的GTP 协议， 仅会根据报文封装后的外层 地址进行转发与处理。当有多条链路转发时，会基于外层地址形成负载分担。因此，对于访问 同一个UPF 地址的流量，若外层的基站地址数量太少，会影响负载分担效果。 \n可编程交换机通过底层芯片可编程逻辑，可将报文进一步解析。针对本应用场景，可编程 交换机解析到GTP 封装头信息，根据该封装的信息进行优化与处理。 \n通过分析发现，对于GTP 消息头，其格式中有一个长度为32bit 的TEID 字段。该TEID 字段（Tunnel Endpoint Identifier，隧道端点标识）用于区分GTP 隧道中的不同连接，通常 TEID 与用户会话绑定，每个用户的会话（每个会话分配一个IP 地址）会分配一个随机生成的 TEID。因此，TEID 的数量变化会远多于基站地址的数量变化。 \n进一步分析，对于GTP 封装后的报文，由于其内层地址为用户地址（UE 地址）和应用地 址（APP 地址），相比之下，内层地址的数量变化也会远多于基站地址的数量变化。 \n针对以上特征，可编程交换机通过底层芯片可编程逻辑，将GTP 封装的报文进一步解析， 解析GTP 的TEID 字段或者内层地址字段，将该字段进行优化与处理，作为Hash 的Key 值。 对于由多个下一跳组成的ECMP 场景，或者由多个聚合口成员组成的聚合链路场景，由于Key 值的散列高，负载分担会更均匀。 \n1）核心网网元UPF 与基站通信，其数据报文为GTP 类型封装报文。 \n2）可编程交换机作为GW，与UPF 网元之间有多个下一跳链路。 \n3）GTP 报文到达GW 后，GW 根据GTP 封装头或者内层地址信息进行Hash。 \n4）数据报文Hash 到多条链路，实现负载分担。 \n图14 镜像流量示意图 \n1）核心网网元UPF 与基站通信，数据报文为GTP 类型封装报文。 2）可编程交换机作为vDPI 网元的GW，为了扩大带宽，配置多条物理连接。 3）在GW 上对原始流量作流镜像至vDPI 服务器。 4）镜像时根据原始报文信息进行Hash，可到多条链路，实现负载分担。', 'id': '1089209_13'}
{'label': 'text', 'content': '3 可编程交换机的应用场景\n3.3 5GC UPF、MEC 增强\n3.3.3 应用价值\n采用可编程交换机作为边缘云网关，可提供： \n\uf0d8 灵活定义表容量，表项容量规格相比传统交换机有着大幅度的提升， 使得边缘云MEC 资源池能够部署更多的租户业务，进一步降低整体组网成本。 \uf0d8 灵活定义协议字段，能够针对各种场景下的不同类型业务报文，通过提供可编程的报 文解析能力，为边缘云MEC 内业务网元的演进提供更有力的保障。例如，上面提及 的针对无线场景特定协议GTP 封装报文解析的优化，使得负载分担能够更加均匀。 \uf0d8 提供硬件BFD 能力，Session 数量多，检测周期短，在大业务背景下流量能够快速切 换，为部署在边缘云MEC 内的5GC 业务提供更优质的可靠性。', 'id': '1089209_14'}
{'label': 'text', 'content': '4 可编程交换机的测试案例\n4.1性能相关\n测试目的：测试交换机设备的整机转发性能。 \n测试设计： 被测交换机在可编程业务部署之后的业务RFC2544 指标及24 小时稳定性拷机 测试。 \n测试组网： 测试步骤： 1、被测交换机部署GFP 业务感知功能和DCI-GW 功能； 2、发送模拟基站GFP 业务流量，VXLAN 封装的DCI 流量，流量覆盖RFC2544 典型字 节，有预期结果1； 3、选用典型线速转发字节进行24 小时稳定性拷机测试，有预期结果2。 \n预期结果： 1、在部署可编程混合业务匹配策略之后， 整机业务流量的RFC2544 指标与业界传统交换机 无差别； \n2、24 小时稳定性拷机测试之后，测试仪表流量显示不丢包。', 'id': '1089209_15'}
{'label': 'text', 'content': '4 可编程交换机的测试案例\n4.2DC 内增强Telemetry 和OAM\n测试目的：测试交换机设备对Inband OAM 的支持能力。 \n测试设计：搭建简易DC 环境，Leaf1，Spine 和Leaf2 节点都是被测交换机设备，对于跨 Leaf 的东西向流量，在Spine 节点模拟丢包和延迟，测试验证被测交换机支持业务流量真实路 径绘制，丢包定位和延迟测量。 \n测试组网： \n测试步骤： 1、搭建上述组网拓扑； 2、Leaf1，Spine 和Leaf2 组建典型DC 内部Spine-Leaf 网络，使能IGP（如ISIS） 打通underlay 路由，有预期结果1； 3、DC 内部署启用BGP 发布EVPN 路由，并设置Leaf1 和Leaf2 为分布式网关，有预 期结果2； 4、测试仪表T_1 和T_2 模拟DC 内同租户跨子网流量，有预期结果3； 5、通过控制器在Leaf1 设备下发Flow ID， 五元组等流特征匹配T_1 到T_2 的流量， 并 使能路径可视功能，有预期结果4； 6、控制器向Leaf2 下发随流检测实例，指定测量源IP 地址为T_2、目的IP 地址为T_1 的流量，检测周期为x 秒，在Spine 上模拟引入丢包和延时，有预期结果5。 \n预期结果： 1、Leaf1，Spine 和Leaf2 设备的Loopback 地址互通可达； 2、Leaf1 和Leaf2 建立EVPN 隧道； 3、双向流量打通且不丢包； 4、控制上展示出真实的路径经过的节点和链路信息； \n5、控制上能统计出丢包数量和流量延时信息。', 'id': '1089209_16'}
{'label': 'text', 'content': '4 可编程交换机的测试案例\n4.3DC 网关间建立SRv6 隧道\n4.3.1 支持8 层SRv6 标签\n测试目的：测试交换机设备对8 层SRv6 标签的支持。 \n测试设计：搭建抽象PE1-P（7 个）-PE2 组网，被测交换机在该场景下处在PE1 位置， 对PE1 和PE2 的端到端流量，在入口PE1 封装8 层SRv6 标签。 \n测试组网： 测试步骤： 1、搭建上述组网拓扑； \n2、PE1 和测试仪表T_2 端口模拟的7 台P 和一台PE2 设备进行组网； \n3、所有设备使能IGP 进行SRv6； \n4、发送端到端流量，有预期结果1； \n5、在测试仪表T_2 进行抓包分析，有预期结果2。 \n预期结果： 1、端到端流量正常接收，无丢包； 2、收到的报文能看到8 层SRv6 标签。', 'id': '1089209_17'}
{'label': 'text', 'content': '4 可编程交换机的测试案例\n4.3DC 网关间建立SRv6 隧道\n4.3.2 支持G-SRv6 标签压缩标准\n测试目的：测试交换机设备对G-SRv6 标签压缩符合标准能力的支持。 \n测试设计：搭建抽象PE1-P-PE2 拓扑，使能端到端L3VPN 业务，业务承载是SRv6 网 络上，逐跳启用抓包确认G-SRv6 封装符合相关标准。 \n测试组网： 测试步骤： 1、搭建上述组网拓扑； \n2、SW1 和SW3 之间配置L3VPN 实例，SW1 和SW3 为PE，SW2 为P 设备； \n3、SW1，SW2 和SW3 配置IGP，如ISISv6，使能SRv6，配置END 和END.X 类型的 SID，部分为普通型，部分为GSID(压缩SID)型； \n4、在SW1 和SW3 上使能多个SRv6 Policy， 整条路径指定path 为GSID， 且block 相 同； \n5、测试仪表T_1 和T_2 发送多个端到端L3VPN 流量，有预期结果1； \n6、SW1 和SW3 上根据BGP 的不同color 把不同vrf 的流量引导入相应policy 中，有 预期结果2； \n7、每一跳分别抓包查看，有预期结果3。 \n预期结果： 1、L3VPN 流量收发正常，无丢包； \n2、不同流量按照color 引导到不同policy 指定的G-SRv6 路径中； \n3、抓包显示封装符合G-SRv6 标准规定。', 'id': '1089209_18'}
{'label': 'text', 'content': '4 可编程交换机的测试案例\n4.3DC 网关间建立SRv6 隧道\n4.3.3 支持终结VXLAN 报文映射到SRv6 Policy 进行灵活选路\n测试目的：测试交换机设备对终结VXLAN 报文并依据SRv6 Policy 进行灵活选路能力的 支持。 \n测试设计：搭建抽象DCI 组网，被测交换机在该场景下处在DCI-GW 位置，对跨DC 的 东西向流量能进行灵活的基于SRv6 Policy 选路。 \n测试组网： 测试步骤： \n1、搭建上述组网拓扑； 2、测试仪表T_1 和T_2 端口分别于DC1-GW 和DC2-GW 模拟建立BGP 邻居，发布 EVPN 路由，有预期结果1； 3、DCI-GW 之间部署SRv6 互通； 4、测试仪表模拟发送端到端跨DC 的东西向overlay 流量，有预期结果2； 5、DCI-GW 上启用SRv6 Policy 进行严格路径选路； 6、查看DCI-GW 多条互联ECMP 链路上的流量统计，有预期结果3。 \n预期结果： 1、正常发布BGP EVPN 路由和建立隧道； 2、端到端流量正常互通，不丢包； 3、DCI-GW 能基于SRv6 Policy 进行灵活选路。', 'id': '1089209_19'}
{'label': 'text', 'content': '4 可编程交换机的测试案例\n4.4 5GC UPF、MEC 增强\n测试目的： 测试交换机设备对GTP 报文的关键字段感知并用于路径Hash 选路能力的支持。 测试设计：搭建抽象MEC 组网，被测交换机在该场景下处在GW 位置，对5GC U 面的 GTP 封装报文中的TEID/内层地址字段进行感知并能用于Hash 选路。 \n测试组网： \n测试步骤： 1、搭建上述组网拓扑； \n2、部署可编程交换机的网关功能并使能GTP 的TEID/内层地址字段感知； 3、测试仪表Tester 发送模拟端到端5GC U 面的GTP 流量到可编程交换机GW 的1 口， 流量报文中构造4 个不同的TEID/内层地址值； 4、观察可编程交换机GW 出口ECMP 链路2,3,4,5 口上的出向流量统计， 有预期结果1； 5、对可编程交换机GW 的4 条出口链路进行分别抓包，有预期结果2。 \n预期结果： 1. 出口ECMP 各个成员链路上都有转发流量，且不丢包； 2. 出口ECMP 各个成员链路上的抓包分别有4 个不同的TEID/内层地址值。', 'id': '1089209_20'}
{'label': 'text', 'content': '4 可编程交换机的测试案例\n4.5 综合组网测试\n4.5.1 支持严格显式路径SRv6 Policy 切换测试\n测试目的：测试交换机设备对SRv6 Policy 严格显示路径能力的支持。 \n测试设计： 搭建一个典型PE1-P （1&2） -PE2 组网， 被测交换机在该组网场景下处在PE1 或者PE2 位置，对PE1 和PE2 的端到端流量，支持SRv6 Policy 的严格显示路径指定。 \n测试组网： \n测试步骤： 1、搭建上述组网拓扑； 2、拓扑中设备使能IGP 打通各PE 和P 设备的loopback 路由，有预期结果1； 3、所有设备配置SRv6 能力和SRv6 Locator，节点SID 等信息； 4、选择PE1 和PE2 互为头尾节点，部署SRv6 Policy，指定双向以PE1-》P1-》P2-》 PE2 的严格路径，同时部署严格备份路径PE1-》P2-》PE2，有预期结果2； 5、配置PE1、PE2 的VPN 的RD、RT，并通过MP-IBGP 相互发布VPN SID 和私网路 由,有预期结果3； 6、测试仪表T_1 和T_2 模拟CE 发送端到端VPN 流量，有预期结果3； 7、通过shutdown PE1 和P1 之间的链路，模拟故障切换，有预期结果4； 8、通过no shutdown PE1 和P1 之间的链路，模拟故障恢复，有预期结果5。 \n预期结果： 1、各个设备的loopback 互通； 2、设备上可以看到两条严格显示路径下发成功； \n3、流量正常转发，不丢包，抓包显示按照主路径封装正确； \n4、流量快速收敛，抓包显示按照备份路径封装正确,且根据丢包数/发包速率计算出收敛 时间； \n5、流量再次快速收敛，抓包显示再次回到主路径并封装正确， 且根据丢包数/发包速率计 算出收敛时间。', 'id': '1089209_21'}
{'label': 'text', 'content': '4 可编程交换机的测试案例\n4.5 综合组网测试\n4.5.2 支持松散显式路径SRv6 Policy 切换测试\n测试目的：测试交换机设备对SRv6 Policy 松散显示路径能力的支持。 \n测试设计： 搭建一个典型PE1-P （1&2） -PE2 组网， 被测交换机在该组网场景下处在PE1 或者PE2 位置，对PE1 和PE2 的端到端流量，支持SRv6 Policy 的松散显示路径指定。 \n测试组网： 测试步骤： 1、搭建上述组网拓扑； \n2、拓扑中设备使能IGP 打通各PE 和P 设备的loopback 路由，有预期结果1； \n3、所有设备配置SRv6 能力和SRv6 Locator，节点SID 等信息； \n4、选择PE1 和PE2 互为头尾节点，部署SRv6 Policy，指定双向以PE1-》P1-》PE2 的松散路径，同时部署松散备份路径PE1-》P2-》PE2，有预期结果2； 5、配置PE1、PE2 的VPN 的RD、RT，并通过MP-IBGP 相互发布VPN SID 和私网路 由,有预期结果3； 6、测试仪表T_1 和T_2 模拟CE 发送端到端VPN 流量，有预期结果3； 7、通过shutdown PE1 和P1 之间的链路，模拟故障切换，有预期结果4； 8、通过no shutdown PE1 和P1 之间的链路，模拟故障恢复，有预期结果5。 \n预期结果： 1、各个设备的loopback 互通； 2、设备上可以看到两条松散显示路径下发成功； 3、流量正常转发，不丢包，抓包显示按照主路径封装正确； 4、流量快速收敛，抓包显示按照备份路径封装正确， 且根据丢包数/发包速率计算出收敛 时间； 5、流量再次快速收敛，抓包显示再次回到主路径并封装正确， 且根据丢包数/发包速率计 算出收敛时间。', 'id': '1089209_22'}
{'label': 'text', 'content': '4 可编程交换机的测试案例\n4.5 综合组网测试\n4.5.3 支持SRv6 Policy VPN FRR 功能部署测试\n测试目的：测试交换机设备对SRv6 Policy VPN FRR 能力的支持。 \n测试设计：搭建一个典型PE1-P（1&2）-PE（2&3）组网，被测交换机在该组网场景下 处在本端PE1 位置， 对PE1 和PE2&3 的端到端VPN 流量， 支持基于SRv6 Policy 的VPN FRR 的故障快速切换。 \n测试组网： 测试步骤： 1、搭建上述组网拓扑； \n2、拓扑中设备使能IGP 打通各PE 和P 设备的loopback 路由，有预期结果1； \n3、所有设备配置SRv6 能力和SRv6 Locator，节点SID 等信息； \n4、选择PE1 和PE2 互为头尾节点，部署SRv6 Policy，指定以PE1-》P1-》PE2 的严 格路径，并使能SBFD，探测发包间隔和探测次数分别为50ms*3，有预期结果2； \n5、同时选择PE1 和PE3 互为头尾节点，部署SRv6 Policy，指定以PE1-》P2-》PE3 的严格路径； \n6、配置PE1、PE2&3 的VPN 的RD、RT，并通过MP-IBGP 相互发布VPN SID 和私 网路由,并在PE1 上使能VPN FRR，PE2 为主，PE3 为备，有预期结果3； \n7、测试仪表T_2 模拟CE 双归到PE2&PE3，T_1 模拟发送到T_2 的VPN 流量，有预 期结果3； \n8、掉电PE2，模拟远端PE 故障切换，有预期结果4； \n9、上电PE2，模拟远端PE 故障恢复，有预期结果5。 \n预期结果： 1、各个设备的loopback 互通； 2、设备上可以看到一条严格显示路径下发成功； 3、PE1 上的VPN 路由选PE2 为主路径，PE3 为备份路径，流量正常转发，不丢包，抓 包显示按照主路径封装正确； 4、流量快速收敛（150ms-200ms），抓包显示按照备PE3 的VPN SID 封装正确； 5、流量再次快速收敛（（150ms-200ms）），抓包显示主PE2 的VPN SID 封装正确。', 'id': '1089209_23'}
{'label': 'text', 'content': '4 可编程交换机的测试案例\n4.6 传统OAM 测试\n4.6.1 支持SRv6 Ping 功能测试\n测试目的：测试交换机设备对SRv6 Policy 传统OAM Ping 路径连通性能力的支持。 \n测试设计： 搭建一个典型PE1-P （1&2） -PE2 组网， 被测交换机在该组网场景下处在PE1 或者PE2 位置，PE1 上发起Ping PE2，验证路径连通性Ping 功能。 \n测试组网： \n测试步骤： 1、搭建上述组网拓扑； 2、拓扑中设备使能IGP 打通各PE 和P 设备的loopback 路由，有预期结果1； 3、所有设备配置SRv6 能力和SRv6 Locator，节点SID 等信息； 4、选择PE1 和PE2 互为头尾节点，部署SRv6 Policy，指定双向以PE1-》P1-》PE2 的显示路径，有预期结果2； 5、PE1 上执行ping SRv6 Policy PE2 动作，有预期结果3。 \n预期结果： 1、各个设备的loopback 互通； 2、设备上可以看到两条松散显示路径下发成功； 3、Ping 显示成功，抓包显示封装正确。', 'id': '1089209_24'}
{'label': 'text', 'content': '4 可编程交换机的测试案例\n4.6 传统OAM 测试\n4.6.2 支持SRv6 Traceroute 功能测试\n测试目的： 测试交换机设备对SRv6 Policy 传统OAM Traceroute 路径连通性能力的支持。 \n测试设计： 搭建一个典型PE1-P （1&2） -PE2 组网， 被测交换机在该组网场景下处在PE1 或者PE2 位置，PE1 上发起Traceroute PE2，验证路径连通性Traceroute 功能。 \n测试组网： \n测试步骤： \n1、搭建上述组网拓扑； 2、拓扑中设备使能IGP 打通各PE 和P 设备的loopback 路由，有预期结果1； 3、所有设备配置SRv6 能力和SRv6 Locator，节点SID 等信息； 4、选择PE1 和PE2 互为头尾节点，部署SRv6 Policy，指定双向以PE1-》P1-》PE2 的显示路径，有预期结果2； 5、PE1 上执行Traceroute SRv6 Policy PE2 动作，有预期结果3。 \n预期结果： 1、各个设备的loopback 互通； 2、设备上可以看到一条显示路径下发成功； 3、Traceroute 显示成功，抓包显示封装正确。', 'id': '1089209_25'}
{'label': 'text', 'content': '5 结束语\n目前，无论是互联网内容提供商还是电信运营商，对于可编程交换机的前景均已基本形成 共识，数据中心将成为可编程交换机应用的先发阵营。主流芯片厂商的交换机芯片均已具备一 定的可编程能力，主要的通讯设备厂商也已提供可商用的可编程交换机产品。总之，从芯片、 设备到市场，可编程交换机生态已经具备较为完整的产业化能力。 \n可以预见，未来可编程交换机将进一步向深度与广度方向发展： \n一是设计更高效的流水线架构，使芯片流水线的模型与转发的业务模型实现更高效匹配。 目前交换机芯片的可编程架构均采用多级stage 处理，每级stage 内置相同容量缓存，但这部 分缓存不能被其他stage 使用。但是从业务流程看，不同的转发表的容量需求是不一样的，这 就造成业务模型与芯片流水线模型不匹配，直接导致宝贵的片上缓存并没有得到充分的使用， 硬件资源的效能无法得到充分发挥。 \n二是提升芯片级的可视化能力以及设备的可视化水平。评估一款芯片效能，除了转发能力 外，芯片的可视化能力也逐渐成为重要指标。芯片运行的各种“异常”状态能够及时反馈给用 户，能够基于流、报文、事件（ 满足未来大规模数据中心智能化流量优化、调度和运维的要求。 \n三是实现算网一体化。在一个交换机内部，利用可编程芯片与x86 CPU、FPGA 等协同， 使网络逐步承担一部分计算功能，提供算网一体化能力，满足未来数据中心高吞吐量、低延迟、 低能耗的需求。目前可编程交换机除了实现交换路由的网络功能外，还可以实现网内遥测、L4 负载均衡、键值缓存等新功能，这些都为可编程交换机的发展开启了新的方向。 \n面向AR、工业互联网等新兴互联网应用发展的大潮，网络面临着一系列新的需求和挑战， 可编程交换机作为一种新型设备，有十分巨大的市场前景，可以有效支撑未来新型业务对网络 \n可定制、高性能、个性化的需求，在我国网络战略发展中具有重要地位。未来网络在接下来的 十年中将发生翻天覆地的变化,可编程交换机作为其中一种高效的基础信息平台，将持续推动网 络创新，更好地服务经济社会的发展。', 'id': '1089209_26'}
{'label': 'text', 'content': '6 缩略语\n下列缩略语适用于本文件： \n<table><tr><th>缩略语 </th><th>英文全称 </th><th>中文</th></tr><tr><th>5GC </th><th>5G Core Network </th><th>5G核心网</th></tr><tr><th>ASIC </th><th>Application Specific Integrated Circuit </th><th>专用集成电路</th></tr><tr><th>BFD </th><th>Bidirectional Forwarding Detection </th><th>双向转发检测</th></tr><tr><th>DPI </th><th>Deep Packet Inspection </th><th>深度包检测</th></tr><tr><th>DC </th><th>Data Center </th><th>数据中心</th></tr><tr><th>ECMP </th><th>Equal-Cost Multipath Routing </th><th>等价路由</th></tr><tr><th>GTP </th><th>GPRS Tunneling Protocol， </th><th>GPRS隧道协议</th></tr><tr><th>GW </th><th>Gate Way </th><th>网关</th></tr><tr><th>g RPC </th><th>Google Remote Procedure Protocol </th><th>谷歌远程过程调用</th></tr><tr><th>Hash </th><th>Hash </th><th>散列函数</th></tr><tr><th>IFA </th><th>Inband Flow Analyzer </th><th>带内流量分析</th></tr><tr><th>INT </th><th>Inband Network Telemetry </th><th>带内网络遥测</th></tr><tr><th>IGP </th><th>Interior Gateway Protocol </th><th>内部网关协议</th></tr><tr><th>LDP </th><th>Label Distribution Protocol </th><th>标签分发协议</th></tr><tr><th>MEC </th><th>Mobile Edge Computing </th><th>移动边缘计算</th></tr><tr><th>MPLS </th><th>Multi-Protocol Label Switching </th><th>多协议标签交换</th></tr><tr><th>NP </th><th>Network Processor </th><th>网络处理器</th></tr><tr><th>OAM </th><th>Operation Administration and Maintenance </th><th>操作维护管理</th></tr><tr><th>PPC </th><th>Packet Processing Cell </th><th>报文处理单元</th></tr><tr><th>RSVP </th><th>Resource Reservation Protocol </th><th>资源预留协议</th></tr><tr><th>SRv6 </th><th>Segment Routing IPv6 </th><th>基于IPv6转发平面的段路由</th></tr><tr><th>SDN </th><th>Software Defined Network </th><th>软件定义网络</th></tr><tr><th>SFC </th><th>Service Function Chain </th><th>服务功能的顺序组合</th></tr><tr><th>UPF </th><th>User Plane Function </th><th>用户面功能</th></tr><tr><th>v DPI </th><th>Virtual Deep Packet Inspection </th><th>虚拟深度包检测</th></tr></table>', 'id': '1089209_27'}
