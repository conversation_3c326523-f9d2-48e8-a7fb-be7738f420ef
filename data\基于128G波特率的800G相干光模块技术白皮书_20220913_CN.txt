{'label': 'document', 'content': '基于128G波特率的800G相干光模块技术白皮书_20220913_CN', 'id': '1085714'}
{'label': 'text', 'content': '基于128G波特率的800G相干光模块技术白皮书\n基于128G波特率的800G相干光模块技术白皮书\n| 版本 | 日期 | 作者 | 审核者 | 备注 |\n|:-|:-|:-|:-|:-|\n| V1.0 | 2022/07/31 | 张良俊、杨振乾 | 冯振华 | |\n| | | | | |\n| | | | | |\n| | | | | |\n目录\n1. 趋势和挑战\t4\n2. 关键技术\t5\n2.1 新型DSP算法架构支撑波特率演进\t5\n2.1.1 多电子载波技术vs单载波\t5\n2.1.2 非线性补偿算法\t7\n2.1.3 高速ADC与DAC的问题和解决方案\t8\n2.2 高速相干光器件助力800G相干光模块\t8\n2.2.1 相干光器件在128G波特率时代的新挑战\t8\n2.2.2 先进集成封装技术助力器件带宽提高\t9\n2.2.3 新型薄膜铌酸锂材料突破带宽和波段限制\t11\n3. 应用场景\t12\n4. 展望\t13\n5. 缩略语\t13\n', 'id': '1085714_1'}
{'label': 'text', 'content': '趋势和挑战\n近年来，随着5G网络逐步普及，传统行业随之与ICT产业进行融合，4K/8K高清视频，VR/AR、全息影像等新业务不断涌现，进一步推动网络流量和带宽需求继续保持指数增长。光网络作为5G新基建的信息高速公路，面临前所未有的机遇和挑战。一方面，5G网络催生的应用需要高性能光网络提供丰富带宽资源及高品质的连接体验。根据LightCounting预测，预计至2027年，全球光网络带宽总规模将达1600Pb，其中Telecom场景占总体光网络带宽的50%以上。另一方面，作为数字经济时代的核心基础能力，算力网络概念的产生对光传送网在网络架构、带宽、时延等方面对提出新的需求。为了进一步支撑算力网络演进，需要以光筑底，以算力为中心构建新一代的扁平化、大带宽、低时延光传送网。\n', 'id': '1085714_2'}
{'label': 'table', 'content': '趋势和挑战\n高速相干光模块作为高带宽光传送网的构建基石，也沿着更大带宽、更低功耗、更紧凑封装、更宽谱宽、更智能化等方向发展，超100G相干光模块技术作为未来光网络发展的超级引擎，将在东数西算工程中提供超强运力。如表1所示，DWDM市场主用的光模块将于2023年进入800G时代。根据LightCounting报告，2022年DCI 800G端口市场达3亿美元，而这一数字将在2027年达到40亿美元。高速端口发货量继续强势增长，势必进一步推动骨干、城域及DCI光网络全面提速扩容，促进高速大容量光传送网、高品质算力光网络蓬勃发展。\n表1 DWDM市场速率发展及主用光模块\n| 应用 | 距离(km) | 主用光模块 | 主用光模块 | 主用光模块 | 主用光模块 | 主用光模块 |\n|:-|:-|:-|:-|:-|:-|:-|\n| 应用 | 距离(km) | 2020~2022 | 2023~2024 | 2023~2024 | 2023~2024 | 2025~ |\n| 短距DCI | 80~120 | 400G ZR/16QAM 可插拔 | 400G ZR/16QAM 可插拔 | 800G ZR/16QAM 可插拔 | 800G ZR/16QAM 可插拔 | 1.6T 16QAM可插拔 |\n| 短距DCI | 80~120 | 600G/800G 64QAM 固定 | 800G/1.2T 64QAM 固定 | 800G/1.2T 64QAM 固定 | 800G/1.2T 64QAM 固定 | 2.4T 64QAM固定 |\n| 接入/汇聚 | 80~240 | 100G DCFP | 100G DCFP | 100G DCFP | 200G/400G DCFP2 | 200G/400G DCFP2 |\n| Metro核心 | 300~600 | 200G/400G 16QAM DCFP2 | 200G/400G 16QAM DCFP2 | 800G 16QAM DCFP2 | 800G 16QAM DCFP2 | 800G 16QAM DCFP2 |\n| 骨干网 | 600~2000 | 200G QPSK/PS16QAM DCFP2 | 200G QPSK/PS16QAM DCFP2 | 400G QPSK/PS16QAM DCFP2 | 400G QPSK/PS16QAM DCFP2 | 800G QPSK DCFP2 |\n| 骨干网 | 600~2000 | 200G/400G QPSK/PS16QAM 固定 | 400G QPSK/800G PS16QAM固定 | 400G QPSK/800G PS16QAM固定 | 400G QPSK/800G PS16QAM固定 | 800G QPSK固定 |\n| 海缆 | >2000 | PS-16QAM~32QAM等高谱效调制码型 | PS-16QAM~32QAM等高谱效调制码型 | PS-16QAM~32QAM等高谱效调制码型 | PS-16QAM~32QAM等高谱效调制码型 | PS-16QAM~32QAM等高谱效调制码型 |\n', 'id': '1085714_3'}
{'label': 'text', 'content': '趋势和挑战\n面对光网络技术快速发展的机遇和挑战，中兴通讯不断对高速相干光模块技术进行优化迭代，推出一系列高速相干光模块产品。基于高速相干光模块产品，中兴通讯在国内外屡创100G/超100G光网络传输记录，200G OTN产品实现了1700公里最长传输距离，并承建了中国电信西北环的全球最大ROADM网络。2017年，中兴通讯助力泰国True部署全球首个长距离灵活栅格200G骨干波分网络，单光纤容量达到21.2Tbps。2019年，在中国移动现网测试中，中兴通讯单载波400G传输距离超过600km，明显优于其他测试厂商，成为唯一实现从北京到济南长距离业务传输的厂商。2022年，中兴通讯助力中国移动在实验室实现了400G QPSK码型超3000km传输测试，为下一代传输技术演进与码型选择指明了方向：96Gbd 400G PS16QAM 应用场景受限，128Gbd 400G QPSK是最佳演进方案。\n面对光传送网向单波800G演进趋势，中兴通讯升级推出基于128G波特率的800G相干光模块，进一步攻克高速DSP算法与芯片、高速相干光器件与控制算法等关键技术，实现更高带宽、更低功耗、更紧凑封装的相干光模块从而实现适应网络需求下的少中继、低成本的超长距离传输，同时为光网络性能保驾护航。\n', 'id': '1085714_4'}
{'label': 'text', 'content': '关键技术\n800G相干光模块相比与当前400G模块速率增加一倍，模块中的核心器件包括DSP和相干光器件的波特率也从64G倍增至128G。面对倍增的波特率，DSP需要采取更先进的算法如多电子载波技术、非线性补偿算法等，同时DSP中ADC/DAC选型也成为瓶颈。同样，相干光器件也面临带宽持续提升带来的挑战，为了突破这一困境，更先进的MCM封装和新型铌酸锂薄膜材料可能被引入相干光调制接收器（组）件。相干光模块中新技术和新材料的引入，不仅减小了模块高速信号的传输损耗，增大了模块带宽，还有望降低模块功耗和成本。图1展示了基于128G波特率的800G相干光模块关键技术图谱。', 'id': '1085714_5'}
{'label': 'text', 'content': '关键技术\n2.1 新型DSP算法架构支撑波特率演进\n2.1.1 多电子载波技术vs单载波\n在传统的相干光通信系统中，单个激光器产生的光信号占据一段连续的频谱，而对于多电子载波技术，在发端通过数字域上的特殊处理可以将频谱上连续的光载波分割成若干个独立的奈奎斯特子载波，如图3所示。子载波的个数根据总的波特率和应用场景可以配置，一般选择4、8或者16。 \n多电子载波技术相比于传统单载波主要有三方面优势，其一是降低色散补偿复杂度，其二是减小长距离传输代价，包括色散补偿带来的EEPN代价和光纤链路带来的非线性代价。 \n当前的相干光通信系统大都在收端DSP处理时补偿光纤链路累积的色散。主流的色散补偿算法是对信号做快速傅里叶变换（FFT）后在频域上进行补偿，而色散补偿需要的滤波器抽头数量与波特率的平方成正比。假设采用4个子载波，在总波特率相同的情况下，每个子载波的波特率是单载波的1/4，那么在色散补偿时需要的滤波器抽头数量只有单载波的1/16，这样在长距离大色散系统中可以极大的节省色散补偿需要的硬件资源。 \n除了降低色散补偿的复杂度，多电子载波技术还可以减小色散补偿导致的传输代价。对于目前收端通用的相干DSP处理结构，均衡增强相位噪声（EEPN）效应并没有得到很好的解决。EEPN效应主要是由收端激光器线宽导致的相位变化和色散补偿滤波器的共同作用导致。对于低价调制格式如QPSK以及波特率较小时，EEPN的影响很小，可以忽略。但是随着调制阶数和波特率的增加，EEPN导致的色散补偿代价就不能再忽略。例如对于128GBd的800G 16QAM传输系统，当收端激光器线宽为300kHz，累积色散为20000ps/nm时，EEPN带来的OSNR代价有0.5dB。而如果采用多电子载波技术，每个子载波的波特率为32GBd时，EEPN的代价就不到0.1dB。 \n最后多电子载波相比于单载波还可以降低长距光纤传输带来的非线性代价。多电子载波技术通过优化每个子载波的波特率以及子载波的个数使自相位调制（SPM）、交叉相位调制（XPM）和四波混频（FWM）这三种非线性效应的综合效应最小，从而降低非线性的损伤。如图4所示，FWM效应随着子载波个数增加而增加，SPM效应随着子载波个数增加而减少，XPM效应介于两者之间。子载波数目存在一个最优区间使得三者的综合效应最小。 \n从以上分析可以看出多电子载波技术相比于单载波技术的主要优势在于色散和非线性效应都很大的超长距传输场景。但是多电子载波由于信号的PAPR较大也会对光电器件的要求更高。业界对于多电子载波与单载波两种技术架构也有不同的看法，Ciena和Infinera是多电子载波的支持者，它们认为多电子载波在性能和灵活性上相比单载波都有优势。而华为和Inphi则认为多电子载波只有在跨洋这种超长传输距离下才具备性能优势。总的来说，两种架构各有优劣，具体可以根据800G的不同应用场景选择不同技术方案。', 'id': '1085714_6'}
{'label': 'text', 'content': '关键技术\n2.1 新型DSP算法架构支撑波特率演进\n2.1.2 非线性补偿算法\n随着光传输系统对传输容量和距离需求的增加，光纤非线性损伤带来的性能劣化进一步加剧。要进一步提高系统性能，光纤非线性补偿是必要的。光纤非线性效应主要分为两大类，第一类非线性效应描述光纤折射率受传输信号光强影响也就是克尔效应，包括自相位调制（SPM）、交叉相位调制（XPM）、四波混频（FWM）；另一类描述光场能量转移也就是受激非线性散射，例如受激拉曼（SRS）和受激布里渊散射（SBS）。SRS、SBS、FWM会引起信号强度变化使不同波长或者频率之间的信号互相串扰，而SPM和XPM都只影响信号相位。由于克尔效应的影响，入射光强被限制，如果可以补偿克尔效应就可以增大入射光功率，提高OSNR。在高速光纤传输系统中，SRS和SBS一般会在光系统设计层面尽量限制其发生，所以光纤非线性补偿主要是针对克尔效应设计DSP算法进行补偿以缓解传输代价。 \n光脉冲在光纤中的传输由薛定谔方程描述，如果可以求解薛定谔方程，即可补偿光纤非线性。但是非线性薛定谔方程没有解析解，所以一种数值解法被提出——数字后向传输方法（DBP）。DBP是在接收端构造一段和传输信道“相反”的虚拟信道，将信道分割成足够小的分段，假设在当前分段中只有色散或者非线性单独作用，逐段补偿色散和非线性。DBP中色散在频域补偿，非线性在时域补偿，所以有大量傅里叶变换操作。另一方面DBP的分段必须足够小才能保证性能，所以计算复杂度高是DBP最大的弊病。DBP的性能是非线性补偿算法的标杆，目前已有很多优化计算量的算法提出。 \n微扰法解薛定谔方程是将非线性损伤项写作幂级数形式，代入薛定谔方程后求解的算法。微扰法使用前提是非线性项很小，所以非线性较大的时候微扰法准确度降低。另外，一般为了限制复杂度微扰法只会展开到一阶幂级数代入求解，即一阶微扰法，也会损失性能。微扰法的计算复杂度比DBP小，但是性能不及后者。微扰法可以在时域补偿也可以在频域补偿比较灵活，也可以工作在单倍符号速率下，进一步降低复杂度和功耗。 \n由于色散和光纤非线性会带来高阶导数，时域薛定谔方程很难求解，可以将薛定谔方程转换到频域（导数变成乘法）再用频域Volterra级数求解。利用求解出来的表达式反向补偿光纤非线性的方法称作反向Volterra级数（IVSTF）补偿法。因为计算复杂度的限制，一般Volterra级数只取到三阶，注意由于光纤信道中没有偶数阶损伤，所以不取二阶。IVSTF补偿法和DBP一样分段完成，但是每段补偿可以并行实现。IVSTF补偿法的性能和微扰法接近，其复杂度介于时域微扰法和DBP之间。其他的微扰改进算法还包括时域Volterra滤波器、Turbo均衡和非线性补偿联合算法等。 \n机器学习是时下热点技术，用机器学习补偿光传输系统损伤的研究不胜枚举。基于机器学习的均衡算法一般分为分类和回归两种，分类是根据非线性噪声的特性来重新划分信号的判决边界。目前用于光纤非线性补偿的机器学习主要是回归法，通过训练在输入和输出间建立映射，学习好这种映射关系后用来补偿非线性。基于人工神经网络（ANN）、卷积神经网络（CNN）、递归神经网络（RNN）及其各种变型优化的算法已应用于光纤非线性补偿，学术界有相关论文报导基于神经网络的方式补偿非线性可以将信号的Q值提升1dB。但是其复杂度也非常高，目前来看难以做到芯片中。 \n总的来说，光纤非线性补偿仍然是一个难点，受限于目前芯片的功耗和面积，复杂度相对较低的Volterra补偿算法可能更加实用。随着芯片工艺的进步和神经网络算法的优化，未来神经网络补偿非线性也可能落地到DSP芯片中。', 'id': '1085714_7'}
{'label': 'text', 'content': '关键技术\n2.1 新型DSP算法架构支撑波特率演进\n2.1.3 高速ADC与DAC的问题和解决方案\n单波800G对于DAC和ADC的采样率需求增加到170Gsa/s，带宽需求也增加到50GHz以上。面对如此高的速率和带宽需求，DAC和ADC都面临不小的挑战。 \n对于超高速DAC方案，可以选择基于SiGe工艺的DAC架构，SiGe相比于CMOS有巨大的带宽优势。但是SiGe的功耗非常高，目前来看难以满足光模块的功耗需求。另外一个路径是用两个DAC结合带时钟控制的模拟复用器（A-MUX）实现高速大带宽的DAC，但是也需要解决时钟失配等问题。 \n对于超高速ADC方案，目前比较普遍的是采用多通道的时间交织ADC（TI-ADC)方案。它基于多个并行的子ADC来实现高速采样，可以通过增加并行度来提高整个ADC的采样速率。但是随着并行度的增加，各子通道之间的失配误差会更加敏感，从而制约ADC的整体性能。在具体架构规划时，要结合采样率，功耗合理分配并行度和子ADC采样率的关系，找到并行度和子ADC速率的最佳组合。 \n最后由于芯片工艺的制约，带来器件失配、芯片内温度和应力分布不均等，使得ADC、DAC转换的性能与设计会有偏差。多种失配会带来谐波失真，进一步降低ADC和DAC的有效分辨率（ENOB）。对于高速的ADC和DAC需要采用数字校准算法来补偿偏置失配、增益失配、采样时间失配以及非线性失配等损伤，从而提高ENOB、无杂散动态范围（SFDR）等指标。', 'id': '1085714_8'}
{'label': 'text', 'content': '关键技术\n2.2 高速相干光器件助力800G相干光模块\n2.2.1 相干光器件在128G波特率时代的新挑战\n相干光器件主要包括窄线宽可调激光器ITLA, 相干光调制驱动器，集成相干光接收机等。面对128G波特率的应用场景，相干光器件面临的首要问题便是带宽限制。对于硅基相干调制器来说，减小MZ调制器长度可以进一步增大调制器的带宽，但减小MZ调制器长度同样会减弱调制效率，需要施加更大的调制电压，这对Driver又提出了更高的要求。除了带宽限制以外，相干光器件的功耗、封装尺寸、工作谱宽等也成为了新的挑战。一方面，随着相干光模块速率越来越高，相干光模块的封装形式越来越紧凑，电信市场应用需求从固定封装形式逐渐演变成目前的DCFP2封装为主。相干光模块的体积缩小对相干光器件的集成程度提出了更高的要求。另一方面，单波提速后，光信号波特率变大需要更大的波道间隔，为保证系统容量与速率同时倍增，光传输系统需要拓宽现有的光谱波段范围，当前相干光通信正向C+L波段扩展，相干光器件也需要支持C+L一体化设计，波段的扩展对器件波长相关性（色散）容忍度提出了更高的要求。', 'id': '1085714_9'}
{'label': 'text', 'content': '关键技术\n2.2 高速相干光器件助力800G相干光模块\n2.2.2 先进集成封装技术助力器件带宽提高\n面对800G光模块带来的带宽、功耗、集成度、波段等多方面的制约，相干光器件采取更先进的封装技术少了高速信号的传输长度与衰减，提高了器件带宽并减少了功耗与成本。图5反应了相干光器件的演进路径。当前相干光器件的材料体系主要分为硅光材料体系与III-V族材料体系。硅光材料体系在128G波特率阶段可以采用光电共封装技术，集成DSP、CDM、ICR等器件成为紧凑封装的MCM（多芯片组件，将多个裸芯片和其它元器件组装在同一块多层互连基板上的组件）器件；III-V材料体系可以采用微光学气密封装集成ITLA、CDM、ICR等器件成为TROSA器件。先进集成封装工艺推动了相干光器件与光模块的小型化，同时紧凑封装减少了高速信号的传输长度与衰减反射，为进一步提高器件和模块带宽打下了基础。 \n相比III-V族材料体系，基于硅光材料体系的PIC（集成光子回路，以介质波导为中心的成光器件的光波导型集成回路）可以兼容已经成熟的CMOS工艺，加工工艺的成熟极大的降低了硅光PIC的生产制造成本，同时硅光PIC可以在片上实现所有的无源光学器件，不需要进行气密性封装，这也进一步节省了成本。由于硅光MZ调制器采用载流子色散效应对材料折射率进行调制，并普遍采用耗尽型的PN结结构，PN结的结电容很大程度上限制了器件带宽的进一步提高。而缩小结电容需要对掺杂和器件长度进行优化，器件长度的缩小以及掺杂浓度的改变又带来了相互制约的调制效率不足的问题。面临这一困境，先进的封装形式从一定程度上起到了缓解作用。图6a为传统2D封装示意图，传统的2D封装是将PIC光芯片和驱动电芯片水平放置，并通过打线的方式将驱动器和PIC封装在一起，之后再与DSP在模块PCB上进行互联。在这一封装形式下，高速模拟信号需要沿着DAC->PCB走线->CDM基板走线->打线->驱动器->打线->PIC这一路径从DSP传输到PIC，如图中红线所示，这一传输路径长、中断连接点多，对高速信号产生的损耗和反射已经很难满足光器件面对的高速传输场景。为了进一步减少信号传输路径长度，减少信号损耗，硅基相干光器件引入了MCM封装形式，如图6b所示。首先驱动器以倒装焊接的方式直接贴装在PIC上，同时DSP裸片与PIC也封装在同一个基板上，将光芯片和电芯片进行混合封装，如图3c，尽可能减小高速信号传输长度，保证了器件的带宽。同时这一封装形式也大大减少了器件体积，并且降低了器件成本。 \n由于硅材料是间接带隙材料，基于硅光体系的PIC很难集成有源的ITLA、SOA等器件，这也是硅光器件的一大劣势。III-V族材料由于其在红外波段优异的发光特性，被广泛用于红外波段的增益材料。得益于先进的封装工艺，基于III-V族材料的集成光器件，可以集成ITLA、CDM、ICR、SOA等器件，成为更紧凑的TROSA器件。图7为TROSA内部结构示意图，由于III-V族材料无法在片上实现高效的偏振转换器件，相干光器件内部需要采用大量的微型空间光学器件实现偏振分束与转换等功能。由于集成了有源ITLA和SOA器件，相干光器件需要采用TEC进行温度控制，以保证器件的稳定工作，同时保证光学器件的粘接不会失效。而空间光路和TEC的应用又使得基于III-V族材料的集成光器件需要采用气密封装，以便隔绝外部灰尘以及腐蚀性气体，保证器件的稳定性。如图7所示，相比分立的器件，TROSA器件大大缩减了在模块内占用的空间，提升了模块的可生产性与可维护性，并降低了模块成本。 \n中兴通讯于2021年发布了基于MCM器件以及TROSA器件的400G可插拔相干光模块，实现了紧凑的CFP2封装形式。针对基于128G波特率的800G相干光模块，前期仿真结果显示使用MCM光电共封装技术可以使器件带宽增大10%以上。光器件先进封装技术的应用可以提升器件带宽，减少器件体积和成本，在基于128G波特率的800G相干光模块中将得到广泛的应用。', 'id': '1085714_10'}
{'label': 'text', 'content': '关键技术\n2.2 高速相干光器件助力800G相干光模块\n2.2.3 新型薄膜铌酸锂材料突破带宽和波段限制\n硅光MZ调制器的带宽受到了载流子色散效应与结电容的制约，先进的封装形式可以从一定程度上提高带宽，但无法从根本上解决更高速率的调制问题。为了从根本上解决材料对器件带宽的限制，铌酸锂薄膜材料被引入相干光器件。如表2所示，不同于硅光和III-V族材料，铌酸锂材料本身拥有较强的Pockels效应，其折射率改变不需要额外的载流子注入，只需要在波导特定方向加入电场即可，这样简化了器件的结构，同时避免了PN结带来的结电容，提高了调制速率。同时，由于铌酸锂材料的折射率改变随着电场强度线性变化，这也避免了材料带来的非线性效应，相比硅光和III-V族材料在高速率高阶调制时有着进一步的优势。再者，铌酸锂材料的折射率在红外波段色散很小，且几乎无吸收，可以有效解决相干光器件波段从C波段扩展到C+L甚至更多波段的色散与损耗问题。\n表2 不同材料调制器调制原理与优劣对比\n| 材料 | Si | III-V | LiNbO3 |\n|:-|:-|:-|:-|\n| 原理 | 载流子色散效应 | 量子限制斯塔克效应载流子色散效应 | Pockels效应 |\n| 优势 | 集成度高、加工工艺成熟 | 可以集成有源器件 | 调制速度快、调制线性、插损小 |\n| 劣势 | 调制不线性 | 插损大、调制不线性 | 加工工艺不成熟、器件集成度低、体积大 |\n传统的铌酸锂调制器是基于体材料制作，通常使用离子注入的方式加工波导，而这种方式加工出来的波导折射率差很小，对光的束缚能力差，波导调制效率低，这就导致波导长度很长，通常需要几个厘米。过长的波导放大了光速与RF信号传播速度的失配误差，从而限制了调制带宽。如图8所示，铌酸锂薄膜材料可以很好的解决这一问题。铌酸锂可以通过离子解理的方法在SiO2或Si衬底上制备薄膜，形成LNOI材料。基于薄膜铌酸锂材料，波导可以通过干法刻蚀进行加工，刻蚀后的脊波导与周围的SiO2材料有较大的折射率差，大大缩小了波导尺寸，提高了调制效率和调制带宽。\n虽然薄膜铌酸锂材料相比与体块铌酸锂材料、硅基材料、III-V族材料有着显著的优点，但当前阶段铌酸锂薄膜的应用还存在着一系列问题。首先铌酸锂薄膜材料无法集成有源器件与探测器件，所以无法进行紧凑的集成与封装，相对来说体积较大，而且无法制作相干接收器件。其次，铌酸锂薄膜的加工工艺还不太成熟，波导刻蚀垂直度和粗糙度需要进一步提升，这也从一定程度上限制了其光学束缚性能。总的来说，薄膜铌酸锂材料相比于硅光和III-V族材料，可以突破调制器带宽限制，在128G波特率以及更高速率场景下有很强的优势。中兴通讯在800G时代也积极探索研究不同材料体系对器件和模块的影响、优化设计制造和封装工艺，以保证相干光器件和模块的稳定运行，为下一代高速集成相干光器件奠定基础。\n', 'id': '1085714_11'}
{'label': 'text', 'content': '应用场景\n相干光模块进入800G时代后，不同的组网场景可以根据传输距离不同选取适合的码型与速率，如图9所示。长距骨干网场景，可以采用800G PS16QAM码型，配合G654光纤或者拉曼放大，实现大于1000km的传输。另外，模块也可以降低速率，采用400G QPSK码型，实现长距离传输。对于城域汇聚场景，同样可以根据传输距离采用800G PS16QAM或400G PS16QAM码型，占据不同的栅格，实现不同频谱效率的灵活传输配置。而对于短距接入、DCI互连场景，可以采用单波1.2T PS64QAM码型，实现更高速率更大容量短距离传输。800G相干光模块可以针对不同场景，实现波特率与码型的灵活配置，从而实现全应用场景的支持。相比于400G光模块，800G相干光模块波特率的灵活配置也大大增加了频谱利用率，进一步增大了总的传输容量。中兴通讯将在800G相干光模块及系统产品中采用Flexshaping 2.0算法族支持波特率与码型的灵活调控，以更灵活的应对各种应用场景提高频谱效率，全面优化光传送网的性能。', 'id': '1085714_12'}
{'label': 'text', 'content': '展望\n800G是相干光模块速率演进的下一个里程碑。面向下一代相干光模块，中兴通讯将进一步推进模块与器件的小型化，推进QDD/OSFP等紧凑型封装模块的落地应用。面对相干光模块下沉的发展趋势，中兴通讯将推进扩展支持800G ZR与800G LR等标准化、短距应用场景。同时，中兴通讯将持续关注新型器件设计与新型材料体系，大力推进更高波特率（128+GBd），更宽波段范围（C+L波段）器件、模块与系统的成熟与商用。另外围绕800G相干光模块中的核心DSP芯片及算法，中兴通讯也会持续加强研究投入，致力攻克可芯片实现的非线性补偿算法和高速AD/DA 关键IP等业界难题。 中兴通讯将始终积极与业内合作伙伴、产业链、高校院所、友商同行一道积极探索、研究并应用各类新器件与新技术，开发推出高效、稳定、灵活、紧凑、智能的相干光模块，助力运营商/互联网客户实现光网络持续提速扩容、性能和成本优化，共同为行业数字化转型提供充足运力，让连接无处不在，信息沟通更加高效。', 'id': '1085714_13'}
{'label': 'table', 'content': '缩略语\n| 缩略语 | 术语 | 描述 |\n|:-|:-|:-|\n| DSP | Digital signal processing | 数字信号处理器，是一种通信专用的信号处理芯片，有着强大的数字信号处理能力。 |\n| MCM | Multi-Chip Module | 多芯片组件，是一种将多个裸芯片和其它元器件组装在同一块多层互连基板上，然后进行封装，从而形成高密度和高可靠性的微电子组件。 |\n| ICT | Information and communications technology | 信息与通信技术，覆盖了所有通信设备或应用软件以及与之相关的各种服务和应用软件。 |\n| DWDM | Dense Wavelength Division Multiplexing | 密集型光波复用，是能组合一组光波长用一根光纤进行传送的技术。在一根指定的光纤中，多路复用单个光纤载波的光谱间距非常紧密，以便可以达到更高带宽的传输性能。 |\n| DCI | Data Center Interconnect | 数据中心间通信，指两个及以上数据中心的连接通信。 |\n| QPSK | Quadrature Phase Shift Keying | 正交相移键控，是一种四相位调制方式，具有良好的抗噪特性和频带利用率，可以应用在相干光通信系统中。 |\n| M-QAM | M-Quadrature Amplitude Modulation | M阶正交幅度调制，也是相干光通信系统中常用的调制方式 |\n| OTN | Optical transport network | 网络的一种类型，是指在光域内实现业务信号的传送、复用、路由选择、监控，并且保证其性能指标和生存性的传送网络 |\n| ROADM | Reconfigurable Optical Add-Drop Multiplexer | 可重构光分插复用器，是一种使用在密集波分复用（DWDM）系统中的器件或设备，其作用是通过远程的重新配置，可以动态上路或下路业务波长。 |\n| MZM | Mach-Zehnder Modulator | 马赫曾德调制器，一种利用双臂干涉进行光调制的器件。 |\n| CDM | Coherent Driver Modulator | 相干驱动调制器，一种集成了驱动器与调制器的相干光器件，用于对激光进行信号调制与加载。 |\n| ICR | Integrated Coherent Receiver | 集成相干接收机，一种集成了接收机与跨阻放大器的相干光器件，用于对相干光信号进行解调。 |\n| ITLA | Integrated\u2002Tunable\u2002Laser\u2002Assembly | 集成可调谐激光器组件，一种波长可调谐的激光器，用于相干光通信光源。 |\n| TROSA | Integrated coherent transmitter and receiver optical subassembly | 集成相干收发光学组件，一种集成了可调谐激光器、相干驱动调制器和相干接收机的相干光器件，用于相干光通信。 |\n| PIC | Photonic integrated circuit | 光子集成电路，以介质波导为中心集成光器件的光波导型集成回路。 |\n| PCB | Printed Circuit Board | 印制电路板，由绝缘底板、连接导线和装配焊接电子元件的焊盘组成，具有导电线路和绝缘底板的双重作用，可以代替复杂的布线，实现电路中各元件之间的电气连接。 |\n| TEC | Thermo Electric Cooler | 半导体制冷器，是利用半导体材料的珀尔帖效应制成的制冷器件，主要用于温度控制。 |\n| TIA | trans-impedance amplifier | 跨阻放大器，一种放大器，可以有效地抑制噪声信号的放大，广泛用于相干光通信。 |\n| PS | Probabilistic constellation shaping | 概率星座整形，通过改变各星座点的概率分布获得整形增益的技术 |\n', 'id': '1085714_14'}
