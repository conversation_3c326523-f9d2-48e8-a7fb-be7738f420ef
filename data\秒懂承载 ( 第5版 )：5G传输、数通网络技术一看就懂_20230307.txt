{'label': 'document', 'content': '秒懂承载 ( 第5版 )：5G传输、数通网络技术一看就懂_20230307', 'id': '932557'}
{'label': 'text', 'content': '《秒懂承载》顾问委员会 简介 主 任： 副主任： 顾 问： 《秒懂承载》编辑部 主 编： 策 划： 编 审：（排名不分先后） 编 辑：（排名不分先后） 排 版：唐迹波 法律声明 本资料著作权属于中兴通讯股份有限公司所有。 未经著作权人书面许可，任何单位或个人不得 以任何方式摘录、复制或翻译。 侵权必究。 中兴通讯股份有限公司 地址：深圳市科技南路55号 邮编：518057 电话：+86-755-26770800 800-830-1118 李强 王泰立 王 强 李新双 胡龙斌 赵福川 陈 勇 陶文强 夏迎春 薄开涛 李宏飚 张宝亚 黄力青 张云龙 胡俊劼 张振朝 魏晓强 孙 明 陈冉升 叶慧聪 刘 哲 潘晓辉 陈冉升 袁 博 吴小照 杨 振 黄 峰 唐 芸 王 伟 梁大鹏 张丽莎 管文勇 叶 虹 俎晓飞 刘冬阳 黄 演 关鲁君 王勇彬 罗来荣 魏登攀 丁 静 王 坦 郝春雷 张 征 欧阳建平 王 颖 赵 佼 崔 健 陈 菲 项 巍 姜 帅 中兴通讯股份有限公司 秒懂承载 秒懂承载 5G传输、数通网络技术 一看就懂 “数字筑路，承载筑基”，数字经济时代，新业务 蓬勃发展，承载网作为网络基础设施其重要性不言 而喻，是运营商数字化转型必不可少的部分。 《秒懂承载》系列文章主要目标受众定位于非技术 型读者和非承载网专业领域的技术型读者，将承载 网络、承载产品跟交通网络、交通工具做类比，在 通俗的文字中穿插易懂的图表，生动诠释5G承载网 络知识，让读者快速了解承载网技术、产品和方案。 2019年《秒懂承载》系列文章在中兴通讯公司微信 公众号对外发布后，受到众多KOL同行和知名媒体 转载并传播，获得外部客户、读者的广泛好评，并 在当年第五届中国企业微课大赛中荣获“百强优秀 微课”证书。 2023年节选了《秒懂承载》系列25篇精彩图文作 品进行第5次印刷，对5G承载网的背景知识和关键 技术进行了介绍，用于指导读者掌握5G承载网的基 础知识。 随着5G承载网络的规模部署，中兴通讯将不断探索 新的知识传播方式，建设知识型品牌，让知识助力 广大通信从业人员更好地建设承载网络。 “承载网作为底层网络，其技术复杂度较 高，理解起来并不那么容易。《秒懂承载》 以通俗易懂、幽默诙谐的方式来介绍承载 产品和技术，致力于让更多的人快速了解 和掌握承载网基础知识，是较好的入门级 科普读物。” 电子邮箱：<EMAIL> 官方网站：http://www.zte.com.cn \nBN产品MKT及方案团队 \n中兴通讯承载网产品线简介 中兴通讯承载网产品线整合包括光传输网和IP网络等基础网络产品， 形成以IP技术为核心，融合分组和光技术的新一代承载网产品解决方案， 坚持成本领先、快速定制和持续创新的经营思路，为运营商业务网络转型 提供可靠、实用、可平滑过渡的低TCO承载网产品和方案。 \n承载网产品线目前拥有5400余件国内专利，3300余件PCT国际专利， 在SPN、OSU、SR、BIER、IP OAM等重点领域均有大量技术积累，拥 有50多个国际标准组织编辑席位。在ITU-T、OIF、IETF提交5G相关专 利800+篇，获100+项专利；担任ITU-T WP3工作组主席，CCSA TC3 副主席，WG2和WG3工作组副组长，CCSA TC6 WG4工作组副组长。 \nLeader 主打产品竞争力被权威机构最高评级 承载网产品已在全球300+运营商稳定运行超20年 \n1 \n什么是承载网？ 01 当今通信圈子里最受欢迎的信息“快递小哥” \n承载网负责业务信息传送和中转 02 时代在变，“快递小哥”的交通工具也在变 03 成功的“移动”货运背后都有一个“回传”快递小哥 \nDWDM波分产品 \n（短途分批快递） （长途集中快递） 城域宽带网 家宽业务传输 数通 波分 数通灵活，任意行 波分固定，有方向 Metro-E / OTN解决方案 构建丰富高效网络 提供全面业务服务 \n骨干网 跨区综合业务传输 IP / OTN 骨干网解决方案 提升信息传递距离 扩大业务拓展范围 \n中兴通讯热销产品 \n5G承载网络技术概述 l 5G承载网，你的路修好了吗？ ................................................ 07 \n5G承载分组传输网络关键技术篇 l FlexE：5G时代，以太网家庭幸福的秘诀是什么？ ..................... 13 \nl SR，助力5G赢未来 ..............................................................23 \nl 3A高精度时间：通信网络是怎么守时的？ .......................... 37 \nl 小颗粒技术：云诊断、云课堂、云旅游背后的力量 ................. 47 \n5G承载光传输网络关键技术篇 l 智宽新光网，为5G新基建按下加速键 .................................... 59 \nl 不是所有的400G都叫Real 400G ........................................... 71 \nl OSU技术：是谁，助力5G时代的品质专线 ............................ 79 \nl FlexO：是谁让5G光传送网变得更灵活更强大? ...................... 89 \nl ROADM为承载网带来了什么？ ............................................. 97 \nl 什么是SDON软件定义光网络技术？ ..................................... 107 \n5G承载数通网络关键技术篇 l 路由器，你究竟把数据怎么了？ .............................................. 115 \nl ”IPv6+”的时代都来了，你还不知道什么是IPv6吗？ ..........123 \nl SRv6技术：开启新IP时代 .................................................133 l BIER技术：5G时代，大规模组播应用离不开TA .................. 145 l iOAM技术 智能运维：谁为5G承载保驾护航？ .................... 161 l Slice+是如何实现1+1>2的？ .......................................... 171 l 原来你是这样的5G电信云 ................................................. 183 l EVPN：5G时代，是谁为数据中心带来了新的活力？ .............. 195 l 5G电信云数据中心的逻辑组网 ............................................ 207 l 堆叠，你能为5G做些什么？ ............................................... 221 \n5G承载网络运维关键技术篇 l 微服务和容器技术：怎样的必杀技让5G网管脱颖而出？ .......... 231 l Netconf：5G网络归我管，不服来战！ ................................ 241 l Ti-LFA：5G承载网，你的稳定我来守护 ........................... 257 l Telemetry遥测技术：No Pull，Just Push .......................... 273 \n中兴通讯承载网主要产品 .................................................... 285 \n缩略语 ................................................................................... 291 \n6 \n常言道“要致富，先修路”，没有交通就没有发展 。 \n那么通信网络想要快速发展，满足日益增长的用户需求，就必须建设好承 载网这张通信交通网。 \n承载网作为无线接入网和核心网之间的信息传送通道，包括接入层、汇聚 层和核心层，肩负着各种语音和数据业务的承载需求。 \n而5G时代的到来，又对承载网的道路建设提出了新的要求。 \n第一步：重新规划全网道路——适配5G无线接入网和5G核心网的架构变 化，优化承载网的结构。 \n先丢一张图感受下5G网络架构的变化： \n对于无线接入网：5G的基站重构为AAU、DU和CU三部分，其中DU和 CU可以合一部署，也可以分开部署，根据场景和需求确定。 \n当CU和DU分开部署时，就需要在CU、DU之间部署“中传”承载网。也 就是说，承载网从原来的前传和回传两部分，变成了三部分： \n前传：AAU和DU之间； 中传：DU和CU之间； 回传：CU之上 对于核心网：5G核心网将控制面和用户面彻底分离，让用户面既可灵活 部署于核心网，也可部署于无线接入网。将用户面下移至无线接入网，地理位 置上靠终端更近，可以减小时延。同时，5G还提出了MEC这个多接入应用使 能平台，靠近无线侧提供应用和服务，使得时延更低。从此，5G核心网的用 户面和CU开始在MEC平台协作，共创高速5G网络！ \n为提供更贴近垂直行业应用的服务，核心网用户面下移，MEC的引入， 需要承载网实现回传流量的本地就近卸载，并承载DC之间的核心网流量。5G 无线业务流向更复杂，需要承载网提供更多的业务连接：网络层（L3）功能下 沉到接入层，回传网的架构更加扁平化，同时，引入新技术支持网络的海量连 接，使得5G对不同应用需求实现灵活调度。 \n于是，在5G无线接入网和5G核心网的驱动下，重新规划承载网的道路时， 需包括架构调整、L3功能下沉两大变更动作。 \n第二步：根据场景需求，对道路进行改造--满足5G三大应用场景的需求， 提升承载网的关键性能指标。 \n相信你已经对5G三大应用场景（eMBB、mMTC、URLLC）耳熟能详了。 要让5G承载网服务于三大场景，首先就需具备网络切片功能，让不同的场景 具有各自的独立道路网络！ \n然后，我们通过下面这个表，看看各场景对承载网的道路性能有怎样的改 造需求，承载网又需要作出哪些相应的变更动作呢？ \n由此可见，在三大应用场景的驱动下，对承载网道路进行改造时，需要包 括提供超大带宽、超低时延、高精度时间、灵活调度、网络切片五大变更动作。 \n现在，5G承载网的道路建设方向已经完全明朗！尽管多个方向让承载网 深感压力！不过别太担心，我们承载网可是见过大世面的，经历过2G~4G的 变革，建设了满足各种速率的通信道路，现正引进多种5G新技术（FlexE、 FlexO、SR、高精度时间）直面全新挑战！ \n那我们一起来认识下这些新技术吧~ \n• FlexE（Flexible Ethernet）灵活以太网，可解决物理链路带宽不足和报 文转发时延高的问题，实现带宽扩展和超低时延传输，同时也是网络切片 间实现物理隔离的必备以太网技术。 \n• FlexO（Flexible Optical Transport Netwrok）灵活光传送网，通过绑 定多个标准速率接口，实现超100G OTN高速率信号的传输。 \n• SR（Segment Routing）分段路由, 用于简化L3到边缘的网络控制协议， 提供基于SDN的网络可编程能力，满足海量连接的承载需求。 \n• 高精度时间，采用时间源下沉至前传网等技术实现高精度时间同步功能。 \n“标准以太网”家庭树立了婆媳关系典范 有一座世界闻名的高楼大厦，人们称之为“OSI七层模型”，里面住着幸 福的一家人——“标准以太网”。 \n今天我们要介绍的是“标准以太网”家中的“婆婆MAC”和“媳妇 PHY”。人们常说一个屋檐下婆媳难相处，但这对婆媳却是例外，两人关系相 处融洽，成为了十里八乡家喻户晓的婆媳典范。 \n温馨融洽的婆媳秘密是什么呢？ 2G~4G 时代，“标准以太网”家庭是当地有名的快递世家，婆婆MAC负 责接收客户货物（即业务），媳妇PHY根据货物类型与大小提供对应的输送工 具（即光模块）。婆婆MAC只要一接到客户货物，就喊来媳妇PHY把货物发 出去，送到下一个快递站点。 \n当时在快递行业有个明文规定——货物大小必须与运输工具的能力匹配 （即 MAC和PHY的速率需匹配）。除此之外，客户货物往往是相对小的包裹 （最高不超过100Gbps），且客户需打包成标准型号大小（如10Mbps / 100Mbps / 1000Mbps / 10Gbps / 100Gbps)。运输工具同样依据业界标准 制定，可轻松运输标准大小的货物。婆婆MAC接到10Mbps速率的包裹，媳妇 PHY就安排10Mbps能力的运输工具；婆婆MAC接到100Mbps速率的包裹， 媳妇PHY就安排100Mbps能力的运输工具......多年来，两人的工作配合得相当 漂亮。 \n5G时代即将来临，“婆媳关系”出现了裂缝... \n5G 新业务（如客户超大包裹以及非标准大小的包裹）是婆媳关系出现矛 盾的根源。 为什么这么说呢？5G 时代客户带来了200Gbps/400Gbps 超大的 包裹，还有多种非标准大小的包裹（如25Gpbs/55Gpbs/75Gpbs等）。由于 情况发生太突然，一下把快递站堵得水泄不通。 \n多年来，婆婆MAC和客户直接打交道，深知客户业务需求与发展趋势， 婆婆MAC默默将自己提升到更高的高度，对超大和非标准大小包裹的接收做 好了充足准备。 \n这下可苦了媳妇PHY，因为媳妇PHY以往都通过婆婆MAC来响应需求， 5G的需求变化太大，没有及时传递到媳妇这边。现阶段媳妇PHY能提供的最 大运输能力为100Gbps。 \n5G时代的货物虽然体积大，但利润相当高。在利益驱使下，婆婆MAC擅 自作了决定，承接了客户超大以及非标准大小的业务，快递站库房都堆满了。 然而，媳妇PHY望着满仓库的大件小件欲哭无泪。婆婆MAC开始抱怨媳妇不 会灵活处理，媳妇埋怨婆婆不通情达理，原本一向温馨的“标准以太网”家庭 开始出现了婆媳俩的吵闹声。 \n拯救婆媳关系的男子汉—— FlexE Shim 登场 婆媳矛盾往往不是说婆婆心地不好，就是说媳妇不够孝顺，而我们却忽略 了一个关键人物，那就是家里的顶梁柱——媳妇的老公，婆婆的儿子。家里婆 媳关系不和，其实他负有不可推卸的责任。如果他在中间不发挥积极作用，那 矛盾可能会愈演愈烈。 \n而出身于“标准以太网”家庭中的这位男子汉FlexE Shim学富五车，才 高八斗，看见亲如母女的婆媳两人最近争吵不断。于是他立即去快递站了解详 情，找到了MAC和PHY之间的矛盾根源，并想到了对应的解决办法。 \n儿子FlexE Shim胸有成竹地给MAC和PHY承诺： \nMAC可继续承接超大以及非标准大小的业务包裹。 PHY仅提供已有的这些运输工具即可（最大100Gbps）， 同时等待能力更强的运输工具研制出来。 \n前提条件是儿子FlexE Shim也要到快递站去工作，MAC接收的包裹需先 交给儿子FlexE Shim处理，再由儿子FlexE Shim派给PHY发货出去。 \n婆媳二人听了十分开心，不仅化解了婆媳之间的干戈，还可以一家人在一 起工作。儿子 Flex Shim的加入促使“标准以太网”家庭转变成新型的“灵活 以太网（FlexE）”家庭模式。 \nFlexE Shim如何解决婆媳矛盾？ 儿子 FlexE Shim来到快递站熟悉环境后，安排自己一个仓库管理员的角 色。别看这个岗位名气不大，却在货物中转运输起着举足轻重的作用！ \nFlexE Shim与MAC的工作对接 无论MAC接单的货物是超大还是非标准大小，都会在进入仓库的环节被 FlexE Shim重新打包成5Gbps大小的包裹。同一客户的包裹会标记成相同颜色， 颜色差异作为区分不同客户的依据。例如：200Gbps的大包裹可以分成40个 5Gbps的小包裹，55Gbps的包裹分成11个5Gbps的小包裹。 \nFlexE Shim与PHY的工作对接 为了能最快运输调整大小后的包裹，并充分利用运输空间，FlexE Shim 仔细研究了PHY那边能力最大的100Gbps运输工具，找到了解决办法：将 100Gbps的空间划分成固定的20个5Gbps空间（即时隙），这些空间相互独 立，彼此不影响。 \n在FlexE Shim精心统筹之后，MAC接收的货物与PHY的对接方式发生了 改变：先将MAC的货物打包成5Gbps包裹，再将5Gbps包裹放入PHY的 5Gbps运输空间，充分利用运输工具，提高转发效率。 \n从上图可以看出，无论客户货物是否标准大小都会拆分成5Gbps大小（若 拆分后小于5Gbps，也放入5Gbps空间，剩余空间可再填入其他业务），再放 到媳妇PHY提供的运输工具内5Gbps的空间，快捷运输。 \n接下来，我们重点解析FlexE Shim如何实现超大包裹的运输。 \n1 MAC接到了400Gbps的货物，入库时FlexE Shim将大包裹拆分成 5Gbps的小包裹（共80个），并标记成相同颜色，便于后续区分与识别。 \nFlexE Shim让PHY 提供了4个100Gbps运输工具：表面上4个独立的 100Gbps，在FlexE Shim眼里却是一个整体（FlexE Group），也是一 个能提供80个相互独立的5Gbps空间的资源池。 \nFlexE Shim根据客户需求，指定每个5Gbps包裹对应放置的5Gbps空间， 并为装好包裹的5Gbps空间分配相应的100Gbps运输工具。每一辆 100Gbps的运输工具仅能装20个5Gbps的空间。 \n由于这4个100Gbps运输工具均听从网管控制中心的指挥，行为完全一 致，可以看成1个400Gbps的运输工具单独完成业务的运输任务。 \nFlexE Shim的加入，带来了更多惊喜... \n惊喜一： \n多个运输工具组成的资源池内的5Gbps空间是独立的，即便是相邻空间的 业务，也不会彼此影响，实现了业务安全的严格要求，适合传输专线业务。 \n惊喜二： 对时延、可靠性等有特殊要求的业务，可将包裹直接通过FlexE Shim转 发，即运输途中不再需要MAC参与，减少了业务处理的流程，大大降低了 时延。 \n惊喜三： 通过将100Gbps运输工具进一步细化为5Gbps的小空间，多种非标准速 率业务也能往里面填充，提升了资源利用率。 \n惊喜四： 对于客户业务，可依据客户的要求，将拆分的5Gbps业务包裹放入特定的 5Gbps空间。客户还可以指定运输每个5Gbps空间的PHY，实现了业务定制化 的需求。 \n“人见人爱，花见花开”的FlexE Shim绝对是处理婆媳关系的榜样，他 帮助老妈MAC实现愿望（引入更丰富的5G新业务），为老婆PHY分担工作 （主动加入转运工作，出谋划策让PHY轻松运输各种新业务）。因为FlexE Shim坚信：家庭幸福的秘诀是“家和万事兴”，他通过自己的行动实现了幸 福的灵活以太网（FlexE）家庭。FlexE，加油！ \n收到快递的你欣喜万分，那你知道快递是怎么运送的吗？ \n一个从深圳寄往长春的快递，调度中心根据网点分布规划好路线（深圳- 武汉-北京-长春），并将路线通告到始发站深圳。于是，运输车在深圳扫描 记录，开往下一站。先后到达武汉、北京，并在每个站点扫描跟踪，直到目的 站点长春。 \n整个运输过程中，运输路线由调度中心规划，并将路线下发至始发站。于 是，只需始发站维护这条路线的详细信息，途经站点只需根据路线中转快递， 不需再去重新规划路线。因此，整个过程只需做一次路线规划。 \n对比简单快捷的快递运输，深感当前传输网络依靠M P L S （Multiprotocol Label Switching，多协议标签交换）标签交换技术建立路 线的方式实在有点累！！！ \n因为MPLS建立路线的时候，调度中心要为每个站点都规划路线，并把路 线下发给所有站点，每一个站点除了转发，还都要维护规划的路线及路线状态。 超负荷的工作已经让MPLS累觉不爱了，又如何去为我们期待已久的高速5G贡 献力量呢？ \n那谁来帮疲惫不堪的MPLS减减压呢？谁又来为高速5G出谋划策呢？ \nSR！对，就是她，SR（Segment Routing，分段路由）来了，奔着为 MPLS减压来了，追随着5G的脚步来了！ \nSR是个什么东东呢？ SR是一种基于源路由思路设计的网络转发数据包的协议，提供了一种具 有源路由特性的转发技术，是传输网络的金牌快递。 \n在始发地深圳，调度中心指定运输路径需经过武汉和北京到达长春。这种 在始发站指定路径的方式就是源路由：在始发地（如深圳），SR的调度中心 指定包裹要途经的部分或者全部的城市和路线信息，其他城市只需根据规划的 路线直接转发。 \n因此，SR只需在网络始发点上维护每个业务流的路由信息，其他途经点 只管转发，不需维护连接状态。So Easy！！相对于MPLS标签交换技术创建 路线，SR的优势是不是太明显啦！！ \nSR调度中心的执行者是谁？ 细心的你，一定发现了SR调度中心在整个过程中举足轻重。事实上，SR 调度中心的执行者是SDN控制器。这是一个极其民主的控制器，从来都是先搜 集民意，再下发命令。 \n搜集民意：已获取全网拓扑信息的站点通过BGP-LS，将城市之间的道路 网络、道路使用状态上报给SDN控制器。 \n下发命令：SDN控制器通过PCEP下发Segment List给始发点。 \nSR的秘密武器又是啥？ 悄悄告诉你，SR的秘密武器就是Segment List哦！ \nSegment List是一串SID（Segment Identifier，段标识）组成的叠叠乐， 每一个SID标识着一个Segment。 \n每一个Segment表示传输网络中的节点或链路。深圳、武汉是一个 Segment，深圳到武汉的一条路也是一个Segment。 \nSR调度中心为每个Segment分配一个SID作为Segment的编号。深圳编 号为755，武汉编号为027，深圳到武汉的这条路线编号为1069。 \nSR调度中心叠好一串SID就是Segment List，从深圳到长春的Segment List规划为[027，010，431]。 \nSegment List工作步骤三步走： • Step1：在始发点（深圳），SR调度中心为报文封装一组有序的Segment List，指定包裹需要经过的城市和路线。 • Step2：携带Segment List的包裹到达最外层的城市（武汉/北京）或路线 时，弹出这一层的SID，继续转发。 \n• Step3：直到弹出Segment List中的所有SID，报文到达目的地址（长春）。 \nSegment有两类 前面提到“每一个Segment表示了传输网络中的节点或链路”。那根据 表示对象的不同，Segment分为两大类。 \n用于标识特定节点的Node Segment是Prefix Segment的一种，也是当前 常用的Prefix Segment，用Node SID表示。 \n不同的SID处于网络的不同位置。 \nSID怎么用？ 实际路线规划时，可以将不同的SID组合成以下三种Segment List。 \n接下来聊聊不同组合是如何建立转发路线的。 \n1. 基于独立Prefix SID的转发路线 Tips：由于当前Prefix SID常用形式为Node SID，文中以Node SID为例说明。 \nSDN控制器为网络各节点分配Node SID后，在始发点指定Segment List=[027，010，431] 。 \n• Step1：根据027找到武汉，弹出027，Segment List=[010，431]。 \n• Step2：根据010找到北京，弹出010，Segment List=[431]。 \n• Step3：根据431找到长春，到达目的站点。完成一条SR路线的建立。 \n若SR路线途经的站点太多，Segment List需要封装很多的Node SID，可 能导致报文过长，因此，Prefix SID建立路线通常需借助SPF（Shortest Path First，最短路径优先）算法来帮忙。 \nSDN控制器不在Segment List中指定路线途经的所有站点，仅指定目的 站点及其他要求必经的站点，始发站点与指定点之间的路径由SPF算法来搞定。 如果无必经站点要求，可仅指定目的站点就行。 \n如：SDN控制器在始发点指定Segment List=[431] ，则仅指定了目的站 点。根据SPF算法，规划出到Node SID=431（长春）的路线。 \nØ 若存在多条同度量值的路线，形成ECMP（Equal-Cost Multi-Path routing，等价多路由）等价路线。 \nØ 若不能形成ECMP等价路线，则形成主备路线。 \n这是Prefix SID常用的路线形成方式。优点是借助SPF算法，设置简洁。 缺点是始发点未强制指定所有路由，途经站点不确定。 \n2. 基于独立Adj SID的转发路线 SDN控制器为网络各节点分配Adj SID后，在始发点指定Segment List=[1069，1152，0975]。 \n• Step1：根据1069找到深圳的转出路线，弹出1069，Segment List=[1152， 0975]。 \n• S t e p 2 ：根据1 1 5 2 找到武汉的转出路线，弹出1 1 5 2 ，S e g m e n t List=[0975]。 \n• Step3：根据0975找到北京的转出路线，到达目的站点长春。完成一条SR 路线的建立。 \n此方式的优点是可通过封装多个SID强制指定所有路由，缺点是若途经路 线较多，可能导致报文过长，影响转发效率。 \n3. 基于独立Prefix SID+Adj SID的转发路线 从独立Prefix SID和Adj SID汲取优点，规避缺点。既能控制报文在特定 节点的转发，又能控制报文的长度。 \nSDN控制器为网络各节点分配Prefix SID和Adj SID后，根据规划的路线 需求（要求先经过北京，再经过西安），在始发点指定Segment List=[010， 1074，451]。 \n• Step1：根据最外层的Node SID=010，结合SPF算法，找到站点北京，弹 出010，Segment List=[1074，451]。 \n• Step2：根据1074找到北京的转出路线，弹出1074，Segment List=[451]，到达西安。 \n• Step3：根据451，结合SPF算法，找到目的站点哈尔滨。完成SR路线的 建立。 \n基于独立Prefix SID+Adj SID的转发路线 高速5G，是我们看得见的未来。而SR是5G路由的源泉，以她简单、高效 的路由技术助力5G一起赢未来！ \n36 \n时间同步在通讯网中为什么那么重要 ？ 在我们的日常生活中方方面面都需要遵守时间，比如我们要按时上班、按 时上学、按时赶飞机等等。 这里我们提到的“按时”其实就是按照一个标准 时间，比如通过广播或者电视整点播报的“北京时间”。 \n而通过电视和广播收到的“北京时间”是经过网络传播后 的有一定误差 的“北京时间”。只是这个误差很小，人们通常察觉不到，对日常生活不会产 生严重后果。 \n类似的，在通信网中有一个主时间源，相当于我们常听到的“北京时间”， 主时间源通常精度很高，同步于国家一级授时中心或者国际标准时间等。为保 险起见，网络中可能还有精度稍差的从时间源（类似电视、广播）。通信网络 中的基站，就相当于上图中看表的人，两个人之间同时执行某项行动时需要互 相对表，不同基站之间通信也需要互相核对时间，专业术语称之为时间同步。 基站可以与主时间源进行时间同步，也可以参考从时间源进行时间同步。 \n但是实际上，基站之间的时间同步是存在误差的。而这个误差过大就可能 导致通信异常，比如话音信号延迟、视频图像卡顿。因此为了减小时间同步的 误差，就必须提升时间同步的精度。那提升时间同步的精度有哪些方法呢？从 上图可以清晰看出有以下两种方法： \n提升网络中设备传递时间的精度 \n提升时间源自身的精度 \n5G对时间同步精度提出了更高的要求，时间同步精度从 4G 时代的 ±1.5 µs （1 µs = 10-6 s ）提升到了±130 ns（1 ns = 10-9 s）。这个时间到底有 多快呢？我们人眨眼的时间平均是 0.2 s，130 ns 相当于人眨眼时间的一百五 十万分之一。这样短的时间人眼是无法察觉的，只有精密的仪器和设备可以探 测到。 \n为了全面提升5G的时间同步精度，ZTE提出了3A的时间同步方案。 \n其中，1A 和 2A 用来提升网络中设备传递时间信息的精度，3A 用来提 升时间源自身的精度。下面就分别来解释一下这3个A是怎么来提升时间同步 精度的。 \nAdvanced time stamp，先进的物理层时戳 100 m短跑比赛中，我们需要记录每个运动员的跑步时间。大家都知道， 比赛规则是以发令枪响开始计时，以运动员通过终点线结束计时，这中间的时 间就是运动员的比赛成绩。我们把开始计时和结束计时的时间点称为时戳。 \n同样，在通信网络中，我们需要记录报文在两个设备之间 传输 的时间。 而报文从设备端口发送出去之前，在设备内部需要经过复杂的处理（逻辑上要 通过下图的七层处理），才能进入两个设备之间的物理媒质进行传输。这样， 我们从哪里开始计时和结束计时才能更准确记录报文的 传输 时间呢？ \n当然是从报文进入物理媒质开始计时，离开物理媒质结束计时才更准确， 因为这样就不会把设备内部进行报文处理的时间算上，从而避免产生较大的误 差。也就是说，从进入物理层开始计时、到离开物理层停止计时，这样能够更 精确地记录报文传输的时间，从而提升时间同步的精度。因此，我们需要在物 理层设置时戳。 \nSelf-Adaptive time algorithm，自适应时间算法 现在大型体育比赛为了精确记录运动员的成绩，一般都用全自动摄影计 时系统，而不会用裁判肉眼计时。 \n当发令枪响时, 计时系统开始工作。运动员跑到距终点10米处时，终点摄 影机开机拍摄。 当运动员一窝蜂冲过终点时，摄影机拍下他们通过终点的图 像，再通过终点成绩判读系统，精确到小数点后三位来确定运动员的名次和 最终成绩。 \n通信网络中包含了各种大大小小的设备，设备处理报文的时间是有差异的。 为了消除这些细微差异，ZTE设计了高精度的微电路可以对其进行测量，好比 一台全自动摄影计时系统，可以精确到ps（1 ps = 10-12 s）级别，从而精确 的统计每个报文经过设备的时间，进一步通过软件算法来消除这些细微差异， 提升时间同步精度。 \nAccurate time source, 精确的时间源 假如在培训教室里，老师规定课间休息十分钟。每个学员可以和主时间源 北京时间对时，但是比较难，因为主时间源位于国家授时中心。 \n还有一个经济易行的方法：学员都和培训教室的时钟对准，只要大家参照 物一样，时间就可以协同。这时只要保证教室的时钟和主时间源北京时间误差 足够小，学员们的时间仍可以保证较小的误差。这里培训教室的时钟就相当于 从时间源。 \n同样，在2G、3G、4G网络中，时间源往往放在网络核心位置，类似于上 例中的北京时间。时间信息通过网络一级级传递到基站，这种方式的优势在于 时间精度较高，但是却有三大劣势： \n而在5G网络中，ZTE提出了高精度时间源方案，具体为： \n在网络汇聚边缘位置设备内置从时间源，同时外接GPS或者北斗等卫星接 收器，时间精度可达到Class D。 \n采用高精度时间源方案，可以满足汇聚核心网络为第三方厂家的场景，为 基站提供低成本高精度的时间同步解决方案。 \n3 A 技术总结 最后，我们来做个总结： \nAdvanced time stamp，先进的物理层时戳 \n提升了设备开始和结束计时点的精度，就像短跑中的起止时间 记录精度。 \nSelf-Adaptive time algorithm，自适应时间算法 \n提升了每个报文在设备中进行数据处理的时间长度精度，就像 短跑中可以精确的计算每个参赛选手的时间差距。 \nAccurate time source, 精确的时间源 \n通过在汇聚边缘设备内置从时间源，缩短了只在核心层部署主 时间源造成的传输路径长，误差大的弊端。 \n随着3A技术在5G场景中的应用，我们在手机上看直播、玩手游、聊视频 电话将会非常流畅，丝毫感觉不到卡顿。我们还能享受无人驾驶、远程医疗带 给我们的生活便利。借助3A技术，还可以实现室内3m的定位精度，可广泛应 用于室内精确导航、定位、寻人、儿童监控等场景。 \n文中涉及的缩略语： Advanced Time Stamp，先进的物理层时戳 Self-Adaptive Time Algorithm，自适应时间算法 \nAccurate Time Source，精确的时间源 \nURLLC（Ultra Reliable Low Latency Communication，超可靠低时延通信） mMTC（massive Machine Type Communication，海量机器通信） eMBB（enhanced Mobile Broadband，增强移动带宽业务） \nVR（Virtual Reality，虚拟现实） \n“云诊断、云课堂、云旅游...”背后的力量 近几年疫情肆虐，全民抗疫行动推进了“云诊断、云课堂、云旅游...”的 快速发展，而这些云活动的实现都离不开VR（Virtual Reality，虚拟现实）技 术。为了让用户有更好的云体验，不同级别的VR业务对承载网提出了明确的 时延需求。 \n与此同时，大规模建设的智能电网也对承载网提出了“端到端时延不大于 10ms”的时延要求，工业互联网业务的灵活发展也要求承载网实现“高达μs （10- 6s）级别的实时响应”需求。 \n上述新兴业务对5G承载网提出了以下需求： \nu 业务高度隔离：多业务并行传输时，业务之间不能互相影响，应具备 非常高的安全隔离性。 \nu 灵活带宽：不同业务的带宽需求不同，智能电网的部分业务带宽低至 2Mbps，极致VR则需要高达3~4Gbps的带宽，而工业网互联网的带宽需求 分布于Mbps级别到Gbps级别。 \nu 确定性时延：各业务都提出了ms（10-3s）级甚至μs（10-6s）级的确 定性时延要求。 \n谁来传送新兴业务？ 以承载网当前的传输管道能否应对这些新的业务需求呢？ \n以中国移动为例，当前最火爆的传输管道当属建立在FlexE接口之上的 SPN（Slicing Packet Network，切片分组网）SC（Slicing Channel，切片 通道）。若直接将这些新业务承载在SPN SC管道是否合适呢？（关于FlexE的详 细解读，请参阅《5G时代，以太网家庭幸福的秘诀是什么？》） \nSPN SC管道提供的是N×5Gpbs带宽，这样来看，SPN SC管道足以承载 上述VR、智能电网、工业互联网业务，并且SPN SC管道可以直接在以太网 PCS（Physical Coding Sublayer，物理编码子层）层进行转发，不需上到 MAC层，也能提供确定性的时延。 \n若为了满足确定性的时延，采用5Gbps的管道来传2Mbps的业务，是不 是太浪费资源了？？ \n那是否可以让多个小带宽业务复用5Gbps的管道，让管道资源得到最大利 用呢？ \n基于分组的复用带宽方式虽然可以让带宽资源得到合理利用，但是谁先传 谁后传则依靠业务优先级等多种因素来决定，无法保证每个业务的确定性时延 要求。 \n那如何才能既不浪费带宽资源，又能确保VR、智能电网等业务的高度隔 离性以及确定性时延要求呢？ \n智慧的5G承载网决定为不同的业务各自提供一条专属的（高隔离、不阻 塞，实现确定性时延）、与业务带宽大小相近（不浪费带宽）的完美传输管道。 \n完美传输管道怎么建？ 我们已经知道，FlexE技术在标准以太网的PHY层和MAC层之间添加了一 层FlexE Shim，通过FlexE Shim层将大的物理带宽划分为多个5Gbps时隙，再 将业务放入5Gbps时隙的SPN SC管道进行传送，即可以实现大带宽业务的灵 活传送。 \n受到FlexE这种实现方式的启发：既然FlexE划分的5Gbps对Mbps级别的 业务太大了，可以对5Gbps再进行时隙划分，划分出更小的时隙粒度（Mbps 级别）。5G承载网将这种更小的时隙粒度划分方式命名为小颗粒技术。 \n前面已经提到，为了实现5Gbps的大颗粒时隙划分，FlexE接口在PHY层 和MAC层添加了一层FlexE Shim。那么，要对5Gbps时隙进一步细分，则需 在FlexE Shim层与MAC层之间再增加一层Sub-Shim来承担小颗粒时隙划分 的工作。 \n简单来说，也就相当于FlexE Shim先对物理带宽（PHY）做了一次粗分 时隙（粒度为5Gbps），再由Sub-Shim做二次细分时隙（即小颗粒时隙，粒 度为Mbps级别），然后基于细分后的小颗粒时隙在设备之间建立起端到端的 专用传送管道。不同产品的小颗粒时隙值可能不同，下文以小颗粒时隙为 10Mbps为例进行说明。 \nSub-Shim具体怎么做？ 当MAC接到客户业务时，先将业务交给Sub-Shim，由Sub-Shim先识别 业务所需带宽。 \nu 如果业务所需带宽达到了5Gbps级别，Sub-Shim直接将业务交给 FlexE Shim处理，由FlexE Shim将业务放在5Gbps时隙的SPN SC管道进行 传送。（详细处理方式请参考《5G时代，以太网家庭幸福的秘诀是什么？》） \nu 如果业务所需带宽小于等于10Mbps，Sub-Shim则直接将业务放在一 个10Mbps的小颗粒时隙管道进行传送。业务只占用一个时隙，可称为单时 隙业务。 \nu 如果业务所需带宽远小于5Gbps，但大于10Mbps，Sub-Shim则先 将业务包按照10Mbps的粒度进行拆分，再将拆分后的业务放入多个特定 的10Mbps小颗粒时隙管道中进行传送。业务占用多个时隙，可称为多时 隙业务。 \n● 如带宽为40Mbps，Sub-Shim将这个业务拆分成4份时隙单元，并 将这4份时隙单元分别放入特定的10Mbps小颗粒时隙管道中进行传送。 \n● 如带宽为非10Mbps整数倍，例如45Mbps，Sub-Shim同样将这个 业务拆分成5份时隙单元（4个10Mbps+1个5Mbps共占用这5份时隙单 元），并将这5份时隙单元分别放入特定的10Mbps小颗粒时隙管道中进 行传送。 \n接下来以10Mbps和40Mbps业务为例，说明单时隙业务和多时隙业务是 如何通过小颗粒技术进行传送的。 \n1.单时隙业务的传送 客户1需向客户2传送一个10Mbps业务，途径网元A-B-C组成的承载网。 具体步骤为： \nu 时隙分配：承载网在途径网元A、B、C上为业务分配特定时隙，网元A 分配时隙1，网元B分配时隙1，网元3分配时隙3，则从网元A到网元C就为 业务创建了一条专用的传送管道。 \nu 时隙交叉：客户1的业务送到网元A时，Sub-Shim将业务放入时隙1， 则建立了业务与时隙的映射关系，然后业务被运送到网元B。在网元B， Sub-Shim将业务放入网元B的时隙1，接下来业务被运送到网元C。在网元 C，Sub-Shim将业务放入时隙3，直到送达客户2。 \n我们看到，在网元B处，网元B将业务从网元A的时隙1交叉到网元B的时 隙1。在网元C处，网元C将业务从网元B的时隙1交叉到网元C的时隙3。这种 时隙交叉的操作为业务在网元转发提供了有力支撑。 \n2.多时隙业务的传送 多时隙业务的传送方式与单时隙业务类似，区别在于Sub-Shim需要为业 务分配多个时隙，并且在接收到多时隙业务时需要先将业务拆成多份时隙单元。 例如：客户1向客户2传送一个40Mbps的业务，途径网元A-B-C组成的承载 网。具体步骤为： \nu 时隙分配：承载网在网元A、B、C上均为业务分配了多个特定的 10Mbps时隙，网元A分配了时隙1~4，网元B分配了时隙1、3、4、5，网 元C分配了时隙3~6，即为业务建立了一条从A到C的专用管道。 \nu 时隙交叉：客户1交给网元A一个40Mbps的业务，网元A的Sub-Shim 先将业务拆分为4个10Mbps的小时隙单元。 \na. 网元A的Sub-Shim将这个4个小时隙单元放入已分配好的时隙（1、 2、3、4），则建立起了业务与时隙的映射关系。 \nb. 当4个小时隙单元到达网元B时，网元B的Sub-Shim将这4个小时隙 单元依次放入分配的可用时隙（1、3、4、5），完成业务交叉动作。 \nc. 当4个小时隙单元到达网元C时，网元C的Sub-Shim将这4个小时隙单 元依次放入分配的可用时隙（3、4、5、6），完成业务交叉动作，最终由网 元C将组合后的业务交到客户2。 \n3.小颗粒技术的关键点总结 理解了上述传送过程，我们来总结一下小颗粒技术的几个关键点： \nu 为特定业务分配特定时隙，建立一条端到端基于分配时隙的专用传送管道。 u 端到端传送管道中间节点间的业务转发通过时隙交叉实现。 \n小颗粒技术带来了哪些新体验呢？ u 每一个小颗粒时隙都是独立的，即便是相邻时隙的业务，也不会彼此影响， 能为业务提供一个高隔离传送管道，足够安全可靠。 \nu 为每个业务提供专用的传送管道，不需按照优先级进行等待，能实现确定 性的超低时延传送要求。 \nu 5Gbps理论上可细分为480个10Mbps的小颗粒时隙（开销会占用部分 时隙），能灵活的安排时隙待传送的业务，使带宽得到最大程度的利用，有 效的节约了带宽资源。 \nu 可根据客户需求，将业务放入客户指定的时隙，实现业务定制化需求。 \n小颗粒技术的低时延高可靠，能为我们提供更精准的“云诊断”，更流畅 的“云课堂”，更真实的“云旅游”......让我们无论身处何处，都能开心学习、 快乐工作、健康生活。小颗粒技术，关键时刻保障我们正常生活背后的力量！ \n5G建设，如火如荼。直播带货、在线教育、企业远程办公等新型业务不 断涌现，导致网络带宽需求激增，这给负责承载业务的光网络带来了新挑战和 新要求。光网络作为5G网络建设的高速公路，是5G产业化进程的重要基石。 \n为了应对带宽激增的需求，提高带宽利用率，同时提升用户体验、保证高 效运营，中兴通讯提出了新的光网络方案——智宽新光网。智宽新光网通过新 算法、新平台、新管控的三方面创新，实现光网络的超宽管道、高效承载和智 能调度，满足各种5G业务的连接需求。 \nFlex Shaping新算法，构建超宽高性能传输管道 如何理解超宽高性能传输管道？这里包括超宽和高性能两层含义。超宽指 传输带宽够大；高性能指带宽的利用率最大化，信号传输距离最大化。 \n智宽新光网通过以下三种技术，保证了超宽高性能传输管道的构建： \n使用大容量芯片 目前，光网络中常见芯片有100G/200G/400G，为了增加传输带宽，智 宽新光网采用了业界最大的1.2T芯片，实现超宽的传输管道。 \n扩展频谱资源 智宽新光网通过将频谱资源由传统C波段（80波）扩展到CE波段（96 波），进一步扩展到C++波段（120波），使频谱带宽增加50%；未来将向 C++&L++波段（240波）扩展，能使频谱带宽增加200%。频谱资源的扩展， 使得光纤中可以传输更多数量的波长，从而承载更多数量的业务，提升了光纤 资源利用率，保证带宽的利用率最大化。 \n采用Flex Shaping新算法 智宽新光网基于Flex Shaping新算法，通过电域整形和光域整形的联动方 式对信号进行整形。 \n• 电域整形 为了便于理解电域整形，我们先介绍下星座图、星座点、低能级点、高能 级点这些基本概念。 \n电域整形包括概率整形、几何整形和混合调制三种方式。 \n下面我们再结合一些例子，进一步理解电域整形。如果把星座图比作运输 货物车辆，星座点好比被运输的货物，那么高能级点类似易燃易爆物，低能级 点类似普通货物。 \n• 光域整形 光域整形是中兴通讯的专利技术，通过对频谱进行切片，并调整频谱的衰 减量，降低信号在穿通ROADM站点时产生的光功率损耗，从而提升ROADM 站点的信号穿通数量。 \n在货物运输中，假如普通货车最大能连跑4座城市，这时车辆发动机就需 要停车休息；而光域整形类似于配套了辅助驾驶的高性能车辆，通过辅助驾驶 来提升车辆发动机的工作效率，使得车辆最大能连跑6座城市，从而增加运输 城市，缩短货物运输时间，提高运输能力，节省了成本。 \n采用Flex Shaping新算法后，通过“电域整形”能延长30%的传输距离， 通过“光域整形”能提升50%的站点穿通数量，保证信号传输距离最大化，实 现城域网场景下更多ROADM站点的穿通能力，实现超长距离传输，减少中继 站点数量，降低网络建设成本。 \n全光交叉和超大容量OTN电交叉新平台， 实现业务灵活调度高效承载 有了新算法，货物的传送能力有了大幅的提升，但在运输过程中，大量的 货物如果不能有序调度运输，则会出现资源浪费且效率低下的问题。 \n光网络同样如此，需要具备四通八达运输能力、站内货物实现海量存储及 高效、精准、智能分发能力的“超级中转站”才能精准解决这个问题。智宽新 光网推出由全光交叉和超大容量OTN电交叉组成的新平台闪亮登场！ \n全光交叉平台 传统ROADM方式，单板之间通过物理光纤连接，这样会造成光纤数量多 且连接复杂的情况，而全光交叉平台提供了基于光背板的OXC（Optical Cross Connect，光交叉连接），使得光信号可以通过光背板进行交叉连接， 内纤连接可以基于光背板完成。就好比把光背板想像成一张纸，上面印刷了很 多物理光纤，单板直接通过光背板来互联互通，实现“0”内纤连接。 \n目前，OXC支持32维光方向调度，可实现扁平云化网络连接，保障时延 敏感业务诉求。同时，配合智能光标签技术，OXC能有效避免光纤错连问题， 部署时间从周降至小时。另外，相比传统ROADM单板，OXC集成了光放、监 控等功能，节省约80%占地面积和70%的功耗。 \n如果把ROADM比作传统的中转站，发往各个地区的货物，都是人工方式 去分发；那么全光交叉类似自动化的超级中转站，货物分发都是通过机器去完 成的，这样即节约了成本又提升了效率。 \n超大容量OTN电交叉平台 OTN电交叉采用通用信元交换平台，以ODUk/分组/VC为颗粒进行映射、 复用和交叉，实现信号的无阻交叉调度。通过客户侧单板接入不同业务信号， 经通用信元交换平台处理后，发送给线路侧单板处理输出OTUk信号，实现业 务信号到OTUk转换和调度。 \n超大容量OTN电交叉平台可实现2 Mbit/s~100 Gbit/s带宽按需无损调整， 及多种服务等级按需随选。中兴通讯最先实现64T交叉容量的单子架商用，同 时推出业界最大的192T集群系统，并可升级至384T。 \nSDON新管控，打造便捷智能的光网络 有了新算法和新平台，货物的传送能力和中转能力得到大幅的提升，但在 实际运输过程中，通往终点的道路千万条，货物如何才能以最高效可靠的方式 送达客户手中是个关键问题。 \nSDON（Software Defined Optical Network，软件定义光网络）采用 可编程技术，可以根据容量、距离、速率、拓扑等参数进行智能计算，实时调 整转发平面设备参数，寻找可用路由，实现业务快速开通、带宽分配调整、路 径保护等功能。 \n（有关SDON的详细介绍，可以查阅5G图文《SDON软件定义光网络是什么技术？》） \n通过采用SDON新管控，智宽新光网实现了对网络的规划、管理、控制和 运维实现一体化全覆盖，能更好地提升集中管控和智能运维等能力，减少网络 建设投资，提升网络运维效率和可靠性。 \n总结：新算法、新平台、新管控 最后，我们总结下智宽新光网的三个创新：新算法使用Flex shaping技术， 构建超宽高性能传输管道；新平台采用全光交叉和超大容量OTN电交叉，实现 业务灵活调度高效承载；新管控通过SDON技术，打造便捷智能的光网络，完 美匹配5G各种业务场景。 \n智宽新光网服务5G新基建，支撑5G新基建，为5G新基建按下加速键，助 力5G产业更快的落地，使我们的生活更加美好。 \n为什么要建400G OTN？ 为了方便大家更好的理解，我们把OTN系统比作餐厅的食材运输系统。 \n以前经济不发达，人们解决温饱就行，所以餐馆只需提供简单的当地菜， 食材运输很容易。 \n现在人们有钱了，山珍海味、八大菜系都要尝一尝，足不出户就想吃到各 地美食，餐馆需要长距离运输各地食材。 \n挑 战 \n大量的食材，怎么长距离、 高效、可靠的运输？ \n和长距离食材运输一样，"东数西算"在增强用户体验的同时，也给"算力网 络"带来大量数据迁移的需求，现有的OTN系统能力遇到瓶颈。 \n1 时延高：路由绕行、增加光电光转换等导致时延变高 2 承载小：无法承载400GE客户侧业务 3 成本高：运营成本、维护成本增加 现有10G/100G OTN平面在系统能力、承载效率、维护成本等各方面无 法满足“东数西算”的要求，需要引入高效传输的400G OTN系统。 \n选择哪种400G OTN？ 招聘 饭店举行“铁马三项”搬运工面试选拔， 要求如下： \n跑得远：日行千万里 负重多：一马驮千斤 吃得少：消耗少量粮草能补充满能量 \n第一项：哪匹马跑的最远？ 汗血宝马代表的Real 400G凭借Flexshaping算法，在传输距离比拼中， 一骑绝尘！ \n第二项：哪匹马运货能力最强？ 汗血宝马代表的Real 400G凭借填充波技术等魔法道具进行分身，在传输 能力比拼中再创佳绩！ \n第三项：哪匹马吃的最少？ 汗血宝马代表的Real 400G凭借新一代成帧芯片及光模块在功耗比拼中， 勇夺第一！ \n发放offer 在400G OTN方案中，Real 400G传输距离最远，系统单Gbit功耗最低， 在C+L波段可以实现80*400G传输能力。 \nReal 400G什么时候具备商用能力？ 2022.07 中国移动实验室模拟400G QPSK从浙江到贵州传输验证，跨越 4个省，总距离3,038km。 \n2023.02 中国移动全球首个400G QPSK现网试点，实现45个光放段总长 度为2,808km的超高速传输，同时完成全球首个陆缆5,616km极限传输，创造 400G QPSK无电中继现网传输距离记录。 \n电信集团广州到上海运输，基于国内首条全G.654E陆缆，实现超过 1900km无电中继传输距离。 \nReal 400G经过层层认证，已经具备商用能力，助力骨干网从100G到 400G演进。 \n5G已来，承载助力。5G承载光传送网OTN（Optical Transport Network）相当于5G端到端网络的高速公路，是整个5G产业化进程的重要基 石，它为高清视频、AR/VR等5G业务提供硬管道、大带宽、低时延的优质管 道服务。 \n而随着5G产业中政企专网、金融专网、在线教育等品质专线的快速发展， 这些专线业务的特点是数量多、带宽小、且需带宽无损调整，这样OTN技术的 硬管道、带宽利用率低的缺点，就无法为专线业务提供高效承载服务。 \n在这种背景下，光业务单元OSU（Optical Service Unit）技术应运而生， 为专线业务提供小颗粒的灵活管道，及无损的带宽调整机制。 \nOSU技术是在OTN技术上新增了OSU容器，通过OSU容器把信号按照 Nx2.6 Mbit/s（N=1,2,3......）进行拆分，可以实现2 Mbit/s到100 Gbit/s的 多种颗粒业务接入和传输。下面我们通过OSU技术和OTN技术的三个对比， 为大家详细介绍OSU技术。 \n对比一：小颗粒业务VS大颗粒业务 • 小颗粒业务：在OTN技术中，速率为2 Mbit/s到1.25 Gbit/s的业务。 \n• 大颗粒业务：在OTN技术中，速率为1.25 Gbit/s以上的业务。 \n例如，常见小颗粒业务有2 Mbit/s，155 Mbit/s、500 Mbit/s、1.25 Gbit/s等；大颗粒业务有2.5 Gbit/s、10 Gbit/s、100 Gbit/s等。 \nOTN技术，采用固定时隙方式划分业务，即业务被划分为ODUk （k=0,1,2,3,4）颗粒，时隙中最小颗粒是ODU0（1.25 Gbit/s），最大颗粒 是ODU4（100 Gbit/s）。同时，我们可以得出一个ODU4最大支持80个 ODU0，即ODU4=80×ODU0，那么单个100G线路接入用户的数量最大80个。 \n固定时隙方式类似于我们日常邮寄快递用的纸箱包装，它只能提供5种 纸箱规格，这样小件鞋帽采用小号纸箱ODU0，中等衣服采用中号纸箱 ODU1/ODU2/ODU3，大件玩具采用大号纸箱ODU4，通过大小不同的纸箱 来满足邮寄需求。 \n同样，不同的ODUk用于传输各种速率的业务，比如ODU0传输1GE以下 业务，ODU2传输10GE业务。OTN技术传输小颗粒业务，会造成带宽浪费， 所以更适用于大颗粒业务传输。 \nOSU技术，采用非固定时隙的净荷方式划分业务，即业务按照净荷块PB （Payload Block）划分颗粒，一个PB为2.6 Mbit/s，即业务被划分为 N×2.6 Mbit/s（N=1,2,3,4......）颗粒。按照国际标准N最小值是1（即最小 颗粒2.6 Mbit/s），那么单个100G线路接入用户的最大数量4000个。 \n还是以纸箱包装来类比，非固定时隙的净荷方式相当于对包装箱的进一步 细化，可以根据物件大小来提供任意包装箱，通过各种制定化的包装箱服务， 使得物件与包装箱一一匹配。 \nOSU技术通过灵活设置PB的数量，来满足不同客户的业务带宽需求。例 如设置1个PB（即2.6 Mbit/s带宽）时，OSU可以高效承载2M客户业务；设 置52个PB（即135.2 Mbit/s带宽）时，OSU可以高效承载100M客户业务。 OSU技术支持大颗粒业务传输，但更适用于小颗粒业务传输。 \n通过上面的介绍，我们知道采用OTN 技术，单个100G线路接入用户的最大数量 80个；而采用OSU技术，单个100G线路接 入用户的最大数量4000个，这样OSU技术 与OTN技术相比，业务连接数提升了50倍， 解决了专线业务数量多的需求。 \n对比二：硬管道VS灵活管道 承载网中，管道为业务提供端到端的传输通道。 \n• 硬管道即刚性管道，其传输通道是固定的时隙颗粒。 \n• 灵活管道即定制管道，其传输通道是灵活的时隙颗粒，可以按照业务 需求定制带宽大小。 \nOTN硬管道方式，是以ODUk的固定时隙进行带宽捆绑，而专线业务为几 百兆以下带宽需求，即便使用ODU0（1.25 Gbit/s）带宽传几百兆业务，也会 造成带宽浪费且利用率不高。 \n这种硬管道方式，好比一个大纸箱装了几颗鸡蛋，即便用最小的ODU0纸 箱装，也会因为鸡蛋太小（且太少）而纸箱太大，出现纸箱装不满，造成空间 浪费。 \n当前，OTN技术也通过增加业务单板，提供VC交叉和分组交换的功能， 承载小颗粒业务，解决带宽浪费的问题。但是这种方式需要引入额外的VC交 叉（STM-16容器等）或者分组交换（MPLS隧道等），采用多级映射传输业 务，不便于运营维护，同时业务单板数量增加，会造成运维成本增加。 \n同样是纸箱装鸡蛋，采用VC交叉和 分组交换功能后，需先把鸡蛋进行分类打 包，再一起装到同一个ODUk的纸箱中， 这种方式虽然可以节省带宽避免空间浪费， 但是包装次数增多了，且各类鸡蛋要放在 同一个纸箱中一起运输，不便于区分。 \nOSU灵活管道方式，通过灵活设置PB的数量，来满足不同客户的业务带 宽需求。通过周期内占用时隙数量控制带宽，业务颗粒更加灵活，带宽利用率 更高。 \n再说纸箱装鸡蛋，OSU技术可以根据鸡蛋数量的多少，直接匹配大小一 致的纸箱装备，完全做到定制化需求。比如6颗鸡蛋一个包装，15颗鸡蛋一个 包装，100颗鸡蛋一个包装，即做到了独立包装，又减少了包装次数，可以满 足各种需求。 \n对比三：带宽灵活调整VS带宽无损调整 带宽调整指根据用户需求，可以随时调整传输通道的带宽大小。 \n• 带宽灵活调整指OTN技术中，通过调整ODU0数量来实现带宽调整。 • 带宽无损调整指OSU技术中，通过调整PB数量来实现带宽调整。 \n带宽灵活调整的调整步长为ODU0（1.25 Gbit/s）的业务颗粒度，调整 范围为1.25 Gbit/s~100 Gbit/s。 \n带宽灵活调整时，按照业务流逐个节点下发调整命令，整条链路调整操作 严格同步，以固定速率慢慢变更带宽，造成调整时间长。 \n带宽无损调整的调整步长为2.6 Mbit/s的业务颗粒度，调整范围为2.6 Mbit/s~100 Gbit/s，这样OSU支持更小的调整步长，及更大的调整范围。 \n带宽无损调整时，源端节点调整PB数量后，宿端节点自动跟随并适配源 端节点调整，实现在源端单点下发调整命令，宿端自动调整，从而简化调整流 程，同时单点下发调整命令，节约了多节点协调下发命令的时间，极大地缩短 了调整时间。 \n总结：OSU技术和OTN技术 通过三个对比，我们了解了OSU技术和OTN技术的区别。这里我们总结 一下，OSU技术是对OTN技术的补充，OTN技术用于大颗粒业务的传输需求， 而OSU技术针对政企品质专线，解决小颗粒业务的需求，二者互相结合，可 完美覆盖各种5G业务场景。 \nOSU技术为5G品质专线提供灵活的带宽需求，能够满足不同客户的差异 化需求，将会在政企专线的承载市场上扮演重要角色，让我们在智能化的网络 中畅游，享受5G带来的便利。 \n都说“5G建设，承载先行”，而5G承载网的光传送网是5G网络的高速公 路，是整个5G网络数据运输的动脉！在我们的身体里，动脉将血液送到身体 的各个器官，以维持生命活动。那在5G网络中，光传送网则负责将5G大带宽 数据运送到通信网络的各个用户。 \n光传送网（Optical Transport Netwrok，简称OTN）是一种波分复用技 术，它将不同波长的光信号复用到光纤中传输。如果将光纤比作将道路，那么 不同波长的光信号可以比作不同车道上行驶的汽车。 \n我们知道，高速公路在综合考虑路基及桥梁建设成本和通行效率后，在高 速公路入口设置了限重要求，禁止通行超重车辆。类似的，光传送网将通过光 模块的速率匹配来判断光信号的通行。下面我们详细看看光模块是如何处理光 信号通行的？ \n在3G/4G时代，我们刷手机微信、进行网购、看视频或者视频聊天，其 实这些速率小于100G的数据是通过光传送网的100G光模块进行接入并传输。 \n而未来5G时代，超高清视频、互动视频及AR&VR的快速发展，将会出现 超100G的数据，但是超100G的数据已超过100G光模块的接入带宽，光模块 会禁止数据通行。这就要求光传送网的光模块接口速率更大，同时光传送网间 的带宽也要从n*100G升级为m*100G（m>n），这样光传送网才能适应5G的 业务变化。 \n那光传送网怎么才能实现超100G业务的传输呢？我们可以想到有两种 方案： \n1. 将100G光模块更换为高速率的200G/400G/1000G光模块。 \n2. 对现有100G光模块辅以一定的技术手段，使其能传输超100G的业务。 \n当前现状是200G/400G光模块成本太高，1000G光模块还不见踪影， 而100G光模块的低成本优势就脱颖而出了，所以暂时只能放弃方案1，选择 方案2了。 \n今天主角FlexO的加入，既可规避高速率光模块成本高的问题，也能解决 超100 Gbit/s业务在现有100G光模块中传输受限的问题，未来还能够平滑扩 展更大带宽容量。 \n那FlexO到底是何方神圣呢？ FlexO是一种用于信号互联的接口技术，通过绑定多个标准速率接口来实 现超100 Gbit/s业务的传输，这样物联网、无人驾驶、AR/VR等高速业务就 能轻松进入光网络传输啦！ \n这里的信号互联指OTUCn信号互联，OTUCn信号即传输速率超过100 Gbit/s的OTN信号。 \nFlexO又是如何工作的呢？ 拆分信号，将OTUCn信号拆分成n个OTUC1信号。 \n目前，100G光模块支持100G速率的OTUC1信号，所以一个OTUCn信号 可以被拆分为n个OTUC1信号，即OTUCn=n×OTUC1。 \n例如，一个OTUC4信号拆分为4个OTUC1信号，即一个400G的OTUC4 信号拆分为4个100G的OTUC1信号。 \n如果把OTUC4信号比作一个大货物，OTUC1比作小货物，那么通过对货 物的拆分，将大信号拆成小信号。 \n建立FlexO映射，将拆分后的OTUC1信号进行速率匹配。 为了传输OTUCn信号，FlexO定义了帧结构FlexO Frame。建立FlexO映 射，就是按照速率将OTUCn信号与FlexO Frame进行匹配，建立映射关系。 \n目前，FlexO Frame仅支持100G速率，即一个100G FlexO Frame只能匹 配OTUC1信号；那么一个OTUCn信号需要映射到n个100G FlexO Frame。 \n如果把OTUC1信号比作货物，100G FlexO Frame比作货箱，那么FlexO 映射就是把货物打包到相对应的货箱。 \n绑定多个光模块，将n个100G FlexO Frame绑定到n个100G光模块进行 传输。 \n例如，一个OTUC4信号，在映射为4路100G FlexO Frame后，就需要绑 定给4个100G光模块进行传输。 \n如果把100G FlexO Frame比作货箱，100G光模块比作汽车，那么绑定 就是把货物装到汽车来运输。 \n现在，我们知道了OTUCn信号通过拆分信号、建立FlexO映射、绑定多 个光模块三个步骤，就实现了OTUCn信号在100G光模块的光传送网进行接入 和传输。 \n同理，当接收端接收到信号后，通过解绑定多个光模块、解除FlexO映射、 复用信号三个步骤，就将拆分的n个OTUC1信号还原为一个OTUCn信号。 \n这样就实现了OTUCn信号的端到端传输。 \nFlexO已为大容量5G业务的到来铺好道路，只等5G大驾光临，带给我们 更智能的生活体验。不久，当你使用VR进行购物体验时，通过FlexO技术就可 同时高速接入多种购物链接，对比选择，显著提升你的用户体验，让你在智能 化的高速网络中畅游，享受5G带来的便利。 \nFlexO通过多路绑定技术实现5G高速信号的传输，使得5G光传送网这个 “ 大动脉” 变得更强大、更灵活、更智能。在不久的将来，高速率 200G/400G光模块成熟后，通过FlexO技术还可以将OTUCn信号拆分为m个 大颗粒的OTUC2/OTUC4信号，映射到200G/400G FlexO Frame，最后采用 多路光模块绑定实现更高速率（T级别）的接入传输。让我们一起期待吧！ \n现如今，通信网络的飞速发展，时时刻刻影响着我们的生活。就在我们忙 着刷微信、网购、看视频时，自动驾驶、高清视频、AR/VR等新应用已经悄 悄的来到了我们身边。 \n而这些新应用的不断出现，需要承载网提供更大的带宽来支撑大量数据的 传输。波分复用技术正是为更大的承载带宽而生的。 \n波分复用技术将不同波长的光信号，在发送端经合波器把光信号复用在一 起，在同一根光纤中进行传输，在接收端通过分波器将光信号解复用，合波器 和分波器统称为复用器。波分复用技术利用同一根光纤中的多个通道来传输多 个波长信号，增加了承载网的传输带宽。 \n如果将光纤比作高速公路，传输通道比作车道，不同波长的光信号比作不 同彩色的汽车，复用器比作检查站，那么彩色汽车通过检查站选定车道并汇入 到高速公路，再从高速公路驶离，就实现了波长信号端到端的传输。 \n为了适应网络的发展，波分复用技术的复用器也经历了三次技术升级。 \n1. OTM（Optical Terminal Multiplexer，光终端复用器） \n2. FOADM（Fixed Optical Add/Drop Multiplexer，固定光分插复用器） \n3. ROADM（Reconfigurable Optical Add/Drop Multiplexer，可重构光 分插复用器） \nOTM实现相邻两站点间，波长信号在固定传输通道和固定传输方向的上 路（发送）和下路（接收），仅支持点到点组网。OTM这种固定传输方式称 为波长相关上下路和方向相关上下路。 \nOTM好比一条没有分叉口的直通路，A点发出的彩色车辆和检查站通道 进行颜色匹配，即黄色车走黄色通道，经特定车道到达B点，实现波长信号的 点到点传送。 \n为了解决OTM中无法分插波长信号的问题，FOADM在OTM的基础上， 通过中间站点来分插波长信号，实现某些波长信号的上路（发送）、下路（接 收）和直通，支持链状或者环状组网。 \nFOADM好比在直通路上增加了交叉路口，这样A点发出的车辆经过检查 站进行颜色匹配，经特定车道达交叉路口进行分插，把绿色车辆汇入到B点， 黄色车辆分离到C点，红色车辆直行到B点，实现车辆的汇入、分离和直行。 \n可以看出，FOADM这种波长相关&方向相关的传输方式，会导致当波长 信号部署完成后，如果要对波长信号进行调整时，需要安排人员现场调整光纤 的传输通道和传输方向，这样造成运维工作量很大。由此，可以总结出 FOADM的三点不足： \n波长相关，传输通道固定。 \n方向相关，调度不够灵活。 \n维护时需人工调整光纤，网络运维难度大且成本高。 \n为了解决FOADM的不足，ROADM应运而生。 \nROADM是可重构光分插复用器，可以远程对波长信号进行配置，动态指 配每个波长信号的上路、下路或直通，实现波长信号的灵活调度。 \n从上面的定义中我们可以得出ROADM具有远程和动态这两个特性，远程 指通过网管远程配置波长信号，动态指波长信号可以任意调度。那么ROADM 是如何实现远程和动态？ \n要想解答这个问题，必须提到在ROADM中当前主流使用的核心器件 WSS（Wavelength Selective Switch，波长选择开关），WSS采用光开关 阵列，将波长信号分插到任意通道进行传输。 \n远程，指通过网管远程对WSS进行配置，实现对波长信号的远程配置。 这样当需要改变波长信号的传输通道或传输方向时，可以远程调整，免去了人 工现场调整光纤，节约了运维成本。 \n动态，指在网管上对WSS的每个波长信号进行配置，使得波长信号可以 进入任意的传输通道，并把波长信号指配到任意一个方向输出，实现任意波长 信号在任意传输通道和任意传输方向（即维度）的动态调度。 \nROADM这种任意调度的方式称为波长无关上下路和方向无关上下路，从 而解决了FOADM波长相关&方向相关带来的问题。 \n如果把WSS比作环岛路，那么采用了WSS的ROADM可以远程对环岛路 的车辆进行调度，这样从A 点发出的车辆，走任意的传输通道，到达 B/C/D/E/F的任意方向，实现波长信号的任意调度。 \n由此得出，ROADM实现了远程对波长新信号的任意调度，有效提高运维 效率。 \n到这里，我们把这三种技术做个总结： \n• OTM是波长相关&方向相关，只能实现波长信号点到点的固定传送。 \n• FOADM是波长相关&方向相关，可以实现波长信号在中间站点的固定传送。 \n• ROADM是波长无关&方向无关，能够实现波长信号在任意通道任意方向的 传送，完成波长信号的灵活调度。 \n显而易见，ROADM进一步提升了业务调度的灵活性，为我们带来了以 下三大收益： \n• ROADM免去了大量的人工调整光纤，有效提高运维效率，降低运营成本。 \n• ROADM支持波长信号多个方向的灵活调度，实现了端到端业务的快速开通。 \n• ROADM推动了波分网络由点对点组网向网络全连接演进。 \nROADM从提出到现在，短短的十年中，从最初的2个维度到现在最大支 持30个维度的调度，一直在不停的完善发展，它使得承载网更智能、更灵活， 从而满足自动驾驶、高清视频、AR/VR等新应用。 \n随着超高清视频、互动视频及AR&VR等新型业务在5G时代的出现和增长， OTN（Optical Transport Network，光传送网）需要实现更加智能化的管理、 并灵活提供适配带宽，以应对部署、运维和资源等方面的压力。而现有OTN的 刚性特征，难以满足这些新的网络要求： \n通过在现有OTN中引入SDN（Software\u2002Defined\u2002Network，软件定义 网络）技术，将控制平面与转发平面分离、网管和SDN控制器融合，实现集中 管控和统一调度，使得光网络更加高效、灵活和智能，这就是SDON （Software\u2002Defined Optical\u2002Network，软件定义光网络）技术。 \n在现有OTN中，传统网管对整体网络缺乏实时和主动控制。而SDON中 SDN控制器负责监控网络中业务资源的变化，并通过一系列智能算法持续地优 化业务的路径、带宽等，以满足业务需求。 \n如果把现有OTN比喻成路，那传统网管就是路上的摄像头，堵车还是封 路，摄像头都只看不管，必须要交警（运维人员）到现场疏导交通，才能保证 路线畅通。 \n与现有OTN相比，SDN控制器更像是智能交通管控中心，基于实时更新 的全局线路实时模型，对当前路况了如指掌，自动管控着路上的一切，不需要 运维人员过多参与，就能通过整合全网资源，实现弹性SDON网络，在业务开 通、带宽分配、可靠性等方面带来一些全新特性： \n下面我们了解下这些特性在SDON中是如何实现的。 \n假如你是实力派大咖，下周每天20:00开始，要从北京西苑饭店去奥体中 心演出。工作人员决定自己先开车去奥体中心，确保以最短的时间从西苑饭店 到达奥体中心。SDON根据当前的路况和车流量情况，通过智能算法，规划一 条用时最短的路线，工作人员就开车上路了。不巧，路上遇到拥堵，SDON马 上重新规划出新的路线，并通知工作人员切换到新的路线上。就这样实现了 SDON的业务快速开通和动态路径保护。 \n巡回演出开始了，你太热心，工作人员和前呼后拥的粉丝，你都要带上。 SDON基于全局线路实时模型提前计算好线路规划，并设置预约时间（即业务 预约开通）。20:00一到，SDON自动开通业务，SDON根据工作人员和粉丝 的人数分配所需数量的专用大巴，相当于为业务分配带宽，顺利把你的团队从 西苑饭店载到了奥体中心。这就是SDON的带宽按需分配和业务预约开通。 \n巡演2天之后，大巴经常没坐满，大巴的费用又贵，于是你想省钱，人多 用大巴，人少用中巴。既然有了新要求，那SDON可以直接授权给你，你就可 以根据实际情况自行调整车辆（带宽自动调整）。如此，SDON完成了带宽自 动调整，提高资源利用率。 \n如果赶上雨季，西苑饭店到奥体中心的一段必经之路出现了塌方。SDON 一看，大巴、中巴是过不去了，只有小车还能走。于是SDON马上通知你们换 小车，虽然慢，好歹还能窜起来。这样，SDON就保证了业务连通性，实现了 动态路径保护。 \n控制平面与转发平面分离、管控融合，使OTN已经从原始的网管集中手 工配置，发展到现在的SDON智能管控。未来，随着网络的不断扩大、业务的 多变性，网络和业务的管理控制将变得更加复杂，对网络的智能化也有了更高 的要求，SDON进一步和AI技术融合是SDON的发展趋势，未来的SDON将具 备学习网络历史数据并进行推理和预测的能力： \nSDON的不断成熟和演进，将带给用户更智能、更简捷、更高效的光网络， 将为5G网络带来更友好的用户体验。 \n商家天天推出各种优惠活动，作为剁手党的我立刻开启了买买买模式，一 小波包裹已在途中。我每天都怀着迫切的心情刷几遍物流信息，盼望着各快递 站点加速收发，尽早将包裹送到我手中。其实，在支撑我们网购的Internet网 络中，也有收发数据包的快递站点——路由器。 \n什么是路由器？ 路由器是连接两个或多个网络的硬件设备，工作在TCP/IP协议的网络层， 在网络间扮演网关的角色。路由器的一个作用是连通Internet中各局域网、广 域网，另一个作用是选择数据包的最佳传送线路。 \n想要深入了解路由器，要从路由器的内部结构讲起了。 \n路由器由输入接口、输出接口、交换结构、路由选择处理器四个部分组成。 执行两个最重要的基本功能：路由功能和交换（转发）功能。相应的路由器内 部整体也分为路由选择和分组转发两个部分。 \n• 路由选择部分工作在控制层面，主要由软件实现。核心组件是路由选择处 理器，通过运行路由协议维护路由表以及连接的链路状态信息，并生成转发表。 • 分组转发部分工作在数据层面，主要由硬件实现。核心组件是处理芯片和 交换结构，交换结构是一个路由器中的网络，将路由器的输入接口和输出接口 相连接。依据转发表来转发分组数据包，将输入接口的数据包移送至适当的输 出接口（在路由器内部进行）。 \n路由表：可以是管理员手工配置的，也可以通过动态路由协议自动学习形 成，通常由路由协议和路由管理模块维护，包括IP地址/IP子网、下一跳、路 由优先级、度量值等信息。 \n转发表：是基于路由表生成的，路由器实际转发时使用转发表，包括IP地 址/IP子网和下一跳/出接口信息。 \n路由器根据IP数据包的目的网段地址查找路由表决定转发路径，路由表记 载着路由器所知的所有网段的路由信息。路由信息中包含要到达此目的网段需 要将IP数据包转发至哪一个下一跳相邻设备地址。而转发表记载着由哪个接口 发出。转发表中每条转发项都指明分组到某个网段或者某个主机应该通过路由 器的哪个物理接口发送，然后就可以到达该路径的下一个路由器，或者不再经 过别的路由器而传送到直接相连的网络中的目的主机。 \n有了路由表和转发表，就好比快递站点根据包裹目的地查到了下一步要将 包裹送到哪一个站点，由谁来负责发出。这样本站点的工作就可以顺利完成了。 \n值得一提的是，路由表被存放在路由器的RAM上，这就意味着路由器如 果要维护的路由信息较多时，必须有足够的RAM，并且路由器重新启动后原 来的路由信息都会消失。 \n路由器是如何传送数据的？ 数据包在网络上的传送就好像是快递公司运送包裹一样，通过多个快递站 点的收发最终将包裹送到客户手中。类似的，每一个路由器只负责本站数据包 通过最佳路径转发，通过多个路由器一站一站的收发将数据包通过最佳路径转 发到目的地。（如果实施路由策略，数据包不一定选择最佳路径） \n每个路由器都有多个输入接口和多个输出接口，它的输入接口收到数据包 后去除数据链路层封装，交给网络层处理。网络层首先检查报文是否是送给本 机的，如果是，去掉网络层封装，送给上层协议处理。如果不是，则根据报文 的目的地址将报文交给相应输出接口的数据链路层，封装接口对应的链路层协 议后，将报文发送给下一个路由器。下一个路由器也重复此动作，直至传给路 径上最后的路由器，再由其将数据包送交目的主机。 \n怎样实现数据包的最佳传送？ 讲到这里我们发现，怎样实现“最佳路径”是个关键，别急，路由器是有 自己的选路原则的。快来一起了解下吧。 \n我们先从了解IP数据包开始。IP数据包结构如下： \nIP数据包中的目的IP地址是IP数据包的重要字段，路由器会根据数据包中 的目的IP地址查找路由表，决定数据包的转发方向。 \n路由器在决定数据包转发路径的时候会依次按照三大规则来选择路由，以 确保实现“最佳路径”。 \n1、最长匹配原则 \n最长匹配原则就是在路由查找时，使用路由表中到达同一目的地的子网掩 码最长的路由。如下所示，去往********的数据包在路由表中同时有3条路由 可以为此数据包进行转发，分别是20.0.0.0、********和********。由于它们 依次匹配到了网段的前8位、16位、24位，根据最长匹配原则，去往******** 的数据包会用********的路由条目进行转发，也就是从接口gei_0/1/0/3进行 转发。 \n2、路由优先级 一台路由器上可以同时运行多个路由协议。不同的路由协议都根据自己的 标准来选择路由，有的采用下一跳次数、有的采用带宽，并且每个路由协议都 把自己认为是最好的路由送到路由表中。这样到达一个同样的目的地址，可能 由多条分别由不同路由协议学习来的路由，路由器必须选择其中的一条路由加 入到路由表中。由于这些路由的子网掩码相同，最长匹配原则已无法用来挑选 路由，路由器应该如何做呢？这时该路由优先级上场了。 \n路由器上的不同的路由协议默认有自己的路由优先级，数值小的优先级高。 当我们有到达同一个目的地址的多条路由时，可以根据优先级的大小，选择优 先级数值最小的作为最优路由，同时将这条路由写进路由表中。 \n3、Metric值 在路由器中，路由协议会学习到所有可能的路由，当同一个路由协议学习 到的路由不止一条时，路由优先级就不管用了，路由器该如何处理呢？这时轮 到Metric值出场了。路由协议根据选路算法赋予每一跳一个metric值，每条路 由的metric值等于路径上每一跳metric值的和。比较各条路由的metric值，选 择metric值最小的路由为最佳路由。 \n例如，R1到R4有三条路由：R1-R2-R4、R1-R4和R1-R3-R4，R1- R2-R4路由的metric值为20（10+10），其他两条路由的metric值为30，路 由器会优先选择R1-R2-R4作为最佳路由。 \n讲完路由，我们再来谈谈转发。传统的IP转发表已不能满足电信骨干网的 高速转发需求，为了提升转发效率，路由器采用MPLS技术，在路由表的基础 上生成MPLS标签转发表，MPLS为每个IP数据包提供一个标签，与IP数据包 一起封装到新的MPLS数据包，标签决定IP数据包的传输路径以及优先顺序。 通过MPLS标签转发数据，路由器只需读取数据包中的MPLS标签，无需读取 每个IP数据包中的IP地址信息，因此数据包的转发速度大大提升。 \n5G网络正在如火如荼地建设，网络中的数据量也会越来越大。路由器将 会与SR、SDN等技术结合，提供更为简捷、高效的数据传送。 \n2017年11月，中共中央办公厅、国务院办公厅印发了《推进互联网协议 第六版（IPv6）规模部署行动计划》。 \n2019年4月，工业和信息化部为深入贯彻落实行动计划开展了专项行动。 \n2021年7月，工业和信息化部、中央网络安全和信息化委员会办公室印发 了《IPv6流量提升三年专项行动计划（2021-2023年）》。 \n不仅部署了行动计划，还连续安排专项行动，IPv6让国家如此重视，为 啥呢？ \n想要弄清楚这个问题，需要先来了解下什么是IP。 \n互联网协议IP（Internet Protocol）主要定义了在相互连接的网络中如何 传递数据。网络中的每一个设备（如计算机），要想和其它设备相互通信传递 数据，就都需要一个像门牌号码一样的标识——这个标识也就是我们熟悉的IP 地址。 \n目前被广泛使用的IP协议是IPv4(IP Version 4)，网络设备使用的地址即 为IPv4地址。 \nIPv4地址长度为32 bit，因此能提供的地址最多为2的32次方（约43亿） 个地址。但是除去一些预留地址以及用于广播、测试、私有网络的专用地址外， 实际可用地址是不足43亿的。 \n互联网发展了这么多年，IPv4地址一直被分配使用。2019年11月25日， 负责英国、欧洲、中东和部分中亚地区互联网资源分配的欧洲网络协调中心 （RIPE NCC）宣布，全球所有 43 亿个 IPv4 地址已全部分配完毕。 \n随着万物互联的5G应用日趋成熟，网络的需求和功能正变的越来越复杂。 除了面临地址枯竭的问题，IPv4网络的缺点和局限也越来越明显。 \n因此，不论是国家、行业还是个人，对IPv6（IP Version 6）都有着迫切 的需求并支持推广： \n• 世界上主要的国家和经济体都在力争尽早完成IPv6网络的建设，争取网络 的率先应用和技术领先（如开篇提到的行动计划和专项行动）。 \n• 市场上大量移动终端、固定设备、应用网站及APP等均已支持IPv4和IPv6 两种协议。 • 智能家居的普及，家庭和个人拥有了众多智能设备，对这些设备的端到端管 理，需要IPv6技术的支持。 \nIPv6技术出现的首要目的是解决地址短缺问题。IPv6地址长度为128 bit， 能提供的最大地址数量为2的128次方，比IPv4地址多出2的96次方倍。这个 数量已经大到无法想象，一个很形象的比喻是——IPv6几乎可以为地球上的每 一粒沙子都分配一个地址。 \n与IPv4地址相比，128位的IPv6地址更长。为了便于记忆和使用，通常将 IPv6地址写成8组，每组为4个十六进制数的格式，中间用冒号隔开，这就是 冒分十六进制表示法。 \n例如一个合法的IPv6地址表示如下： \n1010:0000:0000:102F:054A:30D9:123B:0001 \n每组起始的0可以省略不表示 \n1010: 0: 0:102F: 54A:30D9:123B: 1 \n全为0的组可以用::表示，只可用一次 1010::102F:54A:30D9:123B:1 \nIPv6地址不仅仅是带来了海量的地址资源，同时还对IPv4的“缺点”进 行了全面的改进。 \n更小的路由表 n IPv6地址采用层次化结构，一条记录表示一片子网，缩小了路由表规 模，提高了转发性能 \n更灵活的报文头 n 报文头包含可选项，长度不确定，复杂且处理速度慢 \nn 基本报文头固定长度，简单且处理速度快，扩展报文头根据需要灵活 扩展 \n更简单的网络 n 需要维护大量NAT设备，且无法点对点直接通信 \nn 无需NAT设备，点对点直接通信 \n更好的移动性支持 n IPv4无法为每个设备提供地址，且移动切换后地址会变化 \nn IPv6可以为每个设备提供地址，允许移动切换后保留地址 \n除了以上这些优点，IPv6还增强了安全性、自动地址配置、组播及流量 控制等功能，以及对SDN/NFV应用提供了更多支持。 \nI P v 6 的发展现状 对比IPv4，IPv6明显优秀的多。但是爱思考的朋友一定会产生这样的疑 问——既然IPv6这么好，从协议提出到现在也发展了这么多年，为什么一直没 有被大规模的应用呢？ \n这是因为技术的更新换代从来都不是那么容易的事。 \n• 一方面因为NAT（Network Address Translation）技术的应用，延缓了 IPv4地址枯竭的速度。NAT通过地址转换可以使多个设备共享一个IP地址 来访问互联网，节省了部分地址资源。 • 另一方面网络升级到IPv6的成本巨大。从IPv4到IPv6的升级，不仅仅是更 改下地址那么简单，而是涉及到网络的方方面面。从个人终端到运营商网络， 从各种应用软件到网站服务，都需要全面改造升级。 \n正因如此，从IPv4到IPv6的升级不是一次性完成的，而是需要逐步、分 层次地推进。在过渡期间，为了保证IPv4与IPv6能够共存、互通，出现了一 些过渡技术。 \n• 双栈技术（Dual Stack） \n当同时存在IPv4和IPv6网络时，通过具有双栈功能的设备，结合隧道和转换 技术，实现IPv4网络和IPv6网络的互通，代表技术DS-Lite。 \n• 协议转换技术（NAT） \n当IPv4网络节点或IPv6网络节点要和对方通信时，需要借助协议转换技术将 IPv4和IPv6的报文进行转换后才可互通，代表技术NAT64。 \n• 隧道技术（Tunnel） \n在IPv4向IPv6过渡的初期，IPv4网络已经大量部署，而IPv6网络只是散落在 各地的“孤岛”，隧道技术使IPv6报文可以在IPv4网络中传输，实现IPv6网 络间的孤岛互联。 \n代表技术6PE、6VPE、GRE、6to4等。 \nI P v 6 的未来 尽管面临着许多困难，但IPv6技术强大完善的功能以及5G时代对网络性 能的极大需求，都在推动着IPv4网络稳步地向IPv6网络演进。 \n随着5G网络的逐步建设，我们国家在物联网、大数据、云计算、人工智 能等新兴领域都迎来了快速的发展，那么既能够提供海量地址资源又能解决 IPv4网络缺陷的高速、可靠、安全的IPv6网络无宜是最佳选择。 \n近年，基于IPv6的下一代互联网技术创新体系“I P v 6 + ”也引起了广 泛关注。“I P v 6 + ”是IPv6的升级，是面向5G和云时代的IP网络创新体系。 \n“I P v 6 + ”不仅包括了以SRv6、网络切片、随流检测、BIERv6和 APN6等内容为代表的协议创新，以网络分析、自动调优等网络智能化为代表 的技术创新，还包括以5G承载和云网融合为重点应用场景的业务创新。 \n在北京冬奥会期间，IPv6+技术也是首次在大型赛事活动上进行了应用。 \n属于IPv6以及“IPv6+”的时代已经到来，你准备好了吗？！ \n在学习《SR，助力5G赢未来》过程中，我们知道当前承载网络中的IP数 据传输主要基于MPLS（Multi-Protocol Label Switching，多协议标签交换） 技术，MPLS在提高路由转发效率的同时，也不可避免的带来了一些新的问题： \n• 配置多：需要使用独立的信令协议，如LDP（Label Distribution Protocol，标签分发协议）、RSVP（Resource Reservation Protocol， 资源预留协议）等，并为网络中的每个节点分配标签，配置过程相对复杂。 \n• 效率低：每次进行数据传输，都需要为端到端连接路径上的所有节点 下发配置信息，传输效率低。 \n• 扩展难：每个节点都需要维护每条连接的状态，业务扩展较为困难。 \n• 跨域烦：不同的网络（如IP骨干网、IP城域网、移动回传网等）之间是 独立的MPLS域，网络间的互连需要使用复杂的跨域VPN技术来实现。 \nSRv6的应运而生 基于源路由转发而产生的SR（Segment Routing，分段路由）技术的出 现，改变了传统IP网络中只能基于目的路由转发的思路。通过SR技术，在网 络中的首节点规划并建立端到端连接的路径，中间节点只需转发、无需再维护 连接状态，大大简化了网络的部署和扩展。 \nSR与MPLS的结合（SR-MPLS）有助于解决前面提到的配置多、效率低、 扩展难的网络“痼疾”，但网络中的节点仍然需要都支持MPLS标签转发技术， 还是没有从根本上解决跨域互连的问题；并且MPLS标签的扩展能力有限，难 以更好地满足5G时代多样业务的传送需求。 \n与此同时，为了解决IPv4地址短缺的问题，让IP网络数据的传输更加高 速、可靠和安全，互联网协议正从IPv4（互联网协议版本4）过渡到IPv6（互 联网协议版本6）。 \n随着IPv6的大规模部署，在5G承载和云网融合的重点应用场景中，基于 MPLS的传送技术愈发成为了网络的“瓶颈”。 \n融合了SR与IPv6特点的新一代IP承载网数据转发协议SRv6应运而生， SRv6充分继承了SR的源路由优势和IPv6简洁易扩展的特点。 \n• 不再需要使用独立的信令协议为所有节点分配标签，使网络更容易配置和 管理。 \n• 不再需要所有节点支持MPLS，甚至不需要所有节点都支持SRv6，使网 络具备更好的兼容性。 \n• 基于扩展了报文头的IPv6报文即可实现业务的端到端部署，使网络更加 简洁和高效。 \n• 网络具备可编程能力，可以实现业务的灵活扩展，结合SDN还可以实现网 络的灵活调度。 \nSRv6的灵活源泉 SR技术的关键在于为网络中的每个节点或节点间的每条路径都分配一个 SID（Segment Identifier，SID）作为标识，并在起始节点指定报文需要经过 的节点和路径的SID集合（Segment List）来指导报文的转发。 \nSR-MPLS使用20 bits长度的MPLS标签值作为SID，而SRv6则使用128 bits长度的IPv6地址格式值来作为SID。 \n相比于SR-MPLS的SID，SRv6的SID更长，支持携带更多信息，可以用 来标识节点、链路、L2VPN业务、L3VPN业务、网络服务等多种功能或业务 类型。 \n128bits长度的SRv6 SID包含了Locator、Function、Arguments三个 字段。 \n有了SRv6 SID，SRv6就具备了路径和业务的编排能力，可以预先规划报 文转发的路径以及路径上每一个节点的转发行为，并支持定义任意的网络功能 或业务。 \nSRv6的秘密武器 除了SRv6 SID，SRv6还有另外一个“秘密武器”。 \n前面我们提到，SR与MPLS的结合（SR-MPLS）虽然有助于解决网络中 配置多、效率低、扩展难的“痼疾”，但网络中的节点仍然需要都支持MPLS 标签转发技术，还是没有从根本上解决跨域烦的痛点。 \nSRv6充分利用了IPv6的易扩展特性，通过一种新增的扩展头类型SRH （Segment Routing Header，段路由头），来替代MPLS的标签转发功能， 让SRv6网络可以不需要借助于其他技术、仅基于原生的IPv6技术（Native IPv6）就能实现数据的高效率转发，彻底解决跨域烦的难题。 \n普通的IPv6报文中可以包含0个或多个扩展头，用于实现不同的业务功能， 只有当有需要的时候才会添加扩展头。 \n扩展了SRH后，SRv6报文结构包括了IPv6报文头、SRH扩展头和数据报 文三个部分。 \n• IPv6报文头：用于指定报文的源地址（Source Address，SA）和目的地址 （Destination Address，DA）。 \n• SRH扩展头：用于指定报文的转发路径信息，包含中间节点数（Segments Left，SL）和段列表（Segment List）。段列表是报文传输过程中会经过 的所有节点的SID的清单，中间节点数是指经过的节点的数量。 \n• 数据报文：传送的业务数据信息，传输过程中保持不变。 \n如果要简化理解的话，以上三部分的结构可以用下图来表示。 \n普通IPv6报文中的源地址（SA）和目的地址（DA）分别标识的是报文的 首节点和目的节点，在传输过程中保持不变；而SRv6报文中的目的地址（DA） 标识的是当前报文经过的下一个节点，会随着数据传输过程实时变化。 \nSRv6使用SRH扩展头中的中间节点数和段列表来指导报文的转发，每经 过一个SRv6节点，中间节点数的值减1、目的地址信息更新一次，目的地址信 息由中间节点数和段列表共同决定。例如SL=n时，DA=SID [0]；SL=0时， DA=SID [n]。 \nSRv6的转发过程 说了这么多，是不是还有点不太明白SRv6是怎么工作的。 \n让我们通过一个具体示例来看下SRv6是如何在网络中传输数据的。 \n首节点（节点A）接收到用户（源节点S）的数据传输需求，确定了目的 地（目的节点R）。SRv6规划传输路径为A→B→C→D，那么在节点A发出的 报文中：SA=S，DA=B，段列表为<SID D，SID C，SID B>，中间节点数 SL=2。 \n节点B、节点C收到报文后，将报文中的SL值减1，并将DA改为下一节点， 转发报文。 \n节点D收到数据后，发现SL值为0，判定自己是尾节点，则按普通IPv6报 文转发方式将报文转发给目的用户（目的节点R）。 \n可以看到，通过SRv6进行数据传输时，不需要为网络中的每个节点分发 标签，也不需要维护每条连接的状态，更不依靠MPLS隧道来进行跨域的数据 转发，非常便利、高效。 \nSRv6的工作模式 虽然不再使用MPLS技术，但是SRv6同样支持对不同类型的业务通过不 同的工作模式来提供差异化的服务。 \nSRv6主要工作模式包括两种：SRv6 BE（Best Effort，尽力而为）和 SRv6 TE Policy（Traffic Engineering Policy，流量工程策略）。 \n• SRv6 BE的作用类似于MPLS LDP，LDP是利用IGP（Interior Gateway Protocol，内部网关协议）的最短路径算法计算得到一条最优路 径来指导数据的转发。SRv6 BE仅使用一个业务SID（Service SID）来指 导报文的转发，是一种尽力而为的工作模式。该工作模式下，SRv6功能只 需要部署在首尾节点，实现较为简单，适用于需要快速开通一些普通VPN业 务的场景。 \n• SRv6 TE Policy则利用了源路由的特性，在首结点封装一个有序的 Segment List（路径信息）来指导报文在网络中如何转发。结合Segment List的可编程特性，并引入着色机制（设置color属性），SRv6 TE Policy 可以灵活地指定报文的任意转发路径来实现流量工程、灵活引流、负载分担 等功能。 \n关于SRv6 BE和SRv6 TE Policy两种工作模式，我们后续也会推出专题 图文进行详细介绍，敬请期待哦。 \nSRv6的其它优势 SRv6不仅能够解决本文开篇提到的四大痛点，还具有其它的独有优势。 \n1. 与SDN（Software Defined Network，软件定义网络）技术相结合 \nSRv6可以与SDN（Software Defined Network，软件定义网络）技术 相结合，利用自身的灵活性配合SDN的管理调度能力，整个网络就像一个可 编程的软件系统，灵活地实现各种业务, 同时网络配置及数据传输也变的更为 简单。 \n如下图所示，SDN控制器通过搜集SRv6节点及路径信息，根据业务需求 规划合适的路径以及各节点提供的服务，并将信息通知给首节点（如下图中 的节点A）。首节点根据接收到的信息，将业务数据通过SRv6网络传递到目 的节点。 \n2. 与EVPN（Ethernet Virtual Private Network，以太网虚拟专用网） \n技术相结合 SRv6还可以与EVPN（Ethernet Virtual Private Network，以太网虚拟 专用网）技术相结合，使 IP承载网摆脱众多复杂协议的束缚，简化为Overlay （EVPN）+Underlay（SRv6）的组网格局，大大简化网络的复杂度。 \nSRv6开启IP新时代 SRv6是继MPLS之后的新一代IP承载网核心协议，不仅将简化和统一承 载网络的架构，也将持续推动固（固定网络）移（移动网络）融合、云（云计 算）网（通信网）融合的发展。 \n相信不久的将来，会出现更多基于SRv6的应用创新，推动各行各业向智 能化和数字化方向发展。 \n技术改变生活，让我们一起期待吧！ \n随着5G网络的建设以及5G手机的普及，基于视频流量的业务呈现出了爆 炸式的增长，如网络直播、多方视频会议、远程医疗、在线教育、IPTV等需 求大带宽的视频业务。 \n这些应用都是典型的组播业务，但是除了IPTV，其他的应用目前基本上 采用的仍然是单播技术。随着用户数量的急剧增长，给网络带来了极大的带宽 压力和挑战。 \n咦，不对，组播业务为啥要用单播技术，WHY？ \n这还得从组播的概念说起。 \n什么是组播技术 组播是与单播、广播并列的一种IP网络中的数据传输方式。 \n广播在网络中实现点到多点的传输，源服务器发送一份报文，广播域内的 所有用户端不管需不需要都会收到这份报文。 \n优势是只需要发送一份报文，劣势是用户端无法拒绝接收。 \n单播在网络中实现点到点的传输，源服务器端和用户端需要建立单独的通 道用来进行端到端的传输。 \n优势是用户端独享资源，劣势是随着用户的增多资源消耗巨大。 \n组播在网络中实现的也是点到多点的传输，与广播不同的是，组播是将报 文以组的形式发送给加入组的用户端，其他没有加入的则不会收到报文。 \n可以看出，组播融合了广播和单播的特点，在点到多点传输的应用中有着 先天的优势。 \n但由于大部分组播业务初期用户数量不多，同时传统的组播技术也相对复 杂，这导致了目前大量组播业务其实使用的仍然是单播技术。 \n传统组播技术的局限性 传统的组播技术随着大带宽视频业务的快速发展，存在着严重的局限性， 制约了组播在网络中的大规模应用。 \nn 技术复杂 传统的组播技术，如PIM（Protocol Independent Multicast，协议无关 组播）、MLDP（Multicast Label Distribution Protocol，组播标签分发协 议）、P2MP RSVP-TE（点到多点基于流量工程的资源预留协议）等，需要 在网络中为每个组播组建立从源服务器到客户端的组播分发树，组播分发树中 的每个节点（即路由器）都需要维护组播状态信息，因此又被称为有状态组播 技术。 \n当一个系统中需求的组播组数量非常大时（如数千个），网络中的每台路 由器对应都要维护几千条的组播转发状态信息，这会消耗掉路由器大量的CPU、 内存等宝贵资源，因此难以适应大规模的组播应用。 \nn 用户体验差 当网络中的组播流量越来越多时，网络中需要建立的组播分发树就越多， 网络带宽消耗越大。当组播用户或者网络拓扑发生变化时，需要路由协议重新 计算生成转发路由、组播协议重新计算生成转发树（即重新收敛），这会导致 网络业务恢复的时间较长，严重影响用户体验。 \nn 拓展性差 传统组播业务的开通需要在网络中配置多个组播协议（如PIM、MLDP、 P2MP RSVP-TE等）来实现，网络的部署和运维都相对复杂、可扩展性差。 同时，目前网络正逐步向SRv6（Segment Routing over IPv6，基于IPv6的 分段路由）技术演进，传统的组播协议无法支持基于SRv6的极简网络，不符 合网络发展趋势。 \n因此，急需一种简洁高效的组播技术来打破组播业务大规模应用的制约。 \n“ 对症下药” 的B I E R 技术 BIER（Bit Index Explicit Replication，位索引显式复制）是由IETF （Internet Engineering Task Force，互联网工程任务组）提出的一个新型组 播转发技术，通过分层架构设计实现了组播业务流与转发平面的解耦，简化了 组播流量的转发操作，“对症下药”地解决了传统组播协议中的“顽疾”。 \nIETF将BIER组播架构分为Overlay、BIER和Underlay三层，每层之间衔 接并合作完成组播流量的转发。 \n在认识三层架构前，先了解以下几个概念。 网络中采用BIER技术的路由器都称为BFR（Bit Forwarding Router，位 转发路由器），由BFR组成的转发网络称为BIER域。 \n与组播源直接相连的BFR称为BFIR（Bit Forwarding Ingress Router， 位转发入口路由器），与接收者直接相连的BFR称为BFER（Bit Forwarding Egress Router，位转发出口路由器）。 \nn Overlay层 Overlay层又称为组播业务层（Multicast Flow Overlay），主要负责管 理进入BIER域的组播流量信息，确定从BFIR进入BIER域的组播流量要发送给 哪些BFER节点。 \nOverlay层通常使用MP-BGP（MultiProtocol Border Gateway Protocol，多协议扩展边界网关协议）和SDN（Software\u2002Defined\u2002Network， 软件定义网络）来管理组播流量信息。 \nn BIER层 BIER层（BIER Layer）主要为进入BIER域的组播报文添加BIER报文头并 在BIER域中转发组播报文。 \nBIER域中除BFIR和BFER外的其它BFR节点并不关心组播报文，仅根据 BIER报文头中的目的信息来转发报文。 \nn Underlay层 Underlay层又称为路由层（Routing Underlay），主要负责通过扩展 的路由协议为BIER域内的BFR之间建立路由连接、传递BIER信息，实现网 络互通。 \n常用的路由协议主要是ISIS（Intermediate System-to-Intermediate System，中间系统到中间系统）或OSPF（Open Shortest Path First，开放 式最短路径优先），在一些大型网络中也可使用BGP（Border Gateway Protocol，边界网关协议）。 \nBIER组播架构的分层设计让组播流量的管理和转发更为简单高效，仅需 在BIER域的入节点（BFIR）和出节点（BFER）上保留组播状态信息，中间节 点转发报文时不关注组播信息、不维护组播状态，因此BIER也被称为无状态 组播技术。 \nB I E R 转发原理 n 基本原理 BIER的基本原理主要体现在“Bit（位）”上，它设计引入了一个特定长 度的BitString（二进制比特串）来表示一组BFR。 \nBitString从最低位开始，每个比特位对应一个BFR-id。BIER域中的每个 BFR都可被分配一个不重复的BFR-id，用于唯一标识每台BFR。 \nBIER域中的BFR数量越多，需要的BitString长度（BSL）越长。 \n如果使用长度为3的BitString来表示一组BIER路由器，也就是3个bit，那 么“001”表示BFR-id为1的BFR，“100”表示BFR-id为3的BFR，而 “101”就表示BFR-id为1和3的两台BFR；如果使用5个bit，那么“00011” 则表示BFR-id为1和2的两台BFR。 \n所有的BFR都在一个BIER域中（Domain），当网络太大时可以根据网络 拓扑或地理位置设计多个SD（Sub Domain，子域）。 \n前面已经提到，BIER域中的BFR数量越多，需要的BitString长度就越长。 如果为BIER域中的每台BFR都分配BFR-id，当网络规模较大时，BIER转发表 会变得很大，势必会增加网络复杂度。 \n因此，BIER只需为边缘节点分配BFR-id，这样一来，无论网络规模有多 大，BIER转发表大小只和边缘节点数量相关，这将大大减轻网络的负担和降 低网络复杂度。 \nn 转发过程 看到这里，是不是有种似曾相识的感觉。没错，BIER的原理有点类似SR （Segment Routing，分段路由）。BIER是组播路由技术，同单播路由技术 SR一样，都是为了简化网络的复杂度，让数据的转发更具效率。 \n在BIER域中，每台BFR会将自己的信息（如BFR-id、SD、BSL等）通过 ISIS/OSPF协议传递给其他BFR，再根据自身以及其它BFR传递来的信息，通 过特定算法计算出到达其它BFR的路径并生成BIER转发表，该表被称为BIFT （Bit Index Forwording Table，位索引转发表）。 \n组播报文由BFIR节点封装BIER报文头后进入BIER域，域内的每台BFR在 收到封装后的组播报文后，需要使用报文中的BitString和转发表中每个邻居对 应的表项进行逻辑与（AND）运算，并根据运算结果决定报文的下一跳是哪个 邻居节点。 \n• 当运算结果为0时，说明下一跳就是该BFR自己，即BFER节点。这时， 该BFR将解封装BIER报文头还原组播报文，并转发给接收者。 \n• 当运算结果不为0时，该BFR就复制一份组播报文，并将其中的 BitString改为运算后的结果，再将报文转发给下一跳的邻居节点。 \n由此可见，BIER域内的BFR节点是根据BIER报文头中的信息将BIER组播 报文最终转发到BFER节点，由BFER节点解封装BIER报文头后还原组播报文， 并转发给接收者。整个转发过程中，各个BFR并不关心组播报文中的内容。 \n让我们通过一个示例来加深一下了解吧。 \n如下图所示的BIER域中有6台BFR路由器（A~F），BitString的长度为4， 其中A作为BFIR，D、F、E作为BFER，为他们分配的BFR-id分别为4 （1000）、1（0001）、2（0010）、3（0100），其余BFR不分配BFR-id。 \n在BIER转发表中，如果同一个下一跳邻居可以到达多个BFR，则会合并 成一个F-BM（Forward Bit-Mask）信息。例如上图中的节点B，邻居为C的 F-BM（0011）表示节点B通过邻居C能达到的是BFR-id为1和2的节点。 \n在这个例子中，BIER域转发报文的过程如下： \n1. 节点A~F根据相互传递的信息计算生成BIER转发表。 \n2. 节点A（BFIR）收到组播报文后，查询组播报文中的目的信息，确认 组播报文需要发往节点D和节点E，而节点F不是目的节点。节点A为组播报 文封装BIER报文头，添加BitString信息为0101，即对应节点D（0001） 和节点E（0100）。查询BIER转发表后将BIER组播报文发送给节点B。 \n3. 节点B收到节点A发送的BIER组播报文后，查看BIER报文头获取 BitString信息（0101），并将BitString信息（0101）与转发表中的F- BM条目进行逻辑与（AND）运算，获取到两个下一跳节点（节点C和节点 E）和运算后的BitString信息，从而得知接下来需要将报文分别转发给节 点C和节点E。之后，节点B将BitString信息（0001）封装到转发给节点C 的报文头中并发送、将BitString信息（0100）封装到转发给节点E的报文 头中发送。 \n4. 节点C收到节点B发送的BIER报文后，处理流程同节点B，将报文转发 给节点D。 \n5. 节点E收到节点B发送的BIER报文后，处理流程同节点B，经过与 （AND）运算确认转发邻居为自己，则解封装BIER报文头还原组播报文， 转发给接收者。 \n6. 节点D收到节点C发送的BIER报文后，处理流程同节点E，确认转发邻 居为自己，则解封装BIER报文头还原组播报文，转发给接收者。 \n结合前面的组网图，我们可以看到节点B、C、E之间是互相存在环形转发 链路（环路）的。所以，正是通过逻辑与（AND）运算，这三个节点才能明确 报文的下一跳是哪个节点，从而避免报文在环路间重复发送。 \n另外，我们也可以从这个示例中印证前面提到的内容：在BIER域中转发 组播报文时，中间节点并不关心组播信息，即不管组播报文是啥内容都不拆不 看、仅根据BIER相关信息进行转发，这大大地提升了报文的转发效率。 \nB I E R 的应用场景 BIER组播简洁高效的转发机制为5G时代的组播大规模应用提供了有力的 技术支撑，如大型内容提供平台（IPTV、各类视频、在线教育、游戏运营 等）、金融行业、数据中心等场景中。 \n5G时代，大带宽业务需求的迅速增长让BIER组播技术成为了通信业界研 究的重点，目前中兴通讯在BIER组播技术方面已经能够提供路由器、网络控 制器和APP全套系统的支持，具备端到端业务能力。 \n未来，中兴通讯将一如既往全力支持合作伙伴的5G建设，稳步推进BIER 商用化进程，构建5G时代商业成功。 \n随着5G的成熟商用，AR/VR、自动驾驶等新型业务新起和发展，运营商 的经营模式逐渐从销售带宽转为销售服务体验。网络的差异化服务需求不断增 强，网络维护也日趋复杂。为保障高质量的网络业务服务，需要对网络状态进 行实时检测、精细感知，而传统的网络性能检测技术难以满足需求，具有以下 弊端： \n• 一般采用构造检测报文的方式来探测网络质量，不能完全真实反应业 务SLA（Service Level Agreement，服务等级协议）。 \n• 信息采集周期长，无法捕获突发异常，承载网只能在业务质量劣化或 发生故障后被动响应用户投诉，严重影响服务体验。 \n• 难以快速精准定位故障点。例如下图中，通过传统的网络性能检测技 术检测出AE间链路发生故障了，但具体是AB、BC、CD、DE中的哪 段链路发生故障了呢？这就要逐步缩小范围一次次地排查了，比如先 排查AD间是否故障，再排查AC，再排查AB。而实际组网要比下图 复杂得多，采用传统的网络性能检测技术来定位故障，定位周期长， 定位时间甚至长达数天。 \n为解决以上问题，满足5G承载运维需求，中兴通讯提出了In-Band OAM （In-Band Operation, Administration and Maintenance，带内操作管理维 护）检测技术。 \n什么是I n - B a n d O A M In-Band OAM是一种基于真实业务流的随流测量技术，用于检测识别网 络中的细微异常，精准检测每个业务的时延、丢包等性能信息，使得网络质量 SLA实时可视。 \n如果把网络比作道路，网络承载的业务就是跑在道路上的车辆，传统的网 络性能检测方式就好比路口的摄像头，每经过一个车辆都会对车辆的行驶状况 进行采集，比如车速等信息，但采集的信息有限、精度较低，而且只能在路口 采集信息。 \n而In-Band OAM，是在待测的目标业务流中插入In-Band OAM报文头， 由转发设备基于In-Band OAM报文头采集业务流的性能统计信息，并将采集 到的信息通过Telemetry技术实时上报给分析器。这就好比给每辆车加装行车 记录仪和车内摄像头，伴随着车辆的行驶进行全程记录，车辆的实时车速、行 驶的路径、在每一个交通路口等待的时间、司机和乘客的状态等信息都能有效 收集，并实时上传至数据中心平台。 \n相比传统检测方式，In-Band OAM具有明显优势： \nIn-Band OAM是如何实现这些的呢？我们先从它的网络模型讲起。 \nI n - B a n d O A M 的网络模型 In-Band OAM的网络模型包含四个关键元素：目标业务流、目标网络、 统计分析系统、测量参考点。 \n1. 目标业务流：In-Band OAM的统计分析对象，可以是隧道、伪线、IP业务 流。为了方便区分统计，In-Band OAM使用测量对象标识（Flow ID）唯 一标识一条目标业务流。 \n2. 目标网络：承载目标业务流的网络。 \n3. 统计分析系统：负责In-Band OAM网络性能统计分析的所有设备的集合， 包括以下角色： \n• 控制器：负责提供目标业务流的特征筛选条件和控制功能。控制器将 节点设备（首节点、中间节点、尾节点）采集上报的业务流信息与特 征筛选条件进行匹配，一旦存在成功匹配的业务流时，就为该业务流 分配Flow ID，并将统计指令下发给节点设备，通知它们基于Flow ID 统计数据。 \n• 首节点：目标网络的入口设备。首节点负责接收控制器提供的特征筛 选条件和Flow ID，并根据特征筛选条件筛选出Flow ID对应的目标业 务流。筛选成功后，首节点会为该目标业务流封装包含Flow ID的In- Band OAM报文头，再根据报文头携带的时间戳、报文计数等测量信 息进行丟包、时延等性能统计，并将统计结果上报给分析器。 \n其中，对于不同类型的检测对象（如隧道、伪线、IP业务流）， Flow ID在报文头中的位置不同。例如，对隧道类型，Flow ID位于 VPN私网标签（VPN Label）之前；而对IP业务流类型，Flow ID位 于VPN私网标签之后。 \n• 中间节点：目标网络的中间设备，负责根据报文头中的Flow ID确定 目标业务流，再根据报文头携带的时间戳、报文计数等测量信息进行 丢包、时延等性能统计，并将统计结果上报给分析器。 • 尾节点：目标业务流离开目标网络的出口设备，除了负责和中间节点 相同的工作外，还负责剥离目标业务流的In-Band OAM报文头。 • 分析器：负责收集节点设备上送的网络性能统计数据，并完成数据的 汇总和计算。 \n4. 测量参考点：执行In-Band OAM测量的端口。按在统计分析系统中的不 同位置，可分为入测量参考点、中间测量参考点、出测量参考点。 \n了解了In-Band OAM的网络模型组成以及各组件的作用，它的工作过程 就很容易理解了。 \nIn-Band OAM正常工作的前提是时间同步。在测量工作开始前，要求所 有参与In-Band OAM测量的设备时间已经同步，从而确保各设备能够基于相 同的测量周期进行报文统计和上报。控制器/分析器和所有节点设备之间的时 间同步是通过1588v2协议实现的。 \n讲完前提，就要进入正题了。下面我们来结合下图，一起梳理下In-Band OAM的工作过程吧。 \n1. 控制器为目标业务流分配网络内唯一的Flow ID，并设置统计周期。 \n2. 首节点给目标业务流报文添加包含Flow ID的In-Band OAM报文头，并记录 每个统计周期内收到第一个报文的时间戳、每个周期内收到的目标业务流报 文的个数。然后，按周期将时间戳和报文计数通过Telemetry技术上报给分 析器。 \n3. 中间节点及尾节点识别 Flow ID，按照首节点相同的周期，记录收到每个统 计周期第一个报文的时间戳，同时统计每个周期内收到的目标流报文的个数。 然后，按周期将报文计数和时间戳上报给分析器。 \n4. 尾节点剥离掉In-Band OAM报文头，即把 Flow ID 从标签栈中移除，还原 目标业务流报文。 \n5. 分析器根据设备节点上报的数据，计算各个目标业务流在每个统计周期内的 丢包和时延。 \n• 丢包数 = 上游测量参考点统计的收包数 - 下游测量参考点统计的收包数 • 时延 = 下游测量参考点记录的时间戳 - 上游测量参考点记录的时间戳 其中的“上、下游”是由目标业务流流向而定的，比如目标业务流由A流向 D，则A是上游；由D流向A，则D是上游。 \n怎么样，是不是很简单？其实，In-Band OAM的工作过程和快递寄送的 过程很类似： \nI n - B a n d O A M 的应用 In-Band OAM支持端到端性能统计和逐点性能统计两类场景。 \np 端到端性能统计 端到端性能统计场景用于测量目标业务流在目标网络的首节点和尾节点之 间的丢包以及时延情况。 \np 逐点性能统计 逐点性能统计场景用于测量目标业务流在较小网络单元内的丢包和时延 情况。它的应用场合一般是在发现端到端之间存在丢包或者时延后才实施， 用于进一步精细定位故障所在的网元位置。 \n随着5G商用和企业上云业务的发展，In-Band OAM广泛应用于基于基站 回传/普通专线/云专线等应用的智能运维，实时精准地感知目标业务的状态， 将感知结果作为提前干预、调优的依据，将被动运维转变为主动运维。同时， 与SDN控制器联动，可以形成对业务全生命周期的闭环监控，时刻为5G承载 保驾护航。 \n文章中涉及到的缩略语如下： AR（Augmented Reality，增强现实） \nIn-Band OAM（In-Band Operation, Administration and Maintenance，带内操作管理维护） \nSDN（Software Defined Network，软件定义网络） \nSLA（Service Level Agreement，服务等级协议） \nSR（Segment Routing，分段路由） \nVPN（Virtual Private Network，虚拟专用网络） \nVR（Virtual Reality，虚拟现实） \n5G时代，行业应用日益丰富，业务差异化需求明显，比如高清视频需要 高带宽，车联网需要低时延，远程医疗需要低抖动……业务差异化需求给网络 差异化服务带来了挑战。 \n运营商希望能把网络的潜能充分挖掘，进一步开放网络能力，提升网络资 源利用率。简单来讲，就是在一张物理网络中满足不同用户的不同需求。那么， 如何做到这些呢？ \n将网络进行“切片”，使得不同的逻辑网络资源提供给不同的用户使用， 并保持不同用户之间的隔离。根据业务的不同SLA（Service Level Agreement，服务等级协议）特征，将其划分到相应的网络切片，来实现不同 行业用户特定的端到端服务策略需求，比如将高清视频划分到高带宽切片，车 联网划分到低时延切片，远程医疗划分到低抖动切片等。 \n承载网络切片技术 承载网络常用的切片技术有硬切片、软切片和硬+软切片。 \n硬切片是从硬件角度对网络进行切片，应用比较广的是FlexE（Flex Ethernet，灵活以太网）技术。它是利用FlexE接口对不同的业务进行硬件层 面的严格隔离，虽然保障了带宽和时延要求，但是控制面并不感知FlexE通道， 部署困难。 \n软切片是利用算法从软件角度对网络进行切片，常见的有Flex-Algo （Flexible Algorithm，灵活算法）技术，它是基于SR（Segment Routing， 分段路由） -TE（Traffic Engineering，流量工程）的软切片技术。Flex- Algo技术虽然在控制面实现了对切片的感知，部署灵活，但底层硬件资源是共 享的，隔离效果不如硬切片，难以严格保障带宽和时延。 \n目前硬切片与软切片相互独立，无法统一控制管理。那么，有没有一种切 片技术能扬二者之长，避二者之短呢？中兴通讯推出的软+硬切片技术Slice+ 解决了这个问题。 \n什么是Slice+ Slice+是一种创新的软硬件统一管理的切片方案，实现了切片与底层硬件 资源的灵活匹配，并且可以通过控制器对切片进行统一管理，运维方便。 \nSlice+可以先进行硬切片，从硬件层面隔离不同种类的业务，再在各个硬 切片上进行软切片，以保证同一种业务下不同用户的体验。就好比，一个蛋糕 有两层，上层是草莓味的，下层是巧克力味的。先在两种口味的分界线上水平 切一刀，这就是硬切片，隔离了喜欢吃不同口味的人，即隔离了业务。然后再 在每一层上垂直切，比如某部分有个水果，给某个人，这就是软切片，隔离了 同一种业务的不同用户，实现了同一种业务下的不同用户体验。 \n下面我们通过Slice+的整体框架，了解下这些功能是如何实现的吧。 \nSlice+的整体框架 Slice+的整体框架包括物理网络、资源视图、逻辑切片拓扑、业务视图、 控制器&PCE（Path Computation Element，路径计算单元）几个部分。 \n物理网络是设备的实际组网。 \n资源视图中是用于切片的硬件资源，既有普通的二三层链路，也有基于物 理刚性管道的硬切片，比如FlexE子接口。 \n逻辑切片拓扑中包含了基于不同的承载需求而划分的切片，比如低时延切 片、高带宽切片等。这些切片既可以是基于普通二三层链路形成的软切片，又 可以是基于FlexE子接口形成的硬+软切片。 \n业务视图呈现了VPN业务的最优化转发路径（SR BE/SR TE），这些最 优化转发路径是由控制器中的PCE计算的。 \n控制器通过BGP（Border Gateway Protocol，边界网关协议）-LS （Link State，链路状态）、Netconf、Telemetry等接口协议对切片进行资 源管理、算路优化、拓扑管理以及切片监控等。 \nPCE根据确定的切片网络资源和业务需求，进行BE和TE路径的计算，从 而实现特定意图（如低时延、高带宽、拓扑约束等）的服务能够在最优化的路 径（SR BE/SR TE）上转发。 \n理解了Slice+的整体框架和功能实现后，是不是很好奇Slice+具体是如何 工作的？我们一起来看看吧。 \nSlice+的工作过程 Slice+基于对IGP（Interior Gateway Protocol，内部网关协议）协议 的扩展与信息泛洪，实现了一种轻量级的切片解决方案。下面我们通过一个具 体的例子来说明Slice+的工作过程。 \n传输网络中设备组网如下图所示，其中H表示业务的起始节点（头节点）， E表示该业务的终止节点（端节点）。 \nSlice+的工作过程如下： \n1、分配切片资源。 首先，控制器给节点（头节点H、中间节点1~4、端节点E）和链路配置 专属的slice-ID，用于标识不同的节点和链路。同一节点或链路可以属于多个 slice-ID，且默认总是属于slice-0。 \n本例中所有节点都既属于slice-1又属于slice-2，链路属于的slice-ID在 下图中标出。 \n2、形成基于slice-ID的网络切片。 IGP域内的节点互相通告各自的slice-ID，得到包含所有切片拓扑信息的 数据库，也就是TE（Traffic Engineering，流量工程）-DB（Database，数 据库）。TE-DB中包含所有配置的slice-ID及其所属节点、链路信息。每个 slice-ID都对应一个网络切片，该切片由属于这个slice-ID的节点或链路组成。 比如，基于slice-1的网络切片，就是由所有属于slice-1的节点和链路组成的 切片。也就是在步骤1的组网中，筛选出所有标记slice-1的节点和链路。 \n3、切片信息通过BGP-LS协议上报给控制器。 BGP-LS协议可以汇总IGP协议收集的拓扑信息上报给控制器，控制器上 会将这些拓扑信息形成相应的切片视图。 \n4、计算切片中头节点H到端节点E的SR-BE和SR-TE路径。 SR-BE和SR-TE路径都可以由头节点或控制器中的PCE计算。但是，由 于SR-BE比较简单，一般由头节点计算，而相对复杂的SR-TE由头节点或控 制器中的PCE计算。 \n其实，SR-BE和SR-TE路径都是采用CSPF（Constrained Shortest Path First，基于约束的最短路径优先）算法计算得出的最优化路径，只是二 者的约束条件不同。 \n• SR-BE以slice-ID和最短路径为约束条件，本例中也就是slice-1内的 最短路径，即下图中的绿色线条路径。 \n• SR-TE以slice-ID和color值为约束条件，其中color值对应带宽、时延、 te metric、亲和性（排除或包含某节点/链路）等一种或多种属性的组合。 通过配置不同的color值可以满足多样化需求。比如，下图中color=1000对 应“沿切片1内的红色线条路径转发”。 \n5、部署业务在网络切片中转发。 控制器给不同的VPN业务配置不同的slice-ID和color值，从而把业务引 入对应切片中的对应路径。 \n• 如果slice-ID为slice-1，color为0，则将这个VPN业务引流至slice-1 内的SR-BE路径。 • 如果slice-ID为slice-1，color非0，则将这个VPN业务引流至slice-1 内具有相同color值的SR-TE路径。 \n接着前面的例子，现在有VPN1、VPN2两个业务，它们的slice-ID都为 slice-1，而color值不同：VPN1业务的color=1000，则这个业务被限定走 Slice1切片中color为1000的SR-TE路径（下图中红色线条路径）；而VPN2 业务的color=0，则这个业务走Slice1切片中的SR-BE路径（下图中绿色线条 路径）。这就实现了业务间的隔离，相互不受影响。 \nSlice+的主要优势 和传统的硬切片、软切片比起来，Slice+是有很多优势的，总结起来有四 大优势。 \n一、简化的控制面 传统的切片方式是每个切片都需要一个控制面，各切片的控制面IGP协议 是独立的。而Slice+采用共享的IGP协议，多个切片共享唯一的控制面，更易 于维护和部署。这就很nice了，面对5G行业的海量切片，不用发愁管理和运 维了。 \n二、专属的资源分配和用户视角 Slice+能够实现专属的资源分配，规避了传统的用户视图不清晰的缺点。 对于某个用户，不管是BE还是TE，都是在一个确定的slice-ID形成的切片内 进行计算的。比如，在软切片Flex Algo技术下，对于同一个用户user1，BE 和TE算路时会使用两个资源视图（两个切片）。而在Slice+技术下，user1的 资源集成在同一个切片下，资源更清晰。 \n三、创新的软硬件切片统一管理，支持更加丰富的链路类型。 Slice+切片不但适用于三层链路和三层FlexE子端口，还可以适用于任意 链路类型，比如VLAN或者PPP任意的链路类型。 \n四、良好的兼容性、互通性，满足跨域组网需求 Slice+考虑了对现有切片方案的兼容性（可以兼容Flex Algo），具有良 好的互通性。能满足单域或者跨域，以及单厂家或者多厂家之间联合组网的 需求。 \nSlice+可谓是集合了硬切片和软切片的优势，规避了二者的劣势，真正实 现了1+1>2。 \n5G时代，行业转型的步伐逐步加快，提供符合用户意图的高质量网络服 务将越来越重要。Slice+不但可以灵活地满足业务差异化需求，还能高效地控 制和管理动态、海量的切片，简化网络，真正实现端到端5G行业切片的快速 部署，助力运营商快速切入垂直行业，实现数字化转型和行业升级。未来，随 着5G行业应用不断丰富和业务的井喷式增长，Slice+的优势将愈发明显。 \n随着物联网和5G时代的到来，电信业务日益多样化。VR/AR、无人驾驶、 远程医疗、工业自动化等新业务层出不穷，软硬件解耦、资源共享等需求持续 增加，给运营商的网络带来了很大的压力。 \n在传统运营商网络中，不同的电信业务，都是分开部署在不同的硬件设备 上，各硬件设备之间彼此独立，缺乏全局的资源协同和自动化调度。这使得电 信网络扩展性较差，设备利用率较低，难以满足快速发展的网络业务需求，难 以支撑5G时代的个性化场景。而电信云的出现，解决了这些问题。 \n什么是 电信云? 电信云，是基于虚拟化、云计算等技术来实现电信业务的云化，以NFV （Network Functions Virtualization，网络功能虚拟化）、SDN（Software Defined Network，软件定义网络）为主要技术，构建一个可按需部署、灵活 调整的云化平台，并在上面部署不同场景的软件应用，来实现不同的电信业务 功能。 \nNFV技术的引入，实现了在服务器或VM（Virtual Machine，虚机）上 部署虚拟化的电信网元来处理不同的电信业务，如部署vBRAS（Virtual Broadband Remote Access Server，虚拟宽带远程接入服务器）作为宽带 用户接入Internet的网关。 \n这些虚拟化的电信网元，虽然是通过安装虚拟化软件来实现的，但是可以 提供与硬件电信网元类似的网络功能。就好比，通过手机上的计算器APP，也 可以提供传统计算器的计算功能一样。 \n同时，连接这些网元的承载网络通过SDN技术提供的自动化调度和编排功 能，实现了网络的自动化调整和运维，使得业务上线更快捷，调整更灵活。 \n其实，电信云的发展一直都是与“需”俱进的。早期的电信云并不关注网 络的自动化，随着业务需求的变化，电信云逐渐升级。电信云的发展可以概括 为三个阶段，分别是业务虚拟化阶段、独立电信云阶段和分布式电信云阶段。 \n业务虚拟化阶段 在电信云发展的初级阶段，主要是完成电信业务的虚拟化，比如IMS（IP Multimedia Subsystem，IP多媒体子系统）的虚拟化，这个阶段被称为业务 虚拟化阶段。 \n在这个阶段，业务功能部署在VM上，由于需要部署的虚拟化网元数量较 少，规模很小，因此，对网络的自动化部署没有要求。 \n独立电信云阶段 随着部署的虚拟化网元的增多，网络规模的增大，电信网络开始引入SDN 技术来实现大规模网络的自动化部署，电信云的发展进入了独立电信云阶段。 \n在这个阶段，业务功能一般集中部署在省级的中心DC（Data Center，数据中 心），通过在中心DC中的服务器或VM上部署虚拟化的电信网元来实现不同的业务功 能。 \n由于各个中心DC的电信云基本上是互相独立的，相互之间很少有流量交 互。因此，这个阶段称为独立电信云阶段。 \n4G时代的电信云就属于这个阶段，由于4G业务相对集中，这种由中心 DC统一处理的方式基本可以满足需求。但是，一旦数据量较大，这种方式容 易使大量数据占用广域网的带宽资源，还会造成一定程度的延时。 \n试想无人驾驶业务如果采用这种部署方式，会是怎样的情景？车辆与中心 DC之间的交互信息需要跨越城域网和广域网，大量信息拥堵在广域网上，交 互信息一个返回不及时，我们的爱车就要“凉凉”了。 \n分布式电信云阶段 随着5G的到来和垂直行业的发展，网络规模迅速扩大，电信业务对低时 延、高带宽、大连接的需求也越来越高，继续使用各个DC独立管理的方法已 经很难满足需求了。因此，运营商希望进行DC互联，对中心DC、边缘DC和 接入DC进行统一管理，真正实现资源共享，统一调配。这个阶段称为分布式 电信云阶段，5G时代的电信云就属于这个阶段。 \n5G电信云的三级 分布式网络架构 5G电信云一般采用三级分布式网络架构，包括中心云、边缘云和接入云。 根据承担的5G电信业务的不同，这三级云的部署位置、管理及部署方式也不同。 \n5G电信云网络的指挥中心——中心云 中心云中主要部署了一些管理类、数据类以及对时延不敏感的控制面网元。 简单来说，它要做的事情是管理、控制整个网络，并存储大量的数据信息。中 心云通过统一云管理平台对整个网络进行统一管理，提升资源利用率，实现新 业务的快速上线，降低综合运维成本。 \n中心云一般部署在省/全国节点，网络规模很大，结构和配置都很复杂。 通过统一云管理平台进行业务集中控制后，管理和运维的效率都比较高。但是， 由于业务集中，一旦发生问题，波及范围很大，所以中心云对可靠性和安全性 要求很高。 \n5G电信云网络高性能转发特种兵——边缘云 边缘云主要部署的是高带宽、低时延的用户面网元或媒体转发类的网元， 用于承载VR、直播等对实时性要求比较高的业务。边缘云通过部署边缘多云 集中管理平台，能够同时对多个边缘云和接入云进行统一调度和管理。 \n边缘云一般部署于地市节点，数量较多，可以与网络中的C D N （Contents Distribution Network，内容分发网络）/MEC（Mobile Edge Computing，移动边缘计算）节点重合部署，降低传输成本。此外，边缘云的 存储规模较小，如果采用独立的存储池，不但成本高，而且资源利用率低，因 此，边缘云一般将计算节点和存储节点合一部署。在性能方面，由于边缘云承 载的是实时性要求比较高的业务，因此为了保证用户体验，除采用软件加速技 术外，还可以使用专用的加速硬件来进一步提升性能。 \n5G电信云网络快速响应小分队——接入云 接入云主要部署实时性要求很高的接入类网元和一些MEC功能。由于它 更加靠近用户、时延更低，主要承载超低时延的应用，比如无人驾驶、工业控 制等。 \n为了减少接入云的资源需求，当接入云与边缘云之间的管理网络满足响应 时间、延时、抖动等网络要求时，接入云中可以仅部署计算、存储、网络资源， 而不部署管理模块。相应的运维管理功能，可以由边缘云来远程提供。 \n接入云一般部署在靠近用户的县/区节点，具有规模小、数量多、机房环 境限制多、性能要求高等特点。 \n由于承载的是具有超低时延需求的业务，接入云通常需要增加实时操作系 统和实时虚机，来满足响应时间、延时和抖动等网络要求。同时，接入云提供 了高精度时钟，来满足无线侧的时间精度要求。 \n回到前面提到的无人驾驶业务的例子，采用三级分布式架构后，数据不再 需要跨越城域网、广域网提交给中心DC处理，而是由接入云来“就近”处理。 这样，不但提高了带宽利用率，响应速度也大大增加，满足了超低时延的需求 \n讲到这里，大家应该对5G电信云有了整体的认识。接下来，小编带大家 继续深入，一探中心云、边缘云和接入云的内部组网。 \n中心云主要由服务器、交换机、路由器以及控制器等设备组成，一般采用 Spine-Leaf（脊-叶）架构、基于SDN的VXLAN组网，通过SDN技术实现网 络的集中管理、资源的自动化调度和编排。 \n而边缘云、接入云会根据网络规模的大小与实际需求，灵活地选择是否部 署SDN控制器等，既可以采用传统的内部组网方式，也可以采用类似于中心云 的内部组网方式。 \n那么，中心云的内部组网具体是怎样的呢？ \nSDN控制器是实现“自动化”的秘诀，提供了电信网络的自动配置、集中 控制以及管理等功能。 \n• 通过南向接口对DC GW（GateWay，网关）路由器、Leaf交换机等网络 设备进行统一管理和控制。 \n• 通过北向接口为云化资源池管理平台提供虚拟化网络服务，实现云、 网业务的自动化，便于快速开通新业务。 \n路由器作为电信云的网关，与外部网络连接，是电信云的出口。 \n交换机组成了电信云的Spine-Leaf的交换架构，用来转发数据。在同一 个云内任何两个服务器之间传送数据，经过不超过3个网络设备就可以送达， 满足了高速互联的需求。 \n服务器可以是物理服务器，也可以是VM。它们上面部署了不同功能的软 件应用，来处理不同的电信业务。 \n在NFV和SDN两项核心技术的加持下，5G电信云网络实现了软硬件资源 的统一调度和管理，让“云网融合、网随云动”成为现实。相信，随着5G业 务需求井喷式地增长，以及NFV和SDN技术的发展，5G电信云将在低时延、 高带宽、大连接的道路上越走越远。 \n最后，期待无人驾驶早日实现，毕竟，小编还是没有驾照的人呢，吼吼~ \n5G的蓬勃发展使通信行业的业务数据量飞速增长，给数据中心带来了巨 大的运行压力。为了缓解压力，5G时代的数据中心正逐步向虚拟化数据中心 演进，通过将一台物理服务器虚拟成多台逻辑服务器（即虚机VM，Virtual Machine），可以有效提高服务器的利用率，降低能源消耗和运维成本。 \n但是服务器虚拟化后也带来了新的问题。为了灵活调配服务器资源，或者 对物理服务器进行维修和升级，需要经常将虚机动态迁移。所谓虚机动态迁移， 即保证虚机上业务正常运行的前提下，将虚机在用户无感知的状态中从一台物 理服务器移动到另一台物理服务器的过程。简单来讲，就是对用户而言，整个 过程要做到“神不知鬼不觉”，让用户完全感觉不到业务的变化。传统数据中 心面对这样的要求就有些力不从心了。 \n5 G 数据中心怎么办？ 在传统数据中心的二三层网络架构下，服务器不能随便在不同二层域之间 移动。当服务器从一个二层域迁移到另外一个二层域时，需要重新配置或获取 IP地址，这必然会导致业务的中断。 \n而对于服务器虚拟化的5G数据中心而言，虚机动态迁移会成为一种常见 场景。为了实现服务器跨二层域的动态迁移，需要将所有可能会涉及到跨二层 域迁移的服务器全部纳入到同一个二层域中，搭建一个“大二层”网络，从而 实现虚机大范围无障碍迁移。VXLAN就是搭建这个“大二层”网络的重要技 术之一。 \n什么是V X L A N ？ VXLAN是一种网络虚拟化技术，通过在三层网络上建立二层以太网隧道， 来实现跨域的二层互连。在VXLAN隧道中，原始的二层以太网数据帧加上 VXLAN头部一起封装在一个UDP数据包中进行传输。 \nVXLAN技术在传统物理网络基础上通过建立VXLAN隧道来构建逻辑的 “大二层”网络，克服了VLAN（Virtual Local Area Network，虚拟局域网） 技术的传统限制，能够解决数据中心部署时的扩展问题。 \n通过VXLAN技术，可以将整个数据中心虚拟成一个巨大的二层交换机， 每个虚机都作为一个VXLAN接入点（也被称为VTEP，VXLAN Tunnel End Point，VXLAN隧道端点）直接连在这个“二层交换机”的一个端口上，数据 包通过VXLAN隧道从一个VTEP传输到另一个VTEP。 \n但是，最初的VXLAN技术方案中并没有定义控制平面，必须通过手工方 式配置VXLAN隧道，通过数据报文洪泛的方式进行MAC地址和IP地址的学习。 这种方式不仅配置复杂，且会导致网络中存在大量洪泛报文，不适合大规模 组网。 \n为了解决这些问题，VXLAN引入EVPN（Ethernet Virtual Private Network，以太网VPN）作为其控制平面，在网络中实现自动建立VXLAN隧 道、自动学习MAC地址和IP地址。 \n什么是E V P N ？ EVPN是一种二层网络互联VPN（Virtual Private Network，虚拟专用网 络）技术，它定义了一套通用的控制平面，使用BGP（Border Gateway Protocol，边界网关协议）进行邻居建立、路由通告、隧道建立等工作。在数 据平面上，EVPN支持使用多种封装技术（如MPLS（Multi-Protocol Label Switching，多协议标签交换）、VXLAN等）转发数据。 \n如果把VXLAN比作汽车，那么引入EVPN之后，就像汽车安装了自动驾驶 系统，VXLAN完成了从“人工驾驶”到“无人驾驶”的升级。升级后，驾驶 者不需要关注“具体路线”，只需要输入“目的地地址”，整个驾驶过程变得 更加省心省力。 \nVXLAN引入EVPN后，带来了哪些好处呢，一起来看看。 \n• 分离控制平面与数据平面：控制平面负责发布路由信息，数据平面负责转 发报文，分工明确，易于管理。 \n• 简化配置：通过BGP实现了VTEP自动发现、VXLAN隧道自动建立、 VXLAN隧道与VXLAN自动关联，无需用户手工配置，降低网络部署难度。 \n• 支持多归属：支持同一个站点通过多个VTEP接入VXLAN网络，此时，多 个VTEP所提供的多条路径均可以进行流量转发，提高了网络带宽利用率。 \nEVPN携手VXLAN，可谓是强强联手，为5G数据中心的网络建设带来了 全新的思路。 \nE V P N 是如何携手V X L A N 的？ EVPN通过扩展BGP，新定义了5种BGP EVPN路由来帮助VXLAN自动建 立隧道。 \nEVPN通过RT-3完成VTEP（VXLAN Tunnel End Points，VXLAN隧道 端点）的自动发现、VXLAN隧道的自动建立。通过RT-2完成两个VTEP之间 的MAC地址同步、路由同步两个功能。通过RT-5引入外部路由，进而通告到 EVPN构建的私有网络中，也就是当一个VTEP通过RT-5引入外部路由时，该 私有网络中的其他VTEP都能学到这些外部路由。此外，通过RT-1和RT-4可 以对VXLAN隧道进行冗余保护。这样基本的网络转发条件就准备齐活了，流 量可以顺利转发了。 \n整个过程中，VXLAN隧道的自动建立是关键的一环，那么EVPN是如何使 VXLAN隧道自动建立的呢？ \n在隧道建立过程中，EVPN充当网络调度指挥官，负责管理两个VTEP之 间的信息通告，在两个VTEP之间协商VNI（VXLAN Network Identifier， VXLAN网络标识）、MAC、HOSTIP、VTEP等信息，信息确认无误后，隧道 就自动建立了。成功建立了VXLAN隧道之后，两个VTEP之间的数据包就可以 通过VXLAN隧道进行转发了。 \n有了EVPN技术，建立VXLAN隧道就简单多了，配置集中在控制平面完成， 位于VXLAN网络中的终端设备不再需要额外的配置，整个网络变的更加灵活、 易扩展、易运维。 \n在5 G 数据中心中怎么用？ EVPN携手VXLAN给5G数据中心网络带来了很多惊喜，已经成为5G新一 代数据中心网络建设的必选内容。引入EVPN的VXLAN技术不仅可以实现数据 中心内部虚机的互联，助力虚拟化与云计算服务，还可以实现数据中心间的互 联，扩大网络规模。 \n虚机互联 虚拟化与云计算已成为当今信息产业领域最受瞩目的新兴概念，通过引入 EVPN的VXLAN技术可以实现数据中心内部虚机的互联。其数据平面基于IP网 络，控制平面基于EVPN进行路由信息的通告，利用EVPN的灵活性可以满足 服务器集群或虚机动态迁移等场景，有效地降低运维的复杂度。 \n数据中心互联 数据中心互联时，EVPN携手VXLAN通过控制平面与数据平面分离简化配 置，实现数据流在所有数据中心之间自由互通，可以建设高速高效的数据中心 互联网络。 \nEVPN技术为数据中心网络注入了新的活力，已经成为5G数据中心网络建 设中必不可少的一部分，EVPN携手VXLAN助力5G数据中心的虚拟化演进， 使得大型数据中心网络逐步由传统网络架构迈向云化架构。 \n除了5G数据中心，EVPN还广泛应用于5G网络的其他场景。EVPN的全球 标准也已全面完善发布，不同的设备厂商基于EVPN标准也可以进行对接和互 通，相信在越来越多的5G数据中心网络建设中，EVPN必将发挥更大的作用。 \n在之前讲的“原来你是这样的5 G 电信云”中，我们提到了运 营商通过引入5G电信云来承载5G业务，将传统封闭的电信网络转变成为灵活 开放的云化结构，云化的5G电信云网络采用了中心云、边缘云和接入云的三 级分布式网络架构。 \n随着5G大规模的建设，5G电信云的部署也在逐步推进。目前运营商首先 部署的主要是中心云。中心云的数据中心数量少、规模较大，当前主要应用于 5GC（5G Core Network，5G核心网）业务。今天小编就带大家一起了解中 心云数据中心的逻辑组网。 \n中心云数据中心网络采用NFV（Network Functions Virtualization，网 络功能虚拟化）/SDN（Software Defined Network，软件定义网络）虚拟化 组网方式，与传统数据中心网络相比有了逻辑层次的划分，从下层到上层可分 为三层，依次为：Underlay网络、Overlay网络和业务层网络。 \n什么是U n d e r l a y 网络，如何组网？ Underlay网络即底层网络，在数据中心内，Underlay网络就是数据中心 的物理网络，负责IP报文的高速转发。 \n在云化网络时代，作为中心云数据中心的Underlay网络，需要满足高安 全性、高可靠性、易于扩展等基本要求。为满足这些要求，我们一起来看看在 Underlay网络采取了哪些举措。 \n第一，按功能进行域的划分。 按接入服务器的不同功能，Underlay网络被划分为三个域：计算域、存 储域和管理域。 \n这三个域各自独立部署并结合防火墙技术，以保证网络的安全可靠。 \n第二，采用全新的Spine-Leaf网络架构。 Underlay网络用Spine-Leaf（脊—叶）网络架构代替了传统的三层网络 拓扑结构。 \n传统的数据中心三层网络拓扑结构包括核心层、汇聚层、接入层，虽然经 过多年发展，但仍存在一些限制与弊端，如带宽的浪费、难以适应超大规模网 络等。 \n而Spine-Leaf网络架构只有两层，Spine相当于核心层、Leaf为接入层。 扁平化的Spine-Leaf网络架构具有低时延、高可靠、易扩展等优点，可 满足5G时代数据中心大规模组网以及未来网络规模扩展的需求。 \n第三，使用L3路由组网。 在采用Spine-Leaf网络架构的基础上，Underlay网络使用L3（Layer3，第 3层）路由组网，通过动态路由协议进行网络互联。 \n相较于L2（Layer2，第2层）组网时需要阻塞冗余链路防止环路，通过动 态路由协议互联的L3路由组网的优势就在于： \n• Spine-Leaf节点之间使用L3路由接口进行互联，避免了L2网络互联时可 能产生L2环路的问题。 \n• 形成ECMP（Equal-Cost Multi-Path routing，等价多路由）等价多路 由负载均衡，即到同一目的地有多条等价链路，不仅可以增加带宽，多条等价 链路还可以实现链路备份。 \n除了以上三点，在Underlay网络中，服务器双上行到Leaf交换机，Leaf 交换机成对部署，实现了服务器接入的安全可靠。DC GW（Data Center Gateway，数据中心网关）路由器采用双活方式连接到运营商的IP骨干网络， 实现数据中心与无线侧网元互通、与其他数据中心间互通以及Internet访问。 \n由此可见，数据中心已铺好一个安全可靠的Underlay网络，为Overlay网 络的实现打好了一个稳固的基础。 \n为什么需要O v e r l a y ？ Overlay网络即叠加网络，是基于底层物理网络（Underlay网络）通过虚 拟化技术构建出的一个虚拟网络。 \n5GC网元虚拟化之后，将传统物理网元的功能从专用硬件中解耦出来，由 安装在虚机上的软件实现，虚机则安装在服务器上。为了实现资源共享、负载 均衡及容灾场景，虚机需要在数据中心内的不同服务器之间、甚至不同机房之 间进行动态迁移。 \n所谓动态迁移，就是保证虚机上业务正常运行的前提下，将虚机从一台服 务器移动到另一台服务器上，迁移前后用户无感知、不会察觉到任何差异。在 同一个L2网络内进行虚机迁移时，可以实现动态迁移，迁移前后虚机的IP地址、 MAC等参数可保持不变，迁移过程中业务能够正常运行；如果跨L2网络进行 虚机迁移，会因为需要改变虚机的网关及IP地址，造成业务中断。 \n由于Underlay网络采用的是L3路由组网，所以Underlay网络的结构无法 直接满足虚机跨L2迁移的需求。而对于服务器虚拟化的5G数据中心而言，跨 \nL2的动态迁移会是一种业务常态。既然Underlay网络无法满足，为什么不在 Underlay网络上再构建一层可实现L2网络功能的虚拟网络呢？ \n这就是构建Overlay虚拟网络的一个主要出发点：不受底层Underlay网络 的限制，将所有可能会涉及到跨L2迁移的服务器全部纳入到同一个L2域中， 构建一个可满足虚机动态迁移的“大二层”网络。 \n如何构建O v e r l a y 网络？ 为了实现构建Overlay网络这个目标， 5G电信云使用了EVPN（Ethernet Virtual Private Network，以太网虚拟专用网络）和VXLAN（Virtual Extensible Local Area Network，虚拟可扩展局域网）这两个比较火的技术。 \n（关于EVPN和VXLAN的详细介绍请查阅：5 G 时代，是谁为数 据中心带来了新的活力？） \nVXLAN可看作是一种隧道技术，通过在L3路由组网上建立VXLAN隧道实 现隧道两端的L2互通，这些一个个VXLAN隧道就组成了一个支持跨L2互通的、 虚拟的“大二层”Overlay网络。 \nVTEP （VXLAN Tunneling End Point，VXLAN隧道端点）作为VXLAN 隧道的端点，负责VXLAN报文的封装/解封。 \nVXLAN网关实现不同VXLAN网络之间、或者VXLAN网络和非VXLAN网 络之间的L3路由互通。 \n而EVPN作为VXLAN的控制面，负责实现VTEP自动发现、VXLAN隧道 的自动建立。 \n了解了上面知识，我们接着看5G电信云的Overlay网络是什么样的。 为满足业务迁移需求，5G电信云通常只在业务域部署Overlay网络，而存 储域和管理域仍采用传统VLAN组网。 \n业务域的Overlay组网，主要有硬件Overlay方式和混合Overlay方式。 \n硬件Overlay 硬件Overlay采用硬件网络设备Leaf交换机作为VTEP（即硬件VTEP）， DC GW路由器作为VXLAN网关。SDN控制器对Leaf交换机、DC GW路由器 等Overlay网络设备进行统一的管理和控制，Leaf交换机下接入的服务器不需 要支持Overlay。 \n硬件Overlay架构简单，网络健壮性更好，采用硬件交换机作为VTEP， 可以充分发挥硬件网络设备高性能的优点，适合运营商网络高带宽、大流量、 高可靠的要求。 \n混合Overlay 混合Overlay有两种VTEP，VTEP既可以建立在硬件网络设备Leaf交换机 （即硬件VTEP）上，也可以建立在虚拟交换机vSwitch（即软件VTEP）上。 混合Overlay中依然采用DC GW路由器作为VXLAN网关。 \n在混合Overlay中，硬件VTEP和VXLAN网关之间运行EVPN转发面控制 协议，而软件VTEP和SDN控制器之间运行OpenFlow协议实现转发面控制， SDN控制器与VXLAN网关也通过EVPN交互信息。 \n此时，SDN控制器像一位“翻译家”，负责EVPN与OpenFlow两个转发 控制面信息互通，从而实现整网MAC地址和IP路由信息等表项的同步。 \n混合Overlay网络相对复杂，但能支持多种形态的接入，轻载业务通过软 件VTEP接入，重载业务通过硬件VTEP接入，这样只需要部署少量的VXLAN 硬件网络设备即可完成较大规模网络部署。因此，混合Overlay对网络硬件依 赖低，部署成本低，在对基础网络不做大改动的情况下实现快速部署。 \n这样，通过实施硬件Overlay或混合Overlay方案，就在Underlay网络上 建立了通过一个个VXLAN隧道连接的、虚拟的Overlay网络。 \n因为Overlay网络是构建在Underlay网络之上，因此只要Underlay网络IP 路由可达的地方，都可以通过VXLAN隧道建立跨L2互通的、虚拟的Overlay 网络，这个Overlay网络可以使同一数据中心内部不同机房成为一个“大二层” 网络，也可以跨数据中心构成一个“大二层”网络。 \n正如前面提到，运营商引入5G电信云主要用来承载5G业务，我们前面搭 好的Underlay网络和Overlay网络，最终目的是服务于最上层的业务层网络， 业务层网络可以看作是Overlay网络的应用层。 \n5GC网元虚拟化之后，5GC业务通过各个VNF实现。同一VNF内部各模块 之间的内部通信流量（东西向流量）以及各个VNF之间的业务流量（南北向 流量），通过Overlay网络的VXLAN隧道进行IP转发，形成业务层网络，业 务层网络实现了5GC业务的互通。 \n由于有了Overlay这个“大二层”网络，业务层网络真是获益多多： \n• 完美解决了虚机动态迁移的问题。 \n• VXLAN支持的虚拟网络VLAN的数量由4096个（12比特位）扩展 为1600多万个（24比特位），因此实现对超大规模网络的支持。 \n• 在不变动底层网络的情况下，可根据业务负荷对VNF的资源进行弹 性扩展、收缩，或根据业务需要随时对VNF进行创建和删除。 \n写在最后 至此，通过Underlay和Overlay的完美配合，构建了一个开放、可靠、 高效的5G电信云网络。当前，各大运营商5G业务部署正如火如荼地进行， 5G电信云网络将为5G电信业务的快速发展提供可靠支撑。 \n火车是大家最为熟知的交通工具之一，一般火车都是前后各配置一个火车 头，采用前牵后推共同牵引的方式，共同分担牵引动力。当一个火车头出现故 障时，另外一个车头还能提供全量的牵引力，保障列车正常运行。 \n对于这种主备设备实现负载分担的方式，在通信网络中也是很常见的，例 如我们今天要介绍的堆叠技术。 \n随着社会的发展，生活的各行各业都离不开互联网，基础网络的健壮性受 到了严峻的考验。在即将进入的5G时代，情况会愈加明显。堆叠技术，就具 备这种保障网络健壮性的能力。在网络设备出现异常时，同样位置具备同样功 能的设备能够接管异常网络设备所管业务，保障业务正常运行。 \n什么是堆叠？ 堆叠技术是从3G时代沿用至今的一种设备虚拟化技术，在即将进入的5G 时代，这个技术也将继续在通信网络的接入层和汇聚层发挥强大的作用。 \n堆叠技术是将两台或多台硬件设备组合在一起，虚拟化为一个逻辑设备进 行使用和管理，我们将虚拟化的这个逻辑设备称为一个堆叠系统。同一堆叠系 统中的多个硬件设备之间相互冗余备份，某一硬件设备故障时，该堆叠系统中 的其他硬件设备可以接管控制，避免因单点故障导致业务中断。 \n同时，不同堆叠系统之间采用多链路聚合技术，把原来多个硬件设备间的 多节点、多链路的结构，变成逻辑上单节点、单链路的结构，既保证了高可靠 性，又解决了环路问题。 \n堆叠是怎样实现的？ 堆叠技术经常被用于接入层或汇聚层的交换机组网中。将多台交换机设备 组合在一起，对外呈现为一个堆叠系统，进行统一管理，这种堆叠技术是如何 实现的呢？我们通过下面这个“栗子”来详细了解一下吧！ \n首先要进行角色划分，决定谁是主用火车头、谁是备用火车头、谁是车厢。 为了民主，这个过程要进行选举。一个堆叠系统中要有一个主用交换机和一个 备用交换机，其余为待命交换机，待命交换机只负责提供带外通道和拓扑发现 的功能。 \n选举是有一定规则的，一般来说优先级高的为主用交换机，如果优先级一 样，MAC地址小的为主用交换机。优先级次高或者MAC地址次小的为备用交 换机。其余的全部为待命交换机。优先级和MAC地址一般是通过配置命令提 前配好的。 \n当堆叠系统内的设备发生一些异常时，比如宕机或自动重启等，会导致角 色的变化，类似内阁出现变动时要重新选举一样，我们有详细的规则应对角色 的变化。 \nl 如果系统里只有主用、备用两台交换机，当主用交换机出现问题时， 备用交换机升级为新的主用交换机；原来的主用交换机重启后变为新的备用 交换机。 \nl 如果系统里有三台及以上的交换机，当主用交换机出现问题时，备用交 换机升级为主用交换机，待命交换机中选举出一个升级为备用交换机，原来 的主用交换机重启后变成待命交换机。其中，待命交换机选举升级为备用交 换机时依旧遵循老规矩：优先级高或MAC地址最小的中选。 \n这种因为设备出现异常而导致的主用、备用交换机变更过程我们称为主备 倒换。 \n无论内部怎么选举，对外必须是一个整体，就像一辆列车，无论有几个车 头、几节车厢，都是作为XX车次被车站管理。同样，同一个堆叠系统无论由 几台堆叠，对外都呈现为同一个MAC地址和IP地址，即主用交换机管理口的 MAC地址和IP地址。即使堆叠系统发生主备倒换，该MAC地址和IP地址也不 会变更，就像无论列车是怎样组成的，列车的车次都不会变更一样。 \n内部选举完毕，实现安定团结的局面后，就可以作为一个整体运行了。运 行的过程中，堆叠系统内主备交换机上的各类表项均会自动同步，以保持两边 的一致性，例如ARP（Address Resolution Protocol，地址解析协议）表、 路由表等。一旦堆叠系统发生主备倒换，备用交换机升级为主用交换机后，能 够快速接替原主用交换机转发各类表项。 \n加入一台硬件设备不会影响其他硬件设备的正常业务。当一台新设备加入 堆叠系统时会有以下两种情况： \n去掉一台硬件设备不会影响其他硬件设备的正常业务。当一台设备离开堆 叠系统时会有以下三种情况： \n离开的设备为主用交换机，会触发堆叠系统主备倒换。 \n离开的设备为备用交换机，原先的待命交换机将选举出一个 成为备用交换机。 \n离开的设备为待命交换机，不会触发主备倒换，堆叠系统中 的其他交换机只是更新并转发信息。 \n堆叠有哪些优势？ 简化逻辑与拓扑管理 虚拟化后的堆叠系统，其配置、维护和单台设备几乎相同。因此，在物理 节点相同的情况下，采用堆叠技术后需要管理、维护的节点可以减少一半以上。 \n结合链路聚合去环路 堆叠技术与多链路聚合技术结合可以去除环路，不再需要S T P （Spanning Tree Protocol，生成树协议）、 VRRP（Virtual Router Redundancy Protocol，虚拟路由器冗余协议），简化了网络配置。 \n提高链路利用率 由于STP会阻塞部分链路，因此STP网络中链路并没有完全利用。而采用 堆叠技术后的网络不需要STP协议，也就不会阻塞链路，链路利用率可以达到 100%。 \n堆叠应用在哪里？ 堆叠技术已逐渐成为位于网络接入层或汇聚层的交换机产品的必备技能， 例如，数据中心的网络接入层就通常会使用堆叠技术保证服务器接入的可靠性。 \n目前，虽然很多厂家的交换机设备都支持堆叠技术，但是因为各个厂家的 技术私有性，不同厂家设备之间无法堆叠。另外，因为不管堆叠系统由几台交 换机组成，控制面只在主用交换机上，其他交换机是不能参与控制的，因此虚 拟化规模受限于主用交换机的处理能力。堆叠系统的规模还停留在几台到十几 台的量级。 \n从堆叠技术在交换机设备中的应用之广来看，堆叠无疑算是交换机产品提 高网络健壮性的必杀技之一。在即将进入的5G时代，堆叠技术仍将继续大显 身手。 \n随着5G的发展，万物互联带给了我们对未来生活的无限遐想，但是，对 于通信网络而言，随之而来的是网络规模和复杂度的指数级的增加，那么是谁 在背后默默地支撑着如此庞大的网络，那就是-----5G网管。 \n在通信网络中，既有存量的传统网络，又兼具新型的通信网络。传统网络 底蕴深厚，但不管是网络速度或带宽等方面都已经不满足人们的需求，5G网 络则是传统网络在技术上的延伸和发展。面对这种二者并存的现状，5G网管 在软件架构上装备了最新技能“微服务”和“容器”，具备了对网络资源灵活 调度的功能，既能照顾到传统网络的业务发放，也能满足新型5G网络蓬勃的 业务需求。 \n那么，“微服务”和“容器技术”是怎样的必杀技呢？ \n5 G 网管必杀技之微服务 顾名思义，微服务就是微小的服务。可不要小看这个微服务，麻雀虽小五 脏俱全，每个微服务都可独立提供单一功能、具备独立部署和运维管理能力。 \n在微服务诞生之前，传统网管系统采用耦合技术，各种大大小小的系统编 织在一起形成一个统一的管理系统，这种软件设计方式有不少的弊端，例如开 发维护以及做系统的扩展时，操作的对象都是整个系统，工作量大且系统不易 扩展。 \n微服务技术的到来使得这些问题迎刃而解，原因就是5G网管将各个系统 微服务化。如下图所示，因为5G网管成长在一个虚拟的软件平台（号称云化 平台）上，部署时相当于空降了一个软件“仓库”到云化平台上，而各种系统 功能被抽象成不同用途的微服务存放在这个软件仓库中。应用时，根据业务需 要启用软件仓库中的微服务，微服务之间利用自带的接口互相通信。相比传统 网管，采用微服务软件架构，5G网管整体架构更加灵活自由，且易于扩展。 \n微服务天然具有这样的优势：“职责单一”、“服务自治”和“轻量通 信”，下面我们就来解析下微服务的三个优势是如何助力5G网管的。 \n职责单一 在5G时代，新型业务层出不穷，要求网管能够迅速接管新业务，同时能 够快速定位并解决网络问题，采用微服务架构能满足这种需求。简单来讲，就 是在将功能微服务化后，一个微服务完成一个职责，每个微服务之间有清晰的 界线，仅专注做好自己的事情。 \n微服务职责的单一性使得它可以根据使用需求被灵活调用，一个微服务的 加入或者退出都不会影响到其他的微服务，满足功能的快速迭代，另一方面由 于微服务之间没有功能的交叉，所以不会因为个别微服务的故障导致整个系统 的瘫痪，易于保持高可维护性和开发效率，使得快速交付和应对变化成为可能。 \n服务自治 除了职责专一，微服务的服务自治能力也相当吸引眼球。服务自治是指微 服务可以独立部署运行。这个优势在服务部署和故障定位时帮助非常大。有了 自治能力，每一个微服务拥有跟其他复杂软件系统同样的生命周期，包含了开 发、测试、运维、部署各个阶段，同时拥有自己的数据库。 \n由于微服务具备完善的自我管理的能力，从开发阶段开始，在微服务内部 实现自我修正和检验，直到达到可部署的目标。对比传统的网管系统，这样的 微服务架构使得5G网管不用在每次发布新功能时都进行一次全量校验，只需 要各个微服务内部检验通过就可以了，大大的提高了部署的效率。特别是故障 定位时，工程维护人员在网管系统中找到待维护的微服务进程后，在微服务内 部解决问题，不会对当前的网管系统造成干扰。 \n轻量级通信接口 微服务的独立性决定了不同的微服务模块可以采用不同的软件语言编写。 虽然独立性是微服务的特质，但是为了完成一个应用，微服务之间在必要的时 候也是需要有一定的通信往来的，不同的软件语言有可能阻隔了微服务之间的 往来。为了解决这个问题，实现微服务之间的互通，5G网管采用轻量化的通 信接口。所谓轻量化的接口，就是不考虑编写该微服务的软件语言是什么，不 论使用哪种软件语言编写的微服务，在相互通信的时候都采用统一的标准接口 HTTP/REST。 \n轻量级接口（HTTP/REST）帮助微服务之间互通有无，相互调用，适应 复杂多变的通信网络，临场反应能力是极好的。 \n5G网管采用的微服务技术完成了网络功能的“分而治之”，但是软件产 品最终的目的是实现应用。那么，如何组织微服务完成应用呢？下面就该容器 技术隆重登场了。 \n5 G 网管必杀技之容器 5G网管的应用需要启用软件仓库中的微服务，如下图所示是没有引入容 器技术时的软件架构。应用1需要启用微服务A、B、C、D、F、G，应用2需 要启用A、B、C、D、E、F。 \n按照这样的软件架构，部署应用1和应用2相当于将全部应用相关的微服 务都部署一遍。如果应用1和应用2中有大量的重复微服务，重复部署微服务对 软件的空间和部署效率都是一种伤害。 \n采用容器技术能大大的避免这方面的问题。应用相当于码头，容器相当于 一个集装箱，微服务相当于货物，集装箱装载货物在码头之间运输。因为货物 是装在集装箱中的，所以在上、下码头时，仅对集装箱操作，而不需要一个个 的搬运集装箱中的货物。 \n容器的作用跟集装箱的作用一样，用来封装“微服务”这样的货物。如上 图所示，应用1中启用了微服务A、C、F，采用容器1装载，应用2同样需要容 器1中的微服务，那么在应用2时仅对容器1操作就行了，而不需要重新部署容 器1中的服务，这样就大大提升了软件部署的效率。 \n码头上会根据集装箱内货物情况，将装载相同类型货物的集装箱放在同一 艘货轮上，形成集装箱组合，便于统一管理。同样的，对于传承了微服务独立 性的容器，也需要根据相关性进行统一管理，容器组就担起了这个大任，像一 艘货轮一样装载着具备相近功能的容器。容器组内除了包含一个或多个容器， 还提供了存储空间、网络IP地址、以及容器们在容器组内必须遵守的规则。保 证容器之间既能完美隔离，又能相互配合的完成任务。 \n5G时代，各行各业应用层出不穷，传统网管无法满足不同应用场景的需 求，5G网管借助微服务和容器两种技术，提供面向不同场景的服务，按照需 要将网络资源进行灵活分配，从而实现网络服务能力的灵活组合，方便网络资 源灵活调度、运维及业务部署。 \n随着5G时代的飞速发展，承载网规模不断增大、网络复杂性持续增加， 这就需要简化网络管理模式、加快网络设备和服务的部署，实现网络配置自 动化。 \n目前承载网的管理主要依靠命令行和SNMP两种管理模式。然而这两种管 理模式无法满足5G网络发展需求，主要体现在： \n1. 命令行管理模式： 依赖人工操作，配置、维护成本高，且各厂商差异大，互通困难。举个例 子，当网管人员根据用户需求对设备实施配置之前，需要先阅读设备手册，了 解不同厂商设备在功能实现和命令行格式上的差异，以及配置是否有约束和依 赖等。当完成这个学习过程后，再人工输入命令行，一旦命令行很多，那就苦 不堪言。由此可见，命令行并不适用于大规模的部署。 \n2. SNMP（Simple Network Management Protocol，简单网络管理协议） 管理模式： \n不支持锁定功能、配置效率低、配置失败不能回退。举个例子，当网管人 员A和网管人员B同时对同一设备的同一配置参数进行操作时，由于SNMP不 支持锁定功能，会导致配置冲突且无法自动恢复到操作前的状态。由此可见， SNMP的配置管理能力薄弱，不满足大型网络的复杂配置需求。 \n那么，如何解决目前的网络管理模式无法满足5G网络发展需求的问题呢？ \nNetconf 协议应运而生。 \n什么是N e t c o n f ？ Netconf（Network Configuration Protocol，网络配置协议）是一种网 络设备管理协议。它提供一套管理网络设备的机制，用户可以使用这套机制配 置网络设备、查询网络设备的配置和状态。 \n那么Netconf提供的这套机制相比于前面提到的两种网络管理模式的优势 体现在哪里呢？要想理清这个问题，我们先从系统架构、逻辑分层、数据集和 会话流程四个方面认识下Netconf。 \n系统架构 Netconf采用C/S（Client/Server，客户端/服务器端）架构。客户端是 网管平台、控制器等，服务器端是网络设备，比如路由器、交换机等。客户端 通过Netconf协议对服务器端进行配置管理，工作原理如下： \n1. 客户端和服务器端上存在相同的YANG模型。 \n2. 客户端可以基于YANG模型生成符合Netconf通信规范的XML编码进而 在传输通道上传输。 \n3. 服务器端可以识别接收到的XML编码，根据XML编码的内容执行相应 的操作，达到通信的目的。 \n这里引入了YANG模型和XML编码的概念。YANG（Yet Another Next Generation，一种数据建模语言，目前还没有恰当的中文全称哦）模型是 Netconf的数据模型，用于描述基于Netconf协议通信的客户端和服务器端之 间的交互信息。XML（Extensible Markup Language，可扩展标记语言）编 码是Netconf协议的编码格式，YANG模型可以被完整地转换为XML编码格式 进行传输。 \n怎么理解YANG模型的作用呢？客户端需要通过Netconf对网络设备的配 置和状态做操作，例如：修改配置、获取状态，通过YANG模型就可以对这两 种操作进行建模。建好的YANG模型进行实例化后，再转换成XML编码格式传 输。 \n打个比方加深下理解，一个网店的店长要求店员提供一份本店宝贝的用户 满意度数据，需要包含宝贝名称、评价用户账号、评分和评价内容。于是店员 做了一个用户满意度表格，包含了上述四个要求项目，并根据实际情况填入了 真实信息，最后将表格保存为PDF格式发送给店长。那么店长的描述，就可以 理解为“建模”，YANG模型就是店员制作的用户满意度表格，填入实际信息 的用户满意度表格就是实例化YANG模型，最后保存为PDF就相当于将实例化 后的YANG模型转化为XML编码。 \n逻辑分层 Netconf 协议在逻辑上分为四层，包括传输层、消息层、操作层和内容层。 每个层分别对协议的某一个方面进行包装，并向上层提供相关的服务。分层结 构使得每层只关注协议的一个方面，简化协议复杂度的同时，也降低了各层内 部实现机制的变更对其它层的影响。 \n继续以网店为例，来看看这四层的作用。 \n传输层可以理解为店长和店员对话的媒介，比如电话沟通。 \n消息层可以理解为店长和店员之间按事先规定的方式进行对话。比如，规 定店长和店员彼此之间要先发送一句“HELLO!”来开始接下来的任务对话，当 店长发送下架某个宝贝的消息给店员，店员接收到消息后进行相应操作后，要 回复店长一句“已完成”。 \n操作层规定了可以基于数据模型对店铺宝贝进行哪些操作，比如上架、下 架宝贝、查询宝贝的用户反馈情况等。操作层还提供了错误恢复等功能，例如 在下架宝贝的一系列操作中有一个步骤错了，可以立即中断下架操作，店铺界 面上宝贝的数量等信息会恢复为下架操作前的状态。 \n内容层的作用可以理解为管理店铺宝贝的数据模型。比如下架宝贝模型 （包括下架宝贝名称、下架时间、下架原因）、用户满意度模型（宝贝名称、 评价用户账号、评分和评价内容）、销量分析模型（宝贝名称、在售时间、价 格）等。这样店员就可以根据需要单独查看某一类模型，界面清晰，目的明确。 \n数据集 数据集是Netconf定义的数据空间，用来在服务器端存储各类设备的配置 和状态数据。Netconf定义了以下三种数据集： \n1. 运行数据集：存放网络设备上当前处于活动状态的完整配置。 \n2. 备选数据集：存放设备将要提交到运行数据集的各项配置，对备选数 据集的任何改变都不会影响网络设备。 \n3. 启动数据集：存放设备启动时所加载的配置，相当于已保存的配置文 件。 \n三种数据集之间既实现了配置隔离，又可以通过不同的操作进行配置数据 迁移。 \n这里，运行数据集存储的数据就相当于店铺页面中实时展示出来的宝贝信 息。备选数据集存储的数据就相当于准备提交到店铺页面上的宝贝信息。启动 数据集中存储的数据可以理解为打开店铺页面时需要加载的数据。 \nN e t c o n f 会话流程 了解了Netconf的系统架构、逻辑分层和数据集，我们来看下Netconf是 如何基于这三者进行会话的。客户端与服务器端通过Netconf协议进行会话的 交互流程为： \n举个例子来说明Netconf的会话过程，假设现在需要通过客户端修改设备 IP地址。 \nN e t c o n f 的优势 通过以上关于Netconf的介绍，可以看出Netconf相比于传统网络管理的 优势。 \nN e t c o n f V S 传统命令行 传统命令行管理模式下，网管人员需要学习配置手册，深入了解不同设备 之间命令行格式的差异，再进行人工操作，效率低下、容易出错。 \n而Netconf基于YANG模型对设备进行操作，不同厂商YANG模型的差异 由网管进行解析，用户只需要操作图形化界面的网管就可以实现自动化的配置。 \n相比于命令行，Netconf实现了自动化配置。那么相比于SNMP协议， Netconf又有何优势呢？ \nN e t c o n f V S S N M P \n1. 效率高 传统的SNMP管理模式基于设备属性逐个进行操作，查询中需要多次交互 才能够完成，效率低下。 \n传统的SNMP管理模式基于设备属性逐个进行操作，查询中需要多次交互 才能够完成，效率低下。 \n2. 可靠性高 传统的SNMP管理模式可靠性弱，包括： \n而Netconf能够完美解决以上问题： \n3. 灵活性强 传统的SNMP管理模式下，配置失败不能中断、不能回退到之前的配置。 对于Netconf，当一系列配置操作中的任一操作出错，即可立即中断进程， 使配置数据回退到未进行操作的状态。 \n4. 扩展性强 传统的SNMP管理模式，操作类型固定，不可以自定义，扩展性差。 \n对于Netconf，当协议规定的基本操作不够用时，还可以根据需要自定义 操作。而且，协议采取分层模型，各层之间相互独立，对协议中的某层进行扩 展时，不影响其它层。 \n根据上面四点的描述，可以总结出Netconf相比于SNMP的优势如下。', 'id': '932557_1'}
{'label': 'table', 'content': '根据上面四点的描述，可以总结出Netconf相比于SNMP的优势如下。\n<table><tr><th>特性 </th><th>SNMP </th><th>Netconf</th></tr></table>', 'id': '932557_2'}
{'label': 'text', 'content': 'N e t c o n f 的应用 Netconf协议，以其优于传统网络管理模式的高效率、高可靠性、高灵活 性、强扩展性的特点，能够简化和加快网络设备、服务的部署过程。 \n比如，某社交网络平台流量波动很难预测，突然出现的热点话题，使得平 台流量垂直上升。这时，如果网络设备不能及时做出相应的配置调整，则会导 致网络卡顿甚至瘫痪。当Netconf应用于网络设备的配置管理中，网络设备可 保存一份适配流量高峰期的忙时配置数据于备份数据集中。这样，当流量高峰 期突然到来时，就可以快速切换到忙时配置，保证平台正常运行，用户体验非 常好！ \n5G时代，网络规模变大，复杂程度增加。而Netconf协议，以其优秀的配 置管理能力，证明它就是5G网络管理者的不二人选！ \n“前方道路出现事故，已为您重新规划路线”。 \n经常借助导航日常出行的小伙伴们，听到这句话一定不感到陌生。当出行 道路中出现突发事故或大规模拥堵的时候，导航软件通常会快速计算出新路线， 指导我们更快地到达目的地点，减少出行时间，提高出行效率。 \n那么，在移动通信网络的“交通要道”--承载网中，出现故障时，是否 也有这样“靠谱”的导航系统呢？答案是肯定的。今天就让我们来详细了解下 在5G承载网中强势出道的TI-LFA（Topology-Independent Loop-free Alternate，与拓扑无关的无环路备份）保护技术。 \n首先，在介绍TI-LFA之前，先给大家科普下FRR（Fast Reroute）快速 重路由的概念。 \n什么是F R R 快速重路由? 在通信网络中传送业务数据包时，也会遇到拥塞或出现故障的情况。例如， 当业务传输路径上的节点或某段链路失效时，数据包可能会被丢弃或形成回环， 直到网络通过路由协议重新收敛、计算出新的拓扑和路由，才会恢复正常传送。 \n但是受网络规模、路由条目等影响，网络收敛的时间可达百毫秒至秒级， 造成网络卡顿或断网。而网游、直播、视频会议等新兴应用，由于对网络流量 的稳定性和实时性非常敏感，网络卡顿和断网都会带来不好的用户体验。因此， 当网络发生故障时，在网络收敛之前，临时、快速恢复传输路径显得尤为重要。 \n在通信网络中，这种能快速提供临时修复路径且不产生转发回环的机制， 就是FRR快速重路由技术。 \n针对承载网中的核心节点和核心链路，负责提供保护的节点会预先计算好 FRR修复路径，并写入数据平面。当某个节点/链路出现故障时，保护节点会 激活修复路径、切换业务流量，快速恢复数据包正常传送，同时触发IGP （Interior Gateway Protocol，内部网关协议）路由协议进行收敛计算。FRR 快速重路由技术通常能在50ms内恢复业务，比起前面提到的百毫秒至秒级的 收敛时间，50ms内的业务切换对普通用户来说，基本感知不到流量的中断和 卡顿现象的。 \n在5G时代，随着技术的迭代更新，FRR快速重路由技术也实现了新的突 破，演进出了一种更高效更靠谱的FRR快速重路由技术，也就是我们今天的主 角--TI-LFA技术。 \n别急~~别急~~ 在正式开讲TI-LFA技术之前，不得不先提下它的两位前 辈LFA（Loop-free Alternate，无环路备份）技术和RLFA（Remote Loop- free Alternate，远端无环路备份）技术。 \nL F A 如何实现保护？ LFA技术是最早研究出的IP FRR解决方案，在这个阶段，我们会在网络中 设置本地修复节点（PLR节点，Point of Local Repair），这个节点与被保护 的节点/链路直连，负责为核心节点/链路提供保护，它可以在被保护的节点/ 链路发生故障之前提前计算好恢复路径。 \n当发生故障时，PLR节点找到一个与其直连的邻居，将流量转发到这个邻 居，再由这个邻居根据规划好的常规路径转发到目的地节点，既不会经过故障 部分，也不会将流量回环发回到PLR节点处。这个邻居就称为LFA节点，LFA 节点也称为目的地节点的“释放点”。 \n那么，怎么找到这个稳妥的LFA节点呢？计算公式很简单： \nDist（N，D）<Dist（N，PLR）+Dist（PLR，D） \nN，即PLR的各直连邻居 D，即业务传送的目的地节点 Dist（N，D），即N和D之间的最短路径 \n也就是说，如果某个直连邻居到达目的地节点的最短路径<该邻居到PLR 节点的最短路径+PLR节点到目的地节点的最短路径，那么这个邻居就是LFA 节点。 \n接下来，再结合下面这个动图来加深理解LFA技术是具体如何实现保护的。 \n在上图中，节点2（PLR）负责保护去往目的地节点8的业务流量。当节 点2和3之间的链路出现故障时，通过观察网络拓扑，我们可以看出节点2 的 直连邻居有节点1、3和6，1是源节点、3是故障链路上的节点，那么仅剩的节 点6是不是LFA节点呢？ \n参考上述公式，我们来计算下： \nDist（N，D）<Dist（N，PLR）+Dist（PLR，D） -->Dist（6，8）<Dist（6，2）+Dist（2，8） --> 20 < 10 + 20 满足判断条件，节点6就是目的地节点8的LFA节点。计算方法是不是很 简单？ \nLFA技术保护机制简单，算法也不复杂，但是存在两个不容忽视的缺点。 \n缺点1：不是总能提供最优的恢复路径。 \n根据上图拓扑，我们可以判断出PE4是目的地节点5的LFA节点，但是 PE4相关的两条链路，其代价都非常大（为100），如果用作修复路径，可能 会导致拥塞甚至丢包，显然操作员不希望PE4节点用作流量转接。可是在没有 其他可选的LFA节点时，PE4仍会被用作临时恢复路径，哪怕它不是最优的。 \n缺点2：受网络拓扑限制，不是总能找到LFA节点，无法提供100%覆盖 率的保护。 \n根据上图拓扑，我们可以判断出：除去源节点1和故障链路上的节点3， 节点6是唯一的候选LFA节点，但节点6并不满足最短路径的判断条件。因此在 这种网络拓扑中，无法找到适合的LFA节点，从而无法提供故障时的恢复路径。 \n为了解决在某些拓扑中无法找到LFA节点的情况，RLFA（Remote LFA） 技术诞生了。 \nR L F A 解决了什么？ 在RLFA技术中，通过使用“虚拟LFA节点”扩展了经典LFA算法的覆盖 范围，“虚拟LFA节点”是可以用作LFA的远端节点，也称为RLFA节点。 \n那么，怎么找到RLFA节点？ RLFA节点需要满足以下两个条件： \n1. 从PLR节点到该节点的最短路径不经过受保护链路/节点。 \n2. 从该节点到目的地节点的最短路径不经过受保护链路/节点。 \n满足条件1的节点集合被称为PLR节点关于受保护链路/节点的P空间，满 足条件2的节点集合被称为目的地节点关于受保护链路/节点的Q空间，PLR节 点的邻居节点的P空间被称为扩展P空间（从邻居节点到该节点的最短路径不 经过受保护链路/节点）。 \n同时满足P空间（包括扩展P空间）、Q空间的节点，就是RLFA节点了。 \n我们就拿刚才没能找到LFA节点的那个拓扑来分析下~ \n1. 计算P空间：节点2到该节点的最短路径不经过链路2-3，因此P空间包括节 点1和6。 \n2. 计算Q空间：节点5到该节点的最短路径不经过链路2-3，因此Q空间包括 节点3、7和8。 \n3. 计算扩展P空间：节点2的邻居节点包括节点1和节点6。节点1的P空间包括 节点2，节点6的P空间包括节点2、7和8，因此扩展P空间包括节点2、7、8。 \n4. 找到RLFA：根据上图网络拓扑，没有找到同时满足P空间和Q空间的节点， 但找到了同时满足扩展P空间和Q空间的节点：节点7和8。因此这两个节点 就是候选RLFA节点。 \n根据网络拓扑，节点7距离PLR节点更近，因此选择节点7作为RLFA节点。 当链路2-3出现故障时，节点2（PLR节点）将流量转接到节点7，节点7再将 流量传送至目的地节点5。 \n现在，我们回到本段开始时的那个问题，“RLFA技术解决了什么？” \n相比LFA技术，RLFA技术将目的地节点的保护覆盖率提升到了95%- 99%，更进一步地提升了拓扑覆盖范围。 \n但是RLFA技术仍受到网络拓扑的限制，不能保证提供100%覆盖率，仍 存在无法找到RLFA节点的拓扑，如下图所示：节点3和4之间的直连链路代价 大，不符合最短路径要求；而节点3和4之间的最短路径为3-2-5-4经过了故 障链路，因此节点4不在PLR节点的扩展P空间中，P、Q空间无交集，找不到 合适的RLFA节点。 \n同时，RLFA技术并没有改善在某些情况下无法实现最优备份路径的问题。 \n随着5G时代的到来，更高效、更简单、更易部署维护的传输技术--SR （Segment Routing，分段路由）强势出道。在此背景下，解决RLFA网络拓 扑问题的终极杀手锏--TI-LFA（与拓扑无关的无环路备份）技术应运而生。 \nT I - L F A 如何成为杀手锏？ 让我们想得简单些，回到问题最初的起点，当故障发生时，什么路径是最 优、最自然的恢复路径呢？答案是肯定的：由路由协议收敛后得到的路径是最 合适的。所以直接计算出收敛后的路径作为恢复路径不就好了嘛？ \n那么，在没有SR之前，为什么不这样做？ \n因为通过路由协议收敛得到的转接节点可能不是LFA节点或RLFA节点， 转接节点存在将流量转发回保护节点PLR节点的情况，从而会导致暂时性环路。 因此，只有采用显式路径、强制约束的方式才能确保业务流量沿着收敛后的路 径转发且不形成回环。 \n关于SR的详细解读，请参阅《5G是如何传输数据的》 \n基于SR源路由的流量引导能力，通过将收敛后的路径编码成Segment List（标签栈）就足以避免环路的产生，同时可以确保恢复路径（即收敛后路 径）总是最优的备份路径。 \n一旦出现故障，TI-LFA技术被激活，恢复路径的标签栈就会被压入被修 复流量的数据包中，转发平面根据标签栈信息强制引导流量沿着收敛后的恢复 路径到达转接节点，确保数据包沿最短路径发送至目的地节点。 \n具体怎么实现？我们来看看RLFA技术没能解决的那个拓扑。 \n1. 计算P、Q空间 根据上图拓扑，可以判断出P空间包括节点1、2、3，Q空间包括节点4、5、6。 \n2. 剔除故障链路，计算最优收敛路径 \n剔除故障链路2-5，基于该拓扑计算最短路径时，得到收敛后路径为1-2-3- 4-5-6。 \n3. 沿着收敛路径选择P、Q空间的转发节点 节点3属于P空间，所以源节点到节点3为最短路径可达；节点4属于Q空间， 所以节点4到目的地节点为最短路径可达。同时，节点3与4为直连邻居，且均 在收敛后路径上，因此选择节点3作为P空间的转发节点，节点4作为Q空间的 转发节点。 \n4. 建立恢复路径（即收敛路径）的Repair List 建立节点2（PLR节点）去往目的地节点6的修复路径Repair List {103，777}， 其中103表示节点3的Node SID，777表示节点3到节点4链路的邻接标签Adj SID，用于将流量从节点3引导到节点4。 \n5. 将标签栈压入数据包头中，指引流量转发 带有目的地节点6的Node SID的数据包到达节点2，节点2在标签栈中压入上 一步的两个额外标签{103，777}，103引导流量由节点2转至节点3，再根据 777的邻接标签指引，将流量由节点3转发至节点4。节点4属于去往目的地节 点6的Q空间，节点4会采用常规最短路径将数据包发往目的地节点6。 \n了解了TI-LFA技术的保护机制之后，不难发现它相较于LFA技术和RLFA 技术的绝对优势： \n1. 原理上支持100%拓扑覆盖率 对于某些大型组网，LFA技术和RLFA技术存在P、Q空间无交集、且没有 直连邻居的情况，导致无法计算出临时恢复路径，产生流量丢失的现象。而 TI-LFA技术通过将恢复路径编码为Segment List的方式，采用显示路径强制 引导流量进行转发，对网络的物理拓扑无约束，提供了更高的网络可靠性。 \n2. 实现流量最优化 TI-LFA技术直接使用收敛后的路径作为恢复路径，有效解决了LFA技术 和RLFA技术中存在次优路由的问题，防止网络中产生瞬时拥塞的现象，降低 了网络成本，提高了网络带宽利用率。 \n3. 减少切换次数 直接采用收敛后路径作为业务恢复路径，相较于LFA技术和RLFA技术， 业务流量进行路径切换的次数由两次降为了一次，有效降低了业务可能中断的 时间。 \n4. 操作简单 无需从多个候补LFA节点中进行判断、选择最优LFA节点，从而降低了人 工部署维护的工作量。 \n网游、直播、追番、交通、医疗、工业，智能生活的方方面面都离不开承 载网在背后默默地助力与支撑，保障网络通信的高效与稳定。 \n而TI-LFA技术以其最优路径选择和全覆盖保护等优势，为低时延高可靠 的通信网络保驾护航，为万物互联的5G时代提供了更可靠的支撑！ \n随它瞬息万变，我定护你周全。 \n编者的话：TI-LFA保护机制的强大之处，远不止文中描述的这些，更多 高级特性等待着我们一起去探索哦~~ \nZTE中兴 | 秒懂承载 还记得你第一次学习PUSH和PULL两个新单词的情景吗？你是怎么区分 PUSH和PULL这两个单词的呢？相信下面这个图应该是很形象了~ \n其实，PUSH和PULL这两个动作在我们生活中处处可见，包括在通信网 络中也同样离不开这两个动作。那在通信网络中PUSH和PULL又是干嘛的呢？ \n在通信网络中，PUSH和PULL是采集数据的两种模式。 \nl PUSH：推模式，网络设备端主动发送数据给网管系统。 \nl PULL：拉模式，网管系统从网络设备端获取数据。 \n也就是说，PUSH是设备主动汇报自己的状态等数据，效率高；PULL是 需要网管系统来索取设备数据，设备被动的上报自己的状态等，效率低。 \n而我们已经进入飞速发展的5G时代，网络和设备支持的业务和应用都越 来越多，对网络的质量要求也越来越高，这就需要网络监管提供更高的性能。 而在传统的网络监管技术中，如SNMP（Simple Network Management Protocol，简单网络管理协议），网管系统采用PULL模式来采集数据，一次 查询对应一次响应，设备需要处理大量重复的查询报文，效率较低。 \n那如何才能更实时、高效、精确的采集网络运行数据，为网络监管提供更 高的性能呢？ \n新型数据采集技术——Telemetry应运而生。 \nT e l e m e t r y 是什么？ “Telemetry”源于希腊语，词根：tele = remote，metron = measure， 翻译过来就是“遥远的测量”，即遥测技术。也就是说，Telemetry技术是一 项远程的从网络设备（物理设备或虚拟设备）上高速采集数据的技术。 \nTelemetry技术与传统网络监管技术的本质区别在于：采用Telemetry技 术后，网管系统先向网络设备定制需要查询的数据，然后网络设备通过PUSH 模式主动向网管系统上送定制的数据信息，而不是采用“一次查询一次响应” 的PULL模式进行查询。 \nT e l e m e t r y 是怎么实现的？ Telemetry作为一个遥测技术，分为网络设备侧和网管系统侧两大部分。 \n在网络设备侧，网络设备对原始数据（包括端口信息、带宽信息、CPU信 息、内存信息等统计数据） 按YANG模型描述的结构进行组织，采用GPB （Google Protocol Buffer）格式编码后，通过gRPC（Google Remote Procedure Call ）协议主动推送给网管系统。 \n在网管系统侧，网管系统负责收集、存储和分析网络设备侧上报的数据， 为网络配置调整和流量优化提供依据。 \n那前面提到的YANG模型、GPB编码、gRPC协议究竟是什么呢？ \nYANG模型： YANG是一种数据建模语言，通过树形结构描述了数据的类型及数据间的 层级等，为数据的组织提供了一个架构模型。 \nYANG模型继承了传统数据模型的优点：简单、可读性、可扩展性好，在 实现效率上具有明显优势。 \n比如说，临近年会，领导让我写一个年会奖品购物清单，要求清单包括： 各奖项对应的奖品、奖品价格范围和奖项数量等。领导对购物清单的描述就可 以理解为建模，而YANG模型就描述了Telemetry采集的数据应具备的内容。 \nGPB编码： GPB编码格式，是一种通用的存储数据的结构格式，用于对通信协议、数 据存储的数据进行结构化串行，与编程语言无关、与编程的平台无关。 \n相对于其他的编码格式，GPB的结构相当精简。以GPB、KV-GPB （Key-value GPB）、JSON（JavaScript Object Notation）三种编码格式 为例进行对比，可以看出，当传输相同信息量时，GPB所占字节数最小。 \n因此，Telemetry采用“短小精悍的GPB格式编码”传输数据时，能提供 比其他格式编码的数据更高的信息负载能力，保证了Telemetry业务的数据吞 吐能力，同时节省了CPU，也减少了带宽使用。 \n继续采用上面购物清单的例子，GPB编码的作用就是将购物清单上的各个 条目（奖项设置、奖项对应的奖品、奖品价格范围、奖品数量），按照最合理 最精简的方式组织排列好。 \ngRPC协议： gRPC协议是一个基于HTTP/2传输层协议承载的高性能、通用的协议框 架。 \n因此，当设备和网管系统间采用gRPC协议建立传送通道时，体现出比传 统TCP/UDP协议明显的优势。 \nl HTTP/2 协议提供的“连接多路复用机制”使得gRPC通道能节省带宽， 保证数据的传送高效，并适合大容量数据传输。 \nl HTTP/2 协议提供的“双向流机制”，使得gRPC通道能降低连接次数， 保证数据的传送更安全。 \ngRPC与TCP、UDP传输协议的对比如下。 \n同样，在购物单的例子中，协议的作用就类似于我通过什么方式把购物 单给到领导，是邮件、短信还是微信？gRPC能为我们提供一个最安全快捷的 方式。 \nT e l e m e t r y 有哪些优势呢？ 一、能更实时查询数据 传统网络监管方式使用“网管查询→设备响应”的PULL 模式采集数据， 一次查询对应一次响应，设备需要处理大量重复的查询报文，获取数据时延大。 \n而Telemetry使用“网管定制→设备实时推送”的PUSH模式采集数据， 一次定制对应多次响应，降低了设备不断接收、解析来自网管的查询报文的压 力，能更实时的获取数据。 \n二、能极高效获取数据 传统网络监管方式，采集N台设备的数据时，需要执行N次查询操作，同 时采集M种数据类型，则需要N×M次手动操作。 \n而采用Telemetry技术后，所有设备只需要配置订阅，各种数据主动上报， 中间过程无需人工操作。数据获取非常高效，并适应于大规模网络节点的监控。 \n三、能极大减小数据失真 传统网络监管方式通过PULL模式获取数据，数据采集的精度为分钟级， 如果出现网络卡顿，会导致数据获取不及时，容易造成分钟级别的数据失真。 \n采用Telemetry技术后，设备实时向网管系统推送数据，数据采集精度 为亚秒级，即便出现网络卡顿，也只会出现亚秒级别的数据失真，不会造成 大的数据失真，查询到的数据更精确。 \n简言之，Telemetry使得数据查询更快、更准、更有效啦！ \nT e l e m e t r y 用在哪儿？ Telemetry以其“高效实时精确的数据查询功能”在网络的流量调优场景 中发挥着巨大作用，让网管系统能更快的检测到网络丢包等故障，调整时延大 大缩短，使得用户对流量路径的变化真正做到无感知！ \n举个例子，在传统的网络监管方式下，用户投诉网络卡顿，我们去调取卡 顿期间网络运行数据。由于数据上报存在时延，调取的数据与实际网络卡顿时 间无法准确对应，导致原因定位困难。同时，由于数据上报延时，网管对网络 卡顿或故障的感知也延时，网管就无法快速的为流量重新分配路径，导致用户 能明显感觉到路径调整带来的流量失真或数据丢失，严重影响用户体验！ \n而采用Telemetry方式后，网管能实时采集数据，因此我们可以调取与实 际卡顿时间较准确对应的数据，从而快速定位到卡顿原因。同时，网管能快速 感知卡顿或故障，实时为流量调整可用路径。用户不会感知到路径调整带来的 影响，用户体验非常好！ \n番外，我们看到，Telemetry的强大很大程度取决于PUSH模式的功劳， 也就是“主动”带来的收益。映射到我们的生活和学习中，也是一样呢，“被 动”可能会让我们感受到消极和无趣，“主动”才能让我们的学习更加积极， 生活更加阳光！NO PULL，JUST PUSH！做自己的主人，一起加油！ \nDX63(E)\n支持光板、支线路合一单板 \nOTN光传输产品\nZXMP M721 接入层OTN设备\nCX63A(E) CX66A(E)\n900G交叉容量 2.8T交叉容量 \nZXONE 9700 城域汇聚/核心/骨干层OTN设备\nZXONE 7000 盒式 OTN设备\nC2\n3.2T容量，1.6T/U \nIP分组传输产品 ZXCTN 6000 100G分组接入平台（面向5G承载，支持10GE/25GE/50GE/100GE组网） \nZXCTN 6120H-A/B/C/S 16个业务槽位 支持高密度10GE端口， 支持10/25/50/100GE组网 \nZXCTN 90 ZXCTN 9000-2E8A T级别容量，定位于接入及小汇聚场景， 支持10/25/50/100GE组网，支持SRv6等功能 \nM6000-2S6 M6000-2S16 ZXCTN 6700 T级别SPN平台 ZXCTN 6700-12 ZXCTN 6700-24 ZXCTN 6700-32 汇聚层/核心层设备 汇聚层/核心层设备 汇聚层/核心层设备 12个业务槽位 24个业务槽位 32个业务槽位 \nZXCTN 9000-E T级别5G承载平台（最大支持18个槽位） ZXCTN 9000-2E10A 汇聚层设备 8个业务槽位 \nZXCTN 9000-3EA ZXCTN 9000-8EA ZXCTN 9000-18EA 汇聚层设备 汇聚/核心层设备 汇聚/核心层设备 6个业务槽位 8个业务槽位 18个业务槽位 \n数通高端路由器交换机产品 ZXR10 T8000-18/18E 骨干网集群路由器 单框18个通用业务槽位 \n2+N集群 单机 2+N集群 单机 ZXR10 M6000-S 城域网智能多业务路由器 V6000 虚拟路由平台 vBRAS/vRouter \n数通高端路由器交换机产品 数据中心盒式交换机 ZXR10 5960系列 \n5960M-56QU-HI 5960M-4M-HI 5960M-8M-HI 固定端口 4个业务槽位 8个业务槽位 数据中心框式交换机 ZXR10 9900X系列 9904X 9908X 9916X 4个业务槽位 8个业务槽位 16个业务槽位', 'id': '932557_3'}
{'label': 'table', 'content': '5960M-56QU-HI 5960M-4M-HI 5960M-8M-HI 固定端口 4个业务槽位 8个业务槽位 数据中心框式交换机 ZXR10 9900X系列 9904X 9908X 9916X 4个业务槽位 8个业务槽位 16个业务槽位\n<table><tr><th>缩略语</th><th>英文/中文全称</th><th></th></tr></table>', 'id': '932557_4'}
{'label': 'table', 'content': '缩略语 英文/中文全称 FRR Fast Reroute，快速重路由 IP RAN Internet Protocol Radio Access Network，IP无线接入网 LFA Loop-free Alternate，无环路备份 MAC Media Access Control，介质访问控制 MEC Mobile Edge Computing，移动边缘计算 mMTC Massive Machine Type of Communication，大规模机器类通信 MSE Multi Service Edge Router，多业务边缘路由器 M-OTN Mobile-optimized OTN，面向移动承载优化的OTN NFV Network Functions Virtualization，网络功能虚拟化 OTN Optical Transport Netwrok，光传送网 OTUC1 100 Gbit/s element(slice) of an OTUCn，100Gbit/s光传送单元 OTUCn n*100 Gbit/s Optical Channel Unit，n路100 Gbit/s光传送单元 PHY Physical layer，物理层 PCS Physical Coding Sublayer，物理编码子层 PMA Physical Medium Access，物理介质介入 PMD Physical Medium Dependent，物理媒介相关子层 RAN Radio Access Network , 无线接入网 RLFA Remote Loop-free Alternate，远端无环路备份 SDN Software Defined Network，软件定义网络 SNMP Simple Network Management Protocol，简单网络管理协议 SR Segment Routing，分段路由\n<table><tr><th colspan="2">缩略语</th></tr></table>', 'id': '932557_5'}
{'label': 'table', 'content': '缩略语 英文/中文全称 FOADM Fixed Optical Add/Drop Multiplexer，固定光分插复用器 WSS Wavelength Selective Switch，波长选择开关 IP Internet Protocol，以太网协议 NAT Network Address Translation, 网络地址转换 LDP Label Distribution Protocol，标签分发协议 RSVP Resource Reservation Protocol，资源预留协议 SID Segment Identifier，段标识 SRH Segment Routing Header，段路由头 BE Best Effort，尽力而为 TE Traffic Engineering，流量工程 IGP Interior Gateway Protocol，内部网关协议 PIM Protocol Independent Multicast，协议无关组播 MLDP Multicast Label Distribution Protocol，组播标签分发协议 SRv6 Segment Routing over IPv6，基于IPv6的分段路由 BIER Bit Index Explicit Replication，位索引显式复制 IETF Internet Engineering Task Force，互联网工程任务组 BFR Bit Forwarding Router，位转发路由器 BFIR Bit Forwarding Ingress Router，位转发入口路由器 BFER Bit Forwarding Egress Router，位转发出口路由器 MP-BGP MultiProtocol Border Gateway Protocol，多协议扩展边界网 关协议 ISIS Intermediate System-to-Intermediate System，中间系统到 中间系统\n<table><tr><th></th><th>缩略语</th><th>英文/中文全称</th><th></th></tr></table>', 'id': '932557_6'}
