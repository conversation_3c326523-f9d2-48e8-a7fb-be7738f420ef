{'label': 'document', 'content': '中端交换机以太网OAM技术白皮书_20110711_CH', 'id': '799729'}
{'label': 'table', 'content': '| |\n|-|\n| 中端交换机以太网OAM技术白皮书 |\n|:-|\n| |\n| |\n', 'id': '799729_1'}
{'label': 'text', 'content': '中端交换机技术白皮书\n', 'id': '799729_2'}
{'label': 'table', 'content': '| 版本 | 日期 | 作者 | 审核者 | 备注 |\n|:-|:-|:-|:-|:-|\n| V1.0 | 2011-06-25 | 徐晓东 | 袁智勇 | 第一版 |\n| | | | | |\n| | | | | |\n| | | | | |\n', 'id': '799729_3'}
{'label': 'text', 'content': '目录\n1\t以太网OAM概述\t1\n2\t基于IEEE 802.3ah的以太网OAM 技术\t5\n2.1\tOAM能力发现模式\t6\n2.2\tEFM OAM报文格式\t7\n2.3\t链路事件\t8\n2.4\tEFM OAM PDU\t8\n2.4.1\t信息OAM PDU\t8\n2.4.2\t事件通知OAM PDU\t9\n2.4.3\t变量请求OAM PDU\t9\n2.4.4\t变量响应OAM PDU\t10\n2.4.5\t环回控制OAM PDU\t10\n2.4.6\t组织指定OAM PDU\t11\n3\tCFM技术介绍\t12\n3.1\t基本概念\t12\n3.2\tCFM功能\t14\n3.3\tCFM报文\t15\n4\tY.1731技术介绍\t16\n4.1\t基本概念\t16\n4.2\t故障管理功能简介\t18\n4.3\t性能OAM简介\t22\n4.3.1\t帧丢失测量（ETH-LM：Frame Loss Measurement）\t22\n4.3.2\t帧时延测量（ETH-DM：Frame Delay Measurement）\t23\n4.3.3\t吞吐量测试（Throughput Test）\t23\n5\t典型应用\t24\n6\t缩略语\t26\n7\t参考文献\t27\n', 'id': '799729_4'}
{'label': 'text', 'content': '以太网OAM概述\n以太网OAM英文全称为Ethernet Operations Administration and Maintenance，即针对以太网的操作、管理和维护。\n', 'id': '799729_5'}
{'label': 'table', 'content': '以太网OAM概述\n凭借以太网技术的低成本、易部署等优势，近年来以太网在城域网络建设中的比重逐年增加，网络规模也不断扩大。以太网除了承载Internet、VPN、用户专线等传统业务外，还承载了如IPTV、NGN、3G业务、公众用户的Tripple Play等高价值业务。但是由于以太网最初主要应用于局域网，而局域网对可靠性和稳定性的要求都比较低，因此以太网一直缺乏有效的管理维护机制。多业务环境对以太网的扩展性、可靠性、安全性和可管理性等电信级特性提出了诸多挑战，以太网原有的微弱的OAM能力已经不能满足电信级应用的需求，这已成为以太网应用于城域网和广域网的严重障碍。鉴于此，各研究团体和标准组织制定了一系列的建议、草案、标准等文献，从不同的角度定义了多个OAM管理的方式，其中几个主要的关于以太网OAM的文献有：IEEE定义的链路层OAM（802.3ah）和服务层OAM（802.1ag）标准，以及由ITU-T定义的以太网OAM架构(Y.1731)标准。ITU-T Y.1731中详细定义了在电信级以太网中对OAM的各种机制，并定义了以太网OAM中的层次架构和要求的各种功能。它是目前三种标准中定义最为全面的标准，不仅定义了各种故障管理功能，同时也定义了以太网的性能监控功能。IEEE 802.3ah中定义了以太网网络链路层的OAM管理功能，它在IEEE 802.3标准中增加了一个子层，用来监控链路级的远端故障指示、远端环回、链路监控等链路级操作管理。它使操作者能够监控链路的健康状态，并且快速的检测链路故障和故障点的位置。IEEE 802.1ag是ITU-T Y.1731的子集，定义了以太网网络服务层的OAM管理功能，它也称为连通性故障管理（CFM Connectivity Fault Management）。CFM提供了通过桥网络或LAN 的路径的发现和校验，支持连通性故障的检测和定位。三种标准在以太网络中的应用层次、检测的内容总结如下表：\n表 -1 典型以太网OAM协议\n| 协议标准 | 应用层次 | 应用目的 |\n|:-|:-|:-|\n| IEEE802.3ah | 链路级 | 用于用户设备和网络接入设备之间的直连链路上，在二层网络层面检测链路连通性、监测网络性能、侦测链路故障并告警、以及提供环回测试等功能。 |\n| IEEE802.1ag | 网络级 | 在二层网络层面检测端到端链路的连通性、监测非预期的连接错误、确认故障并确定故障发生的位置。监测范围可以是链路、网络的一部分和整个网络。 |\n| ITU-T Y.1731 | 网络级 | 涵盖IEEE802.1ag规范定义的全部OAM功能，还能在二层网络层面测量网络的时延、抖动、吞吐量这些性能参数；能够抑制来自服务层之下的冗余的故障告警，能向对等端通告自己正在执行网络诊断和管理行为，以及在发生故障时通知对等端自己有缺陷发生。 |\n', 'id': '799729_6'}
{'label': 'text', 'content': '以太网OAM概述\nIEEE802.1ag和IEEE802.3ah之间的不同归纳如下：\n- 物理介质\nIEEE 802.3ah只用于802.3类型的网络，IEEE 802.1ag能用于支持802帧的所有物理介质网络上。\n- 环回\nIEEE 802.3ah在远端打环，数据真正被环回，影响正常数据传输。IEEE 802.1ag不打环，数据没有被环回，只是类似于ping的请求与响应，不影响正常数据传输。\n- 连接性检查\nIEEE 802.3ah只能检查链路是否连接，不能检查连接是否是预期的连接。IEEE 802.1ag既可以检查是否连接，也可以检查到非预期的连接错误。\n- 监控范围\nIEEE 802.3ah只能监控单条链路，不可以监控部分或者整个网络。IEEE 802.1ag既可以是单条链路，也可以是部分或者整个网络。\n- 扩展性\nIEEE 802.3ah允许厂商定义自己的TLV，也允许用私有的操作码来定义新功能。IEEE 802.1ag只允许厂商定义自己的TLV，不允许厂商扩展新功能。\nITU-T Y.1731相对IEEE802.1ag扩展的内容如下：\n- 告警指示信号（AIS）\n用于在检测到服务层的故障时压制上层告警。当服务层检测到缺陷情形时，通知客户层抑制告警。缺陷情形包括：CC使能时的信号失效情形，CC禁止时出现告警指示信号(AIS)条件或锁定(LCK)条件。MIP对带有AIS信息的帧是透明的，不做处理。\n- 远端缺陷指示（RDI）\n用于向远端通告本端故障。当本端出现服务层信号失效故障时，向远端发送RDI帧。MIP对带有RDI信息的帧是透明的，不做处理。\n- 测试(TST)\n用于单向在线分析测试或用于单向离线分析测试，包括验证带宽吞吐量、帧丢失、比特误差等。当执行测试时，MEP插入带有ETH TST的测试帧，指定其吞吐量、帧长和发送模式。离线测试模式下诊断实体中断数据业务，MEP向邻居的客户层发送LCK帧。在线测试模式下诊断实体不中断数据业务，MEP以预先设定的发送速率发送测试帧。MIP对测试帧透明，不需要为MIP做任何配置。\n- 锁定（LCK）\n用于通知MEP，相应的服务层或子层MEP出于管理上的需要，已经将正常业务中断。从而使得收到LCK帧的MEP可以判断业务中断是预知的还是由于故障引起的。MIP对带有LCK信息的帧是透明的，不做处理。\n- 网络性能监控OAM\n用于检测网络的不同性能参数，包括以下内容： \\- 帧丢失测量\n用于测量本端和远端帧丢失、丢包率。根据测量方法的不同，可分为双端帧丢失测量(LM)测量和单端LM测量。 \\- 时延和时延变化测量\n用于测量时延。根据测量方法的不同，分为双程DM测量和单程DM测量。其中单程DM测量要求发送MEP和接收MEP的时钟要同步，双程DM测试则没有时钟同步要求。 \\- 吞吐量测量\n通过发送速率逐渐增加的帧（最大到理论最大值），当只有部分帧被接收时，报告帧将被丢弃时的速率，这个值就是网络的吞吐量。可分为单向的吞吐量测量和双向的吞吐量测量。\n- 其他OAM功能 \\- 自动保护倒换(APS)用于控制保护倒换，提高可靠性； \\- 管理信息通道(MCC)用于提供管理平面通信，在一对MEP之间创建一个管理通道来执行远端维护。MIP对MCC帧是透明的； \\- 信令信息通道(SCC)用于提供控制平面通信； \\- 同步状态消息(SSM)用于传递同步信息； \\- 实验功能(EX)用于在一个管理域内出于实验目的发送帧； \\- 厂商特定功能(VS)用于发送设备提供商特定功能的OAM帧；\n', 'id': '799729_7'}
{'label': 'table', 'content': '以太网OAM概述\nITU-T Y.1731和IEEE802.1ag对以太网管理实体和参考点的定义如下表：\n表 -2 ITU-T Y.1731和IEEE802.1ag对以太网管理实体和参考点\n| OAM管理实体 | ITU-T Y.173 | IEEE802.1ag |\n|:-|:-|:-|\n| 维护实体组 | ME组（MEG） | 维护联盟（MA） |\n| 维护实体组标志 | MEG ID | MA ID |\n| 维护域级别 | MEG级别 | 维护域级别（MA level） |\n| 维护实体 | MEP/MIP | MEP/MIP |\n', 'id': '799729_8'}
{'label': 'table', 'content': '以太网OAM概述\nITU-T Y.1731和IEEE802.1ag定义的以太网管理功能的总结如下表：\n表 -3 ITU-T Y.1731和IEEE802.1ag定义的以太网管理功能对照表\n| OAM功能 | ITU-T Y.1731 | IEEE802.1ag |\n|:-|:-|:-|\n| 故障管理 | 联通性检测（ETH-CC） | 联通性检测（CFM-CC） |\n| 故障管理 | 以太网环回（ETH-LB） | 环回（CFM-LB） |\n| 故障管理 | 以太网链路跟踪（ETH-LT） | 链路跟踪（CFM-LT） |\n| 故障管理 | 以太网告警指示信号（ETH-AIS） | - |\n| 故障管理 | 以太网远端缺陷指示（ETH-RDI） | - |\n| 故障管理 | 以太网锁定信号（ETH-LCK） | - |\n| 故障管理 | 以太网测试信号（ETH-Test） | - |\n| 故障管理 | 以太网自动保护倒换（ETH-APS） | - |\n| 故障管理 | 以太网维护通信通路（ETH-MCC） | - |\n| 故障管理 | 以太网实验OAM（ETH-EXP） | - |\n| 故障管理 | 以太网厂商特定OAM（ETH-VSP） | - |\n| 性能管理 | 帧丢失测量 | - |\n| 性能管理 | 帧时延测量 | - |\n| 性能管理 | 吞吐量测试 | - |\n', 'id': '799729_9'}
{'label': 'text', 'content': '基于IEEE 802.3ah的以太网OAM 技术\n在功能上，IEEE 802.3ah定义了以太网的链路层一种操作、维护和管理机制，它是主要针对“链路”级别的管理，可以对网络中的点到点（或虚拟点到点）以太网链路进行监控和故障处理。它对网络用户最后一公里的连接管理有重要的意义，最后一公里的连接也是故障比较容易发生的位置。IEEE 802.3ah定义的主要功能包括： \n- OAM能力发现 \n通过激活OAM能力发现，本地维护终端能够确定远端终端设备是否使能EFM OAM功能，并能发现对端的配置参数（如所支持的最大OAM PDU长度）和支持的功能（如是否支持远端环回）； \n- 远端故障指示 \n- 通知远端终端设备本地DTE设备接收链路运行状态异常； \n- 远端环回 \n- 提供了一种支持数据链路层的帧级别的环回检测模式； \n- 链路监测 \n- 提供事件通知，可包含诊断信息。并提供OAM MIB变量的查询机制。 \n在标准上，IEEE 802.3ah标准已经合入到IEEE 802.3 -2005的标准的第57章中，但多数人还是比较习惯将将此OAM功能集称为802.3ah，一般称此OAM 技术为“Ethernet OAM”或者称为“EFM：Etherent in the First Mile”。 \n在ISO模型中，EFM工作在以太网的数据链路层上实现，它是一个可选的子层，在数据链路层流程中，OAM子层在系统中位置下图所示。 \n在模型结构上，Ethernet OAM层主要由两个部分组成：一个是OAM客户端、另一个是OAM子层：OAM客户端主要是用来给上层提供接口，同时为下层的OAM 子层提供接口；OAM子层如下图所示，是插入到数据链路层中的MAC子层和LLC子层（或MAC客户端）中的可选子层，这个子层主要由控制模块（Control Block）、复用器（Multiplexer）和协议解析器（P-Parser）组成，用来收发OAM的数据，分解OAM数据和非OAM数据，及进行一些特殊的控制功能（如环回功能）。', 'id': '799729_10'}
{'label': 'table', 'content': '基于IEEE 802.3ah的以太网OAM 技术\nOAM能力发现模式\n分为主动和被动两种模式，EFM OAM连接只能由工作在主动模式的OAM实体发起，被动模式的OAM实体只能等待主动OAM实体发来的连接请求。工作在主动模式的OAM实体不接受被动模式下的远端OAM实体发来的远端环回命令和变量请求。一个OAM实体允许同时开启主动模式和被动模式，也可以二者选一。下表描述了主动和被动EFM OAM实体的行为。\n表 -1 主动和被动OAM实体的行为\n| 能力 | 主动EFM OAM实体 | 被动EFM OAM实体 |\n|:-|:-|:-|\n| 发起OAM发现流程 | 是 | 否 |\n| 在OAM发现流程发起后进行对应操作 | 是 | 是 |\n| 发送信息OAM PDU | 是 | 是 |\n| 允许发送通知OMA PDU | 是 | 是 |\n| 允许发送变量请求OAM PDU | 是 | 是 |\n| 允许发送变量响应OAM PDU | 是 | 是 |\n| 允许发送环回控制OAM PDU | 是 | 否 |\n| 按照环回控制OAM PDU进行操作 | 是 | 是 |\n| 允许发送组织特定OAM PDU | 是 | 是 |\n', 'id': '799729_11'}
{'label': 'text', 'content': '基于IEEE 802.3ah的以太网OAM 技术\nEFM OAM报文格式\nEFM OAM报文使用的是慢协议报文，称作OAM协议数据单元（OAMPDU），用来OAM实体之间进行状态交换和监控。OAM PDU只在单独的链路上传送，不被OAM客户端转发。EFM OAMPDU的报文格式下图所示。\n其中 Code 字段是OAM报文的编码字段，此字段的编码值参见下表。\n表 -2 EFM OAM PDU编码\n| 编码 | OAM PDU | 注解 |\n|:-|:-|:-|\n| 00 | 信息 | 通知本地和远端OAM信息 |\n| 01 | 事件通知 | 通知远端终端设备发生链路故障 |\n| 02 | 变量请求 | 请求一个或多个特定的MIB变量 |\n| 03 | 变量响应 | 返回一个或多个特定的MIB变量 |\n| 04 | 环回控制 | 使能或禁止远端环回 |\n| 05-FD | 预留 | 保留 |\n| FE | 组织规范 | 保留给组织做扩展，由组织标识符进行区分 |\n| FF | 预留 | 保留 |\n标志域（Flag）的两个字节以单个比特进行编码，附件的信息使用事件通知OAM PDU进行发送，标志域特定比特所代表的内容如下表所列。\n表 -3 EFM OAM报文标志域的填充值及对应的含义\n| 比特 | 名称 | 含义描述 |\n|:-|:-|:-|\n| 15:7 | 预留 | 留给将来做扩展，发送时全设置为0，接收时忽略这些比特位。 |\n| 6 | 远端稳定状态 | 当“远端稳定状态”位为TRUE时，远端稳定状态和远端评估值应是上一次从远端OAM实体接收到的有效的本地稳定状态和本地评估值。否则，这两个值设置为0。 |\n| 5 | 远端评估值 | 当“远端稳定状态”位为TRUE时，远端稳定状态和远端评估值应是上一次从远端OAM实体接收到的有效的本地稳定状态和本地评估值。否则，这两个值设置为0。 |\n| 4 | 本地稳定状态 | 0x0：发现不能完成0x1：本地OAM实体发现过程未完成0x2：本地发现过程已完成0x3：保留，该值不能被发送。如果接收到0x3应被忽略，不能修改上一次的接收值。 |\n| 3 | 本地评估值 | 0x0：发现不能完成0x1：本地OAM实体发现过程未完成0x2：本地发现过程已完成0x3：保留，该值不能被发送。如果接收到0x3应被忽略，不能修改上一次的接收值。 |\n| 2 | 严重事件 | 1：严重事件已发生0：严重事件未发生 |\n| 1 | 本地失效条件Dying Gasp | 1：不可恢复的本地失效条件已发生0：不可恢复的本地失效条件还未发生 |\n| 0 | 链路故障 | PHY已经检测到本地OAM实体的接收方向发生故障，如链路故障或物理层故障。1：本地OAM实体接收通道已经检测到故障0：本地OAM实体接收通道没有检测到故障 |\n', 'id': '799729_12'}
{'label': 'table', 'content': '基于IEEE 802.3ah的以太网OAM 技术\n链路事件\n接入链路OAM定义了一组链路事件，同时还定义了将事件通知给远端的机制。链路事件由链路事件TLV发送给远端实体，链路事件TLV被封装在事件通知OAM PDU中。链路事件分为一般和紧急两大类，总结如下表：\n表 -4 一般链路事件和紧急链路事件\n| 事件分类 | 事件类型 | 中文含义 | 事件描述 |\n|:-|:-|:-|:-|\n| 紧急链路事件 | Link fault | 链路故障 | 在本地DTE的接收方向发生了物理层故障 |\n| 紧急链路事件 | Dying Gasp | 致命故障 | 发生了不可恢复的本地故障 |\n| 紧急链路事件 | Critical Event | 紧急事件 | 发生了未定义的严重事件 |\n| 一般链路事件 | Errored symbo period event | 错误符号周期事件 | 在单位时间内，错误符号的数量超过阈值 |\n| 一般链路事件 | Errored frame event | 错误帧事件 | 在单位时间内，错误帧的数量超过阈值 |\n| 一般链路事件 | Errored frame period event | 错误帧周期事件 | 在收到指定数量帧的时间内，错误帧的数量超出阈值 |\n| 一般链路事件 | Errored frame seconds summary event | 错误帧秒累积事件 | 在指定时间内，错误帧秒的数量超出阈值 |\n', 'id': '799729_13'}
{'label': 'text', 'content': '基于IEEE 802.3ah的以太网OAM 技术\nEFM OAM PDU\n信息OAM PDU\n信息OAM PDU用于发送OAM状态信息，实现OAM对等之间的自动发现。首先，在OAM协议建立前，OAM 客户端会通过信息报文来进行协商，即对OAM 的两端的OAM 功能模式（主动或被动）、OAM 能力，如最大允许的OAMPDU 尺寸，支持哪些OAM 功能，如在给定链路上OAM远端环回等进行协商。只有得到一致的OAM对端才能进入到正常的Ethernet OAM交互阶段，才能正常进行OAM功能。当OAM链路建立后，设置之间会周期性的发送信息OAM PDU，对链路进行监控，在信息OAM PDU中，将包括本地信息TLV和远端信息TLV，通过周期性的信息OAM PDU报文的交换，可以实现链路连通性监控检测，如果在规定时间内没有接收到信息OAM PDU，证明链路层发生故障。', 'id': '799729_14'}
{'label': 'text', 'content': '基于IEEE 802.3ah的以太网OAM 技术\nEFM OAM PDU\n事件通知OAM PDU\n事件通知OAM PDU用来向远端通告多种链路状态告警，如通知紧急链路事件、链路事件（错误信号周期事件、错误帧事件、错误帧周期事件、错误帧秒累计事件）、客户指定的链路事件等。这些链路告警可以是根据用户配置的参数来检查的，当链路上错误超过配置的阈值的情况下，OAM会就会发送相应的OAM PDU事件通知报文告知对端；告警也可能是本地发生的紧急告警，OAM也可以通过此事件通知对端设备。有些事件通知是单次的，也有些是周期性发送的。', 'id': '799729_15'}
{'label': 'text', 'content': '基于IEEE 802.3ah的以太网OAM 技术\nEFM OAM PDU\n变量请求OAM PDU\n变量请求OAM PDU用来从远端设备请求一个或者多个MIB变量。在变量请求OAM PDU中，本端设备将需要请求的对端设备的MIB变量封装到请求报文中发送到对端设备，当对端设备接收到变量请求后收集本地的MIB变量信息，填充后通过变量响应OAM PDU进行回复。完成本地对远端的MIB 变量查询。通过OAM的变量请求和变量响应功能，可以实现最后一公里设备接入设备对用户设备的监控和管理功能。', 'id': '799729_16'}
{'label': 'text', 'content': '基于IEEE 802.3ah的以太网OAM 技术\nEFM OAM PDU\n变量响应OAM PDU\n用来向发送变量请求OAM PDU的设备返回一个或多个MIB变量。', 'id': '799729_17'}
{'label': 'text', 'content': '基于IEEE 802.3ah的以太网OAM 技术\nEFM OAM PDU\n环回控制OAM PDU\n环回控制OAM PDU用来控制远端终端设备的OAM远端环回状态，由主动模式的OAM实体来使能或者去使能远端被动模式的OAM实体的环回（loopback）功能。环回功能对设备的硬件或软件来说是可选的，当远端设备使能了环回功能后，所有的非OAM PDU的报文不能被转发，都需要返回到本端，因此这种环回功能是要中断业务的。在此过程中，本端和远端一般要求有对报文进行计数的能力，然后使用这些计数信息来检测链路的链路状态。\n下表列出了已经定义的OAM远端环回指令。\n表 -5 OAM远端环回指令编码\n| 命令编码 | 描述 |\n|:-|:-|\n| 0x00 | 保留，不应发送，OAM客户层接到后忽略 |\n| 0x01 | 使能OAM远端环回 |\n| 0x02 | 禁止OAM远端环回 |\n| 0x03-0xFF | 保留，不应发送，OAM客户层接到后忽略 |\n', 'id': '799729_18'}
{'label': 'text', 'content': '基于IEEE 802.3ah的以太网OAM 技术\nEFM OAM PDU\n组织指定OAM PDU\n保留给设备厂商的OAM报文，设备厂商可以自定义各种OAM报文TLV，使设备厂商能灵活的控制最后一公里的用户设备，加强对最后一公里链路的管理。但这种非标准的应用需要限制OAM的对等实体都是相同厂商的设备，对应不同厂商的设备的对接一般会忽略这种的特定组织OAM PDU，同时不会影响标准OAM的处理过程。', 'id': '799729_19'}
{'label': 'text', 'content': 'CFM技术介绍\n基本概念\nCFM定义了域、维护点和维护等级等概念，用来实现在运营商、提供商和用户之间的层次化的OAM功能。CFM几个关键的定义解释如下： \n.\t维护域(Maintenance Domain MD) \n其连通性故障可以被管理的网络或网络的一部分。维护域的边界使用一组域服务接入点（DoSAP）来进行定义，每个DoSAP可以成为到服务实例的连续性的一个点。 \n.\t维护联合（Maintenance Association MA) \n为检查一个单独的服务实例的完整性而建立的带有相同维护联合ID(MAID)和维护域等级(MD)定义的一组MEP。一个MA也可以被认为是由一组配置的维护联合端点（MEP）组成的全网格的一个管理实体。 \n.\t维护联合ID(Maintenance Association Identifier MAID) \n维护联合的一个标识，它在域中使唯一的，CFM用MAID用来保护服务实例的异常串联。MAID有两部分：MD名和短MA名。 \n.\t维护域等级(MD Level) \n每个维护域都有一个维护域等级，是实现CFM的嵌套使用的关键参数。MDLevel域VLAN tag中的VID一起用来标识一个CFM帧的VID关联的是哪个维护域，并标识这个CFM帧属于哪个维护联合。MD等级的范围为0-7。 \n.\t维护联合端点(maintenance association End Point MEP) \n与一个特定的服务实体的域服务接入点(DoSAP)相关的一个活跃管理的CFM实体。它可以产生和接收CFM PDU和追踪任何响应。它是一个MA的终点，是对每个在同一个MA中的其他MEP的一个分离的维护实体的终点。 \n.\t维护域中间点半功能(MIP Half Function MHF) \n与一个单一维护域相关联的CFM实体， 因此也与一个单一维护域等级和一组VID的相关联。它仅在响应接收的CFM PDU时才产生CFM PDU。 \n.\t维护域中间点(Maintenance domain Intermediate Point MIP) \n由两个MHFs组成的一个CFM实体。 \n.\t维护点(Maintenance Point MP) \nMEP或者MIP之一，是他们的统称。 \n通过以上定义的概念，CFM可以实现在多个层次的OAM管理。首先管理者需要将网络划分称不同的维护域，并且为各维护域分配不同的维护域等级，各层次之间的OAM管理通过维护域等级进行隔离。在各层网络的边缘，通过设置多个MEP和MIP来对网络的点到点或者多点连接进行监控和管理。CFM中各种维护实体之间的关系，以及MEP、MIP和维护域等级的概念如下图所示。', 'id': '799729_20'}
{'label': 'text', 'content': 'CFM技术介绍\nCFM功能\nCFM 标准中定义了三个主要功能：连通性检测、链路跟踪和环回功能。 \n.\t业务的连通性检测 \n使用CCM报文来检测维护端点之间的连通状态。可以检测出由于设备故障或配置错误造成的连通性失败和非意愿的连通性（错误连接的情况）。该功能的实现方式是：由维护端点周期性地发送CCM 报文，该报文是组播报文，相同维护集的其它维护端点接收该报文，并由此获知远端状态。若维护端点在3.5个CCM报文发送周期内未收到远端维护端点发来的CCM报文，则认为链路有问题。CCM报文的发送速率根据的不同的应用可以设置为不同的值，范围是从3.3ms到10min之间，当使用CCM报文进行业务保护倒换时，为了保证业务的快速切换，需要将CCM发包周期设置为比较小的值（可能是3.3ms、10ms或100ms）；当使用CCM报文进行网络监控时，为了降低网络的资源的占用，可以将CCM发包周期设置为比较大的值（可能是1s、10s或1min）。在CCM报文的交互过程中，除提供业务的连通性检测的同时，还可以通过报文中的各种字段的检查来发现如MEP的异常接入、维护等级异常等错误。 \n.\t环回 \n类似ip层的ping，用于验证维护节点之间的连通性，在故障确认时使用。环回功能是一种按需的OAM功能，它通过在MEP上发起一个单播的请求，目的地址是指定的MP（MEP或MIP），期望接收到目的MP的响应报文，如果接收到响应报文，证明两个维护点之间的业务是连通的，否则认为是非连通的。该功能的实现方式是：由维护端点发送LBM（Loopback Message，环回报文）报文给远端维护点，并根据能否收到对端反馈的LBR（Loopback Reply，环回应答）报文来检验链路状态。LBM报文和LBR报文都是单播报文。 \n.\t链路跟踪 \n与IP层的Trace route功能类似，用于确定源端到目标维护端点的路径，它是进行故障定位的一个有效手段。链路跟踪功能也是一种按需的OAM功能，它通过发送一个组播的OAM报文，报文经过的维护节点会响应此报文，同时根据目的地址进行转发，直到此报文被传递到目的地址。发起链路跟踪的维护节点会收集各中间节点的响应报文，然后计算出到目的地址的路径。当到目的维护节点的中间节点或链路出现故障时，使用链路跟踪功能可以定位到故障发生的具体位置，为以太网网络中的故障定位提供一个有效的手段。其实现方式是：由源端发送LTM（LinktraceMessage，链路跟踪报文）报文给目标维护端点，目标维护端点及LTM报文所经过的维护中间点收到该报文后，会发送LTR（Linktrace Reply，链路跟踪应答）报文给源端，源端则根据收到的LTR报文来确定到目标维护端点的路径。LTM报文是组播报文，LTR报文是单播报文。', 'id': '799729_21'}
{'label': 'text', 'content': 'CFM技术介绍\nCFM报文\nCFM的诸多功能是通过5个CFM报文来实现的。 \n.\t连续性检查消息(Continuity Check Message CCM） \n一个组播的CFM协议数据单元。它被一个MEP周期性发送以确认MA拥有的MEP的通过此MA上的连续性。接收CCM的MP不响应此报文。 \n.\t链路跟踪消息(Linktrace Message LTM) \nMEP发起的一个CFM PDU，用来追踪从MEP到达一个目标MAC地址的路径，直到LTM到达它的目的MEP或者不能再被转发。每个到目标的路径中的MP 产出一个LTR。 \n.\t链路跟踪响应(Linktrace Reply LTR) \n一个MP响应从MEP接收到LTM而发送的到这个MEP的单播报文。 \n.\t环回消息(Loopback Message LBM) \n一个MEP发送的到一个指定的MP的单播CFM PDU，期待接收LBR。 \n.\t环回响应(Loopback Reply LBR) \n一个MP响应从MEP接收到的LBM而发送的到这个MEP的单播报文。', 'id': '799729_22'}
{'label': 'text', 'content': 'Y.1731技术介绍\n基本概念\n.\t维护实体（ME：Maintenance Entity） \n表示需要管理的一个实体，它是两个维护实体组（MEG：MaintenanceEntity Group）端点之间的一种关系。ME可以相互嵌套，但是不能重叠，下图描述了网络中维护实例定义的示例，其应用术语基于ITU-T G.8010中的定义。 \n.\t维护实体组（MEG：Maintenance Entity Group） \nME组(MEG)中包括能满足以下条件的不同的ME：一个MEG的ME存在于同一个管理域的边界之内；同时一个MEG的ME具有同样的MEG等级；并且一个MEG的ME属于同一个点到点的ETH（以太网）连接或者多点的ETH连接。对于一个点到点的ETH连接，一个MEG仅包含单个ME。对于一个有n个端点的多点的ETH连接，一个MEG 包含n*(n–1)/2个ME。 \n.\t维护实体组端点（MEP：MEG End Point） \nMEG端点（MEP）标志一个ETH MEG的端点，它能够发出和终止OAM帧，用于差错管理和性能监测。OAM帧不同于ETH中数据流，OAM帧被加入到会聚的ETH数据流中，且可将与被监测的ETH 中数据流经受同样的转发处理。MEP不会在ETH数据流中加入新的转发识别码。MEP也不终止ETH中的数据流，尽管它可以观察数据流（例如对帧进行计数）。 \n.\t维护实体组中间点（MIP：MEG Intermediate Point） \nMEG中间点（MIP）是MEG中的一个中间点，它能对某些OAM帧做出反应。MIP并不发起OAM帧。MIP也不对中转的ETH数据流采取动作。 \n.\t业务流调控点（TrCP：Traffic Control Point） \n业务流调控点（TrCP）是一个能够按照ITU-T G.8010/Y.1306建议书的规定执行ETH业务流调控功能的ETH连接点。 \n.\t维护实体组等级（MEG Level） \n在MEG嵌套的情况下，每一个MEG的OAM信令流必须能清楚地识别，并能与其他MEG的OAM信令流相区分。当OAM信令流不能被以太层的报文区分时，OAM帧中的MEG等级将在相嵌套MEG的OAM信令流之间进行区分。标准中定义了八个MEG等级，基本可以满足网络部署的不同情景。当客户、提供商和运营商数据通道的信令流不能依据ETH层报文加以区分时，可以在它们之间分享这八个MEG等级来区分属于客户、提供商和运营商的相嵌套MEG的OAM帧。在客户、提供商和运营商角色之间，MEG等级默认的分配如下： \\- 客户角色分配三个MEG等级：7、6 和5。 \\- 提供商角色分配两个MEG等级：4和3。 \\- 运营商角色分配三个MEG等级：2、1和0。 \n上述MEG等级默认的分配可以通过客户、提供商和运营商之间相互协商来改变。尽管有八个等级可用，但不是所有的MEG等级都要使用。所使用的MEG等级的数量，取决于OAM信令流不能通过ETH层报文加以区分的被嵌套的ME的数量。', 'id': '799729_23'}
{'label': 'text', 'content': 'Y.1731技术介绍\n故障管理功能简介\n标准中定义了多种故障管理的功能，主要的目的是为了检测、验证、定位和通告不同\n的故障情况。标准定义的主要功能及其功能描述如下。\n.\t以太网连通性检查（ETH-CC：Ethernet Continuity Check）\n以太网连续性检查是主动OAM，它用于检测一个MEG中任何一对MEP间连续性的丢失（LOC）。ETH-CC也可以检测两个MEG之间非期望的连接（错误混入），在MEG内与一个不要求的MEP（非期望的MEP）间不希望有的连通性，以及其它故障情况（例如非期望的MEG 等级、非期望的周期等）。ETH-CC同时也可应用于差错检测、性能监测或保护转换的应用。ETH-CC是周期性传送的，对于一个MEG中所有的MEP，ETH-CC的传输周期是同样的。根据不同的OAM应用，ETH-CC的传输周期可以设置为不同的值，一般情况下，差错管理默认的传输周期为1s；性能监测默认的传输周期为100ms；保护倒换默认的传输周期为3.33ms。当一个MEP能产生带有ETH-CC信息的帧时，它也预期从MEG中它对等的MEP处接收带有ETH-CC信息的帧。在ETH-CC报文信息中包括了MEG ID、MEP ID对等的MEP ID的清单、MEG 等级、ETH-CC传输周期、优先级。通过以太网连续性检查的功能，可以提供多种故障管理特性：当MEP在3.5倍的传输间隔内没有接收到来自某个对等的MEP的ETH-CC报文时，它就检测出与那个MEP 失去了连续性；当接收到的ETH-CC报文中的MEG 等级低于本身的等级时，它就检查出非期望的MEG等级连接；当接收到不等于本身MEG ID的报文时，它就检查出错误混入；如果接收报文中的MEP ID有错误，它就检查出非期望的MEP接入；当接收报文的传输周期与本身不一致，它就检查出非期望的周期。同时，连续性检查功能会与网络设备的告警和网管系统协作将检测出的故障上报到指定的系统进程中。\n.\t以太网环回（ETH-LB：Ethernet Loopback）\n用于检验一个MEP与一个MIP或对等的ME间的连通性。有两种ETH-LB 类型：单播的ETH-LB和组播的ETH-LB。\n单播ETH-LB是一种按需的OAM功能，可以单次发送或者重复性传输，用于验证MEP/MIP之间的双向连通性，或者在一对MEP之间，执行双向的服务期间或服务中断时的诊断测试，包括带宽通量的验证、检测比特误码率等。\n组播ETH-LB功能用于验证一个MEP与它对等的MEP之间的连通性。组播ETH-LB也是按需的OAM功能。当在一个MEP上请求组播的ETH-LB功能时，该MEP将向组播ETH-LB的发起者回送一个要进行双向连通性检测的、它的对等MEP的一个清单。当一个MEP上请求组播LB时，带有ETH-LB请求信息的一个组播帧将从一个MEP送往同一MEG中对等的其它MEP。该MEP将预期在规定的时间周期内从它对等的MEP处接收带有ETH-LB回复信息的单播帧。一旦接收到带有ETH-LB请求信息的组播帧，接收的MEP将检验该带有ETH-LB请求信息的组播帧，并在一个0到1s范围以内的数值随机的时延之后，发送带有ETH-LB回复信息的单播帧。\n.\t以太网链路追踪（ETH-LT：Ethernet Link Trace）\n以太网链路追踪功能是一种按需的OAM功能，它主要用于差错定位和邻近关系确认。当一个差错（如一个链路和/或装置的失灵）或转发平面的环路出现时，ETH-LT所得到的MIP和/或MEP的序列很可能不同于所期望的，序列中的差别将提供有关差错位置的信息，或者通过某些节点的超时判断故障点。ETH-LT也可以进行邻近关系确认，执行ETH-LT功能的结果是一系列的MIP，从源MEP到目标的MEP或MIP，每一个MIP和/或MEP将由其MAC地址标识，这样就可以确定从源MEP到目的MEP/MIP之间的邻接关系。\n.\t以太网告警指示信号（ETH-AIS：Ethernet Alarm Indication Signal）\n以太网告警指示信号功能用于在服务器层（子层）检测到故障情况后止住告警。即，MEP使用ETH-AIS通知以太层MEP检测到以太网业务用户当前维护实体组等级（MEG Level），或当前维护实体组等级（MEG Level）的嵌套MEG Level上有连接性故障发生。若故障发生在前维护实体组等级，以太网业务用户可以自行定位故障点。若故障发生在当前MEG Level的嵌套等级，则用户可以联系运营商来定位故障发生点。\n由于在生成树协议（STP）环境下提供有独立恢复能力，ETH-AIS不期望用于STP环境。带有ETH-AIS信息的帧可以由MEP（包括服务器MEP）在检测到故障情况时在客户的MEG等级上发出。作为例子，故障情况有：ETH-CC使能情况下信号失效，或者在关闭ETH-CC情况下出现的AIS条件或LCK条件。在多点ETH连通性的情况下，在接收到带有ETH-AIS信息的帧时，MEP将不能确定遇到故障情况的那个特定的服务器层（子层）实体。更重要的是，它不能确定它的对等MEP中相关联的需要止住告警的那个子集，因为收到的ETH-AIS不包含那种信息。因此，在接收到带有ETH-AIS信息的帧时，MEP必须抑制所有对等MEP的告警，不管是否仍有连通性。然而对于点到点的ETH连接，MEP只有单个对等的MEP。因此，在它接收到ETH-AIS信息时，有关哪个对等MEP应该止住告警，不存在任何含糊性。只有一个MEP（包括服务器MEP）被配置成能发出带有ETH-AIS信息的帧。在检测到故障情况时，该MEP可以立即开始在配置的客户MEG等级上周期性地发送带有ETH-AIS信息的帧。MEP将继续周期性地发送带有ETH-AIS信息的帧，直到故障情况消除。一旦接收到一个带有ETH-AIS信息的帧，MEP即检测AIS情况，抑制住与它所有对等MEP相关联的失去连续性的告警。MEP在无AIS情况下检测到失去连续性故障时，将恢复产生失去连续性故障的告警。\n.\t以太网远程端故障指示（ETH-RDI：Ethernet Remote Defect Indication）\n以太网远程端故障指示功能可以由MEP与它对等的MEP交换已经遇到的故障情况。ETH-RDI仅在ETH-CC传输实现时才使用。ETH-RDI有如下两种应用：\n（）\t单端故障管理：接收MEP检测到一个RDI故障条件，与该MEP的其它故障条件相关联，它可能会是这些故障的原因。MEP未接收到ETH-RDI消息将指示整个MEG中无故障。\n（）\t用于远端性能的监测：它反映远端曾有过的故障情况，可以作为性能监测进程的输入。\n处于故障状态的MEP发送带有ETH-RDI信息的帧。MEP在接收到带有ETH-RDI信息的帧时，可以确定它对等的MEP已遇到故障情况。然而，对于多点的ETH连接，MEP在接收到带有ETH-RDI信息的帧时，不能够确定它的哪个对等MEP发生了故障，因为发送的MEP本身并没有这种信息的。\nMEP检测到在与其对等的MEP相关的故障时，MEP在缺陷状态期间内置位CCM帧中的RDI字段。当缺陷条件清除后，MEP在随后传输的CCM帧中应清除RDI字段。\n.\t以太网锁定信号（ETH-LCK：Ethernet Locked Signal）\n以太网锁定信号用于MEP在进行管理或诊断操作时同它的中间客户MEP进行通信，让它的客户层MEP能够区分服务层MEP是在有意的进行管理/诊断操作还是发生了故障。服务层MEP主动的管理/诊断操作和发生了故障这两种情况都可能中断业务数据流，故障造成的中断需要报告，有意的操作造成的中断不需要报告。有意的管理/诊断操作的一个例子是进行离线的ETH测试时，为MEP配置一个管理锁定状态。\nMEP根据发送周期发送带有ETH-LCK信息的帧，直到管理/诊断条件消失。对点到点的ETH连接，客户MEP阻塞带有ETH-LCK信息的帧，如果需要，它向自己的MEP客户发送ETH-AIS帧。MIP对带有ETH-LCK信息的帧是透明的，不需要任何信息以支持ETH-LCK信息帧。\n（）\tETH-LCK信息帧的发送\n当MEP配置了管理或诊断操作而将数据业务中断时，将以LCK周期发送LCK帧，发送周期同AIS周期，第一个帧应在管理或诊断操作后立即发送。当服务层MEP配置了管理/诊断操作时，客户层可能存在多个MEP需要通知以抑制告警，服务层MEP应向它的每个客户层MEP发送LCK帧。\n（）\tETH-LCK信息帧的接收\n在接收到LCK帧后，MEP检测到LCK条件后，如果在LCK发送周期的3.5倍时间间隔内没有收到LCK帧，MEP将清除LCK条件。\n.\t以太网测试信号（ETH-Test：Ethernet Test Signal）\n用于执行在线或者离线的诊断测试，包括验证带宽通量、帧丢失、比特误码等。当配置成进行这种测试时，MEP将插入带有ETH-Test信息的帧，指定其吞吐量、帧长和发送模式。\n当执行离线诊断测试时，数据业务将被诊断实体中断，MEP向邻近的客户层MEP发送LCK帧。当执行在线诊断测试时，数据业务不会被中断。在线测试过程中，ETH-Test信息帧的发送速率是预先设定好的。\n.\t以太网自动保护倒换（ETH-APS）\n用于控制保护倒换操作，提高可靠性。ETH-APS机制的应用在ITU-T G.8031/Y.1342建议书中定义。\n.\t以太网维护通信通路（ETH-MCC）\n用于执行远端维护，ETH-MCC在一对MEP之间提供一个维护通信通路，MEP可发送给它的远端对等MEP来响应远端维护请求。MIP对ETH-MCC帧是透明的，不需要MIP配置配置任何信息以ETH-MCC功能。在标准中没有具体规定。\n.\t以太网实验OAM（ETH-EXP）\n用于实验的OAM功能，可在一个域内暂时使用，不能穿越不同的维护域。在标准中没有具体规定。\n.\t以太网提供商特定OAM（ETH-VSP）\n留给网络设备厂家进行自定义的扩展，不需要进行标准化。\nITU-T Y.1731定义的各种OAM的属性类型和ITU-T Y.1731定义的消息总结如下：\n', 'id': '799729_24'}
{'label': 'table', 'content': 'Y.1731技术介绍\n故障管理功能简介\n表 -1 ITU-T Y.1731各种OAM的属性及对应的消息\n| 属性 | 类型 | 功能 | ITU-T Y.1731消息 |\n|:-|:-|:-|:-|\n| 主动OAM | 状态 | 连续性验证和连通性验证 | 连通性故障管理CC消息 |\n| 主动OAM | 性能 | 中断 | 连通性故障管理CC和RDI消息 |\n| 主动OAM | 性能 | 帧丢失 | 连通性故障管理LM消息 |\n| 主动OAM | 维护 | 告警抑制 | AIS |\n| 主动OAM | 维护 | 锁定指示 | LCK |\n| 主动OAM | 维护 | 远端缺陷指示 | 连通性故障管理RDI消息 |\n| 主动OAM | 维护 | 保护倒换 | APS |\n| 按需OAM | 状态 | 连通性验证 | LBM/LBR |\n| 按需OAM | 性能 | 帧丢失 | LMM/LMR |\n| 按需OAM | 性能 | 帧时延 | DMM/DMR,1DM |\n| 按需OAM | 性能 | 帧时延抖动 | DMM/DMR,1DM |\n| 按需OAM | 性能 | 吞吐量 | LBM/LBR,TST |\n| 按需OAM | 故障定位 | 通道连通性 | LBM/LBR |\n| 按需OAM | | 业务流连通性 | LTM/LTR |\n| 按需OAM | 维护 | 远端维护 | MCC |\n', 'id': '799729_25'}
{'label': 'text', 'content': 'Y.1731技术介绍\n性能OAM简介\n在 ITU-T Y.1731中定义的性能监控功能的OAM是针对点到点ETH连接的。它可以用于性能监测的OAM功能可以测量不同的性能参数。这些性能参数主要包括：帧丢失率、帧时延、帧时延变化和吞吐量。标准中提供的性能监控的功能主要为以下几项。', 'id': '799729_26'}
{'label': 'text', 'content': 'Y.1731技术介绍\n性能OAM简介\n帧丢失测量（ETH-LM：Frame Loss Measurement）\nETH-LM用于收集输入和输出业务帧的计数器的值，该计数器用来记录在一对MEP之间发送和接收的数据帧数。ETH-LM通过发送带有ETH-LM信息的帧到远端对等MEP，以及从对等MEP接收带有ETH-LM信息的帧来实现。ETH-LM可以使用MEP执行近端和远端的帧丢失测量。对MEP，近端帧丢失表示输入数据帧的丢失，远端帧丢失表示输出数据帧的丢失。近端帧丢失和远端帧丢失都可用于近端严重错误秒（Near-End SES）和远端的严重错误秒（Far-End SES），二者一起形成了不可用时间。 \nMEP维护两个本地计数器，为每一对被监测的点到点MEP和每一对优先级类所执行的丢失测量进行计数，分别为向对等MEP发送的数据帧的计数器和从对等端MEP接收到的数据帧的计数器。帧丢失测量的精确度依赖于添加到数据流中的带有ETH-LM信息的帧的数目，基于硬件的实现可以提高计数的精确度。 \nMIP对ETH-LM信息帧是透明的。 \nETH-LM 可以以两种方式进行：双端ETH-LM和单端ETH-LM。双端的ETH-LM用于性能监测的主动的OAM，可应用于故障管理。在这种情况下，在一个点到点的ME中，每个MEP向它对等的MEP周期地发送带有ETH-LM信息的双端的帧，以便于对等MEP处的帧丢失测量。每个MEP都终结带有ETH-LM信息的双端的帧，并进行近端和远端的丢失测量； \n单端的ETH-LM用于按需的OAM。在这种情况下，为进行丢失测量，MEP向其对等的MEP发送带有ETH-LM 请求信息的帧，并从其对等MEP接收带有ETH-LM回复信息的帧。', 'id': '799729_27'}
{'label': 'text', 'content': 'Y.1731技术介绍\n性能OAM简介\n帧时延测量（ETH-DM：Frame Delay Measurement）\n帧时延测量是按需OAM，测量网络的帧时延和帧时延抖动。帧时延和帧时延抖动的测量是通过向对等MEP周期地发送带有ETH-DM信息的帧，并在诊断间隔内从对等MEP 接收带有ETH-DM信息的帧来完成的。每一个MEP都可以执行帧时延和帧时延抖动的测量。 \n当MEP能产生带有ETH-DM 信息的帧时，它向同一ME内它对等的MEP周期地发送带有ETH-DM信息的帧。当MEP有能力生成带有ETH-DM信息的帧时，它也预期在同一ME中能从对等的MEP接收带有ETH-DM信息的帧。 \nETH-DM可以用两种方式来进行：单向ETH-DM和双向ETH-DM。在单向ETH-DM的情况下，每个MEP在点到点ME中向它对等的MEP发送带有单向ETH-DM信息的帧，以便于在对等的MEP上进行单向帧时延和/或单向帧时延变化的测量；在双向ETH-DM的情况下，MEP向其对等的MEP发送带有ETH-DM请求信息的帧，并从其对等的MEP接收带有ETH-DM回复信息的帧，来进行双向帧时延和双向帧时延变化的测量。 \n单向时延测量需要发送MEP和接收MEP之间进行时钟同步，双向时延测量不需要发送MEP和接收MEP之间进行时钟同步。', 'id': '799729_28'}
{'label': 'text', 'content': 'Y.1731技术介绍\n性能OAM简介\n吞吐量测试（Throughput Test）\nRFC2544规范了吞吐量的测试，它通过递增速率来发送数据帧（最高可达理论的最大值）直到帧开始被丢失，这个速率就是某一长度的数据帧在指定带宽下的吞吐量。ITU-T Y.1731规范下，单播的ETH-LB（如带有数据字段的LBM帧和LBR帧）和ETH-Test帧（带有数据字段的TST帧）可用于吞吐量测试。MEP可以在某一速率上插入带有所配置长度、码型等的TST帧或LBM来测试吞吐量，进行单向或双向的吞吐量测试。基于硬件的实现才能保证测量的精确性。', 'id': '799729_29'}
{'label': 'text', 'content': '典型应用\n三种以太网OAM机制在不同的应用场合上使用，相互配合相互补充。对ITU-TY1731定义的OAM机制，目前主要使用其定义的性能测试机制进行网络性能的监控和测量，运营商使用它监控对大客户或关键业务的SLA。 \nIEEE 802.3ah功能主要被用在最后一公里接入的位置，用来监控和管理最后一公里的链路，与网管系统协同可以实现最后一公里的有效监控。EFM 典型应用如下图所示。 \nIEEE 802.1ag CFM功能主要被用于业务层的OAM管理，它主要被应用在基于连接的各种应用中，如使用到PBT网络管理、使用的L2 VPN的连接管理等。同时慢速的CFM 也可以应用到普通的以太网网络（基于VLAN 或QinQ）的网络进行网络管理。CFM可与核心网（MPLS/PBB 网络）中的OAM一起组成整个网络的OAM功能。全网的OAM部署的典型应用方案下图所示。 \n随着电信级以太网技术的不断发展，以太网OAM技术也在不断完善。提供完善的OAM 能力将成为以太网设备的基本要求，中兴通讯的E系列中端以太网交换机产品全面支持层次化的以太网OAM功能，以满足运营商的对以太网网络管理的要求，提高运营商的维护管理能力。', 'id': '799729_30'}
{'label': 'table', 'content': '缩略语\n| 英文缩写 | 英文全称 |\n|:-|:-|\n| CCM | Continuity Check Message |\n| CFM | Connectivity Fault Management |\n| EFM | Ethernet in the First Mile |\n| ETH-AIS | Ethernet Alarm Indication Signal |\n| ETH-CC | Ethernet Continuity Check |\n| ETH-DM | Frame Delay Measurement |\n| ETH-LCK | Ethernet Locked Signal |\n| ETH-LB | Ethernet Loopback |\n| ETH-LM | Frame Loss Measurement |\n| ETH-LT | Ethernet Link Trace |\n| ETH-RDI | Ethernet Remote Defect Indication |\n| ETH-Test | Ethernet Test Signal |\n| PDU | Protocol Data Unit |\n| MA | Maintenance Association |\n| MAID | Maintenance Association Identifier |\n| MD | Maintenance Domain |\n| ME | Maintenance Entity |\n| MEG | Maintenance Entity Group |\n| MEP | MEG End PointMaintenance Association End Point |\n| MHF | MIP Half Function |\n| MIP | MEG Intermediate PointMaintenance domain Intermediate Point |\n| MP | Maintenance Point |\n| LBM | Loopback Message |\n| LBR | Loopback Reply |\n| LTM | Linktrace Message |\n| LTR | Linktrace Reply |\n| OAM | Operations Administration and Maintenance |\n', 'id': '799729_31'}
{'label': 'text', 'content': '参考文献\nITU-T Y.1731 OAM functions and mechanisms for Ethernet based networks \nIEEE draft 802-1ag Connectivity Fault Management (CFM) \n工信部：传送网承载以太网（EOT）技术要求 第4部分：以太网运营、管理和维护（OAM） \nIEEE 802.3ah：Part 3: Carrier Sense Multiple Access with Collision Detection (CSMA/CD) Access Method and Physical Layer Specifications. Amendment: Media Access Control Parameters, Physical Layers, and Management Parameters for Subscriber Access Networks', 'id': '799729_32'}
