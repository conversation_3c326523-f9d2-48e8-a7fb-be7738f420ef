{'label': 'document', 'content': '承载MKT文档第一课：承载产品售前文档体系介绍与使用指南 v4.4_20210812', 'id': '1038239'}
{'label': 'text', 'content': 'BN产品MKT及方案团队 陈冉升10193751', 'id': '1038239_1'}
{'label': 'text', 'content': '课程大纲及目标说明\n课程简介 Overview \n本课程内容实操性较强，主要介绍公司售前文档体系、售前文档查询方法、售前文档模板使用方法，重点讲述： \nl 售前文档发布平台、文档常见类型 l 售前文档系统简介、文档查询技巧及注意事项 l 售前文档PPT、WORD模板使用', 'id': '1038239_2'}
{'label': 'text', 'content': '课程目标  Target\n学习和了解售前文档的分类情况、资料查询的快捷方式、常用文档模板的使用事项，让MKT人员在日常工作和文档查询 使用过程中少走弯路。 \n课程对象 Object \n本课程适合公司产品售前MKT岗位人员阅读和使用，包括且不限于以下人群： l 刚入职/转岗MKT岗位的新员工 l 已入职MKT岗位但仍不了解售前文档系统的员工 \n承载产品售前文档\n体系介绍', 'id': '1038239_3'}
{'label': 'text', 'content': '目      录\n承载产品售前文档\n查询指南 \n承载产品售前文档\n模板使用', 'id': '1038239_4'}
{'label': 'text', 'content': '承载产品售前文档体系介绍\n售前文档发布平台介绍 售前文档常见类型区分', 'id': '1038239_5'}
{'label': 'text', 'content': '无法让人忽视的文档数字\nBN正式发布的售前文档 1 \n3600+ 即使这样的数量级 文档仍在继续增加 4 BN售前文档月度下载数 \n2 每月更新的售前文档 15000+ 每月下载学习的数量 巨大 100+ 每月更新文档量非常大， 必须及时刷新同步 3 维护文档的作者们 100+ 编写文档的人员包含规 划、策划、配置、测试 人员等……', 'id': '1038239_6'}
{'label': 'text', 'content': '各系统作用不同，需要注意区分\n对内：仅在公司内网可用，仅内部员工可被授权 | 对外：外网可用，外部客户可被授权 公开：资料可见范围大，权限管理由管理员负责 | 私密：资料可见范围小，权限管理由个人为主、管理员为辅负责', 'id': '1038239_7'}
{'label': 'text', 'content': 'MKT等团队，发布在TSM/UDM文档库\nl 用于证明ZTE产品市场应用情况 用户列表信息一般包括国家、运营商、运行 日期、项目类型（商用局/实验局） 、项目/ 产品相关的信息等 注意：提交局方时需要对用户列表定制，做 到“不多不少”，对于局方联系人信息和合 同号信息一般不提供。 l 市场准入通行证 包含进网许可证书、进网试用批文、CCC 认证证书、抗震性能检测合格证、CB认 证证书、CE认证证书、ROHS认证……', 'id': '1038239_8'}
{'label': 'text', 'content': '能、互通性方面的能力\n包含：1）证书申请时，认证机构根据测 试标准对产品进行检测输出的报告；2） 运营商、运营商所属研究院、行业客户 等组织的测试输出的测试报告。 注意：优先提供具有测试单位公章或签 字的报告 \nl 其他产品实力证明材料 包括：项目初、终验证书、专利 证书、标准论坛参与情况、获奖 证书…… 注意：对项目有利的资质均可利 用 l 用于证明ZTE产品在现网的应用规模、运行情 \n包含：1）用户写给ZTE的信函，除产品运行信息外，一般 还有用户联系方式、客户签名和公章。 注意：优先使用近期的用户报告', 'id': '1038239_9'}
{'label': 'text', 'content': 'BN百宝箱空间\n中后台支撑 BN国内MKT一体化 BN国内MKT管理： 需求对标、项目策划、 项目进展、资源梳理……', 'id': '1038239_10'}
{'label': 'text', 'content': 'BN国际MKT一体化\nBN国际MKT管理： 需求对标、项目策划、 项目进展、资源梳理…… \n文档 品牌 \n文档规范、编写指南、 展会/展厅策划 文档规划、文档规划、 媒体传播 路标管理…… 品牌资料维护……', 'id': '1038239_11'}
{'label': 'text', 'content': '发布在Support和TSM系统\n产品描述类（基本信息） 工程实施类（安装调试） 运营维护类（操作维护） 除部分产品描述在成果鉴定评审阶段输出外，其他文档主要在设计定型评审阶段输出', 'id': '1038239_12'}
{'label': 'text', 'content': '秘密▲ 面向产品用户的交付/培训类文档库位置\nhttp://support.zte.com.cn', 'id': '1038239_13'}
{'label': 'text', 'content': '承载产品售前文档体系总结\niCenter \n文档索引/地图/知识 手机和PC端查询，不定期更新，专员和个人共建 \nTSM UDM \n产品基准文档 区域MKT文档 \nPC端查询，定期更新，专员发布 PC查询，无更新周期，个人发布', 'id': '1038239_14'}
{'label': 'text', 'content': '承载产品售前文档查询指南\n售前文档系统简介及权限申请 售前文档查询技巧及注意事项', 'id': '1038239_15'}
{'label': 'text', 'content': '浏览器地址栏输入\nhttp://tsm.zte.com.cn/', 'id': '1038239_16'}
{'label': 'text', 'content': '选择“售前文档”\n基准胶片、一页纸、产品介绍、产品路标、 功能清单、配置指导书、市场推广策略指 导书、成功故事、技术规范书 ……', 'id': '1038239_17'}
{'label': 'text', 'content': '秘密▲ 承载产品基准文档库位置\nhttp://tsm.zte.com.cn/tsm/default.aspx?Language=CN', 'id': '1038239_18'}
{'label': 'text', 'content': '秘密▲ 如何获知个人的TSM系统阅读权限？\n检查“阅读权限”栏个人的“岗位”对应的“产品名称”，即包含可以查阅的产品线售前文档权限。如果没有包含，那么可 以进行相关的权限申请。', 'id': '1038239_19'}
{'label': 'text', 'content': '秘密▲ 如何申请TSM系统阅读权限？\n点击“与我有关” → “个人设置” → “阅读权限” → “权限申请”', 'id': '1038239_20'}
{'label': 'text', 'content': '秘密▲ 看懂TSM文档页面授权信息\n开放范围：通常设定为“中兴通讯”，在 TSM网站进行BN产品售前文档权限申请后， 具有BN产品文档权限的人员，都能查看该文 \n档。 开放群组：即使没有申请TSM网站售前文档 权限，但是在这些群组中的人员，也能查看 该文档。 n 左图中的“BN产品MKT及方案团队，BN产品团队”是 公司IT定义的通用部门群组 n 左图中的“IPTN/MSTP+XX，OTN/WDM+XXX， ES/CSR+XXX”是公司IT定义的“标签+岗位”群组 n 左图中“BN标签人员（国际运营商-中方）、BN标签人 员（国内运营商）”，是自定义群组，人员名单是由产 开发范围、开放群组、可编辑权限、不受控制权限这四项是独立的且是“或”的关系，即用户在其中任意一 品线文档总监维护 n 如果员工有组织架构调整或者变化，原有权限都会撤销 \n项里面的群组都可以看到该文档。 需要重新申请。', 'id': '1038239_21'}
{'label': 'text', 'content': '秘密▲ 如何批量下载TSM文档附件\n同时勾选多个附件，然后点击“批量下载”按钮 如果附件总数超过每页显示的设定行数时，可调整数值，确保不用翻页即可完成一次批量下载', 'id': '1038239_22'}
{'label': 'text', 'content': '秘密▲ 如何反馈产品文档意见？\n如果只有意见，而没有修订好的文档附件，则建议直接在“资料评价”区反馈。 \nl文档质量评价 文档评价意见处理', 'id': '1038239_23'}
{'label': 'text', 'content': '总结：可以求助的那些人（Need  Help？）\n文档发布人：发布文档，进行推送 \n资质维护人：维护资质文档，归纳&汇总 \n文档编写人：每篇文档的作者，对其中的内容进行解答 \n直属领导：特殊文档的索取，需要获得直属领导的批准 \nBN产品售前文档总监：陈冉升', 'id': '1038239_24'}
{'label': 'text', 'content': '承载产品售前文档模板使用\n售前文档PPT格式模板使用 售前文档WORD格式模板使用', 'id': '1038239_25'}
{'label': 'text', 'content': '秘密▲ 售前文档模板使用技巧~模板获取\n售前文档样式模板： \nhttps://i.zte.com.cn/#/space/ae275bf2b196467693d8cbc6cafbb0d8/wiki/page/923f49d2f6b5', 'id': '1038239_26'}
{'label': 'text', 'content': '主要包括：\nü 通用： 售前文档样式模板（WORD、PPT、Excel） ü 特殊： 一页纸精华模板（PPT/WORD） 易拉宝（PPT） HTB模板（待补充） PD模板（待补充）', 'id': '1038239_27'}
{'label': 'text', 'content': '秘密▲ PPT：安装“文档资源库”获取设计素材\nü 公司文档开发部设计了一套素材“文档资源库”，为大家提 供制作幻灯片、长图、一页纸、海报等对外文档所需的模板、 高清图片、矢量图标、产品图标、设备图、地图/Logo等各 类资源，以及制作这些文档所需的一键提效功能，如一键拼 图、字体/替换、动画复制等。 \nü 文档资源库下载地址，大家可以下载试用，有问题可反馈 何纯浩10016961。 \nhttps://it.zte.com.cn/its/app/download/Download/ getDownloadDetails.action?download.itsInfoDownl \noadId=6065', 'id': '1038239_28'}
{'label': 'text', 'content': '秘密▲ 学会做“标题党”，杜绝常规PPT页标题，别让观众思考\n告别常规标题\n标题直击观点 \nl 项目背景 l项目背景\nl发展趋势\nl产品介绍\nl方案亮点\nl商用案例 \nl网络需求 景切片应用的基石\nl 网络需求：云网融合是角逐DICT的最佳手段\n小改变 大不同 l 发展趋势：超宽是基础，开放极简&智慧便捷是关\n键能力\nl技术亮点 l 产品介绍：全系列高性能自研芯片，保证产品长期\n服务能力\nl 方案亮点：基于AI的智能管控，实现智力协同云网\nl 商用案例：中兴通讯助力黑龙江移动省干OTN网络\n能力升级，优架构、降成本', 'id': '1038239_29'}
{'label': 'text', 'content': '秘密▲\n4种常用标题类型，推荐在PPT编写过程中灵活应用 \n总结型标题 数字型标题 提问型标题 展望型标题 标题应该是整个PPT的总结或 恰当使用数字，能让主题更加 抛出一个大家都很关注的问题， 这种标题非常大气，具有前瞻 亮点，告诉别人这个PPT是在 聚焦。数字自带聚光灯效果， 引发大家的思考，随后给出个 性，很多人都喜欢，但很忌讳 讲什么 很容易吸引读者目光 人见解和答案 大字报式的喊口号 \n标题是Power Point，需要在3秒内吸引观众注意并告知重点内容', 'id': '1038239_30'}
{'label': 'text', 'content': '常用篇章逻辑\n1、需求分析-解决方案-应用案例', 'id': '1038239_31'}
{'label': 'text', 'content': '2、提问形式直接针对客户关注问题\n3、目录页和总结页使逻辑更清晰 \n常用页面逻辑 语句逻辑要点\n1、并列 1、表达规范\n专业化解决方案描述，\n避免口语化表达 \n2、对比 2、语言精练\n对论据、观点、结论等\n提炼归纳', 'id': '1038239_32'}
{'label': 'text', 'content': '秘密▲ 用思维导图列好提纲，梳理逻辑架构\n思维导图提升效率 从上到下搭建逻辑', 'id': '1038239_33'}
{'label': 'text', 'content': '点击左侧“大纲”查看各页标题及逻辑\n标题是逻辑学，抛出因，用内文做分析和结果，前后照应', 'id': '1038239_34'}
{'label': 'text', 'content': 'WORD~模板分类\n以WORD中文版本为例，我们平时使 用的模板分两类： \nü 公司通用模板：无封面封底，日常 文案均需使用，右上有密级。 \nü 售前文档模板：有封面封底，TSM 上传文档均需使用。（本培训主要 以此为主）', 'id': '1038239_35'}
{'label': 'text', 'content': '秘密▲ WORD：用模板新建文档\n将模板保存到电脑，双击打开。此文档上方出现的“ZTE样式模板”页中，为所有通用的样式均做好了宏， 用户可直接选中文中需要设置的段落，点击按钮，进行样式变更。 \n注意： \nü 此中文模板已更新为WPS-OFFICE兼容模板。 ü 如判断可能是因为兼容性导致文档交接后无法打开，可请原作者检查一下可有直接在文中绘制的图片，截为JPG替换后 重新保存文档，再次交接，看是否问题可以解决。', 'id': '1038239_36'}
{'label': 'text', 'content': '秘密▲ WORD：套用模板更新老文档\n条件：旧文档使用的模板与新模板中的所有（或大部分）样式名称一致。 \n弊端：页眉页脚和其他部分样式无法自动更新，需要手动调节。', 'id': '1038239_37'}
{'label': 'text', 'content': '秘密▲ WORD：样式宏按钮分类\n• 正文相关样式 \n• 交叉引用相关样式 \n• 图片相关样式 • 表格相关样式 • 其他辅助按钮 如无法使用样式设置按钮，选择“文件”-“选项”-“信任中心”，启用所有宏，再次尝试。', 'id': '1038239_38'}
{'label': 'text', 'content': '秘密▲ WORD：图表样式设置注意点-1\nü 无论图名或表名，都应该在图表的上方偏左。 ü 图/表编号默认为A-B，A为章节第A章，B为该章中第B个图/表。', 'id': '1038239_39'}
{'label': 'text', 'content': '秘密▲ WORD：图表样式设置注意点-2\nü 表格宽度可根据内容长短设置为10厘米宽、13厘米宽\n或者页宽。选中表格首行，点击任意表格宽度即可一键\n设置。 \nü 如用上述步骤发现表头未在后页重复显示，则说明整\n个表格都被误设置为“重复出现”，此时，先选中整\n个表格，在右键“表格属性”中去除对勾后，再次选\nü 表格宽度改变的同时，表头也会在不同页重复出现。 中首行，设置为“重复出现”，即可。', 'id': '1038239_40'}
{'label': 'text', 'content': '秘密▲ WORD：图表样式设置注意点-3\nü 如不希望表格中同一格的内容因为太长而导致跨 页断行，可选中整个表格，在右键“表格属性” 中，去掉“允许跨页断行”的对勾，即可。', 'id': '1038239_41'}
{'label': 'text', 'content': '秘密▲ WORD：“与下段同页”设置方式\nü 如图名和图、表名和表被割裂在两页，不要 使用“Enter”强制下移。此时，可以选中图 名/表名，右键“段落”-“换行和分页”中， 勾选“与下段同页”，即可实现。', 'id': '1038239_42'}
{'label': 'text', 'content': '秘密▲ WORD：删除页面上锁定的内容控件\nü 页面上有多个内容控件，删除文字后仍无法 删除干净，此时，点击“开发工具”-“属 性”，将“无法删除内容控件”的对勾去掉， 再次删除，即可全部删除干净。', 'id': '1038239_43'}
{'label': 'text', 'content': '秘密▲ WORD：分隔符设置注意事项\n分隔符主要包括分页符和分节符。 ü 分页符仅将段落物理分页，增删不影响任何样式。 ü 分节符可将文档分为若干节，每节可有不同的设置，包括页面 设置、页眉页脚等。增删分节符将会影响前后文的样式。', 'id': '1038239_44'}
{'label': 'text', 'content': '注意：\n如果从其他文件中将内容拷入我们的模板后，发现页眉页脚甚至正文样式发生莫名混乱，则极有可能是从外界带入了 分节符所致。此时，退回去检查，删除原文中所有分节符后，重新拷贝再试。', 'id': '1038239_45'}
{'label': 'text', 'content': '秘密▲ WORD：其他注意事项\nü 如果是标书，封面的运营商的Logo是否忘记填写？ ü 正文改动后，目录、图表目录是否进行了更新？ ü 如果文档中没有图或者表，是否把相应的模板目录删掉？ ü 删除内容时注意，如果前后有 “分节符”，千万小心不要删除！ ü 是否有多余的空格、空行、空白页？ ü 模板中的编写指南是否都删除了？ ü 评审修订后的文档，提交时是否已经接受了所有的修订？ \nü ……', 'id': '1038239_46'}
{'label': 'text', 'content': '秘密▲ EXCEL：页眉和页脚的设置\n“页面布局”-“页面设置”-“页眉/页脚”', 'id': '1038239_47'}
{'label': 'text', 'content': '秘密▲ EXCEL：标题行重复打印的设置\n“页面布局”-“页面设置”-“打印标题”', 'id': '1038239_48'}
{'label': 'text', 'content': '秘密▲ 再次强调：文档客户化意识是重中之重\n01 关注文档阅读对象 忌一篇文档走天下。 需根据实际市场情况对基准文档进 行客户化定制。 \n忌光谈自我优秀。 需从客户角度出发，从为客户带来 价值的点切入。 \n忌平铺直叙。 需浓缩精炼，突出亮点，一目了然。 \n04 巧妙使用图表 忌简单的数字堆砌。 需筛选数据，提炼精华，有冲击性。', 'id': '1038239_49'}
{'label': 'text', 'content': '秘密▲ 总结：熟悉10类文档4大系统3种模板\n十类重点售前文档 四大文档系统 \n2星以上文档 iCenter | TSM | UDM | Support \n一页纸 | 产品介绍 BN百宝箱空间集成售前文档为知识服务\n解决方案 | 基准胶片 TSM权限向部门管理干部申请\n功能清单 | 产品路标 UDM文档范围和权限由个人定义并管理\n推广策略 | 配置策略 Support系统可查产品用户手册等售后资料 \n成功故事 | 技术规范', 'id': '1038239_50'}
{'label': 'text', 'content': '三种文档模板\nPPT | WORD |Excel \n安装“ZTE文档资源库”PPT插件\n下载售前文档WORD样式模板\n尽量交付pdf格式文档给客户\n注意文档客户化表达 \n感 谢 聆 听 \n让沟通与信任无处不在', 'id': '1038239_51'}
