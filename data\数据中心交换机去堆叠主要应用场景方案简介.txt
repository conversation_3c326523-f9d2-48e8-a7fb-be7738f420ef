{'label': 'document', 'content': '数据中心交换机去堆叠主要应用场景方案简介', 'id': '1009017'}
{'label': 'table', 'content': '| 版本 | 日期 | 作者 | 审核者 | 备注 |\n|:-|:-|:-|:-|:-|\n| V1.0 | 20201111 | 邵会勇 | | 拟制 |\n| | | | | |\n', 'id': '1009017_1'}
{'label': 'text', 'content': '核心网配套场景\n国内运营商5GC NFVI传统组网方案\n适用范围\n中国移动5G网络云控制面 \n中国电信5G电信云控制面 \n中国联通5G通信云控制面', 'id': '1009017_2'}
{'label': 'text', 'content': '核心网配套场景\n国内运营商5GC NFVI传统组网方案\n组网方案\n整个网络从上到下分成3层，出口层、核心层、接入层： \n出口层：负责与外部网络的互联，保证资源池内部网络高速访问外部网络，并对资源池内网和外网的路由信息进行转换和维护，部署IP承载网网/Internet等接入设备。 \n核心层：负责向下汇聚网络内的所有接入层交换设备，保证网络内接入层交换设备之间的高速交换;负责向上通过出口路由设备与对应外部网络进行互联。部署核心交换机（业务/存储/管理EOR）、EOR配对路由器（DCGW）及内层防火墙等 \n接入层：负责接入各类服务器和存储设备，部署接入交换机（业务/存储/管理/硬件管理TOR）等 \n按照承载业务的属性分为3个区域，管理区域、业务区域、存储区域： \n管理区域：用于实现资源池网络管理、资源管理、业务管理、运营管理等功能，部署MANO、VIM、EMS等管理节点服务器。 \n业务区域：承载各功能网元内部各部件交互的数据流、跨网元互访的数据流以及网元访问外部网络流量。业务区域又划分为2个子区：可信区和DMZ区（DMZ视需求配置），部署各类VNF网元的计算节点服务器。 \n存储区域：承载各节点服务器访问存储服务器的存储数据流以及存储内部数据均衡的数据流。部署存储类型的服务器。', 'id': '1009017_3'}
{'label': 'text', 'content': '核心网配套场景\n国内运营商5GC NFVI传统组网方案\n组网方案\n业务网部署方案\n业务网络采用：业务TOR -- 业务EOR -- DCGW三级架构 \n计算节点服务器专门提供一对10GE网口承载业务数据流，通过BOND-4（负载分担）方式实现服务器侧的链路冗余，双归连接成对业务TOR \nVIM服务器提供一对10GE网口双归接入成对业务TOR，用于承载DHCP、VIM Metadata等相关业务数据流（按需） \nDCGW、业务EOR、业务TOR采用双机冗余结构。成对业务EOR之间配置两组互联LAG链路，分别作为MC-LAG功能的Peerlink通道和keepalive通道；成对业务TOR之间配置两组互联LAG链路，分别作为MC-LAG功能的Peerlink通道和keepalive通道；成对DCGW之间配置互联LAG链路，作为出口层三层逃生通道。 \n成对业务TOR与一对业务EOR采用交叉互联，实现冗余保护 \nDCGW与成对管理EOR采用交叉互联方式，用于MANO访问业务平面 \nDCGW与Internet外层防火墙之间、DCGW与IP承载网各CE之间均采用口字型互联，实现冗余能力。 \nDCGW部署方式 \nDCGW配置两台路由器，单独部署，不开启VSC或MC-LAG \nDCGW作为业务网络网元的三层网关，根据业务诉求，采用ipv4和ipv6双栈部署。 \nDCGW对于业务EOR采用路由子接口互联，对于CE路由器采用路由子接口形式互联，对于旁挂设备（如主备防火墙）采用vlan三层接口形式互联。 \n对于主机型网元，配置VRRP实现业务网关冗余保护，VRRP心跳采用单独二层vlan互通，可通过DCGW之间连线承载。 \n对于路由型网元，在网元侧为默认路由配置两个下一跳，分别为两个DCGW网关的地址。 \n每台DCGW分别配置BGP，导入网元业务路由，并对外发布 \n每台DCGW分别与一对业务EOR互联的端口配置为一组LAG端口 \nDCGW与管理EOR间交叉互联，之间配置ipv4/v6的动态路由ospf或静态路由，可采用低速端口互联 \n每台DCGW分别配置到核心网网元Loopback地址的静态路由，下一跳为核心网网元的接口地址，并在每台DCGW配置双向BFD保护（配置静态路由并enable BFD的方式） \n配置VRF实现对不同业务的隔离 \n两台DCGW与业务CE/业支CE等设备之间配置v4和v6的动态路由协议OSPF实现三层路由互通。 \n两台DCGW设备之间的连线配置一组LAG端口，采用路由子接口形式打通一条v4/v6三层通路，作为逃生通道。 \n业务EOR部署方式 \n业务EOR做为二层透传设备，通过MC-LAG实现节点可靠性，配置vlan作为业务隔离。 \n每对业务EOR面向每台DCGW相连的端口配置为一组MC-LAG，与其对应的DCGW配置为一组LAG。 \n一对业务EOR与每对业务TOR之间所有交叉连接的端口配置一组MC-LAG，与其对应的每对业务TOR上的互联端口也配置一组MC-LAG。 \n两台业务EOR之间的连线配置为二组LAG链路，分别作为peerlink与keepalive通道 \n其中，peerlink通道需要采用高速端口互联，采用vlan三层接口互通；keepalive通道可采用低速端口互联，采用路由子接口互通。 \n全局使能STP，模式为RSTP，用于环路保护，对于MC-LAG口则为v-STP，与DCGW互联的MC-LAG端口需要配置为边缘端口 \n业务TOR部署方式 \n业务TOR做为二层透传设备，通过MC-LAG实现节点可靠性，配置vlan作为业务隔离。 \n每对业务TOR对上、对下均开启MC-LAG。每对业务TOR与每台服务器之间配置为一组MC-LAG，对应每台服务器的两个业务面网口开启BOND-4。 \n每对业务TOR与一对业务EOR之间配置一组MC-LAG，对应的一对EOR也配置一组MC-LAG。 \n两台业务TOR之间的连线配置为二组LAG链路，分别作为peerlink与keepalive通道 \n其中，peerlink通道需要采用高速端口互联，采用vlan三层接口互通；keepalive通道可采用低速端口互联，采用路由子接口互通。 \n根据业务需要，配置灵活QinQ功能，实现上行流量时服务器出来的两层vlan报文，经业务TOR剥离报文外层vlan送给业务EOR；下行流量则是将一层vlan报文再打上一层外层vlan后送给服务器。 \n全局使能STP，模式为RSTP，用于环路保护，对于MC-LAG口则为v-STP。业务TOR与服务器相连的接口配置为STP边缘端口来加快网络拓扑的收敛时间，并配置业务TOR对TC类型BPDU报文以及BPDU保护功能来加强网络的稳定性。 \n业务TOR下行端口配置为Hybrid模式，上行端口配置为Trunk模式。', 'id': '1009017_4'}
{'label': 'text', 'content': '核心网配套场景\n国内运营商5GC NFVI传统组网方案\n组网方案\n管理网部署方案\n管理网络采用：管理TOR -- 管理EOR 架构 \n计算节点服务器专门提供一对10GE网口承载业务数据流，通过BOND-1（主备模式）实现服务器侧的链路冗余，双归连接到成对管理TOR \nVIM服务器提供一对10GE网口双归接入成对业务TOR，承载管理数据流，双归连接到成对管理TOR \nVNFM\\EMS\\NFVO服务器提供两对10GE网口，分别承载基础设施管理流和业务管理流，双归连接到成对管理TOR \n存储服务器提供一对10GE网口承载管理流量，双归连接到成对管理TOR \n管理EOR、管理TOR采用双机冗余结构。成对管理EOR之间配置2组LAG互联链路，分别作为MC-LAG的Peerlink通道和keepalive通道&出口层逃生通道；成对管理TOR之间配置2组LAG互联链路，分别作为MC-LAG的Peerlink通道和keepalive通道。 \n管理TOR与管理EOR之间、管理EOR与内层防火墙之间均采用交叉互联，实现冗余保护。 \n硬件管理TOR，单机时采用双归连接到一对管理TOR；堆叠时与一对管理TOR交叉连接。 \n管理EOR与管理CE之间采用口字型互联，实现网络可靠性。 \n管理EOR与DCGW之间交叉相连，实现网络可靠性 \n管理EOR部署方式 \n管理EOR作为管理面网元的三层网关，根据组网情况，采用ipv4或ipv6地址部署。 \n管理EOR对于对于CE路由器采用路由子接口形式互联，对于旁挂设备（如主备防火墙）采用vlan三层接口形式互联，对于管理TOR采用vlan三层接口形式互联，对于硬件管理TOR采用vlan三层接口形式互联。 \n一对管理EOR对每对管理TOR之间配置一个MC-LAG组，一对管理EOR对每对硬件管理TOR之间配置一个MC-LAG组 \n两台管理EOR之间的连线配置为二个LAG组，分别作为peerlink和keepalive通道。 \n其中peerlink通道需要采用高速端口互联，采用vlan三层接口互通；keepalive通道可采用低速端口互联，采用路由子接口互通 \n配置VRRP双主模式，实现三层网关的可靠性。 \n管理EOR与管理CE之间配置动态路由协议ipv4/ipv6 ospf，实现管理网资源池内外的三层路由互通 \n管理EOR与管理CE之间配置LINK BFD，来关联管理CE的黑洞路由。 \n成对管理EOR与成对DCGW之间推荐配置ipv4/ipv6 ospf路由实现特定管理网元访问业务网的要求，如果局方特别要求，可采用静态路由方式，但需要部署BFD进行路由可用性检查 \n配置VRF实现对不同业务的隔离 \n全局使能STP，模式为RSTP，用于环路保护，对于MC-LAG口则为v-STP，与DCGW互联的端口上关闭STP功能 \n管理TOR部署方式 \n管理TOR做为二层透传设备，通过MC-LAG实现节点可靠性，配置vlan实现业务隔离。 \n每对管理TOR对上、对下均开启MC-LAG。 \n每对管理TOR与每台服务器之间配置两组MC-LAG，每组MC-LAG仅加入一台管理TOR与服务器相连的下行端口，对应每台服务器的管理面网口开启BOND-1。 \n每对管理TOR与一对管理EOR之间配置一组MC-LAG，对应的一对管理EOR也配置一组MC-LAG。 \n两台管理TOR之间的连线配置为二组LAG，分别作为peerlink与keepalive通道 \n其中，peerlink通道需要采用高速端口互联，采用vlan三层接口互通；keepalive通道可采用低速端口互联，采用路由子接口互通。 \n全局使能STP，模式为rstp，用于环路保护，对于MC-LAG口则为v-STP。管理TOR与服务器相连的接口配置为STP边缘端口来加快网络拓扑的收敛时间，并配置管理TOR对TC类型BPDU报文以及BPDU保护功能来加强网络的稳定性。 \n管理TOR下行端口配置为Hybrid模式，上行端口配置为Trunk模式。', 'id': '1009017_5'}
{'label': 'text', 'content': '核心网配套场景\n国内运营商5GC NFVI传统组网方案\n组网方案\n存储网部署方案\n存储网络建议采用：存储TOR -- 存储EOR 架构 \n存储网络没有对外出资源池接口 \n管理区域和业务区域的计算型服务器专门提供一对10GE网口双归接入成对存储TOR，通过BOND-4实现服务器侧的链路冗余 \n存储型服务器提供一对10GE网口双归接入成对存储TOR，通过BOND实现服务器侧的链路冗余 \n存储EOR、存储TOR采用双机冗余结构。一对存储EOR之间配置互联链路，作为MC-LAG的Peerlink通道和keepalive通道；成对存储TOR之间配置互联链路，作为MC-LAG的Peerlink通道和keepalive通道。 \n存储TOR与存储EOR之间采用交叉互联，实现冗余保护。 \n存储EOR部署方式 \n存储EOR作为存储面网元的三层网关，采用ipv6地址部署。 \n一对存储EOR对每对存储TOR之间配置一个MC-LAG组， \n两台存储EOR之间的连线配置为二个LAG组，分别作为peerlink和keepalive通道。 \n其中peerlink通道需要采用高速端口互联，采用vlan三层接口互通；keepalive通道可采用低速端口互联，采用路由子接口互通 \n配置VRRP双主模式，采用vlan三层接口，提供三层网关的可靠性。 \n配置VRF实现对不同业务的隔离 \n全局使能STP，模式为rstp，用于环路保护，对于MC-LAG口则为v-STP \n存储TOR部署方式 \n存储TOR做为二层透传设备，通过MC-LAG实现节点可靠性，配置vlan实现业务隔离。 \n每对存储TOR对上、对下均开启MC-LAG。 \n每对存储TOR与每台服务器之间配置一组MC-LAG，对应每台服务器的存储面网口开启BOND-4。 \n每对存储TOR与一对存储EOR之间配置一组MC-LAG，对应的一对存储EOR也配置一组MC-LAG。 \n两台存储TOR之间的连线配置为二组LAG，分别作为peerlink与keepalive通道 \n其中，peerlink通道需要采用高速端口互联，采用vlan三层接口互通；keepalive通道可采用低速端口互联，采用路由子接口互通。 \n全局使能STP，模式为rstp，用于环路保护，对于MC-LAG口则为v-STP。存储TOR与服务器相连的接口配置为STP边缘端口来加快网络拓扑的收敛时间，并配置存储TOR对TC类型BPDU报文以及BPDU保护功能来加强网络的稳定性。 \n存储TOR下行端口配置为Hybrid模式，上行端口配置为Trunk模式。', 'id': '1009017_6'}
{'label': 'text', 'content': '核心网配套场景\n国内运营商5GC NFVI传统组网方案\n组网方案\n硬件管理网部署方案\n硬件管理交换机用于接入服务器IPMI管理口，业务/管理/存储TOR、业务/管理/存储EOR、DCGW、防火墙等网络设备的带外管理口。 \n根据资源池的大小，采用多台硬件管理交换机，独立方式和堆叠方式部署。 \n对于配置2个硬件管理口的设备，如框式网络设备（EOR、路由器）等通过2个GE电口分别连接一对硬件管理TOR的不同设备上，成对部署的硬件管理TOR为堆叠部署。 \n对于配置1个硬件管理口的设备，如计算服务器、存储服务器、盒式网络设备（TOR）等通过1*GE电口连接单台硬件管理TOR上，该单台硬件管理TOR为非堆叠方式部署。 \n硬件管理TOR配置为硬件管理面的网关，与管理EOR三层互通， 管理EOR为其配置单独的硬管VRF，使之与其他管理业务隔离。', 'id': '1009017_7'}
{'label': 'text', 'content': '核心网配套场景\n国内运营商5GC NFVI SDN组网方案（硬件overlay）\n适用范围\n中国电信5G电信云控制面 \n中国联通5G通信云控制面', 'id': '1009017_8'}
{'label': 'text', 'content': '核心网配套场景\n国内运营商5GC NFVI SDN组网方案（硬件overlay）\n组网方案\n1、采用管理、业务、存储三网分离的组网架构，保证业务的安全隔离（边缘DC可以采用管理和存储共用EOR交换机的方案） \n2、业务网采用EOR-TOR架构，提供可扩展的基础网络架构 \n3、引入路由形态DCGW，提供动态路由，BFD等能力，实现与NFV网元之间的路由及可靠性保障', 'id': '1009017_9'}
{'label': 'text', 'content': '核心网配套场景\n国内运营商5GC NFVI SDN组网方案（硬件overlay）\n组网方案\n业务网部署方案\nUnderlay网络： \n采用三层路由组网，各业务TOR、业务EOR、以及DCGW之间全部采用路由子接口互联（VRF-0），开启动态路由协议，实现三层网络互通。 \nUnderlay网络路由协议采用OSPF。DCGW、业务EOR、业务TOR同属于一个OSPF area，DCGW/业务TOR分别与业务EOR建立OSPF邻居，公告自己的underlay路由信息。此外，为节省DR/BDR选举时间，加速路由的快速收敛，将OSPF邻居配置为P2P模式。 \n服务器专门提供一对网口通过BOND实现服务器侧的链路冗余，双归连接成对业务TOR交换机。 \n业务TOR采用成对设置，提升网络的高可靠性。 \n业务EOR独立设置，与业务TOR和DCGW交叉相连，运行IGP路由协议（如OSPF），形成到远端节点的ECMP等价路由，实现网络的高可靠性。部署负载分担增强，根据实际组网业务情况，增加UDP源端口号或者接收流量的物理端口作为分担的hash因子，以期实现更均匀的业务分担。 \nDCGW采用双活网关方式部署 \n与业务EOR交叉相连，配置IGP路由协议，形成ECMP等价路由。 \n与管理EOR采用口字型相连，通过静态路由或动态路由实现三层互通。 \n与出口层设备采用口字型相连，通过静态路由或动态路由实现三层互通。 \n与旁挂设备FW交叉相连，FW为集群模式且配置一个SmartGroup（不协商LACP协议），DCGW则配置一个ESI组，采用SmartGroup on模式与之互联。 \nOverlay网络： \n采用EVPN+层次化端口方案，具体说明如下： \nNFV网元采用vSwitch、SR-IOV或裸金属方式部署 \n采用业务TOR交换机作为VTEP，通过层次化端口方式将vSwitch、SR-IOV方式部署的虚拟机映射到相应的vni，vSwitch仅工作在二层方式 \n业务TOR交换机采用MC-LAG+Vtep Group方式部署，采用非对称IRB转发模式 \n控制器根据VIM的编排，自动生成业务网络配置，并通过netconf通道下发至业务TOR、DCGW \n业务TOR、DCGW间运行EVPN协议，完成overlay网络的搭建 \nEOR节点作为EVPN RR，缩减EVPN邻居数量 \nNFV网元与DCGW间通过overlay通道运行BGP协议，完成业务网络路由通告', 'id': '1009017_10'}
{'label': 'text', 'content': '核心网配套场景\n国内运营商5GC NFVI SDN组网方案（硬件overlay）\n组网方案\n管理网部署方案\n同*******', 'id': '1009017_11'}
{'label': 'text', 'content': '核心网配套场景\n国内运营商5GC NFVI SDN组网方案（硬件overlay）\n组网方案\n存储网部署方案\n同*******', 'id': '1009017_12'}
{'label': 'text', 'content': '核心网配套场景\n国内运营商5GC NFVI SDN组网方案（硬件overlay）\n组网方案\n硬件管理网部署方案\n同*******', 'id': '1009017_13'}
{'label': 'text', 'content': '核心网配套场景\n国内运营商5GC NFVI SDN组网方案（混合overlay）\n适用范围\n中国移动5G网络云控制面', 'id': '1009017_14'}
{'label': 'text', 'content': '核心网配套场景\n国内运营商5GC NFVI SDN组网方案（混合overlay）\n组网方案\n按照层次化架构设计，业务域资源池自上而下分为出口层、核心层、接入层三层，各层部署以下设备： \n出口层：出口层作为网络云站点内部和外部网络互联互通的纽带，对外完成与外部设备高速互联，对内负责与核心层交换设备互联，完成与站点外部路由信息转发和维护。出口层可复用现网承载及传输设备，包括CMNET、IP专网、PTN等。 \n核心层：部署核心交换机（EOR）和DCGW，并按需配置内层防火墙、IDS/IPS、WAF以及负载均衡器等设备，核心交换机（EOR）向下汇聚所有接入层网络设备，保证接入层设备之间的高速交换，其中DCGW作为资源池出口网关，向上通过承载网CE、外层防火墙等出口路由设备与对应外部网络进行互联。 \n接入层：部署TOR交换机，负责向下接入各类服务器和存储设备，向上连接核心交换机。', 'id': '1009017_15'}
{'label': 'text', 'content': '核心网配套场景\n国内运营商5GC NFVI SDN组网方案（混合overlay）\n组网方案\n业务网部署方案\n采用SDN方式组网，DCGW和vSwitch为overlay网络的vxlan的VTEP，纳入SDN控制器管理。业务EOR与业务TOR为underlay网络设备，不纳入SDN控制器管理。 \nUnderlay网络： \n采用三层路由组网，各业务TOR、业务EOR、以及DCGW之间全部采用路由端口互联，开启动态路由协议，实现三层网络互通。 \nUnderlay网络路由协议采用OSPF。DCGW、业务EOR、业务TOR同属于一个OSPF area，DCGW/业务TOR分别与业务EOR建立OSPF邻居，公告自己的underlay路由信息。此外，为节省DR/BDR选举时间，加速路由的快速收敛，将OSPF邻居配置为P2P模式。 \n服务器专门提供一对10GE网口通过BOND实现服务器侧的链路冗余，双归连接成对业务TOR交换机。 \n业务TOR成对设置，与每台服务器之间配置一组MC-LAG，实现网络的高可靠性。配置直连路由，打通vSwitch与业务TOR的三层通路。 \n业务EOR独立设置，与业务TOR和DCGW交叉相连，运行IGP路由协议（如OSPF），形成到远端节点的ECMP等价路由，实现网络的高可靠性。部署负载分担增强，根据实际组网业务情况，增加UDP源端口号或者接收流量的物理端口作为分担的hash因子，以期实现更均匀的业务分担。 \nDCGW采用双活网关方式部署， \n与业务EOR交叉相连，配置IGP路由协议，形成ECMP等价路由。 \n与管理EOR采用口字型相连，通过静态路由或动态路由实现三层互通。 \n与出口层设备（业务CE、业支CE、CMNet出口路由器）采用口字型相连，通过静态路由或动态路由实现三层互通。 \n与旁挂设备FW交叉相连，FW为集群模式且配置一个SmartGroup（不协商LACP协议），DCGW则配置一个ESI组，采用SmartGroup on模式与之互联。 \nOverlay网络 \n采用混合Overlay方式组网，即vSwitch和DCGW做为VTEP节点。由于网络云大区不涉及SR-IOV，因此暂无VTEP部署在SDN TOR上的场景。 \n控制面由两部分组成： \n对于vSwitch，控制器采用OpenFlow方式进行流表管理等控制。 \n对于DCGW，控制器启动agent，与DCGW建立BGP EVPN，进行表项同步。 \n控制器则将两者表项进行转换与同步，进而实现整网控制面表项的一致性。 \n转发面采用VXLAN协议，在VTEP节点之间建立vxlan隧道。 \nSDN网关按照双活方式部署，采用集中式路由引入， \n对于VNF与外部网络流量，VNF同时和两个SDN网关上的Loopback地址建立eBGP邻居，实现VNF服务IP（如loopback IP）对外发布，也可以实现VNF内的用户地址（如UE地址）对外部网络的发布 \n配置静态路由，保证eBGP路由可达。 \n部署单臂BFD，将指向VNF网元的静态路由关联BFD session。每台DCGW分别用不同的Loopback IP与VNF网元vNIC建单臂BFD，BFD报文通过Overlay网络承载，按目的MAC（为静态路由下一跳vNIC接口MAC地址）转发给VNF网元对应vNIC。 \n相应地，VNF网元需支持单臂BFD的回弹，即收到路由器侧发出单臂BFD报文后，不能丢弃，根据报文目标IP（DCGW的loopback IP）查表转发。', 'id': '1009017_16'}
{'label': 'text', 'content': '核心网配套场景\n国内运营商5GC NFVI SDN组网方案（混合overlay）\n组网方案\n管理网部署方案\n同*******', 'id': '1009017_17'}
{'label': 'text', 'content': '核心网配套场景\n国内运营商5GC NFVI SDN组网方案（混合overlay）\n组网方案\n存储网部署方案\n同*******', 'id': '1009017_18'}
{'label': 'text', 'content': '核心网配套场景\n国内运营商5GC NFVI SDN组网方案（混合overlay）\n组网方案\n硬件管理网部署方案\n同*******', 'id': '1009017_19'}
{'label': 'text', 'content': '核心网配套场景\n专U组网方案\n适用范围\n中国移动5G专U \n中国电信5G专U \n中国联通5G专U', 'id': '1009017_20'}
{'label': 'text', 'content': '核心网配套场景\n专U组网方案\n组网方案\n专U设备所有外部互联端口均由业务交换机提供，分别连接到回传网络、Internet、IP专网等外部网络 \n两台业务交换机间通过2组Smartgroup端口互联，一组作为下联服务器MC-LAG的Peerlink链路，一条作为上联外部网络的逃生通道和MC-LAG的keepalive链路。 \n两台业务交换机开启MC-LAG功能，通过跨设备的Smartgroup端口与服务器的端口互联 \n服务器提供多种接口，业务端口、管理端口和IPMI管理口，业务口/管理口采用Bond捆绑方式与业务交换机互联，其中业务口采用Bond-4分担模式，管理口采用Bond-1主备模式 \n业务交换机的带外管理口、服务器的IPMI管理口连接到硬件管理交换机的GE端口，硬件管理交换机通过10GE上行端口与业务交换机的业务口互联。', 'id': '1009017_21'}
{'label': 'text', 'content': '核心网配套场景\n专U组网方案\n组网方案\n业务网部署方案\n网元部署方式 \nUPF网元采用虚拟化方式部署，网元的虚机分布在各计算节点，虚机间需要二层互通。核心网网元以业务交换机作为下一跳网关，其中UPF网元不同PFU接口板采用同网段不同的IP地址（IPv4/IPv6）与业务交换机互联，并采用自己的Loopback地址（IPv4/IPv6）与业务交换机的Loopback地址（IPv4/IPv6）建立BGP路由邻居，公告自己服务用户的路由信息（下一跳根据用户路由的绑定关系修改为相应PFU的接口地址）。 \n服务器业务接口采用Bond-4方式互联，与业务交换机间运行LACP协议，工作在负载分担模式。 \n注：网元侧暂不部署BFD进行下一跳网关的可达性检测，后续网元支持单臂BFD方式后，网元发起到下一跳网关的单臂BFD检测，业务交换机回弹网元发送的单臂BFD检测报文。 \n业务交换机部署方式 \n业务交换机上根据业务需要，配置多个业务VRF（例如：S1、Gi等） \n两台业务交换机上部署MC-LAG与服务器互联，工作在负载分担模式，并以Trunk方式属于多个vlan，同时创建相应的三层Interface Vlan接口，归属于相应的业务VRF。 \n两台业务交换机配置AnyCast IP地址，作为核心网网元相应业务的下一跳网关 \n两台业务交换机分别配置Loopback地址，与核心网网元建立BGP邻居，接收核心网网元发布的用户路由信息，并将其公告给外部承载网路由器设备；同时，公告缺省路由给核心网网元设备 \n两台业务交换机分别配置到核心网网元Loopback地址的静态路由，下一跳为核心网网元的PFU接口板地址，并关联BFD Session。BFD session 的源地址为本业务交换机相应VRF的Loopback地址，目的地址为核心网网元相应VRF的PFU接口板地址，检测间隔时间设置为400ms*5。 \n两台业务交换机与外部承载网设备采用口字型连接，业务交换机与外部承载网路由器件、两台业务交换机间运行OSPF路由协议。两台业务交换机间的三层通道作为故障时的迂回通道，同时公告自己的Loopback地址。 \n业务交换机将通过BGP学习到的核心网用户路由重分布到OSPF路由中，公告给外部网络。', 'id': '1009017_22'}
{'label': 'text', 'content': '核心网配套场景\n专U组网方案\n组网方案\n管理网部署方案\n网元部署方式 \n服务器业务接口采用Bond方式互联，工作在负载主备模式，不与业务交换机交互LACP协议。 \n网元管理面采用Floating ip方式部署（v4/v6），下一跳网关为业务交换机 \n业务交换机部署方式 \n业务交换机上根据业务需要，配置多个业务VRF（例如：EMS、MANO等） \n因为核心网网元的管理面采用主备方式，且不进行LACP协议交互，因此，两台业务交换机上需要部署2个MC-LAG，工作在on模式，每个MC-LAG仅包含一个端口 \n这两个MC-LAG上配置相同的vlan 属性，并创建相应的三层Interface Vlan接口，归属于相应的管理VRF \n两台业务交换机配置AnyCast IP地址，作为核心网网元相应业务的下一跳网关 \n两台业务交换机间，业务交换机与管理平面外部路由器间在管理VRF内部署OSPF v2/OSPFv3协议，进行路由交互', 'id': '1009017_23'}
{'label': 'text', 'content': '核心网配套场景\n专U组网方案\n组网方案\n硬件管理网部署方案\n服务器的IPMI口，业务交换机的管理网口通过GE端口连接到硬件管理交换机 \n服务器的IPMI口，业务交换机的管理网口配置在同一网段，下一跳网关为硬件管理交换机的三层接口地址 \n硬件管理交换机上配置三层接口地址*********，作为服务器IPMI口，业务交换机的管理网口的网关 \n硬件管理交换机上的两个万兆端口捆绑为Smartgroup接口，工作在负载分担模式，并修改分担方式为SIP+DIP，与业务交换机的业务口互联，并配置三层接口******* \n硬件管理交换机上配置缺省路由，下一跳为业务交换机的接口地址******* \n业务交换机与硬件管理交换机互联的端口配置为MC-LAG接口，工作在负载分担模式，并配置三层Interface vlan接口，ip地址为*******，归属于管理VRF \n两台业务交换机的管理VRF内配置静态路由，目的地址为硬件管理网段（本例中为*******），下一跳为硬件管理交换机的接口地址（本例中为*******） \n两台业务交换机间、业务交换机与管理面外部路由器间在管理VRF内运行OSPFv2/OSPFv3路由协议，重分布静态/直连路由，完成路由交互。 \n两台业务交换机间、业务交换机与管理面外部路由器间互联端口捆绑为smartgroup，并将分担方式修改为SIP+DIP \n注：当特别小型的U面设备中，可能没有部署硬件管理交换机，在这种情况下，服务器的IPMI接口直接连接到业务交换机上，业务交换机采用与管理面相同的方式对于服务器的IPMI接口，即采用MC-LAG的方式，但仅包含一个物理端口的方式。业务交换机的硬件管理通道采用带内方式，即直接通过与外部路由器的互联端口，不在采用带外管理口。', 'id': '1009017_24'}
{'label': 'text', 'content': 'IT资源池场景\nSDN组网方案\n适用范围\n中国移动IT资源池', 'id': '1009017_25'}
{'label': 'text', 'content': 'IT资源池场景\nSDN组网方案\n组网方案\n采用EVPN VTEP GROUP + MC-LAG 的方案，分布式网关方案，存在LEAF作为vtep和DVS作为vtep两种形式 \n接入侧 \n采用MC-LAG作为AC侧设备的接入，通过peerink实现AC侧设备ARP/MAC表项同步 \n由于MC-LAG自身机制，需要两台设备之间配置peerlink和 keepalive链路 \n隧道侧 \n采用VTEP GROUP，与远端建立两个邻居，但形成一条虚隧道（使用相同虚拟VTEP IP与MAC），实现在overlay层面依旧表达同一个VTEP节点信息', 'id': '1009017_26'}
{'label': 'text', 'content': 'IT资源池场景\nSDN组网方案\n组网方案\n业务网部署方案\nunderlay： \nDCGW、SPINE、LEAF之间运行IGP协议（IT云采用ISIS），实现三层路由可达。SPINE与DCGW、LEAF间链路捆绑为Smartgroup \noverlay： \nSPINE \n作为BGP RR，与各VTEP GROUP建立BGP-EVPN邻居 \n由于下一跳的IP是相同的虚 IP，从underlay层面负载分担 \nDCGW \n配置为Vtep group，公告相同的虚拟IP，与远端的另一个Vtep group节点建立一条vxlan隧道。 \n两个GW分别与RR建立两个邻居，将发布的路由下一跳改为组地址 \n对于远端，收到RR反射的相同下一跳路由，形成同一表项 \n对于同组GW，会从RR收到对端信息，由于信息相同，只接收不下表 \nGW使能IRB功能，支持二三层Vxlan转发 \nLEAF（overlay） \n配置mc-lag，与服务器bond4捆绑对接 \n配置为Vtep group，与GW类似，与远端形成单虚隧道 \n两台LEAF之间配置peerlink用于表相同步和keepalive用于保活检测 \nLEAF（underlay） \n配置mc-lag，与服务器bond4捆绑对接 \n通过peerlink口同步表项，keepalive口保活检测', 'id': '1009017_27'}
{'label': 'text', 'content': 'IT资源池场景\nSDN组网方案\n组网方案\n管理网部署方案\n同*******', 'id': '1009017_28'}
{'label': 'text', 'content': 'IT资源池场景\nSDN组网方案\n组网方案\n存储网部署方案\n同*******', 'id': '1009017_29'}
{'label': 'text', 'content': 'IT资源池场景\nSDN组网方案\n组网方案\n硬件管理网部署方案\n同*******', 'id': '1009017_30'}
{'label': 'text', 'content': 'IT资源池场景\n非SDN组网方案\n适用范围\n中国电信云公司资源池', 'id': '1009017_31'}
{'label': 'text', 'content': 'IT资源池场景\n非SDN组网方案\n组网方案\n采用EVPN VTEP GROUP + MC-LAG 的硬件overlay方案，与SDN方案的区别在于中国电信云公司的网络设备为异厂家，因此未部署SDN控制器，且中国电信云公司存在集中式网关和分布式网关两种部署方式 \n接入侧 \n采用MC-LAG作为AC侧设备的接入，通过peerink实现AC侧设备ARP/MAC表项同步 \n由于MC-LAG自身机制，需要两台设备之间配置peerlink和 keepalive链路 \n隧道侧 \n采用VTEP GROUP，与远端建立两个邻居，但形成一条虚隧道（使用相同虚拟VTEP IP与MAC），实现在overlay层面依旧表达同一个VTEP节点信息', 'id': '1009017_32'}
{'label': 'text', 'content': 'IT资源池场景\n非SDN组网方案\n组网方案\n业务网部署方案\nunderlay： \nDCGW、SPINE、LEAF之间运行IGP协议（中国电信云公司采用OSPF），实现三层路由可达。SPINE与DCGW、LEAF间链路捆绑为Smartgroup \noverlay： \nSPINE \n作为BGP RR，与各VTEP GROUP建立BGP-EVPN邻居 \n由于下一跳的IP是相同的虚 IP，从underlay层面负载分担 \nDCGW \n配置为Vtep group，公告相同的虚拟IP，与远端的另一个Vtep group节点建立一条vxlan隧道。 \n两个GW分别与RR建立两个邻居，将发布的路由下一跳改为组地址 \n对于远端，收到RR反射的相同下一跳路由，形成同一表项 \n对于同组GW，会从RR收到对端信息，由于信息相同，只接收不下表 \nGW使能IRB功能，支持二三层Vxlan转发 \nLEAF \n配置mc-lag，与服务器bond4捆绑对接 \n配置为Vtep group，与GW类似，与远端形成单虚隧道 \n两台LEAF之间配置peerlink用于表相同步和keepalive用于保活检测 \n集中式网关部署方案时需开启ARP代答功能', 'id': '1009017_33'}
{'label': 'text', 'content': 'IT资源池场景\n非SDN组网方案\n组网方案\n管理网部署方案\n同*******', 'id': '1009017_34'}
{'label': 'text', 'content': 'IT资源池场景\n非SDN组网方案\n组网方案\n存储网部署方案\n同*******', 'id': '1009017_35'}
{'label': 'text', 'content': 'IT资源池场景\n非SDN组网方案\n组网方案\n硬件管理网部署方案\n同*******', 'id': '1009017_36'}
{'label': 'text', 'content': 'CDN配套场景\n组网方案\n适用范围\nCDN配套', 'id': '1009017_37'}
{'label': 'text', 'content': 'CDN配套场景\n组网方案\n组网方案\n中心CDN节点网络方案（有直播专网）\nSW1-2启用MSDP协议，实现Anycast RP功能，分别作为头端码流、DLBN的RP。 SW1-2之间运行PIM-SM协议，同时SW1-2运行VRRP协议做各设备网关，并通过OSPF等路由协议实现SW之间、与SR之间的单播互通。 \n直播专网服务器DLBN从头端拉码流，进行转码后会将直播码流单播发送到下一级直播专网服务器。如果支持全网组播则直接组播推送到终端。 \nCDN node负责将直播专网的直播码流引入到本地，不再对外发送频道组播码流，用户的VOD、频道回看等以单播码流发送出去。 \nOTT Encoder将头端码流引入到本地，进行重新编码加密以单播的形式发给Mobile CDN Node和PC CDN Node使用。', 'id': '1009017_38'}
{'label': 'text', 'content': 'CDN配套场景\n组网方案\n组网方案\n中心CDN节点网络方案（无直播专网）\nSW1-2分别启用MSDP协议，实现Anycast RP功能，作为头端码流的RP。 SW1-2之间运行PIM-SM协议，同时SW1-2运行VRRP协议做各设备网关，并通过OSPF等路由协议实现SW之间、与SR之间的单播互通。 \n频道组播码CDN node负责将头端码流引入到本地，对外以组播码流形式发送直播流，以单播码流发送用户的VOD、频道回看等码流。 \nOTT Encoder将头端码流引入到本地，进行重新编码加密以单播的形式发给Mobile CDN Node和PC CDN Node使用。', 'id': '1009017_39'}
{'label': 'text', 'content': 'CDN配套场景\n组网方案\n组网方案\n区域/边缘CDN节点网络方案（有直播专网）\n区域节点部署DLBN，向中心节点发送节目数据请求，中心节点的节目源以单播形式向区域节点推送； \n区域节点DLBN负责将从中心节点请求的节目数据，从单播转换成组播，并负责向区域节点、边缘节点的CDN推送直播节目； \n区域节点的交换机负责对DLBN、CDN、FCC设备进行汇聚及网关作用，同时负责VRRP、组播及路由协议部署； \n边缘节点分双机、单机组网，主要差别在于汇聚交换机的数量。边缘双机的组网中，汇聚交换机可实现CDN的保护与切换。边缘单机的组网中仅配置一台交换机作为CDN的IP承载，无对应保护通道。', 'id': '1009017_40'}
{'label': 'text', 'content': 'CDN配套场景\n组网方案\n组网方案\n区域/边缘CDN节点网络方案（无直播专网）\n区域节点部署DLBN，向中心节点发送节目数据请求，中心节点的节目源以单播形式向区域节点推送； \n区域节点DLBN负责将从中心节点请求的节目数据，从单播转换成组播，并负责向区域节点、边缘节点的CDN推送直播节目； \n区域节点的交换机负责对DLBN、CDN、FCC设备进行汇聚及网关作用，同时负责VRRP、组播及路由协议部署； \n边缘节点分双机、单机组网，主要差别在于汇聚交换机的数量。边缘双机的组网中，汇聚交换机可实现CDN的保护与切换。边缘单机的组网中仅配置一台交换机作为CDN的IP承载，无对应保护通道。', 'id': '1009017_41'}
