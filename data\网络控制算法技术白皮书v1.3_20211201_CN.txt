{'label': 'document', 'content': '网络控制算法技术白皮书v1.3_20211201_CN', 'id': '1052454'}
{'label': 'text', 'content': '| 版本 | 日期 | 作者 | 审核者 | 备注 |\n|:-|:-|:-|:-|:-|\n| V1.0 | 2021/12/01 | ZTE | ZTE | 新建 |\n| | | | | |\n| | | | | |\n| | | | | |\n关键词：SDN,网络控制算法\n摘要：SDN（Software\u2002Defined\u2002Network）控制器具有网络全局资源信息，包括全网拓扑、链路带宽、流量等信息，通过引入路由基础算法、启发式算法、AI算法等各类算法组合，提供了一种解决全网控制优化问题的有效手段\n1 网络控制算法技术发展与挑战\t6\n1.1 SDN网络背景\t6\n1.2 网络控制总体架构\t6\n1.3 网络控制算法\t7\n2 网络控制算法应用场景\t8\n2.1 大规模业务开通与恢复\t8\n2.1.1 批量业务开通\t8\n2.1.2 批量业务重路由\t11\n2.1.3 批量业务重优化\t12\n2.2 网络多维度预测\t12\n2.2.1 网络流量预测\t12\n2.2.2 网络风险预测\t14\n2.2.3 网络资源预测与分片\t16\n2.2.4 网络业务预测与调度\t18\n2.3 网络流量控制与调优\t20\n2.3.1 拥塞控制与预防\t20\n2.3.2 全网流量均衡\t23\n2.4 栈深自动优化\t25\n2.4.1 SR-iLEA\t25\n3 网络控制关键算法\t27\n3.1 路由计算算法\t27\n3.1.1 双向单路径算法\t27\n3.1.2 双向多路径算法\t28\n3.2 优化算法\t28\n3.2.1 粒子群算法( Particle Swarm Optimization)\t29\n3.2.2 模拟退火算法，SA( Simulated Annealing)\t30\n3.2.3 单种群遗传算法\t30\n3.2.4 多种群遗传算法\t32\n3.3 预测算法\t34\n3.3.1 基于LSTM的预测算法\t34\n3.3.2 基于CNN的预测算法\t35\n3.3.3 基于时空网络的预测算法\t36\n3.4 SR-iLEA算法\t38\n3.4.1 Bellman\t38\n3.4.2 严格标签压缩（SLEA）\t40\n3.4.3 松散标签压缩（LLEA）\t42\n3.4.4 算法应用与技术项对应关系\t43\n4 现网部署应用案例\t44\n4.1 SPN大批量重路由案例\t44\n4.2 SPN 流量预测案例\t47\n4.3 SR-Policy流量调优案例\t48\n4.4 IPRAN栈深优化案例\t50\n5 控制算法技术展望\t54\n5.1 传统控制算法面临的挑战\t54\n5.2 智能网络控制算法技术的新发展\t55\n5.2.1 智能网络控制算法新课题\t55\n5.2.2 智能网络控制算法新方向\t56\n6 术语及缩略语\t57\n参考文献\t58\n', 'id': '1052454_1'}
{'label': 'text', 'content': '网络控制算法技术发展与挑战\nSDN网络背景\n在SDN出现之前，网络是分布式的，传统的方法没有全局视角，存在以下问题： \n1）网络规划、部署复杂 \n2）路径分布式计算，无全局视角 \n3）带宽利用率低，很难达到最优 \n4）难以实时响应网络流量变化 \n5）运维复杂，对运维人员要求高 \n6）网络流量可视化差 \n随着SDN技术的发展和成熟，SDN控制器具有网络全局资源信息，包括全网拓扑、链路带宽、流量等信息，通过引入路由基础算法、启发式算法、AI算法等各类算法组合，从而为解决全网控制优化问题提供了一种行之有效的手段。', 'id': '1052454_2'}
{'label': 'text', 'content': '网络控制算法技术发展与挑战\n网络控制总体架构\n网络控制主要包含以下几个模块，如图 1所示： \nUME Service:准备资产数据 \nBigDNA大数据分析服务 \nTopology 拓扑服务：提供路由计算数据 \nBGP-LS/OF/QX 南向通道：数据上报通道 \nOPCE Service最优路径计算服务：最优路径计算 \nE2E Service 端到端服务：业务管理 \nChannel Service 通道服务：隧道下发', 'id': '1052454_3'}
{'label': 'text', 'content': '网络控制算法技术发展与挑战\n网络控制算法\n网络控制算法应用目前主要分为三类，如图 2所示： \n基础路由算法，主要场景是单路径路由计算和多路径路由计算。 \n元启发式算法，主要应用场景是路径优化，算法是遗传、粒子群、模拟退火，在单种群基础上还有多种群算法。 \nAI预测算法，主要应用场景是网络预测，使用模型是LSTM、CNN、时空网络。', 'id': '1052454_4'}
{'label': 'text', 'content': '网络控制算法应用场景\n大规模业务开通与恢复\n批量业务开通\n目前业务开通主要是人工单条创建为主，但是也有很多场景下需要进行批量业务开通，例如网络规划，往往需要进行批量业务开通。当进行批量业务开通时，因为控制器有全局视角，因此可以对批量业务进行合理的规划布局，比如整体带宽占用最小、整体时延最小、整体metric最小、整体带宽利用率均衡等。 \n关键技术主要有带宽占用最小、时延最小、Cost最小、带宽利用率均衡四项，下面分别阐述。 \n带宽占用最小 \n带宽占用，或者称作带宽跳数乘积，是衡量网络状态的一个重要指标，带宽占用越小，意味着网络中各个业务涉及到的链路之间的相互影响就越小。针对批量开通场景，我们设计了一种带宽占用最小的算法，值得注意的是，该问题属于NP-hard范畴，我们的算法只能保证较优解，针对小规模场景可以得到最优解。该算法结合了贪婪算法和启发式算法的特征，能够保证在较短的收敛时间内得到较优解。而且该算法同时满足单业务开通与多业务开通。该算法步骤如下： \n(1)对新来的批量业务根据优先级和带宽值进行排序，以保证批量业务请求中优先保证高优先级大带宽的业务。 \n(2)结合Open-CSPF框架，设置各个业务算路过程中的权值阅读器为跳数阅读器，以保证对每个业务进行算路时是按照跳数最小的原则进行。 \n(3)对排序后的业务逐个算路并部署进网络，得到一个较优的解，在该解的基础上，引入启发式算法，以网络中带宽与跳数乘积之和作为解的性能好坏的衡量值，试图寻找更优的解，直至算法收敛，返回最终解。 \n该算法通过贪婪算法保证了算法的快速收敛，同时通过启发式算法，保证了算法具有较优的性能。 \n时延最小 \n即带宽时延乘积最小，具有重要的意义。简单来讲，时延越短，用户所感受到的网速就越快，体验就越好。网络中带宽时延乘积之和越小就意味着网络在单位时间内可传输的流量就越多。针对批量开通场景，我们设计了一种带宽时延最小的方法。与带宽占用最小算法相似，该算法也结合了贪婪算法和启发式算法的特征，该算法步骤如下： \n(1)对新来的批量业务根据优先级和带宽值进行排序，以保证批量业务请求中优先保证高优先级大带宽的业务。 \n(2)结合Open-CSPF框架，设置各个业务算路过程中的权值阅读器为时延阅读器，以保证对每个业务进行算路时是按照时延最小的原则进行。 \n(3)对排序后的业务逐个算路并部署进网络，得到一个较优的解，在该解的基础上，引入启发式算法，以网络中带宽与时延乘积之和作为解的性能好坏的衡量值，试图寻找更优的解，直至算法收敛，返回最终解。 \n该算法通过贪婪算法保证了算法的快速收敛，同时通过启发式算法，保证了算法具有较优的性能。 \nCost 最小 \n带宽代价乘积最小，此处的代价可认为是传输代为流量的开销。代价越小，意味着设备的开销越小。针对批量开通场景，我们设计了一种cost最小的算法，与带宽占用最小算法相似，该算法也结合了贪婪算法和启发式算法的特征，该算法步骤如下： \n(1)对新来的批量业务根据优先级和带宽值进行排序，以保证批量业务请求中优先保证高优先级大带宽的业务。 \n(2)结合Open-CSPF框架，设置各个业务算路过程中的权值阅读器为cost阅读器，以保证对每个业务进行算路时是按照时延最小的原则进行。 \n(3)对排序后的业务逐个算路并部署进网络，得到一个较优的解，在该解的基础上，引入启发式算法，以网络中带宽与cost乘积之和作为解的性能好坏的衡量值，试图寻找更优的解，直至算法收敛，返回最终解。 \n该算法通过贪婪算法保证了算法的快速收敛，同时通过启发式算法，保证了算法具有较优的性能。 \n带宽利用率均衡 \n针对单路径计算和批量路径计算，我们设计了两种不同的带宽负载均衡算法（BwLB）。 \n针对单条路径的负载均衡问题，BwLB算法基于Open-CSPF框架，通过扩展路径计算请求的权值计量器，将网络中各链路的带宽负载情况作为链路权值，基于Dijkstra算法计算出链路权值最短路径，从而保证所得路径上带宽资源相对充足。同时，基于Dijkstra算法，能够保证相对较短的路径跳数，避免了带宽资源的浪费。 \n针对批量路径负载均衡问题，BwLB算法结合了贪婪算法和遗传算法的特征，能够保证在较短的收敛时间内求得较优解。该算法步奏如下： \n(1)对批量业务根据优先级和带宽值进行排序，以保证批量业务计算过程中优先保证高优先级大带宽的业务。 \n(2)基于单路径BwLB算法，为每个请求预分配一条可选路径，并作为遗传算法的初始种群。 \n(3)使用遗传算法，对初始种群进行交叉、变异和选择操作，不断生成新的染色体，并保留较优的染色体继续进行交叉和变异。以此类推，直至算法结束，从最终的种群中挑选出最优的染色体作为批量路径的可行解。 \n该方案通过贪婪算法保证了算法的快速收敛，同时通过遗传算法，保证了算法性能。', 'id': '1052454_5'}
{'label': 'text', 'content': '网络控制算法应用场景\n大规模业务开通与恢复\n批量业务重路由\n设备和链路的状态可以通过不同通道及时上报给控制器，交由控制器处理，统一拓扑解析后，对于Link的增删，将会触发隧道的回切、或断链自动恢复。其中当发生并发断链时，出现大批量需要恢复的路径，需要OPCE模块能短时内达成批量算路的请求。 \n在重路由算路时，对于原请求的SLA需要尽力满足。 \n关键技术主要有基于BFS、使用带宽管理、使用自定缓存三项，下面分别阐述。 \n基于BFS \n假设批量请求数1000，设定单批并发个数为N \n(1)对初始1000的请求集合，进行循环批量计算，每批N个； \n(2)说明：其中每批计算的过程中不严格校验带宽，每批计算完毕，再扣除带宽。 \n(3)\xa0每批完毕及时统计，算不出路径的，加入新的忽略带宽的集合A；算出路径的，执行批量扣除带宽，扣除失败的，加入一个新的集合B，扣除成功的加入待返回集合； \n(4)说明：带宽扣除应该按路径为单位串行执行，先整条校验充足性，再扣除其经由链路带宽 (扣除链路时可以考虑并行)。以路径为单位时不可以并行，否则会发生大家都检验充足了，扣除到某一跳时却失败了，这时是需要还原的，会复杂化逻辑。 \n(5)\xa0对步骤2中新的请求集合B，重新循环批量计算，每批N个，跳2扣除带宽。直到不再创建新集合B，跳4。 \n(6)\xa0对步骤2中新的请求集合A，去除带宽约束，直接循环批量计算，结果存入返回结果集。 \n基于DFS和BFS，使用带宽管理 \n(1)\xa0基于DFS算路，计算下一跳权值时不考虑带宽充足性，对于拟定的候选下一跳集合，按序择优去带宽管理内扣除带宽，直至成功，若无，则回退至上一跳； \n(2)\xa0算路失败的加入新集合C，成功算路的加入返回集合； \n基于DFS和BFS，使用自定缓存 \n(1)基于DFS算路，计算下一跳权值时不考虑带宽充足性，对于拟定的候选下一跳集合，按序择优去自定缓存内扣除带宽，直至成功，若无，则回退至上一跳； \n说明：自定缓存的初始查询来自带宽管理。 \n算路失败的直接加入新集合C；成功算路的串行去带宽管理进行带宽扣除，扣除成功的加入返回集合，扣除失败的也加入集合C；', 'id': '1052454_6'}
{'label': 'text', 'content': '网络控制算法应用场景\n大规模业务开通与恢复\n批量业务重优化\n在当前控制器场景下，除了Node/Link的增删可以通告给控制器，对于SLA的裂化也一样可以通过BGP-LS上报给控制器，比如SD裂化，时延抖动、Metric裂化等。控制器根据受影响的Node/Link关联到相关业务，对受影响的业务进行批量重优化，降低裂化带来的业务质量受损。 \n关键技术同上一节批量重路由。', 'id': '1052454_7'}
{'label': 'text', 'content': '网络控制算法应用场景\n网络多维度预测\n网络流量预测\n网络扩容背景 \n随着互联网的飞速发展和人们日益增长的网络业务使用，为保证网络服务的稳定运行，对于网络端口、链路经常需要进行扩容。网络扩容指因业务需要，在原有的拓扑网络中增加端口或对部分链路增加带宽等增加网络容量的工程。扩容看似简单，但实际上是一个软硬件结合的复杂操作过程。除了硬件上需要进行断纤、增加站点、连纤的操作外，软件上还需要在网管上进行相关的数据配置。从承载网的经验来看，需要三个月左右的时间才能完成一次局部扩容。综合而言当前传统扩容有以下不足： \n1）传统的被动式扩容，需要网络发生拥塞后才扩容，而整个扩容周期可能长达三个月，期间造成重大损失； \n2）当今网络流量的不均衡分布日益严重，常会出现局部链路拥塞，但是其余链路轻载情况，迫切需要找到准确的瓶颈点； \n3）热点区域流量增长迅速，传统的被动式扩容，会出现反复扩容，造成资源浪费。 \n基于上述扩容背景下，流量预测的需求相应产生。根据端口、链路、环等对象的历史流量数据，利用机器学习方法学习到各对象的流量变化规律，以预测未来中长期(3个月以上）的流量数据。通过预测的未来流量，判断流量拥塞情况，进而针对性的进行扩容。 \n网络负载均衡背景 \n由于不同场景下用户行为的不确定性，导致网络中可能同时存在超忙区域与超闲区域。超忙区域导致拥塞率上升、业务质量下降；超闲区域使得资源利用率过低，资源浪费严重。所谓负载均衡，是指通过一定的协调，降低超忙区域的利用率、提高超闲区域的利用率，使得各区域负荷分担更加均衡，在区域资源利用率和业务质量之间寻找到一个合适的平衡点。 \n现有的负载均衡算法基于当前网络的情况进行负载均衡，这会导致负载均衡算法没有前瞻性。由于流量本身具有周期性，没有前瞻性的负载均衡只会在当前很短的时间内起作用，无法长期有效。 \n基于流量预测的负载均衡算法可以解决该问题，预测算法可以预测未来中长期的的流量数据，以此指导负载均衡，达到中长期负载均衡的目的。 \n关键技术：流量预测的整体流程，如图 3所示，图中仅标出与流量预测相关的简化模块或组件。 \n数据采集、存储：其流程包括图中①②③，主要用于对于流量的获取，具体技术包括：1）经南向接口时刻（间隔小于1秒）采集设备当前的网络流量信息；2）对于时间采集的数据，数据整理模块对于数据进行整理汇聚，例如汇聚为每小时的总流量，或者每天的总流量；3）对于整理汇聚后的数据存储到存储介质上。 \n模型训练：其流程包括图中④，主要用于训练AI模型，具体包括：1）该部分定时器触发，例如间隔1周，触发一次；2）触发后，将数据存储模块里面所有的历史数据进行获取，并依照算法进行训练，得到模型文件，存储到硬盘中；3）对所有历史数据训练进行预测，预测结果存到硬盘中。 \n推理：流程包括图中⑤中，主要用于推理未来流量走势，具体包括：1）该部分流程由展示界面控制，即人为触发某一批对象（端口或链路）的流量预测任务；2）对于触发的对象，从数据存储模块获得历史流量信息，从训练模型获得已经存储的模型文件，通过两者对于未来流量进行预测3）将预测结果反馈到页面展示。 \n负载均衡：流程包括图中⑥中，通过未来流量走势，平衡当前路由，使得整体负载均衡，具体包括：1）触发方式有三种，一是人为触发全局负载均衡，二是通过流量预测算法预测到将要拥堵的链路，三是需要计算路由时；2）OPCE模块根据当前训练后的预测结果计算链路预测拥堵权重；3）OPCE模块结合链路预测拥堵权重，和网络拓扑结构进行算路；4）控制器通过南向接口将路径结果下发至设备，完成路径的切换。', 'id': '1052454_8'}
{'label': 'text', 'content': '网络控制算法应用场景\n网络多维度预测\n网络风险预测\n实际网路中，由于接触不良、温度、湿度、运行时间等多个原因会导致部分网络设备在某个时间段频繁出现“断开-连接-断开-连接”的现象，这种现象称之为设备震荡。当出现网络“断开”时，为保证网络的畅通，需要将已部署的业务从“断开”的设备上迁移到其它正常设备上，称为重路由；当设备从“断开”变为“连接”时，为保证原先业务的最优路径，同时保持网络流量分配的最优性和稳定性，需要将之前迁移走的业务还原到当前设备，称为回切。当某个网络设备频繁出现“断开-连接-断开-连接”时，伴随着大量的重路由与回切操作，进而造成业务频繁变动，增大了转发面数据丢包风险；同时，在大规模网络下，频繁的重路由和回切操作对控制面的计算能力、处理能力都带来极大的挑战。 \n由于多种因素都可能导致网络设备震荡，不同的因素相互叠加，使得设备的震荡具有偶发性，即在某一个时段，某个设备会发生震荡，持续一段时间后，该设备可能恢复正常。对于管控来说，典型的应对方案是：对于正在发生震荡的设备，减少该设备相关的无效重路由与无效回切；对于从震荡恢复到正常的设备，恢复正常的重路由和回切。因为部分因素具有一定的周期性、可重复性，例如温度白天高、晚上低，所以之前震荡过的设备较大的概率会在后续时间内再发生震荡，这种未来可能再次发生震荡的设备就存在一定的风险，称为网络风险。这种典型的方案缺少对风险设备的管理，将风险链路与正常链路同等对待，没有考虑到风险链路对于业务的影响。同时，现有技术对于震荡设备缺乏自适应的手段，往往采用延时回切或累计多次再重路由的方式抑制震荡，缺乏自适应性。 \n为了解决管控产品在因为网络风险而导致的问题，提出一种基于历史统计分析的多层次风险链路识别和自适应抑制技术。该算法包括风险识别和风险抑制。各个模块的基本功能以及相互之间的关系如下： \n风险识别模块：该模块对于网络设备的上传信息进行及时的反应，实时输出该设备的风险信息。风险程度粗分为三个等级，低风险、中风险、高风险，同时也可以用风险率表示，范围为0-1。对于识别为高风险的设备，无论设备处于何种状态，所有路由均绕开该高风险设备，即不再计算重路由或回切，直至其不为高风险。对于其它风险链路，可以继续计算重路由或回切，但是需要考虑风险率的影响。 \n风险抑制模块：主要用于重路由或回切。基于风险抑制策略的路径计算需要优先考虑低风险率的链路，确保路径优先经过无风险链路、低风险链路，或者相对风险率低的链路。 \n模块之间关系：上述两个模块之间的关系如图 4所示，它们共同属于网络控制器管控产品中的模块，执行风险链路识别与自适应抑制的过程如下： \n第一步：通过接受的链路震荡消息，触发风险链路识别，风险识别模块通过长期、中期、短期三个阶段进行特征提取，通过提取的特征进行风险识别，得到风险率； \n第二步：风险抑制模块在路径计算结束后，对于经过的每一个设备进行风险判断，获得路径的累计风险，根据累计风险开始重路由或回切；', 'id': '1052454_9'}
{'label': 'text', 'content': '网络控制算法应用场景\n网络多维度预测\n网络资源预测与分片\n随着网络技术的快速发展，如SDN、NFV（Network Functions Virtualization，网络功能虚拟化），如何有效利用有限的网络带宽资源、在保证网络质量的同时提高业务路由效率，成了各设备厂商重点研究的问题。 \nSDN场景下，控制器可以收集全局拓扑信息，当有批量的业务需要进行部署时，例如场景一：用户新业务的批量开通；场景二：网络发生动荡时，对受影响的业务进行重新路由。针对上述场景，需要决策如何部署这些业务，且保证业务的QoS，各运营商对此都提出了较高的要求。 \n通过在SDN控制器中对网络资源管理模块进行功能扩充，即进行网络分片，以此来提供一种有效的方法，可以实现灵活、快速地进行资源部署。通过将网络带宽进行横向分片，网络分片带宽资源彼此间隔离，并设计深度强化学习算法对批量业务进行归属分片预测，同时动态调整分片间的带宽资源，实现并行地路由计算，在提高网络带宽资源利用率以及保证业务的Qos的同时，提升了业务路由的成功率与耗时性能。 \n网络分片是将一个物理网络分割成多个虚拟的端到端网络，本方案中主要关注基于网络带宽资源的横向分片，即各个网络分片具有相同的物理结构，但是带宽资源相互独立，这区别于传统的纵向切片。其关键技术是：当对批量业务请求进行路由时，借助分片结构，对批量业务先进行分片归属分配，即先确定将业务部署到哪个分片，分配时应该确保较高的传输成功率，因为分片带宽资源稀缺会导致某些业务无法传输。为了解决该问题，基于分片架构，本方案提出了两个协同方法，一是基于强化学习的业务分片分配方法，以确定各个业务应该分配到哪个片上进行部署；二是各个分片之间带宽资源的动态调整方法。上述两种方法相结合可以保证在不牺牲部署成功率以及Qos的情况下加速路由计算。 \n深度强化学习业务分片：强化学习(RL，reinforcement learning)是机器学习的一个分支，强调如何根据环境采取行动，以最大化累计期望收益。强化学习模型包括环境状态集合S、动作集合A，状态转移概率以及状态转移后的即时回报。当智能体做出决策后，它可以从环境中获得奖励，通过不断地与环境进行交互，从而学到一个达到特定目标的策略。训练一个智能体，其能根据每个分片的网络信息以及业务信息来决定将业务分配到哪个分片中。深度强化学习可以是DQN（Deep Q-learning）、PPO（Proximal Policy Optimization）、DDPG（Deep Deterministic Policy Gradient）、A3C（Asynchronous Advantage Actor-Critic）等。强化学习中智能体与环境的交互流程可参见下图 5： \n具体来说，将该网络分片问题建模为一个马尔科夫决策问题，模型中的状态、动作和奖赏函数分别为： \n状态：智能体观测到的信息包括两部分—即所有分片的信息以及待部署业务量的信息，即，其中，表示每个分片可用带宽的邻接矩阵，表示业务量的源点、汇点以及大小。 \n动作：每个业务去往的分片索引号，若有k个分片，则动作集合A={1,2,…,k} \n奖赏函数：一种简单的奖赏函数可以是：，其中表示业务根据模型预测结果部署到切片时是否能够部署成功，若能成功则值为1，若失败则为-1，以这种方式设计的奖励函数可以很好地反映智能体决策的正确性。 \n状态转移概率：转移概率是确定性的，其取决于业务的部署方式，包括路由方案以及带宽间的资源动态调整策略。 \n当分片的带宽资源能满足业务的需求时，说明智能体做出了正确的决策，智能体将被给予正回报。反之，错误的决策总是会产生负的回报。 \n切片间资源动态调整：动态资源调整的主要思想是使经常在分片中传递的链路获得更高的带宽，使得切片可以动态适应场景的变化。该模块中，将每个切片看做一个执行单元，该切片主要进行四项工作： \n接受带宽补充：其它切片会将多余的带宽资源转移到当前切片，当前切片对转移涉及到的链路进行带宽的扩充； \n路由计算：即将分配到该切片的业务按照其算路策略进行路由计算； \n响应其它切片带宽扩充申请：对于其它切片发送到的资源转移请求，判断是否满足转移条件，如果满足，则将本切片多余的带宽转移到其它切片； \n发送带宽申请请求：结束“路由计算”后，本切片内会对路由结束的这个业务进行带宽资源扣除，由于某些链路上带宽资源的减少，可以向其它切片发送带宽扩充申请。申请的扩充资源大小可以是该业务带宽值； \n值得注意的是，单个切片内，路由计算与其它三项工作是串行的，即只有等网络带宽资源状态固定了，才会进行路由计算。', 'id': '1052454_10'}
{'label': 'text', 'content': '网络控制算法应用场景\n网络多维度预测\n网络业务预测与调度\n近年来，随着网络接入方式的不断丰富以及网络使用成本的不断降低，网络传输的流量逐年升高。而用户对业务QoS的要求却在不断增高。传统的基于全局业务的网络资源优化算法，虽然能够提升网络资源利用率，但是无法满足网络中业务请求的动态性和实时性要求。网络智能调度技术采用基于深度学习模型的手段，使得网络中的动态业务同时满足时间要求和性能要求。 \n目前在利用机器学习解决路由问题上，主要的方法有两种，分别是强化学习和监督学习，对于强化学习，有许多论文曾利用强化学习来解决路由问题。如论文[17]中描述先计算出请求源节点到目的节点的k条路径，然后利用强化学习去分配流量在每条路径的比例，通过将reward设置为优化目标去探索解。在论文[17][18]中，使用DDPG方法，根据链路的状态，去改变链路的权值，设定优化目标，使得权值能够让利用根据权值利用算法去寻路所得的优化目标较高。 \n由于网络全局路由优化中变量众多，复杂度高，使用精确算法求得精确解不太现实，因此传统的网络路由方案一般会使用遗传算法等启发式算法，这些算法通过不断的迭代进行全局搜索寻找近似解，但存在以下问题：1）算法的参数众多，且算法结果的优劣严重依赖这些参数的选择；2）算法无法有效的利用历史的路由数据，因此得到较精确的优化结果需要较多的训练时间。 \n网络智能调度技术主要包含两个部分：离线训练过程和在线决策过程。 \n离线训练过程：将统计网络中的历史请求信息，然后将批量业务请求和网络信息作为输入，利用长时间的全局最优路径智能生成算法计算业务路由信息，基于此路由信息通过深度学习模型进行离线训练。 \n在线决策过程：当控制器收到新的业务请求时，将请求信息和当前网络状态信息作为输入传递给的深度学习模型，经过模型在线推理得到精简网络拓扑。最后根据精简后的拓扑，通过在线快速路径计算得到最优路径。 \n网络智能调度技术通过发挥深度学习模型的优势，使得高动态业务的不仅满足实时路由需求，同时控制面还能够保证用户业务的QoS。对于控制器总体来说，提升了网络资源利用率，降低网络阻塞率。', 'id': '1052454_11'}
{'label': 'text', 'content': '网络控制算法应用场景\n网络流量控制与调优\n拥塞控制与预防\n流量调优的主要对象是链路和隧道。发生的主要原因是链路流量发生拥塞、链路SD裂化、隧道时延裂化等。 \n表 1 MPLS流量调优调优对象 \n当判定链路/隧道带宽利用率超阈值、链路/隧道质量劣化超阈值、隧道流量突发等情况时，可触发流量调优,如下图 7所示。 \n策略1：拥塞业务调整数量最少 \n拥塞调整都是基于周期内统计的的实际流量进行，以降低流量的最大利用率为目标进行调整。 \n通过将拥塞的链路作为尽力排除的节点约束，基于Dijkstra算法进行相对较短的路径计算，可以有效的绕开拥塞路径，同时结合遗传算法，进行相对较优的组合优化。具体流程如下： \n根据拥塞门限，寻找拥塞的链路，及拥塞链路上的可以调整的业务； \n对拥塞的业务，将初始拥塞链路作为尽力排除节点，基于Dijkstra算法算出N条可以调整的路径； \n对算出的路径，进行若干次随机组合，并且保证规划带宽不超限，流量利用率不限，然后作为遗传算法的初始种群，之后种群总是选择扰动较小的达标解进行后代遗传和交叉变异，若干次迭代后，选择扰动最小的解，作为返回的部署方案 \n策略2：带宽利用率均衡 \n拥塞调整都是基于周期内统计的的实际流量进行，以降低流量的最大利用率为目标进行调整。 \n通过将拥塞的链路作为尽力排除的节点约束，基于Dijkstra算法进行相对较短的路径计算，可以有效的绕开拥塞路径，同时结合遗传算法，进行相对较优的组合优化。具体流程如下： \n根据拥塞门限，寻找拥塞的链路，及拥塞链路上的可以调整的业务； \n对拥塞的业务，将初始拥塞链路作为尽力排除节点，基于Dijkstra算法算出N条可以调整的路径； \n对算出的路径，进行若干次随机组合，并且保证规划带宽不超限，然后作为遗传算法的初始种群，之后种群总是选择最大利用率较小且扰动也较小的解进行后代遗传和交叉变异，若干次迭代后，选择最大利用率较小的解，作为返回的部署方案。 \n策略3：带宽利用率与业务调整数量均衡 \n拥塞调整都是基于周期内统计的的实际流量进行，以降低流量的最大利用率同时兼顾扰动量为目标进行调整。 \n通过将拥塞的链路作为尽力排除的节点约束，基于Dijkstra算法进行相对较短的路径计算，可以有效的绕开拥塞路径，同时结合遗传算法，进行相对较优的组合优化。具体流程如下： \n根据拥塞门限，寻找拥塞的链路，及拥塞链路上的可以调整的业务； \n对拥塞的业务，将初始拥塞链路作为尽力排除节点，基于Dijkstra算法算出N条可以调整的路径； \n对算出的路径，进行若干次随机组合，并且保证规划带宽不超限，然后作为遗传算法的初始种群，之后种群总是选择最大利用率较小的解进行后代遗传和交叉变异，若干次迭代后，选择最大利用率较小且扰动也较小的解，作为返回的部署方案。 \n策略4：资源抢占,抢占扰动数量最少 \n隧道开通与隧道优化的过程中，都有可能出现业务部署问题。在隧道开通时，在存量网络下，对新的请求算路时，由于网络的剩余带宽不足，会出现算路失败的现象。在拥塞场景优化中，优化后网络中的个别链路还是会存在拥塞现象，即优化效果不明显。针对这两种常见情况，我们设计了一种基于请求优先级进行资源抢占的功能。允许高优先级的请求去抢占低优先级请求的路径，尽量满足高优先级请求的部署。 \n在进行资源抢占时，希望抢占涉及到的请求数量尽可能少，即有抢占扰动数量最少的目标。该算法的步骤如下: \n根据优先级门限，过滤得到需要进行抢占的高优先级请求，按照优先级从高到低排序。逐个对请求进行抢占部署。 \n针对单个高优先级请求，找到其路径中带宽不足或者拥塞的链路集合，建立集合中链路与其涉及到的低优先级请求的映射关系。 \n用启发式算法对选择要拆除的低优先级请求，该过程中将拆除请求数量最小作为优化目标。直至算法收敛，返回要拆除的低优先级请求集合。 \n策略5：资源抢占,抢占低优先级 \n在进行资源抢占时，希望抢占涉及到的请求对应的优先级越低越好，用需要拆除的请求的优先级之和去衡量这个目标，该算法的步骤如下: \n根据优先级门限，过滤得到需要进行抢占的高优先级请求，按照优先级从高到低排序。逐个对请求进行抢占部署。 \n针对单个高优先级请求，找到其路径中带宽不足或者拥塞的链路集合，建立集合中链路与其涉及到的低优先级请求的映射关系。 \n用启发式算法对选择要拆除的低优先级请求，该过程中用需要拆除的请求的优先级之和最小作为优化目标。直至算法收敛，返回要拆除的低优先级请求集合。 \n策略6：资源抢占,抢占数量与优先级均衡 \n在进行资源抢占时，希望同时权衡抢占涉及到的请求数量和优先级之和，即将二者联合起来考虑，属于多目标优化问题。该算法的步骤如下: \n根据优先级门限，过滤得到需要进行抢占的高优先级请求，按照优先级从高到低排序。逐个对请求进行抢占部署。 \n针对单个高优先级请求，找到其路径中带宽不足或者拥塞的链路集合，建立集合中链路与其涉及到的低优先级请求的映射关系。 \n用启发式算法对选择要拆除的低优先级请求，该过程中将拆除请求数量和优先级之和赋予不用的权值系数进行加和，使其最小作为优化目标。直至算法收敛，返回要拆除的低优先级请求集合。', 'id': '1052454_12'}
{'label': 'text', 'content': '网络控制算法应用场景\n网络流量控制与调优\n全网流量均衡\n对网络SLA的防护需要层层升级。首先，当业务开通时，我们可以通过已有业务的部署情况预测未来流量走势，从而规划合适的路径。其次，当网络局部因为流量突发而拥塞时，可以通过拥塞调优解决。除了这两种事前规划，事中解决，还需要事后防范，即全网流量均衡。 \n简单来说，我们优化需要定期对全网路径进行巡检，对全网路径进行有步骤有策略的全局优化，使得全网业务达到某种均衡策略。全局均衡策略主要有以下4类关键技术：全局带宽占用最小、全局时延最小、全局Cost最小、全局负载均衡。 \n关键技术1：全局带宽占用最小 \n当网络中业务部署方式不得当时，会导致带宽占用值较大，针对该问题，我们设计了一种全网带宽占用优化的算法，使用该算法可以保证当前网络中所有请求部署成功的情况下降低带宽占用值，提升网络性能。该算法结合了贪婪算法与启发式算法的特征，步骤如下： \n查找过滤可以进行调整的业务：对当前网络中已经部署的业务，根据优先级门限和“保护”业务（需要进行路径保护，不能调整的业务）过滤得到可以调整的业务，并按照优先级和带宽大小进行排序。 \n结合Open-CSPF框架，设置各个业务算路过程中的权值阅读器为跳数阅读器，以保证对每个业务进行算路时是按照跳数最小的原则进行。 \n对步骤1中的业务逐个算路并部署进网络，得到一个较优的解，在该解的基础上，引入启发式算法，以网络中带宽与跳数乘积之和作为解的性能好坏的衡量值，试图寻找更优的解，直至算法收敛，返回最终解。 \n经过全部优化后的网络，较优化前性能会有提升。 \n关键技术2：全局时延最小 \n与带宽占用全局优化类似，当网络中业务部署方式不得当时，会导致带宽时延乘积较大，影响网络的传输性能，针对该问题，我们设计了一种全网带宽时延优化的算法，使用该算法可以保证当前网络中所有请求部署成功的情况下降低带宽与时延乘积之和，提升网络性能。该算法结合了贪婪算法与启发式算法的特征，步骤如下： \n查找过滤可以进行调整的业务：对当前网络中已经部署的业务，根据优先级门限和“保护”业务（需要进行路径保护，不能调整的业务）过滤得到可以调整的业务，并按照优先级和带宽大小进行排序。 \n结合Open-CSPF框架，设置各个业务算路过程中的权值阅读器为cost阅读器，以保证对每个业务进行算路时是按照cost最小的原则进行。 \n对步骤1中的业务逐个算路并部署进网络，得到一个较优的解，在该解的基础上，引入启发式算法，以网络中带宽与cost乘积之和作为解的性能好坏的衡量值，试图寻找更优的解，直至算法收敛，返回最终解。 \n经过全部优化后的网络，较优化前性能会有提升。 \n关键技术3：全局Cost 最小 \n与带宽占用全局优化类似，当网络中业务部署方式不得当时，会导致带cost乘积较大，影响网络的传输性能，针对该问题，我们设计了一种全网带宽cost优化的算法，使用该算法可以保证当前网络中所有请求部署成功的情况下降低带宽与时延，提升网络性能。该算法结合了贪婪算法与启发式算法的特征，步骤如下： \n查找过滤可以进行调整的业务：对当前网络中已经部署的业务，根据优先级门限和“保护”业务（需要进行路径保护，不能调整的业务）过滤得到可以调整的业务，并按照优先级和带宽大小进行排序。 \n结合Open-CSPF框架，设置各个业务算路过程中的权值阅读器为时延阅读器，以保证对每个业务进行算路时是按照时延最小的原则进行。 \n对步骤1中的业务逐个算路并部署进网络，得到一个较优的解，在该解的基础上，引入启发式算法，以网络中带宽与时延乘积之和作为解的性能好坏的衡量值，试图寻找更优的解，直至算法收敛，返回最终解。 \n经过全部优化后的网络，较优化前性能会有提升。 \n关键技术4：全局负载均衡 \n针对全网离线优化带宽负载均衡问题，依旧采用贪婪+遗传的策略。流程如下所示： \n保留优化前请求及路径信息，并存储至初始种群中。 \n拆除待优化的请求，然后根据优先级和带宽值对待优化请求进行排序，以保证优先计算高优先级大带宽的业务路径。 \n基于单路径BwLB算法，为每个请求预分配一条可选路径，并作为遗传算法的初始种群。 \n使用遗传算法，对初始种群进行交叉、变异和选择操作，不断生成新的染色体，并保留较优的染色体继续进行交叉和变异。以此类推，直至算法结束，从最终的种群中挑选出最优的染色体作为批量路径的可行解。 \n如果最优染色体的带宽利用率不优于优化前结果则返回路径优化失败。有过优于优化前结果，则返回优化成功。', 'id': '1052454_13'}
{'label': 'text', 'content': '网络控制算法应用场景\n栈深自动优化\nSR-iLEA\nSR网络中，数据包的转发路径由源节点指定，一条Segment Routing Path (SRP)由一个有序的Segment列表组成。Segment由Segment标识（Segment Identifier，SID）表示。SID最常见的格式是标签，故SRP可由标签栈表征。TE场景中, 基于多约束的SRP计算，得到的路径是可行路径，但不保证是最短路径，所以标签栈深大概率会随着路径的长度增加而增加。但是实际工程网络中，压入数据包报文头部标签数目会受到转发设备的物理条件限制，即MSD约束，这就会导致路径计算空间变小，一定程度上影响网络资源的有效利用。 \n针对上述问题，提出了一种完备的标签栈深优化算法SR-iLEA，该算法由三部分组成。分别是带有最大跳数约束的严格路径计算(Strict Path Calculate With Hop Constraint, SPCH)、严格标签压缩(Strict Label Encoding Algorithm, SLEA)、松散标签压缩(Loose Label Encoding Algorithm, LLEA)。SR-iLEA算法将Node SID和Adj SID相结合，并支持ECMP，与已有的标签压缩算法相比，该算法在处理多约束时，如必经节点、排除节点、必经链路、排除链路等方面具有更优的性能。 \n带有跳数约束的严格路径计算(SPCH) \n作为SR-iLEA算法的第一部分，SPCH基于Bellman-ford，实现最大跳数MSD约束下的最优路径计算。此时用来表征SRP的标签栈中只包含SRP中链路的Adj SID，无Node SID。 \nBellman-ford算法通过对链路不断的松弛操作，来更新两点之间的最短距离。把对图中所有的链路遍历松弛一遍记为一次全网松弛，那么通过次全网松弛，可以得到两点间的全网最短路径。在本算法中，对Bellman-ford进行了改进，进行次全网松弛就停止算法，可以得到跳数小于等于下的最优路径。逐跳表示SRP时，改进过的算法极好地吻合栈深压缩要求。 \n严格标签压缩（SLEA） \nSPCH部分中提到的严格路径算法由于只使用Adj SID来进行SRP表征，标签栈深会随着路径长度的增加而线性增加，当MSD约束较苛刻时，严格路径算法有较高的算路失败率。在该章节提出的SLEA算法可以有效的改善上述问题，针对一条给定的SRP可以使用SLEA最大程度的对路径标签栈深进行压缩，具体实现思路为：利用SR域中Segment指令特点，使用Node SID来代替批量的Adj SID集合，且仅当两节点之间只有一条最短路径时才会进行代替操作，即不使用ECMP，此时可以保证标签栈表征的转发路径也是唯一的，此时类似RSVP-TE思路，在TE工程中，针对唯一路径进行资源预留更为简单，且对网络流量处理更加精确细致。 \n松散标签压缩（LLEA） \n与SLEA最大的不同之处就是考虑了ECMP。值得注意的是，使用SLEA时，可以保证路由下发路径唯一，而使用LLEA则会利用ECMP，路由下发路径不唯一，此时在TE场景中，进行网络流量资源扣除时，认为ECMP中每条路径分担的业务流量是相等的，即基于数据报文的负载分担形式。', 'id': '1052454_14'}
{'label': 'text', 'content': '网络控制关键算法\n路由计算算法\n双向单路径算法\n双向Dijkstra算法旨在进一步加快传统Dijkstra算法的路径计算速度，双向Dijkstra算法的应用场景与传统Dijkstra算法的应用场景基本相同，凡是Dijkstra算法支持的场景，双D绝大多数可以兼容。 \n传统Dijkstra算法是从一个顶点计算到其余各顶点的最短路径算法，解决的是有向图中最短路径问题。其基本原理为：每次新扩展一个距离最短的点，更新与其相邻的点的距离。当所有边权都为正时，由于不会存在一个距离更短的没扩展过的点，所以这个点的距离永远不会再被改变，因而保证了算法的正确性。 \n双向Dijkstra算法改变传统Dijkstra算法从单一方向搜索的方式，让起点与终点同时展开搜索，其双向搜索过程中需要注意两个方面，一是正反向算路的切换条件，另一个是整体算法的结束条件，通过合理设计这两个条件，寻找到最优的交点，然后根据交点回溯出正向和反向两条路径，将二者拼接即可。 \n在网格状拓扑上，根据其扫过的面积计算，双向Dijkstra扫过的面积约为，传统Dijkstra扫过的面积约为，因此其效率理论上可以实现2倍的提升；实际拓扑，效率提升更高。', 'id': '1052454_15'}
{'label': 'text', 'content': '网络控制关键算法\n路由计算算法\n双向多路径算法\nK条最短路径算法（ k-shortest pathes，KSP）对于小规模网络或业务数量较少的情况下的路径计算，具有较高的计算效率，但是对于大规模网络或者批量业务的场景下的路径计算与恢复，传统的Dijkstra算法或KSP算法的计算效率相对较低，尤其是在计算K条多路径时。 \n在计算多路径的场景，KSP算法需要逐跳偏移，多次调用Dijkstra算法来计算，因此其算路效率大打折扣。双向多路径算法（Bi-Multiple-Paths，BMP）是在双向Dijkstra进行扩展。在双向Dijkstra的计算过程中，会产生过个交点，每个交点对应一条路径，因此在双向Dijkstra找到最优路径后，算法不停止，继续搜索可以获得多条不完全相交的路径。', 'id': '1052454_16'}
{'label': 'text', 'content': '网络控制关键算法\n优化算法\n我们要解决的网络流量优化问题，属于NP完全问题，是无法在多项式时间内取得最优解。因此，退而求其次，尽量的时间允许的范围内去追求较优解。 \n“自然界的蚁群、鸟群、鱼群、羊群、牛群、蜂群等，其实时时刻刻都在给予我们以某种启示，只不过我们常常忽略了大自然对我们的最大恩赐！......” \n当前我们主要使用的算法是多种启发式算法的组合使用，主要包含粒子群算法、遗传算法、模拟退火算法。除此之外，路径优化问题主要是线性规划问题，所以也包含了数学规划算法。', 'id': '1052454_17'}
{'label': 'text', 'content': '网络控制关键算法\n优化算法\n粒子群算法( Particle Swarm Optimization)\n设想这样一个场景：一群鸟在随机搜索食物。已知：1）在这块区域里只有一块食物; 2）所有的鸟都不知道食物在哪里; 3）但它们能感受到当前的位置离食物还有多远. \n那么:找到食物的最优策略是什么呢？搜寻目前离食物最近的鸟的周围区域，根据自己飞行的经验判断食物的所在。粒子群算法正是从这种模型中得到了启发：信息的社会共享。 \n1）每个寻优的问题解都被想像成一只鸟，称为“粒子”。所有粒子都在一个D维空间进行搜索；2）所有的粒子都由一个适应度函数值以判断目前的位置好坏；3）每一个粒子必须赋予记忆功能，能记住所搜寻到的最佳位置。即个体经历过的最好位置；4）每一个粒子还有一个速度以决定飞行的距离和方向。这个速度根据它本身的飞行经验以及同伴的飞行经验进行动态调整；5）在所有粒子的最佳位置中挑选出最优的作为当前全局最优解 \n粒子群算法算法流程如下：', 'id': '1052454_18'}
{'label': 'text', 'content': '网络控制关键算法\n优化算法\n模拟退火算法，SA( Simulated Annealing)\n热力学上，退火现象指物体逐渐降温的物理现象，温度愈低，物体的能力状态会低；够低后，液体开始冷凝与结晶，在结晶状态时，系统的能量状态最低。大自然在缓慢降温（退火）时，可找到最低能量状态：结晶。但是，如果过程过急过快，快速降温（淬炼）时，会导致不是最低能态的非晶形态。 \n慢工出细活，缓缓降温，使得物体分子在每一个温度时，能够有足够时间找到安顿位置，则逐渐的，到最后可得到最低能态，系统最安稳。', 'id': '1052454_19'}
{'label': 'text', 'content': '网络控制关键算法\n优化算法\n单种群遗传算法\n遗传算法是基于达尔文的自然选择和遗传学机理的生物进化理论而提出的一种优化计算模型，通过模拟自然进化过程达到求解问题最优解的目的。结合自然界生物进化论，给出遗传算法的重要概念。 \n基因（Gene）：它是遗传算法的最小组成单元，对实际问题进行编码时，它可以是一个二进制位，实数或字符。 \n染色体（Chromosome）：它是遗传物质的主要载体，表示待求解的一个解，一条染色体对应一个个体，由多个基因组成，是遗传算法的基本操作单位。 \n种群（Population）：它是由一定数量的染色体组成，构成待求解问题的解空间，是遗传算法的搜索空间。 \n适应度（Fitness）：它是根据生物进化理论中“优胜劣汰”的进化原理而提出的一个量化的概念，表示个体所对应的解的优劣，常用适应度函数表示。 \n选择（Choose）：计算每个个体的适应度值，采用某种方法从上一代种群中选择一定数量的优良解遗传到下一代。 \n交叉（Crossover）：两个染色体的部分基因进行交换以产生新一代的个体，该过程体现了信息交叉的思想，是遗传算法中最主要的遗传操作。 \n变异（mutation）：根据变异因子的大小改变个体中的基因值以产生新一代的个体。 \n遗传算法的算法流程如下：', 'id': '1052454_20'}
{'label': 'text', 'content': '网络控制关键算法\n优化算法\n多种群遗传算法\n突破单个群体进行遗传的框架，引入多个种群进行优化搜索，不同的种群分配以不同的控制参数，实现全局与局部的协同优化。种群之间进行较优解的交互。结合多核的计算机架构实现并行搜索。能够更好的应对复杂的优化场景；多种群引入的重要概念。 \n迁移操作（Migrate）：模仿自然界中动物的迁移后交配从而产生物种的多样性。算法中是将最佳个体按确定的拓扑结构在各种群中传递 \n迁移间隔（Migration interval）：单个种群需要迭代多少次之后进行种群之间的迁移。 \n迁移率（Mobility）：选择迁移最佳个体的数量，占整个种群的比例', 'id': '1052454_21'}
{'label': 'text', 'content': '网络控制关键算法\n预测算法\n基于LSTM的预测算法\n长短期记忆（Long Short-Term Memory, 简称 LSTM）的模型如图 14所示，它可以用来连接先前的信息到当前的任务上。 \nLSTM的观念建在于细胞状态，即0上方贯穿的横线。细胞状态类似于传送带。直接在整个链上运行，只有一些少量的线性交互。信息在上面流传保持不变会很容易。LSTM 有通过精心设计的称作为“门”的结构来去除或者增加信息到细胞状态的能力。门是一种让信息选择式通过的方法。他们包含一个 sigmoid 神经网络层和一个按位的乘法操作。LSTM 拥有三个门，来保护和控制细胞状态。 \n基于LSTM的变体，比较出名的是 Gated Recurrent Unit (GRU)，这是由 Cho 等人在 (2014) 提出。它将忘记门和输入门合成了一个单一的 更新门。同样还混合了细胞状态和隐藏状态，和其他一些改动，最终的模型比标准的 LSTM 模型要简单。 \n基于LSTM类方法衍生出大量的预测类算法，例如论文[20]~[22]都基于LSTM或其变体的预测类算法', 'id': '1052454_22'}
{'label': 'text', 'content': '网络控制关键算法\n预测算法\n基于CNN的预测算法\n基于CNN的预测类算法，主要是源自论文[22][23]提出的卷积方法。该论文提出因果卷积的方式，通过使用因果卷积，可以确保模型不会违反对数据进行建模的顺序：模型在时间步长t发出的预测t之后的值，而不依赖于任何未来时间步长，如图 16所示。 \n由于具有因果卷积的模型没有递归连接，因此与RNN相比，它们的训练速度通常更快，尤其是应用于非常长的序列时。因果关系问题之一是需要许多层或大型滤波器才能增加感受野。 \n借鉴空洞卷积，因果卷积可以通过同样方式增加感受野，即通过以一定步长跳过输入值，达到滤波区域大于其长度的效果。', 'id': '1052454_23'}
{'label': 'text', 'content': '网络控制关键算法\n预测算法\n基于时空网络的预测算法\n时空网络的预测算法，综合时间关联和空间关联，得到的预测方式，该预测方式常借助于GNN的方式。如所示的结构，在每个网络节点既考虑时间维度预测，同时又获得空间的相关性。 \n论文[23][25]均涉及到时空卷积的方式，可以在改动较小的情况下迁移到IP网络中。但是现有技术无法解决较大拓扑图的问题。', 'id': '1052454_24'}
{'label': 'text', 'content': '网络控制关键算法\nSR-iLEA算法\nBellman\n作为SR-iLEA算法的第一部分，SPCH基于Bellman-ford，实现最大跳数MSD约束下的最优路径计算。此时用来表征SRP的标签栈中只包含SRP中链路的Adj SID，无Node SID。 \nBellman-ford算法通过对链路不断的松弛操作，来更新两点之间的最短距离。把对图中所有的链路遍历松弛一遍记为一次全网松弛，那么通过次全网松弛，可以得到两点间的全网最短路径。在本算法中，对Bellman-ford进行了改进，进行次全网松弛就停止算法，可以得到跳数小于等于下的最优路径。逐跳表示SRP时，改进过的算法极好地吻合栈深压缩要求。 \n以上0展示该部分算法流程，业务请求从A到E。令，为了方便阐述，不失一般性，直接设置链路的动态权值为图上数值。图2-a展示了图的初始化状态，设置其它节点到源节点的距离为，源节点本身为0。图2-b展示了经过第一次全网松弛后的状态。因，被更新为10。公式表示的第一次松弛结果是由节点A得来的。图2-c展示了经过第二次松弛后的结果，可以得到当前阶段A到E的最优路径为A->D->E，且总的权值/距离。类似的图2-d、图2-e分别展示了经过第三次和第四次松弛后的结果。至此可以得到情况下A到E的最小权值。可以通过进行路径回溯来确定最优路径，表示第四次全网松弛后E的父节点是C，同理表示第三次全网松弛后为B，依次类推，直到到达源节点A，可以得到最优路径为A->D->B->C->E。可以构造标签栈{A->D, D->B, B->C, C->E}。值得注意的是不考虑MSD约束时，A到E的最优路径是A->D->B->C->F->E，路径权值为13。综上，不难理解，使用SPCH可以得到跳数小于等于条件下的最优权值路径。算法流程可总结如下表 2所示： \n表 2 带有跳数约束严格路径计算流程 \n| Algorithm: Finding minimum weight path with hop constraint |\n|:----------|\n| INPUT: Demand with MSD constraintOUTPUT：, the label stack of the :The value of MSD: The source node of demand:The destination node of demand:The Adj SID of |\n| STEP1: Calculating for each node do end for for source nodefor to do for each link with do if then end if end forend forfor to 1 do if exists then add to end ifend forreturn |\n| STEP2: The construction of the label stackfor to do end for |', 'id': '1052454_25'}
{'label': 'text', 'content': '网络控制关键算法\nSR-iLEA算法\n严格标签压缩（SLEA）\nSPCH部分中提到的严格路径算法由于只使用Adj SID来进行SRP表征，标签栈深会随着路径长度的增加而线性增加，当MSD约束较苛刻时，严格路径算法有较高的算路失败率。在该章节提出的SLEA算法可以有效的改善上述问题，针对一条给定的SRP可以使用SLEA最大程度的对路径标签栈深进行压缩，具体实现思路为：利用SR域中Segment指令特点，使用Node SID来代替批量的Adj SID集合，且仅当两节点之间只有一条最短路径时才会进行代替操作，即不使用ECMP，此时可以保证标签栈表征的转发路径也是唯一的，此时类似RSVP-TE思路，在TE工程中，针对唯一路径进行资源预留更为简单，且对网络流量处理更加精确细致，而下文中的LLEA则会使用ECMP来进一步提高算路成功率。 \nSLEA算法可进一步拆分成两部分，一是将完整的SRP采用最长子路径匹配原则拆分成多个子路径，存入队列中；二是根据每个子路径中链路元素的个数来决定使用Node SID或者Adj SID，进而构造标签栈。 \n借助0展示SLEA算法的流程。图中链路权值与设备采用IGP协议缓存最短路径时的链路权值一致。假设有一个请求从A到E，采用多约束路径计算方法计算得到一条严格路径SRP，A->B->C->F->E，针对该路径使用SLEA进行压缩。 \n首先从节点A开始，采用最长匹配原则得到从A节点开始沿着SRP上A节点之后剩余节点的最长路径，经过匹配可得到子路径A->B，解释如下，采用最长匹配原则时，从节点E开始，已存在的IGP最短路径中节点A到节点E的最短路径为A->B->C->F->E和A->D->B->C->F->E,虽然可以匹配到SRP，但存在ECMP，这种情况下不进行代替。同理匹配E的上一个节点F时，已存在的IGP最短路径中节点A到节点F的最短路径是A->B->C->F和A->D->B->C->F，同样存在ECMP，与此类似，匹配节点C和节点B时也存在ECMP。最终匹配到节点B时，将满足条件的子路径A->B加入到队列中。接下来以子路径A->B的尾节点B为起点，再次从SRP节点的尾节点E开始匹配，不难发现，子路径B->C->F->E满足条件，将该子路径加入队列中。至此SLEA的第一部分结束。进行构造标签栈时，首先对子路径A->B进行判断，只有一条链路，中加入链路A->B 对应的Adj SID。接下来对子路径B->C->F->E进行判断，链路条数为4，中加入节点E的Node SID。结束第二部分，得到最终的为{A->B, E}，根据SR技术可知，当有流量从节点A到节点E时，只需要在数据报文头部压入{A->B,E}即可引导流量沿着路径A->B->C->F->E。具体的算法流程如下表 3所示。 \n表 3 SLEA算法流程 \n| Algorithm: Strict Label Encoding Algorithm |\n|:----------|\n| INPUT：The expressed as a list if linksOUTPUT：, the minimum label stack: Holds the list if sub-paths: The final label stack for : The best match path for : The Node SID of : The Adj SID of |\n| STEP 1: Computation of the sub-paths: Points to the current node of the : The size of while do if exist then else end ifend while |\n| STEP2: The construction of the label stackfor to do if then else end ifend for |', 'id': '1052454_26'}
{'label': 'text', 'content': '网络控制关键算法\nSR-iLEA算法\n松散标签压缩（LLEA）\n与SLEA最大的不同之处就是考虑了ECMP。同样以0为例，使用LLEA得到的标签栈可以为{C, E}，即当有流量需要从A到E进行传输时，在数据报文头部压入{C, E}即可引导流量先沿着路径A->B->C、A->D->B->C从A到C，然后再沿路径C->F->E从C到达E。算法的流程和SLEA类似。值得注意的是，使用SLEA时，可以保证路由下发路径唯一，而使用LLEA则会利用ECMP，路由下发路径不唯一，此时在TE场景中，进行网络流量资源扣除时，认为ECMP中每条路径分担的业务流量是相等的，即基于数据报文的负载分担形式。 \nSR-iLEA中包含的三种压缩算法彼此之间互不影响，可以根据真实场景确定三类方法的执行顺序，比如可以先执行SPCH，若成功则直接返回，若失败，接着执行SLEA，若失败，则继续执行LLEA，对应的流程图如上图3-4。也可以有其它方式，如并行执行SPCH和SLEA，因为二者都可以表示唯一一条路径，可以比较二者的路径权值，选取路径权值较优的结果，若二者都失败，则再使用LLEA进行计算。三类方法可以灵活组合，正因如此，SR-iLEA可以很好地解决实际工程问题。', 'id': '1052454_27'}
{'label': 'table', 'content': '网络控制关键算法\nSR-iLEA算法\n算法应用与技术项对应关系\n每一项技术的最终实施都依赖于多种算法的组合，才能达到工程需要的能力和可靠性。本文介绍的技术项与算法对应关系如下表 4所示。其中标明“主要”表示对应技术项主要使用的算法类别，非“主要”为组合使用到的算法。\n表 4 算法与技术项对应表\n| 技术项算法 | 路由计算 | 优化 | 预测 | SR-iLEA |\n|:-|:-|:-|:-|:-|\n| 大规模业务开通与恢复 | √（主要） | √ | √ | |\n| 网络多维度预测 | √ | √ | √（主要） | |\n| 网络流量控制与调优 | √ | √（主要） | | |\n| 栈深自动优化 | √ | | | √（主要） |\n', 'id': '1052454_28'}
{'label': 'text', 'content': '现网部署应用案例\nSPN大批量重路由案例\n问题描述\n5G自动控制面已具备拓扑变更自动重路由和重优化的能力，以中移SPN为例。组网如图 23所示,SPE1-1->SPE1-3承载2K LSP：\n', 'id': '1052454_29'}
{'label': 'table', 'content': '现网部署应用案例\nSPN大批量重路由案例\n| 链路 | LSP路径 | LSP数量 |\n|:-|:-|-:|\n| SPE1-1->SPE1-3 | UPE1-1->UPE1-3->UPE2-1->UPE2-3->SPE1-1->SPE1-3 | 1000 |\n| SPE1-1->SPE1-3 | UPE1-2->UPE1-4->UPE2-2->UPE2-4->SPE1-2->SPE1-1->SPE1-3 | 1000 |\n', 'id': '1052454_30'}
{'label': 'text', 'content': '现网部署应用案例\nSPN大批量重路由案例\n当前汇聚环链路SPE1-1->SPE1-3发生故障，影响其上承载2000条带恢复的LSP，若该LSP为无保护业务，将发生业务中断，如下图 23所示.\n实施方案\nBGP-LS可以检测到接口down，相关服务会自动关联到相关链路，通知OPCE Service进行自动重路由。如下图 24所示：\n此时OPCE Service会立即进行批量并发重路由。步骤如下：\n1.加载该链路影响到的LSP集合\n2.根据LSP集合源宿节点，启用剪枝算法对当前计算Topology进行剪枝，加速重路由计算性能，剪枝后拓扑如下图 25所示.\n3、使用并发资源扣除算法进行2K LSP并发重路由计算\n4、对局部计算失败的LSP进行补算，完成重路由计算。\n5、下发重路由路径。\n效果\n', 'id': '1052454_31'}
{'label': 'table', 'content': '现网部署应用案例\nSPN大批量重路由案例\n重路由前后路径对比见表 5、图 26所示。\n表 5 重路由前后路径对比\n| 状态 | LSP路径 | LSP数量 |\n|:-|:-|-:|\n| 重路由前 | UPE1-1->UPE1-3->UPE2-1->UPE2-3->SPE1-1->SPE1-3 | 1000 |\n| 重路由前 | UPE1-2->UPE1-4->UPE2-2->UPE2-4->SPE1-2->SPE1-1->SPE1-3 | 1000 |\n| 重路由后 | UPE1-1->UPE1-3->UPE2-1->UPE2-3->SPE1-1->SPE1-2->SPE1-4->SPE1->3 | 1000 |\n| 重路由后 | UPE1-2->UPE1-4->UPE2-2->UPE2-4->SPE1-2->SPE1-4->SPE1-3 | 1000 |\n', 'id': '1052454_32'}
{'label': 'text', 'content': '现网部署应用案例\nSPN 流量预测案例\n问题描述 \n根据网络某端口的历史流量情况，预测端口的未来3个月每一天的流量。图 28展示了该端口的2020年6月30号之前的历史流量状态，请预测2020年6月30~2020年9月30的流量. \n实施方案 \n通过以下步骤进行： \n1 加载训练完毕的AI模型 \n2 加载该端口2020年6月30之前的历史数据 \n3 通过模型和历史数据，预测2020年6月30~2020年9月30中每一天数的流量 \n效果 \n耗时：秒级 \n预测准确率：大于90%，将误差20%以内的算为预测准确', 'id': '1052454_33'}
{'label': 'text', 'content': '现网部署应用案例\nSR-Policy流量调优案例\n问题描述\n假设给定网络拓扑模型:\n', 'id': '1052454_34'}
{'label': 'table', 'content': '现网部署应用案例\nSR-Policy流量调优案例\n基于上述拓扑进行SR-Policy业务创建，1 SR-Policy->1 Capth ->1SL。网络初始信息如表 6:\n表 6 业务请求属性表\n| 路径1 | S1->A->B->C->E1 |\n|:-|:-|\n| 路径2 | S1->A->C->E1 |\n| 路径3 | S2->A->B->C->E2 |\n| 路径4 | S2->A->C->E2 |\n| 路径5 | S3->A->B->C->E3 |\n| 路径6 | S3->A->C->E3 |\n', 'id': '1052454_35'}
{'label': 'table', 'content': '现网部署应用案例\nSR-Policy流量调优案例\nSR-Policy请求对应表 7:\n表 7 业务请求属性表\n| 属性业务 | 起始点 | 终点 | 大小 |\n|:-|:-|:-|:-|\n| 业务1 | S1 | E1 | 40M |\n| 业务2 | S2 | E2 | 30M |\n| 业务3 | S3 | E3 | 20M |\n', 'id': '1052454_36'}
{'label': 'text', 'content': '现网部署应用案例\nSR-Policy流量调优案例\n假设条件:\n网络中已有3个SR-Policy业务部署，对应的业务属性和部署方式分别如上所示，此时有一个新的SR-Policy业务4请求需要部署进网络中，大小为110M且起始点是S1,终点是E1。\n观察现网络状态不难发现:无论业务是经过路径1或路径2都会导致链路拥塞现象\n实施方案\n针对上述链路拥塞问题，需要对业务路径进行调优，并满足下面约束：\n1 需要调整前3个业务中的一个或者几个业务的传输路径\n2 保证业务4能够成功部署进网络\n3 同时使得整个网络处于均衡状态\n路径优化后效果：\n', 'id': '1052454_37'}
{'label': 'table', 'content': '现网部署应用案例\nSR-Policy流量调优案例\n表 8 业务请求属性表\n| 属性业务 | 路径1 | 路径2 | 路径3 | 路径4 | 路径5 | 路径6 | 最大链路利用率 |\n|:-|:-|:-|:-|:-|:-|:-|:-|\n| 业务1 | | √ | | | | | 80% |\n| 业务2 | | | | √ | | | 80% |\n| 业务3 | | | | | | √ | 80% |\n| 业务4 | √ | | | | | | 80% |\n', 'id': '1052454_38'}
{'label': 'text', 'content': '现网部署应用案例\nSR-Policy流量调优案例\n路径调整前后对比：\n只改变了业务1的传输路径\n保证业务4的部署\n各链路带宽利用率为60%、60%、80%是网络所能达到的最优平衡状态。\n效果\n大规模模拟测试：\n拓扑规模：1700节点,9万链路，1K SR-Policy *2 Cpath* 2SL\n调优策略：①负载均衡②负载均衡&最小扰动\n收敛速度：秒级\n', 'id': '1052454_39'}
{'label': 'text', 'content': '现网部署应用案例\nIPRAN栈深优化案例\n问题描述\n在IPRAN场景下，如下图 31所示，控制器/设备计算节点A到节点G的LSP，得到的LSP为：A->B>C->E->F->G，Adj SID构成的栈深列表长度为5；若设备A能支持的最大栈深MSD=3，此时会下发失败。\nStrict LEA 方案\n使用Node SID来代替批量的Adj SID集合，且仅当两节点之间只有一条最短路径时才会进行代替操作，即不使用ECMP，此时可以保证标签栈表征的转发路径也是唯一的，此时类似RSVP-TE思路，在TE工程中，针对唯一路径进行资源预留更为简单，且对网络流量处理更加精确细致。\n此时使用Strict LEA压缩算法，可以得到压缩后的标签栈列表为：{E_NODE_SID,G_NODE_SID}，其中E_NODE_SID表示引导流量从A沿着到E的最短路径A->B->C->E转发,G_NODE_SID表示引导流量从E沿着到G的最短路径E->F->G转发。此时使用两个标签即可引导流量沿着唯一的一条最短路径从A到G。\nLoose LEA 方案\n与Strict LEA最大的不同之处就是考虑了ECMP。值得注意的是，使用Strict LEA时，可以保证路由下发路径唯一，而使用Loose LEA则会利用ECMP，路由下发路径不唯一。\n在本案例中，由于不存在路由约束，如节点链路排除约束，可以直接使用节点G的节点标签即可完成对业务流量的路径引导，但值得注意的是此时从C到F的最短路径有两条，代表着流量在该段是进行均分流量的。\n效果\nSR作为一种新型的网络架构，路径计算时受转发设备所能支持的最大栈深限制，针对该问题，SR-iLEA算法可以有效改善该问题，SPCH是基于Bellman-ford的严格路径方法，可以快速计算满足约束的严格路径，SLEA在SPCH基础上进行栈深压缩，大大提高了算路成功率，LLEA则在SLEA基础上增加ECMP支持，进一步提高算路成功率。为了验证SR-iLEA算法的有效性，使用典型网格状拓扑和真实运营商网络与经典算路算法Dijkstra算法进行对比验证，实验结果表明SR-iLEA算法在约束条件下有更高的算路成功率，且具有更好的栈深压缩效果，可以很好地满足实际工程需求。此外，使用动态链路权值计算模型，SR-iLEA还可以解决网络拥塞，提升网络吞吐量等。在当下网络规模不断变大、业务请求日益复杂、流量飞速增加的背景下，SDN与SR在网络演变趋势中扮演着重要角色，SR-iLEA为SR技术的推广应用提供了强有力的支持。\n为了验证SR-iLEA算法的有效性，在典型网格状网络和国内某运营商真实网络下进行性能验证，为了对比SR-iLEA算法的高算路成功率，选择经典的最优路径计算方法Dijkstra算法作为对比算法，具体实验信息如下表 10所示：\n表 10 网络信息\n| 数据 | 来源 | 节点规模 | 链路规模 | 请求数目 | 条件约束 |\n|:-|:-|-:|-:|-:|:-|\n| 网格状拓扑 | 仿真构造，典型网格状网络 | 1200 | 4660 | 3000 | 带宽、排除节点 |\n| 运营商网络 | 国内某运营商真实网络 | 1161 | 2760 | 3000 | 带宽、排除节点 |\n分别从改变对算路成功率的影响、平均栈深两方面进行衡量算法的有效性。对比的方法为工程中应用最多的最优路径计算方法Dijkstra。在满足多约束条件的同时，标签栈深小于等于认为算路成功，测试过程中设置的多约束条件，有带宽约束、排除节点约束，此外还考虑了TE场景，利用了网络的动态权值计算方法。\n与运营商网络构造不同，网格状拓扑构造比较特殊，使用30*40节点拓扑，请求起点和终点位置随机分散选取。由图 33可见，当MSD(=10)比较小时，严格路径计算的方法，Dijkstra与SPCH失败率都很高。但是使用SLEA与LLEA就可以取得100%的算路成功率了。这一定程度上由网络拓扑形状决定的，网格状拓扑两点的最短路径大概率不会产生绕路，而是直达形式，使用Node SID来引导路径更具优势。这也侧面反映了，SR-iLEA可以根据不同的网络拓扑形态，来进行不同的组合以获得更优的计算效率。\n以运营商网络为例，图 34展示了当MSD=7时，各个算法得到的算路结果的平均栈深，这里只考虑了算路成功的请求，可以看到相比较于普通的算路，SR-iLEA很大程度上减小了标签栈深。\n', 'id': '1052454_40'}
{'label': 'text', 'content': '控制算法技术展望\n随着近来5G互联网的高速发展，网络中传输的数据量将呈现井喷式增长，网络规模日趋增大。大规模网络有迫切的自动化、智能化需求。同时，控制面逐步加强智能化能力, 可以帮助网络运营决策科学化、业务个性化、维护精准化和服务高效化。', 'id': '1052454_41'}
{'label': 'text', 'content': '控制算法技术展望\n传统控制算法面临的挑战\n面对网络规模、容量数倍或十数倍的增长、以及百万级业务的超大容量的网络，当前的网络控制将面临如下挑战： \n最优路径的实时性：创建业务最优路径的耗时将随着网络规模增大而增加。未来网络中的三大愿景万维互联、算网融合和精准网络，不仅使传输的数据量增长，而且对业务QoS的要求进一步增高。 \n链路震荡风险增加：因气候温度变化、链路流量拥堵、设备老化等原因导致网络中部分链路在特定时段呈现频繁震荡。在等概率的条件下，网络规模的增大会增加链路震荡次数。频繁的链路震荡会增加转发面数据丢包的风险，影响用户体验。 \n全网SLA的裂化增加：业务量的持续增加，当前路径SLA的贪心选择，必然带来SLA的持续裂化。例如，局部关键链路负载过高，从而带来时延、SD裂化、流量拥塞。传统的基于全局业务的网络资源优化算法，虽然能够提升网络资源利用率，但是无法满足网络中业务请求的性能和精度要求。 \n流量的突发性和差异性：随着未来网络规模的增大，网络流量的突发性和差异性将更加明显，很容易产生部分链路流量过度拥塞，同时存在部分链路空闲的情况。因为流量本身具有周期性和相关性，拥塞和空闲可能会在同一条链路的不同时段出现。流量的拥堵将会影响网络的时延，造成不好的用户体验。 \n栈深长度增加：超大规模的网络集中控制下的全局最优路径计算，为了满足各种各样的SLA诉求，必然带来栈深长度增加，尤其是回传环状网络，栈深长度超过20是常见现象。标签长度过长带来转发面栈深不足，出现丢包等现象，影响用户体验。', 'id': '1052454_42'}
{'label': 'text', 'content': '控制算法技术展望\n智能网络控制算法技术的新发展\n基于数据驱动的人工智能近来大放异彩，而未来网络中积累了大量数据，可以通过人工智能的手段将数据转化为知识，在复杂的大规模网络中做出更好的科学化路由决策。智能控制面将与不同类型的业务服务进行交互，并将业务意向与数据面相结合。面对当前控制器面临的问题，智能控制面将伴随意图网络、数字孪生结合AI辅助路由的手段，既拥有AI对未来网络变化趋势的预测性，又利用经典算法的稳定性和敏捷性，使得控制面的性能进一步提高。', 'id': '1052454_43'}
{'label': 'text', 'content': '控制算法技术展望\n智能网络控制算法技术的新发展\n智能网络控制算法新课题\n从传统控制算法到智能控制算法，当前还有很多待研究的技术项。智能网络控制算法新课题： \n意图网络，控制器将决定如何把意图转化为针对网络设备的配置手段，从而使网络以期望的方式行事，网络意图可以理解为一种操作网络的抽象高级策略。通过人工智能的手段，将用户的意图进行翻译，将翻译后的意图通过自动化控制引起下发到物理设备，物理设备的执行情况则可以通过网络感知和意图保障确保网络稳定。意图网络可以将用户意图快速实现，减少业务开通时间，对于运维人员技能要求降低。 \n数字孪生，数字孪生是深入研究物理器件的特性、网络的运行机制，通过人工智能的辅助，在信息空间高度仿真、精确模拟一个数字化网络，通过该网络可以实现网络设备的的在线管理、网络动态重构、实时动态反馈，提升控制器的故障检测率，以及验证控制器算法的有效性。数字孪生需要非常复杂的非线性函数进行拟合，因人工智能的强非线性拟合能力，故可采用该方式能来构造该函数。 \n故障根因分析，一个网络设备故障，经常会辐射到附近多个网络业务同时出现故障。控制器可通过多层次分析，找到出故障的设备，但是这对于开发人员的要求极高。而通过对于控制器的日志、告警等进行分析，利用人工智能的手段，找到一种适用性广、泛化性强的故障根因分析智能方法，是对于开发人员的减负，同时也保证故障定位的准确性。', 'id': '1052454_44'}
{'label': 'text', 'content': '控制算法技术展望\n智能网络控制算法技术的新发展\n智能网络控制算法新方向\n智能网络控制算法，存在新的演进方向。未来智能网络控制算法的新方向有以下内容： \n算力网络，为更迅捷高效的响应业务的计算需求，算力资源逐渐被下沉至靠近用户的边缘，并形成异构多样、分布式的算力部署新态势。基于网络基础设施对算力资源进行感知、调度和编排，在网络层提供满足网络连接和计算服务。在算力网络体系之下，路由表的创建需要动态感知分布式的算力、存储等资源信息，这对智能网络控制面算法提出更高的要求，算力网络的算力将成为网络控制器算法的重点关注指标。 \n精准链接，未来网络作为基础设施需要满足具有不同承载质量要求的业务综合承载，不同业务的承载质量对带宽，丢包率，时延和抖动以及隔离性等都有差异化的要求，传统的单一转发技术无法满足日益增长的差异化承载质量的需要，因此，未来网络要求网络控制算法具有支持灵活插件式的转发技术，精准满足不同业务的承载质量要求。 \n确定性网络，随着对网络服务质量有严格要求的业务出现，例如实时多媒体、网络通信，确定性网络应运而生，它提供了极低的分组丢失率、保证的带宽和有界的端到端传输延迟来传递数据流。利用更低的计算成本，提供更加稳定的网络服务，对于网络控制器算法是一个新的方向。', 'id': '1052454_45'}
{'label': 'table', 'content': '术语及缩略语\n| 英文缩写 | 英文全称 | 中文全称 |\n|:-|:-|:-|\n| SDN | Soft defined network | 软件定义网络 |\n| AI | Artificial Intelligence | 人工智能 |\n| LSTM | Long Short-Term Memory | 长短期记忆网络 |\n| CNN | Convolutional Neural Networks | 卷积神经网络 |\n| BwLB | Bandwidth load balancing algorithm | 带宽负载均衡算法 |\n| OPCE | Optimal Path Computation Element | 最优路径计算单元 |\n| BFS | Breath First Search | 广度优先算法 |\n| DFS | Deepth First Search | 深度优先算法 |\n| NFV | Network Functions Virtualization | 网络功能虚拟化 |\n| QoS | Quality of Service | 服务质量 |\n| BGP | Border Gateway Protocol | 边界网关协议 |\n| RL | reinforcement learning | 强化学习 |\n| SRP | Segment Routing Path | 分段路由路径 |\n| SPCH | Strict Path Calculate With Hop Constraint | 带有最大跳数约束的严格路径计算 |\n| SLEA | Strict Label Encoding Algorithm | 严格标签压缩 |\n| LLEA | Loose Label Encoding Algorithm | 松散标签压缩 |\n| KSP | k-shortest pathes | k条最短路径算法 |\n| BMP | Bi-Multiple-Paths | 双向多路径算法 |\n| SLA | Service Level Agreement | 服务级别协议 |\n| LSP | Label Switched Path | 标签交换路径 |\n', 'id': '1052454_46'}
{'label': 'text', 'content': '参考文献\nC. Filsfils, N. K. Nainar, and Pignataro. The Segment Routing Architecture. Proc.IEEE Globecom, 2105. \nONF Market Education Committee. Software-Defined Networking:The New Norm for dianyiNetworks [M]. ONF White Paper, Palo Alto, US: Open Networking Foundation, 2012. \nOpenFlow Switch Specification [DB/OL]. http://www.openflow.org, 2011. \nS. Agarwal, M.kodialam, T.V. Lakshman. Traffic engineering in software defined networks[C]. Proceedings of IEEE INFOCOM, 2013, pp. 2211-2219. \nS. M. Kodialam, T.V. Lakshman. Minimum interference routing with applications to MPLS traffice engineering[C] . Proceedings of IEEE INFOCOM, 2000, pp. 884-893. \nB. Fortz, J. Rexford, M. Thorup. Traffic engineering with traditional IP routing protocols [J]. IEEE Communications, Mag.40(10) (2002) 118-124. \nD. Cai, A. Wielosz, and S. Wei. Evolve carrier ethernet architecture with sdn and segment routing[C]. High Performance Switching and Routing(HPSR), IEEE International Conference, 2014. \nA. Sgambelluri, et al. Experimental Demonstration of Segment Routing[J]. Journal of Lightware Technology, 2015, 49(1): 91-103. \nR. Hartert, S. Vissicchio, P. Schaus, O. Bonaventure, et al. A Declarative and Expressive Approach to Control Forwarding Paths in Carrier-Grade Networks[C]. Proc.ACM SIGCOMM,2015, pp 15-28. \nTrimponias G, Xiao Y, Xu H, et al. Centrality-based Middlepoint Selection for Traffic Engineering With Segment Routing [J]. IEEE/ACM Transactions on networking, 2017. \nF. Lazzeri, G. Bruno, J. Nijhof, A. Giorgetti, et al. Efficient Label Encoding in Segment-Routing Enabled Optical Networks[C]. Proceedings of International Conference of Optical Network Design and Modeling(ONDM),2015, pp. 34-38. \nGuedrez R, Dugeon O, Lahoud S, et al. A new Method For Encoding MPLS Segment Routing TE Paths[C]. IEEE Network of the Future , 2018, pp. 58-65. \nGiorgetti A, Castoldi P, Cugini F, et al. Path Encoding in Segment Routing[C]. IEEE Global Communications Conference, 2015. \nT.H. Cormen, C.E. Leiserson, R.L. Rivest. Introduction to Algorithm [M]. MIT Press,Cambridge, MA,1990. \n谢婷婷.基于IPRAN的传输网IP演进策略研究与规划. [D].成都.电子科技大学.2105. \n叶俊俊.基于IPRAN的本地传送网的规划及应用.[D].浙江.浙江大学.2014. \nZ. Xu, J. Tang, J. Meng, W. Zhang, Y. Wang, C. H. Liu, D. Yang, "Experience-driven Networking: A Deep Reinforcement Learning based Approach," in IEEE INFOCOM 2018, Honolulu, USA. \nP. Sun, J. Li, Z. Guo, et al, “SINET: Enabling Scalable Network Routing with Deep Reinforcement Learning on Partial Nodes” in IEEE SINGCOM 2019, Beijing, China. \nhttps://colah.github.io/posts/2015-08-Understanding-LSTMs/ \nWen, Ruofeng, et al. "A multi-horizon quantile recurrent forecaster."\xa0arXiv preprint arXiv:1711.11053\xa0(2017). \nFlunkert, Valentin , D. Salinas , and J. Gasthaus . "DeepAR: Probabilistic Forecasting with Autoregressive Recurrent Networks." International Journal of Forecasting 36.3(2020). \nModeling Long- and Short-Term Temporal Patterns with Deep Neural Networks[J]. ACM, 2018. \nOord A , Dieleman S , Zen H , et al. WaveNet: A Generative Model for Raw Audio[J]. 2016. \nWu, Zonghan, et al. "Graph WaveNet for Deep Spatial-Temporal Graph Modeling" abs/1906.00121 (2019): 1907-1913. \nZHENG, Chuanpan, et al. Gman: A graph multi-attention network for traffic prediction. In: Proceedings of the AAAI Conference on Artificial Intelligence. 2020. p. 1234-1241..', 'id': '1052454_47'}
