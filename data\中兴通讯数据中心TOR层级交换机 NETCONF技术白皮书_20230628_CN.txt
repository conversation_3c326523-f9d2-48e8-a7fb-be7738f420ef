{'label': 'document', 'content': '中兴通讯数据中心TOR层级交换机 NETCONF技术白皮书_20230628_CN', 'id': '1121294'}
{'label': 'text', 'content': '中兴通讯数据中心TOR层级交换机NETCONF技术白皮书\nV1.0\n（内部版）\nNETCONF技术白皮书\n| 版本 | 日期 | 作者 | 审核者 | 备注 |\n|:-|:-|:-|:-|:-|\n| V1.0 | 2023/6/28 | 禹乐 | 施玮 | 适用于59系列/59X系列/59M系列产品 |\n| | | | | |\n', 'id': '1121294_1'}
{'label': 'text', 'content': '中兴通讯数据中心TOR层级交换机NETCONF技术白皮书\n技术背景\n随着现代网络技术的发展，网络设备规模呈指数倍扩大，设备种类越来越丰富，设备数量越来越多，网络的多样性和复杂性也急剧增长，设备的管理维护也变得格外关键。因此需要一种安全、高效，具备统一管理能力的网络配置工具。目前最常用的手段有命令行方式和简单网络管理协议 SNMP（Simple Network Management Protocol）。命令行是一种传统的网络最基本的管理方法，网络设备厂商通常会提供一套基本命令管理设备，仅需控制台或 telnet 即可完成设备的配置管理。但这些命令的形式、用法、使用条件和输出格式都依赖于厂商，不利于自动化管理，也提高了 网络管理员的学习成本，因此该方式不具有统一管理能力。SNMP 是为解决互联网上设备管理问题而提出的网络管理协议，其最初的目标是要花最小的代价用最高的效率完成配置，传输层一般使用简单但不可靠的 UDP 协议，也缺乏安全机制，因此导致容易被各种手段攻击。 \n为了弥补命令行和 SNMP 的缺陷，提高设备管理的自动化程度，更好地满足网络管理人员的需求，NETCONF协议应运而生。NETCONF 协议是一种运用可扩展标记语言XML（Extensible Markup Language）封装数据的网络管理协议。它可建立在任何满足稳定性和安全性条件的传输层上，具有统一的远过程调用 RPC（Remote Procedure Call）操作方法， 将数据按模块和表进行划分，能同时操作多个模块/表。在NETCONF 协议中，所有数据都有固定的层级位置，有利于自动化管理；各层具有通俗易懂的名称，有利于人工维护。与命令行和SNMP 相比，NETCONF 协议能够让网络管理变得更加简单、直观和安全，因此在现网中大规模应用。', 'id': '1121294_2'}
{'label': 'text', 'content': '中兴通讯数据中心TOR层级交换机NETCONF技术白皮书\n2、NETCONF技术\n2.1、NETCONF技术简介\nNETCONF（Network Configuration Protocol）网络配置协议，提供了一套管理网络设备的机制，用户可以使用这套机制对网络设备的配置进行增加、修改、删除、查询操作，获取网络设备的配置和状态信息。通过NETCONF协议，网络设备可以提供规范的应用程序编程接口API（Application Programming Interface），应用程序可以直接使用这些API，向网络设备发送和获取配置。', 'id': '1121294_3'}
{'label': 'text', 'content': '中兴通讯数据中心TOR层级交换机NETCONF技术白皮书\n2、NETCONF技术\n2.2、NETCONF基本概念\nNETCONF基本网络架构如图1所示，整套系统必须包含至少一个NMS（Network Management System）作为整个网络的网管中心，NMS运行在NMS服务器上，对设备进行管理。下面介绍网络管理系统中的主要元素： \nNETCONF Manager：担任网络中的Client，利用NETCONF协议对网络设备进行系统管理。向NETCONF Agent发送<rpc>请求，查询或修改一个或多个具体的参数值。接收NETCONF Agent发送的告警和事件，以获知被管理设备的当前状态。 \nNETCONF Agent：担任网络中的Server，用于维护被管理设备的信息数据并响应NETCONF Manager的请求，把管理数据汇报给发送请求的NETCONF Manager。', 'id': '1121294_4'}
{'label': 'text', 'content': '中兴通讯数据中心TOR层级交换机NETCONF技术白皮书\n2、NETCONF技术\n2.2、NETCONF基本概念\n2.2.1、NETCONF建模语言\nYANG是专门为NETCONF协议设计的数据建模语言，用来为NETCONF协议设计可操作的配置数据、状态数据模型、远程调用（RPCs）模型和通知机制等。 \nYANG数据模型为一个面向机器的模型接口，明确定义数据结构及其约束，可以更灵活、更完整的进行数据描述。', 'id': '1121294_5'}
{'label': 'text', 'content': '中兴通讯数据中心TOR层级交换机NETCONF技术白皮书\n2、NETCONF技术\n2.2、NETCONF基本概念\n2.2.2、NETCONF相关概念\nNETCONF Client和Server之间使用RPC机制进行通信。Client必须和Server成功建立一个安全的、面向连接的会话才能进行通信。Client向Server发送一个RPC请求，Server处理完用户请求后，给Client发送一个回应消息。Client的RPC请求和Server的回应消息全部使用XML编码。\nNETCONF协议提供了定义capabilities语法语意规范，协议允许Client与Server交互各自支持的capabilities，Client只能发送Server支持的capabilities范围内的操作请求。\nXML编码作为NETCONF协议的编码格式，用文本文件表示复杂的层次化数据，即支持使用传统的文本编译工具，也支持使用XML专用的编辑工具读取、保存和操作配置数据。基于XML网络管理的主要思想是利用XML的强大数据表示能力，使用XML描述被管理数据和管理操作，使管理信息成为计算机可以理解的数据库，提高计算机对网络管理数据的处理能力，从而提高网络管理能力。\nXML编码格式文件头为<?xml version="1.0" encoding="UTF-8"?>，其中：\n', 'id': '1121294_6'}
{'label': 'table', 'content': '中兴通讯数据中心TOR层级交换机NETCONF技术白皮书\n2、NETCONF技术\n2.2、NETCONF基本概念\n2.2.2、NETCONF相关概念\n表1、XML编码格式头文件元素说明表\n| 元素 | 说明 |\n|:-|:-|\n| <?： | 表示一条指令的开始。 |\n| xml： | 表示此文件是XML文件。 |\n| version： | NETCONF协议版本号。"1.0"表示使用XML1.0标准版本。 |\n| encoding： | 字符集编码格式，当前仅支持UTF-8编码。 |\n| ?>： | 表示一条指令的结束。 |\n', 'id': '1121294_7'}
{'label': 'table', 'content': '中兴通讯数据中心TOR层级交换机NETCONF技术白皮书\n2、NETCONF技术\n2.2、NETCONF基本概念\n2.2.2、NETCONF相关概念\nNETCONF协议使用RPC通信模式，采用XML编码的<rpc>和<rpc-reply>元素提供独立于传输层协议的请求和回应消息框架，一些常用的RPC元素如表2所示。\n表2、XML编码rpc元素说明表\n| 元素 | 说明 |\n|:-|:-|\n| <rpc> | <rpc>元素用来封装Client发送给NETCONF Server端的请求。 |\n| <rpc-reply> | <rpc-reply>元素用来封装<rpc>请求的应答消息，NETCONF Server给每个<rpc>操作回应一个使用<rpc-reply>元素封装的应答消息。 |\n| <rpc-error> | 在处理<rpc>请求过程中，如果发生任何错误，则在<rpc-reply>元素内只封装<rpc-error>元素返回给Client。 |\n| <ok> | 在处理<rpc>请求过程中，如果没有发生任何错误，则在<rpc-reply>元素内封装一个<ok>元素返回给Client。 |\n', 'id': '1121294_8'}
{'label': 'text', 'content': '中兴通讯数据中心TOR层级交换机NETCONF技术白皮书\n2、NETCONF技术\n2.2、NETCONF基本概念\n2.2.2、NETCONF相关概念\n能力集（Capability）是一组基于NETCONF协议实现的基础功能和扩展功能的集合。设备可以通过能力集增加协议操作，扩展已有配置对象的操作范围。\n每个能力使用一个唯一的URI(Uniform Resource Identifier)进行标识。\n', 'id': '1121294_9'}
{'label': 'table', 'content': '中兴通讯数据中心TOR层级交换机NETCONF技术白皮书\n2、NETCONF技术\n2.2、NETCONF基本概念\n2.2.2、NETCONF相关概念\n配置数据库是关于设备的一套完整的配置参数的集合。NETCONF协议定义的配置数据库如表3所示：\n表3、NETCONF协议配置库说明表\n| 配置数据库 | 说明 |\n|:-|:-|\n| <running/> | 此数据库存放当前设备上运行的生效配置、状态信息和统计信息等。除非NETCONF Server支持candidate能力，否则<running/>是唯一强制要求支持的标准数据库。如果设备要支持对该数据库进行修改操作，必须支持writable-running能力。 |\n| <candidate/> | 此数据库存放设备将要运行的配置数据。管理员可以在<candidate/>配置数据库上进行操作，对<candidate/>数据库的任何改变不会直接影响网络设备。设备支持此数据库，必须支持candidate能力。 |\n| <startup/> | 此数据库存放设备启动时所加载的配置数据，相当于已保存的配置文件。设备支持此数据库，必须支持Distinct Startup能力。 |\n', 'id': '1121294_10'}
{'label': 'text', 'content': '中兴通讯数据中心TOR层级交换机NETCONF技术白皮书\n2、NETCONF技术\n2.3、NETCONF的优势\nNETCONF的一个重要特点是NETCONF协议实现可以直接使用设备已有的功能模块，这样降低了NETCONF协议的开发成本，而且随时可以使用设备未来将会支持的新特性。NETCONF可以让Client发现Server支持的扩展功能集合，这些协议能力允许Client调整自己来充分利用设备提供的功能。\nNETCONF协议与SNMP协议对比情况如表4所示\n表4、NETCONF协议与SNMP协议对比表\n| 协议名称 | 协议名称 | NETCONF | SNMP |\n|:-|:-|:-|:-|\n| 标准化程度 | 标准化程度 | 高 | 高 |\n| 用途 | 监控能力 | 中 | 强 |\n| 用途 | 配置能力 | 强 | 弱 |\n| 传输方式 | SSH | 支持 | 不支持 |\n| 传输方式 | SOAP | 支持 | 不支持 |\n| 传输方式 | UDP | 不支持 | 支持 |\n| 配置方式 | 配置方式 | 多 | 少 |\n| 建模能力 | 建模能力 | 强 | 弱 |\n| 并发控制 | 并发控制 | 支持 | 不支持 |\n| 事务处理 | 事务处理 | 支持 | 不支持 |\n| 安全性 | 安全性 | 高 | 中 |\n| 易开发性 | 易开发性 | 高 | 中 |\n| 可扩展性 | 可扩展性 | 高 | 中 |\n', 'id': '1121294_11'}
{'label': 'text', 'content': '中兴通讯数据中心TOR层级交换机NETCONF技术白皮书\n3、NETCONF原理介绍\n3.1、NETCONF协议框架\n和OSI协议模型一样，NETCONF协议也采用了分层结构。每层分别对协议的某一方面进行包装，并向上层提供相关服务。 \n分层结构使每层只关注协议的一个方面，实现起来更简单，同时使各层之间的依赖、内部实现的变更对其他层的影响降到最低。NETCONF协议划分为4层如图2所示： \n1、内容层表示的是被管对象的集合，编码为UTF-8。内容层的内容需要来自数据模型中，到目前为止，没有标准的NETCONF数据建模语言和数据模型。YANG模型作为主流推荐使用，后文详细描述。 \n2、操作层定义了一系列在RPC中应用的基本的原语操作集，这些操作将组成NETCONF的基本能力。NETCONF全面地定义了九种基础操作，功能主要包括三个方面取值操作、配置操作、锁操作和会话操作，其中get、get- config用来对设备进行取值操作，而edit- config、copy- config、delete- config则是用于配置设备参数，lock和unlock 则是在对设备进行操作时为防止并发产生混乱的锁行为，close- session和kill- session则是相对比较上层的操作，用于结束一个会话操作。详见RFC 6241 (7. Protocol Operations) \n3、RPC层为RPC模块的编码提供了一个简单的、传输协议无关的机制。通过使用\xa0RPC和RPC-reply\xa0元素对NETCONF协议的客户端和服务器端的请求和响应数据（即操作层和内容层的内容）进行封装，正常情况下元素封装客户端所需的数据或配置成功的提示信息，当客户端请求报文存在错误或服务器端处理不成功时，服务器端在\xa0元素中会封装一个包含详细错误信息的元素来反馈给客户端。 \n4、承载层，NETCONF制定了RFC4742、RFC4743 和RFC4744，分别给出了向传输协议SSH、SOAP和BEEP映射的实现方案。这些安全协议通过加密和认证等方法来保证网络连接的安全性。', 'id': '1121294_12'}
{'label': 'text', 'content': '中兴通讯数据中心TOR层级交换机NETCONF技术白皮书\n3、NETCONF原理介绍\n3.2、NETCONF报文格式\nNETCONF报文由三部分组成： \nMessage：消息层，为RPC报文提供一个简单的，独立的传输帧机制。客户端把RPC请求封装在一个<rpc>元素内，服务器把请求处理的结果封装在<rpc-reply>元素内，回应给客户。 \nOperations：操作层，定义了一组基本NETCONF协议操作，被带有基于XML编码参数的RPC方法调用。 \nContent：管理对象层，定义了配置数据模型，目前主流的数据模型有Schema模型，YANG模型等。 \n一个完整的NETCONF报文如图3所示：', 'id': '1121294_13'}
{'label': 'text', 'content': '中兴通讯数据中心TOR层级交换机NETCONF技术白皮书\n3、NETCONF原理介绍\n3.3、NETCONF能力集交互\nNETCONF会话一旦建立，Client和Server端会立即向对端发送Hello消息（含有本端支持的能力集列表<hello>元素），通告各自支持的能力集。这样双方就能利用共同支持的能力实现特定的管理功能。 \n能力集协商结果，对于标准能力集（除Notification外），以Server支持的能力集为协商结果，对于扩展能力集，以双方支持的能力集交集为协商结果。', 'id': '1121294_14'}
{'label': 'text', 'content': '中兴通讯数据中心TOR层级交换机NETCONF技术白皮书\n4、YANG模型介绍\n4.1、YANG基本概念\nYANG是NETCONF协议的一种建模语言。YANG模型定义了数据的层次化结构，可用于基于NETCONF的操作，包括配置、状态数据、远程过程调用和通知。可以对NETCONF客户端和服务器端之间发送的所有数据进行一个完整的描述。', 'id': '1121294_15'}
{'label': 'text', 'content': '中兴通讯数据中心TOR层级交换机NETCONF技术白皮书\n4、YANG模型介绍\n4.2、YANG模型特性\n1、建模XML格式数据并由控制器元素提供功能：具有自己的语法格式，可以无差地转化为XML格式，同时通过yangtools\xa0plugin可以生成相应的java接口、类及方法等，为OpenDaylight内部数据（控制器元素）处理编程提供了便利。 \n2、定义语义元素和他们的关系，模拟所有的元素作为一个系统，YANG模型是一种树形结构的建模语言，通过YANG模型本身的语法和语义关系可以看出其定义方式的灵活性。 \n3、YANG数据模型的XML特性提供了一种自表述数据的方式，控制器元素和采用控制器北向接口API的应用可以以一种原生格式与数据模型一起调用。 \n4、利用一种模式语言简化控制器元素和应用的开发。模块中提供功能的开发者可以定义一个模型，从而可以创建对于所提供功能的更简单的、数据类型的API。因此降低了通过服务抽象层提供的数据结构的错误交互。', 'id': '1121294_16'}
{'label': 'table', 'content': '中兴通讯数据中心TOR层级交换机NETCONF技术白皮书\n4、YANG模型介绍\n4.3、NETCONF基本操作（YANG）\nNETCONF对设备所具备的基本操作能力如表5所示\n表5、NETCONF基本操作能力表\n| RPC名称 | 说明 | 备注 |\n|:-|:-|:-|\n| get-config操作 | 用于获取网元配置数据 | |\n| edit-config操作 | 对配置数据进行创建、修改、删除等操作，操作结点上的operatrion字段可以指定细粒度操作行为，有create、merge、delete、remove、replace几种。 | create：创建一个已存在的实例是报错，建议使用merge。delete：删除不存在的实例报错，建议使用remove。replace：配置替换，建议谨慎使用或不用。因为配置数据的关联性，该操作的结果可能不符合预期。 |\n| lock操作 | 排他锁，防止其他用户修改网元配置 | |\n| unlock操作 | 解锁 | |\n| get操作 | 获取网元配置和状态数据 | |\n| close-session操作 | 优雅关闭当前netconf链接 | |\n| kill-session操作 | 强制关闭指定的其他netconf链接 | |\n| commit操作 | 使用edit-config修改candidate库后，用该rpc提交到running | |\n| discard-changes操作 | 作废candidate库中未提交的配置修改 | |\n| 自定义rpc | 根据YANG1.0/1.1语法在YANG模型中用rpc语句定义的RPC | |\n| action操作 | 私有扩展RPC，用于动作类请求，和自定义RPC类似，没有返回信息。 | |\n| get-next私有操作 | 是对get的私有扩展，用于获取大量数据，每次应答返回一部分结果。多次get-next调用直到返回所有数据，或调用release-resource终止。 | |\n| release-resource操作 | 和get-next结合使用 | |\n', 'id': '1121294_17'}
{'label': 'text', 'content': '中兴通讯数据中心TOR层级交换机NETCONF技术白皮书\n4、YANG模型介绍\n4.4、NETCONF标准能力集（YANG）\nNETCONF能力集是补充基本NETCONF规范的一组功能。 该能力由统一资源标识符（URI）标识。 \n可以命名一个或多个依赖的能力集。为了支持一种能力集，服务器必须支持它所依赖的任何能力集。 \nNETCONF包含的标准能力集如下： \n1、Writable-running能力：指明设备支持对配置数据库的写能力，即设备支持对运行的配置进行和操作。 \n2、Candidate Configureation能力：指明设备支持备用配置数据库。 \n3、Confirmed Commit能力：指明设备支持协议操作携带参数和。 \n4、Rollback on Error能力：指明设备具备错误回滚能力。 \n5、Distinct Startup能力：指明设备具备独立启动的能力，即设备能够区分运行数据库和启动数据库。 \n6、Notification能力：指明设备可以通过NETCONF协议的Notification能力向客户端上报告警和事件，以便客户端及时感知设备配置等的变更。 \n同时NETCONF还具备如下的拓展能力集： \n1、Action能力：指明设备具备处理操作能力。 \n2、Execute CLI能力：指明设备在处理请求过程中，能与发送端进行交互。 \n3、Discard Commit能力：用来取消或终止正在执行的confirmed-commit操作。', 'id': '1121294_18'}
{'label': 'text', 'content': '中兴通讯数据中心TOR层级交换机NETCONF技术白皮书\n5、NETCONF 应用场景\nNETCONF采用Manager/Agent的结构，典型应用场景如图4所示。 \n由Manager端（网管）向Agent端（主机）通过具体的承载层发起会话连接，在承载层建链并验证。 \nManager端（网管）和Agent端（主机）双方互发Hello报文，双方在收到对方的Hello报文后，建立NETCONF会话。 \n会话建立后，Manager端（网管）可以使用协议操作向Agent端（主机）下发配置或获取信息。', 'id': '1121294_19'}
{'label': 'text', 'content': '中兴通讯数据中心TOR层级交换机NETCONF技术白皮书\n6、NETCONF接口实例\n6.1、模型结构\n6.1.1、zxr10-vlan\n子接口VLAN业务实例配置在/vlan:configuration分支下，包括子接口上配置单层、双层的range类型和非range类型的VLAN实例的创建与删除，子接口上的PID-TAG、广播使能标记等内容的创建与删除。目前没有状态统计信息和操作类定义。\n| module: zxr10-vlan +-rw configuration +-rw vlan +-rw vlan-tpid | +-rw vlan-pidtag* [if-name] | +-rw if-name ncd:ifname | +-rw external-tpid? enumeration | +-rw internal-tpid? enumeration +-rw vlan-dynamic-encapsulation | +-rw any-other-vlan* [if-name] {any-other-vlan}? | | +-rw if-name ncd:ifname | | +-rw vlan-anyother enumeration | +-rw user-dynamic-vlan* [if-name] {user-dynamic-vlan}? | +-rw if-name ncd:ifname | +-rw dynamic-flag enumeration +-rw vlan-encapsulation | +-rw vlan-dot1q* [if-name] | | +-rw if-name ncd:ifname | | +-rw vlan uint16 | +-rw vlan-do1q-range* [if-name vlan] | | +-rw if-name ncd:ifname | | +-rw vlan ncd:single-range | +-rw vlan-qinq* [if-name] | | +-rw if-name ncd:ifname | | +-rw inter-vlan uint16 | | +-rw exter-vlan uint16 | +-rw vlan-qinq-range* [if-name inter-range-vlan exter-range-vlan] | | +-rw if-name ncd:ifname | | +-rw inter-range-vlan ncd:single-range | | +-rw exter-range-vlan ncd:single-range | +-rw vlan-untag* [if-name] {vlan-untag}? | | +-rw if-name ncd:ifname | | +-rw untag empty | +-rw vlan-qinq-inter-any* [if-name exter-range-vlan] | | +-rw if-name ncd:ifname | | +-rw exter-range-vlan ncd:single-range | +-rw vlan-qinq-exter-any* [if-name inter-range-vlan] | | +-rw if-name ncd:ifname | | +-rw inter-range-vlan ncd:single-range | +-rw vlan-nnis | +-rw vlan-nni* [if-name] | +-rw if-name ncd:ifname | +-rw nni-vlan uint16 | +-rw leaf-vlan? uint16 +-rw vlan-range-brdcst-flag | +-rw vlan-range-brdcst* [if-name] | +-rw if-name ncd:ifname | +-rw range-enable? enumeration | +-rw single-layer-enable? enumeration +-rw alarm-threshold {alarm-threshold}? | +-rw alarm-threshold* [board-name] | +-rw board-name ncd:board | +-rw ratio uint32 ..... |\n|-|\n', 'id': '1121294_20'}
{'label': 'text', 'content': '中兴通讯数据中心TOR层级交换机NETCONF技术白皮书\n6、NETCONF接口实例\n6.2、通用配置\n6.2.1、数据模型\nXPATH：/vlan:configuration/vlan/vlan-encapsulation/vlan-dot1q\n', 'id': '1121294_21'}
{'label': 'table', 'content': '中兴通讯数据中心TOR层级交换机NETCONF技术白皮书\n6、NETCONF接口实例\n6.2、通用配置\n6.2.1、数据模型\n表6、VLAN dot1q配置表\n| 结点 | 类型 | 必选 | 取值范围 | 说明 | 备注 |\n|:-|:-|:-|:-|:-|:-|\n| if-name | 字符串 | 是 | 长度1..31 | 封装DOT1Q 类型VLAN信息的子接口名 | |\n| vlan | 整型 | 是 | 1..4094 | 子接口封装DOT1Q 类型VLAN 配置信息 | |\n', 'id': '1121294_22'}
{'label': 'text', 'content': '中兴通讯数据中心TOR层级交换机NETCONF技术白皮书\n6、NETCONF接口实例\n6.2、通用配置\n6.2.1、数据模型\nXPATH：/vlan:configuration/vlan/vlan-encapsulation/vlan-qinq\n', 'id': '1121294_23'}
{'label': 'table', 'content': '中兴通讯数据中心TOR层级交换机NETCONF技术白皮书\n6、NETCONF接口实例\n6.2、通用配置\n6.2.1、数据模型\n表7、VLAN QinQ配置表\n| 结点 | 类型 | 必选 | 取值范围 | 说明 | 备注 |\n|:-|:-|:-|:-|:-|:-|\n| if-name | 字符串 | 是 | 长度1..31 | 封装QinQ 类型VLAN信息的子接口名 | |\n| Inter-vlan | 整型 | 是 | 1..4094 | 子接口封装QinQ 类型VLAN,内层VLAN配置信息 | |\n| exter-vlan | 整型 | 是 | 1..4094 | 子接口封装QinQ 类型VLAN,外层VLAN配置信息 | |\n', 'id': '1121294_24'}
{'label': 'text', 'content': '中兴通讯数据中心TOR层级交换机NETCONF技术白皮书\n6、NETCONF接口实例\n6.2、通用配置\n6.2.1、数据模型\nXPATH：/vlan:configuration/vlan/vlan-encapsulation/vlan-dot1q-range\n', 'id': '1121294_25'}
{'label': 'table', 'content': '中兴通讯数据中心TOR层级交换机NETCONF技术白皮书\n6、NETCONF接口实例\n6.2、通用配置\n6.2.1、数据模型\n表8、dot1q-range配置表\n| 结点 | 类型 | 必选 | 取值范围 | 说明 | 备注 |\n|:-|:-|:-|:-|:-|:-|\n| if-name | 字符串 | 是 | 长度1..31 | 封装DOT1Q range类型VLAN信息的子接口名 | |\n| vlan | 字符串：single-range | 是 | 表示1..4094范围内的VLAN段的字符串 | 子接口封装DOT1Q range类型VLAN 配置信息 | |\n', 'id': '1121294_26'}
{'label': 'text', 'content': '中兴通讯数据中心TOR层级交换机NETCONF技术白皮书\n6、NETCONF接口实例\n6.2、通用配置\n6.2.2、报文实例\nedit-config：merge\n创建一个子接口上的dot1q类型的VLAN。也可以通过merge操作来修改已存在的子接口上的VLAN ID值，方法相同。\n', 'id': '1121294_27'}
{'label': 'table', 'content': '中兴通讯数据中心TOR层级交换机NETCONF技术白皮书\n6、NETCONF接口实例\n6.2、通用配置\n6.2.2、报文实例\n表9、创建dot1q的XPATH路径\n| 操作 | XPATH |\n|:-|:-|\n| edit-config | /vlan:configuration/vlan/vlan-encapsulation/vlan-dot1q |\n', 'id': '1121294_28'}
{'label': 'text', 'content': '中兴通讯数据中心TOR层级交换机NETCONF技术白皮书\n6、NETCONF接口实例\n6.2、通用配置\n6.2.2、报文实例\n请求示例：\n', 'id': '1121294_29'}
{'label': 'table', 'content': '中兴通讯数据中心TOR层级交换机NETCONF技术白皮书\n6、NETCONF接口实例\n6.2、通用配置\n6.2.2、报文实例\n| <edit-config xmlns="urn:ietf:params:xml:ns:netconf:base:1.0"> <target> <running/> </target> <default-operation>merge</default-operation> <error-option>rollback-on-error</error-option> <config> <vlan:configuration xmlns:vlan="http://www.zte.com.cn/zxr10/netconf/schema/rosng/vlan"> <vlan:vlan> <vlan:vlan-encapsulation> <vlan:vlan-dot1q> <vlan:if-name>fei-0/1/0/1.1</vlan:if-name> <vlan:vlan>3</vlan:vlan> </vlan:vlan-dot1q> </vlan:vlan-encapsulation> </vlan:vlan> </vlan:configuration> </config></edit-config> |\n|-|\n', 'id': '1121294_30'}
{'label': 'text', 'content': '中兴通讯数据中心TOR层级交换机NETCONF技术白皮书\n6、NETCONF接口实例\n6.2、通用配置\n6.2.2、报文实例\n响应示例：\n', 'id': '1121294_31'}
{'label': 'table', 'content': '中兴通讯数据中心TOR层级交换机NETCONF技术白皮书\n6、NETCONF接口实例\n6.2、通用配置\n6.2.2、报文实例\n| <?xml version="1.0" encoding="utf-8"?><rpc-reply xmlns="urn:ietf:params:xml:ns:netconf:base:1.0" message-id="100"> <ok/></rpc-reply> |\n|-|\n', 'id': '1121294_32'}
{'label': 'text', 'content': '中兴通讯数据中心TOR层级交换机NETCONF技术白皮书\n6、NETCONF接口实例\n6.2、通用配置\n6.2.2、报文实例\nedit-config：remove\n删除子接口上的dot1q配置实例，该删除报文并不会删除子接口。\n', 'id': '1121294_33'}
{'label': 'table', 'content': '中兴通讯数据中心TOR层级交换机NETCONF技术白皮书\n6、NETCONF接口实例\n6.2、通用配置\n6.2.2、报文实例\n表10、删除dot1q的XPATH路径\n| 操作 | XPATH |\n|:-|:-|\n| edit-config | /vlan:configuration/vlan/vlan-encapsulation/vlan-dot1q |\n', 'id': '1121294_34'}
{'label': 'text', 'content': '中兴通讯数据中心TOR层级交换机NETCONF技术白皮书\n6、NETCONF接口实例\n6.2、通用配置\n6.2.2、报文实例\n请求示例：\n', 'id': '1121294_35'}
{'label': 'table', 'content': '中兴通讯数据中心TOR层级交换机NETCONF技术白皮书\n6、NETCONF接口实例\n6.2、通用配置\n6.2.2、报文实例\n| <edit-config xmlns="urn:ietf:params:xml:ns:netconf:base:1.0"> <target> <running/> </target> <default-operation>merge</default-operation> <error-option>rollback-on-error</error-option> <config> <vlan:configuration xmlns:vlan="http://www.zte.com.cn/zxr10/netconf/schema/rosng/vlan"> <vlan:vlan> <vlan:vlan-encapsulation> <vlan:vlan-dot1q operation="remove"> <vlan:if-name>fei-0/1/0/1.1</vlan:if-name> <vlan:vlan>3</vlan:vlan> </vlan:vlan-dot1q> </vlan:vlan-encapsulation> </vlan:vlan> </vlan:configuration> </config></edit-config> |\n|-|\n', 'id': '1121294_36'}
{'label': 'text', 'content': '中兴通讯数据中心TOR层级交换机NETCONF技术白皮书\n6、NETCONF接口实例\n6.2、通用配置\n6.2.2、报文实例\n响应示例：\n', 'id': '1121294_37'}
{'label': 'table', 'content': '中兴通讯数据中心TOR层级交换机NETCONF技术白皮书\n6、NETCONF接口实例\n6.2、通用配置\n6.2.2、报文实例\n| <?xml version="1.0" encoding="utf-8"?><rpc-reply xmlns="urn:ietf:params:xml:ns:netconf:base:1.0" message-id="100"> <ok/></rpc-reply> |\n|-|\n', 'id': '1121294_38'}
{'label': 'text', 'content': '中兴通讯数据中心TOR层级交换机NETCONF技术白皮书\n6、NETCONF接口实例\n6.2、通用配置\n6.2.2、报文实例\nget-config\n可以获取所有接口或指定接口的dot1q配置，这里给出获取指定接口的dot1q配置的报文。\n', 'id': '1121294_39'}
{'label': 'table', 'content': '中兴通讯数据中心TOR层级交换机NETCONF技术白皮书\n6、NETCONF接口实例\n6.2、通用配置\n6.2.2、报文实例\n表11、获取dot1q信息的XPATH路径\n| 操作 | XPATH |\n|:-|:-|\n| get-config | /l2vpn:configuration/l2vpn/pw-specification/pw-entry |\n', 'id': '1121294_40'}
{'label': 'text', 'content': '中兴通讯数据中心TOR层级交换机NETCONF技术白皮书\n6、NETCONF接口实例\n6.2、通用配置\n6.2.2、报文实例\n请求示例：\n', 'id': '1121294_41'}
{'label': 'table', 'content': '中兴通讯数据中心TOR层级交换机NETCONF技术白皮书\n6、NETCONF接口实例\n6.2、通用配置\n6.2.2、报文实例\n| <?xml version="1.0" encoding="UTF-8"?><rpc xmlns:xc="urn:ietf:params:xml:ns:netconf:base:1.0" message-id="100"> <get> <filter type="subtree"> <vlan:configuration xmlns:vlan="http://www.zte.com.cn/zxr10/netconf/schema/rosng/vlan"> <vlan:vlan> <vlan:vlan-encapsulation> <vlan:vlan-dot1q> <vlan:if-name>fei-0/1/0/1.1</vlan:if-name> </vlan:vlan-dot1q> </vlan:vlan-encapsulation> </vlan:vlan> </vlan:configuration> </filter></get></rpc> |\n|-|\n', 'id': '1121294_42'}
{'label': 'text', 'content': '中兴通讯数据中心TOR层级交换机NETCONF技术白皮书\n6、NETCONF接口实例\n6.2、通用配置\n6.2.2、报文实例\n响应示例：\n', 'id': '1121294_43'}
{'label': 'table', 'content': '中兴通讯数据中心TOR层级交换机NETCONF技术白皮书\n6、NETCONF接口实例\n6.2、通用配置\n6.2.2、报文实例\n| <?xml version="1.0" encoding="utf-8"?><data xmlns="urn:ietf:params:xml:ns:netconf:base:1.0"> <vlan:configuration xmlns:vlan="http://www.zte.com.cn/zxr10/netconf/schema/rosng/vlan"> <vlan:vlan> <vlan:vlan-encapsulation> <vlan:vlan-dot1q> <vlan:if-name>fei-0/1/0/1.1</vlan:if-name> <vlan:vlan>3</vlan:vlan> </vlan:vlan-dot1q> </vlan:vlan-encapsulation> </vlan:vlan> </vlan:configuration></data> |\n|-|\n', 'id': '1121294_44'}
{'label': 'text', 'content': '中兴通讯数据中心TOR层级交换机NETCONF技术白皮书\n7、参考文献\n1、RFC 6241 Network Configuration Protocol (NETCONF) \n2、RFC 6242 Using the NETCONF Protocol over Secure Shell (SSH)', 'id': '1121294_45'}
{'label': 'table', 'content': '中兴通讯数据中心TOR层级交换机NETCONF技术白皮书\n8、缩略语\n| 缩略语 | 术语 | 描述 |\n|:-|:-|:-|\n| NTECONF | Network Configuration Protocol | 网络配置协议 |\n| API | Application Programming Interface | 应用程序编程接口 |\n| NMS | Network Management System | 网络管理系统 |\n| URI | Uniform Resource Identifier | 统一资源标识符 |\n| SNMP | Simple Network Management Protocol | 简单网络管理协议 |\n| XML | Extensible Markup Language | 可扩展标记语言 |\n', 'id': '1121294_46'}
