{'label': 'document', 'content': '中兴通讯SPN网络保护技术白皮书_20200518_CN', 'id': '963752'}
{'label': 'text', 'content': '| 版本 | 日期 | 作者 | 审核者 | 备注 |\n|:-|:-|:-|:-|:-|\n| V0.1 | 2020/05/7 | 温建中 | | 新建未评审初稿 |\n| | | | | |\n| | | | | |\n| | | | | |\n关键词：SPN，保护\n摘要：SPN网络采用分层保护架构，保护体系可分为切片通道层保护、切片分组层保护和客户业务层保护，结合分层保护架构，以及集中式控制器提供的重路由能力，可以实现SPN网络中链路、设备故障的冗余保护，为客户业务提供更高的可靠性。\n', 'id': '963752_1'}
{'label': 'text', 'content': '概述\nSPN网络分层模型\nSPN（Slicing Packet Network）即切片分组网，是PTN技术的进一步演进，SPN兼容PTN，通过引入FlexE能力增强了以太网链路应用的灵活性，并在此基础上引入切片通道层，提供端到端的硬管道技术。在分组转发层面，SPN引入分布式控制面以及集中控制器，支持SR隧道，增加了 分组业务调度的灵活性。SPN的层网络模型如下图所示：', 'id': '963752_2'}
{'label': 'table', 'content': '概述\nSPN保护层次架构\n在上述层网络模型架构下，SPN保护技术也提供了相应的分层保护架构，保护体系可分为切片通道层保护、切片分组层保护和客户业务层保护。SPN网络层次及对应保护技术如下表所示：\n| 网络分层 | 网络子层 | 保护对象 | 保护技术 | 保护触发机制 |\n|:-|:-|:-|:-|:-|\n| 切片通道层 | SPN Channel层 | SPN Channel | APS 1:1/1+1 | SPN Channel OAM |\n| 切片分组层网络传送子层 | 虚段层（VS） | MPLS-TP环网 | 环网保护 | 环网OAM |\n| 切片分组层网络传送子层 | 虚通路层（VP） | MPLS-TP Tunnel | APS 1:1/1+1 | MPLS-TP OAM |\n| 切片分组层网络传送子层 | 虚通路层（VP） | SR-TP | APS 1:1/1+1 | MPLS-TP OAM |\n| 切片分组层网络传送子层 | 虚通路层（VP） | SR-BE | TI-LFA | 端口状态；链路状态(如BFD检测或VS层OAM检测) |\n| 切片分组层网络传送子层 | 虚通道层（VC） | PW | APS 1:1/1+1 | MPLS-TP OAM |\n| 切片分组层网络传送子层 | 虚通道层（VC） | PW | 双归保护 | MPLS-TP OAM |\n| 切片分组层客户业务子层 | VPN业务保护 | L3VPN | VPN FRR | 网络侧隧道OAM或APS保护组状态 |\n| 切片分组层客户业务子层 | 用户侧接入链路 | 以太接入链路 | LAG | 端口状态；链路状态(如BFD检测) |\n', 'id': '963752_3'}
{'label': 'text', 'content': 'SPN保护技术介绍\n切片通道层保护\n切片通道层保护即为SPN Channel线性保护，由工作、保护两条中间P节点采用SE-XC的端到端SPN Channel构成，如下图所示。\n该线性保护支持的保护类型有：\n单向1+1保护：即并发优收的保护机制，A->Z方向和Z->A方向独立进行选收倒换；\n双向1+1保护：即并发优收的保护机制，A->Z方向和Z->A方向同时进行选收倒换，保证双向路径一致；\n双向1：1保护：即单发单收的保护机制，A->Z方向和Z->A方向同时进行发收方向的倒换，保证双向路径一致。1：1保护倒换为返回式，即：工作路径恢复正常后，经过WTR时间（默认为5分钟）会返回到工作路径；\n实际应用中，通常采用双向1+1保护。\n为支持该保护机制，需要在SPN Channel层引入APS协议，该APS协议消息由SPN Channel层的66bit码块承载，其格式如下：\n在该码块中包含一个4字节的APS消息，其格式同G.8031标准，如下：\n', 'id': '963752_4'}
{'label': 'table', 'content': 'SPN保护技术介绍\n切片通道层保护\n| APS1 | APS1 | APS1 | APS1 | APS1 | APS1 | APS1 | APS1 | APS2 | APS2 | APS2 | APS2 | APS2 | APS2 | APS2 | APS2 | APS3 | APS3 | APS3 | APS3 | APS3 | APS3 | APS3 | APS3 | APS4 | APS4 | APS4 | APS4 | APS4 | APS4 | APS4 | APS4 |\n|:-|:-|:-|:-|:-|:-|:-|:-|:-|:-|:-|:-|:-|:-|:-|:-|:-|:-|:-|:-|:-|:-|:-|:-|:-|:-|:-|:-|:-|:-|:-|:-|\n| 8 | 7 | 6 | 5 | 4 | 3 | 2 | 1 | 8 | 7 | 6 | 5 | 4 | 3 | 2 | 1 | 8 | 7 | 6 | 5 | 4 | 3 | 2 | 1 | 8 | 7 | 6 | 5 | 4 | 3 | 2 | 1 |\n| 请求/状态 | 请求/状态 | 请求/状态 | 请求/状态 | 保护类型 | 保护类型 | 保护类型 | 保护类型 | 被请求信号 | 被请求信号 | 被请求信号 | 被请求信号 | 被请求信号 | 被请求信号 | 被请求信号 | 被请求信号 | 被桥接信号 | 被桥接信号 | 被桥接信号 | 被桥接信号 | 被桥接信号 | 被桥接信号 | 被桥接信号 | 被桥接信号 | 预留 | 预留 | 预留 | 预留 | 预留 | 预留 | 预留 | 预留 |\n| | | | | A | B | D | R | | | | | | | | | | | | | | | | | | | | | | | | |\n', 'id': '963752_5'}
{'label': 'text', 'content': 'SPN保护技术介绍\n切片通道层保护\n其中各字段含义详见G.8031。\nSPN Channel线性保护倒换动作的触发条件有：\n倒换控制命令，包括：清除（Clear）、保护锁定（LP）、强制倒换（FS）、人工倒换（MS）、练习（EXER）；\n信号失效（SF）或信号劣化（SD），其中：\nSF检测方式基于SPN Channel OAM的OAM基本码块的CC功能。由于OAM码块发送周期与SPN Channel管道的速率相关，不同速率下CC的检测周期是不同的，以下为各种速率管道的默认CC周期：\n', 'id': '963752_6'}
{'label': 'table', 'content': 'SPN保护技术介绍\n切片通道层保护\n| 速率 | 默认周期 |\n|:-|:-|\n| 5G | 200us（默认） |\n| 10G | 100us（默认） |\n| 25G | 160us（默认） |\n| 50G | 80us（默认） |\n| 100G | 320us（默认） |\n| 200G | 160us（默认） |\n| 400G | 80us（默认） |\n', 'id': '963752_7'}
{'label': 'text', 'content': 'SPN保护技术介绍\n切片通道层保护\n当3.5倍周期没有收到基本OAM码块时判断为产生SF，3.5倍周期收到n（n>=3）个基本OAM码块后消除SF告警。\nSF告警也可根据误码越限来产生，其中误码是根据基本OAM码块中的BIP字段来检测的，此时告警产生门限默认为10–7, ；告警消失门限缺省为10–8 ；\nSD告警是根据基本OAM码块中的BIP字段来检测的，此时告警产生门限缺省为10–9, 告警消失门限建缺省议为10–10 ；\n保护状态变化：等待恢复（WTR）时间到、反向请求（RR）、非返回（DNR）、无请求（NR）；\n', 'id': '963752_8'}
{'label': 'text', 'content': 'SPN保护技术介绍\n切片分组层网络保护\nSPN切片分组层网络保护包括： \n环网保护：MPLS-TP环网保护； \n隧道层保护：MPLS-TP 线性保护, SR-TP 线性保护； \nPW层保护：PW双归保护； \nSR-BE保护：TI-LFA保护。', 'id': '963752_9'}
{'label': 'text', 'content': 'SPN保护技术介绍\n切片分组层网络保护\nMPLS-TP环网保护\nMPLS-TP环网保护主要用于和MPLS-TP线性保护叠加，实现多处故障（主要是接入和汇聚异侧断纤）场景下对MPLS-TP隧道承载的业务的保护。 \nMPLS-TP环网保护在转发层面采用了MPLS标签嵌套技术，将需要保护的MPLS-TP隧道压入到环网Ring LSP标签，多条MPLS-TP隧道共享一条Ring LSP，因此又称为MPLS-TP共享环保护。 \nMPLS-TP共享环网保护采用G.8132所定义的APS协议进行保护倒换控制，实现环网中各节点保护倒换动作的协同，通过TMS层OAM机制实现环网节点和链路的故障检测。 \n在SPN网络中，主要借助控制器重路由能力来实现多处故障场景下的业务恢复，较少用到MPLS-TP环网，此处不再详细介绍，有需要可参见《PTN共享环保护技术白皮书》。', 'id': '963752_10'}
{'label': 'text', 'content': 'SPN保护技术介绍\n切片分组层网络保护\nMPLS-TP线性保护\nMPLS-TP线性保护由工作、保护两条MPSL-TP LSP构成，如下图所示。 \nMPLS-TP线性保护功能符合G.8031标准，支持的保护类型有： \n单向1+1保护：即并发优收的保护机制，A->Z方向和Z->A方向独立进行选收倒换； \n双向1：1保护：即单发单收的保护机制，A->Z方向和Z->A方向同时进行发收方向的倒换，保证双向路径一致。1：1保护倒换为返回式，即：工作路径恢复正常后，经过WTR时间会返回到工作路径（WTR默认为5分钟）； \n在实际应用中，通常采用返回式双向1：1保护。 \nMPLS-TP线性保护倒换动作的触发条件有： \n倒换控制命令，包括：清除（Clear）、保护锁定（LP）、强制倒换（FS）、人工倒换（MS）、练习（EXER）； \n信号失效（SF）或信号劣化（SD），其中： \nSF检测方式：可基于物理层链路检测（可检测PE节点链路），MPLS-TP OAM的CC/CV检测（端到端检测）。基于CC/CV功能检测时，检测周期为3.3ms，连续三次未收到CC/CV报文时认为故障产生。 \nSD检测方式：对于以太网接口，采用基于MAC层FCS的SD检测方式（缺省告警产生门限为10–5）；对于FlexE接口，采用基于切片通道层BIP的SD检测（告警产生门限建缺省议为10–9，告警消失门限缺省为10–10 ）； \n保护状态变化：等待恢复（WTR）时间到、反向请求（RR）、无请求（NR）； \n在MPLS-TP线性保护APS协议消息采用MPLS-TP OAM PDU承载，MPLS-TP OAM采用G-Ach封装（详见G.8113.1），APS协议消息格式见图4，保护倒换机制详见G.8031描述。', 'id': '963752_11'}
{'label': 'text', 'content': 'SPN保护技术介绍\n切片分组层网络保护\nSR-TP线性保护\nSR-TP隧道除了采用严格约束的Adj-SID+path segment标签栈描述转发路径外，其OAM功能沿用了MPLS-TP OAM（G.8113.1）机制，其线性保护功能均沿用了MPLS-TP的机制（G.8031），因此SR-TP与MPLS-TP线性保护的保护方式、APS协议、告警/误码检测机制均完全相同，在此不再重复描述。', 'id': '963752_12'}
{'label': 'text', 'content': 'SPN保护技术介绍\n切片分组层网络保护\nPW双归保护\nPW双归保护用于在L2VPN业务场景下，当业务落地PE节点发生故障时，实现业务恢复。根据《中国移动分组传送网（PTN）设备技术规范》，PW双归保护应采用DNI PW机制，如下图所示。 \n在SPN网络内，对每一条业务配置接入设备（上图中PE1）到双归落地节点（上图中PE2和PE3）的两条PW（上图中PW1和PW2），同时再配置双归节点之间的一条DNI-PW，形成跨机架的PW MC-APS。 \n在双归节点内部，将工作/保护PW、DNI-PW和UNI侧链路三个接入点，看作三点桥，在任一时刻，允许在三点桥的两个点之间建立双向桥接。 \n在正常情况下，双归主节点上业务PW1与Link1进行桥接，双归备节点上业务PW2与Link2进行桥接，PE1上配置PW 1：1（单发单收或单发双收）。 \n在故障情况下，双归节点根据各个接入点的故障状态、APS状态进行决策，将其中的两个点进行桥接，不同的故障场景下，通过三点桥的不同桥接状态来实现业务的保护。 \n为了在双归节点之间协调三点桥的倒换动作，传递倒换相关信息， 需要配置ICCP冗余组，创建ICCP通道，双归节点之间通过ICCP传递本点信息给对端节点。 \n各种故障场景下DNI PW的保护倒换机制，以及DNI PW实现并发、并收等场景方案参见《DNI-PW双归保护技术白皮书》。', 'id': '963752_13'}
{'label': 'text', 'content': 'SPN保护技术介绍\n切片分组层网络保护\nLSP ECMP\n在多链路组网场景下，为实现流量的负载均衡，同时实现部分链路故障情况下的业务保护，可以配置多条同源同宿，但采用不同中间链路的LSP（MPLS-TP或SR-TP），并将这些LSP配置为一个LSP ECMP组。 \nLSP ECMP组有以下特性： \n承载在LSP ECMP组中的业务，可基于IP五元组进行Hash的方式实现逐流的业务负载分担； \nLSP ECMP组中成员采用MPLS-TP/SR-TP OAM实现成员的故障检测，当某个成员故障后，其流量可重新Hash计算后承载到其余正常的成员上； \nLSP ECMP可以配置最小成员数，当整个LSP ECMP组可用成员数小于配置的最小成员数时，认为该LSP ECMP组不可用，需要触发其它保护机制（如VPN FRR）实现业务恢复； \nLSP ECMP组中每个成员可以叠加配置MPLS-TP/SR-TP线性保护；', 'id': '963752_14'}
{'label': 'text', 'content': 'SPN保护技术介绍\n切片分组层网络保护\nSR-TP重路由\nSR-TP重路由有三种模式： \n无保护带恢复方式 \n若未配置线性保护的SR-TP隧道启用了重路由功能，则当该隧道发生故障后，通过控制器重新计算新的SR-TP转发路径并下发到设备。在返回模式下，当原始的SR-TP转发路径恢复正常后，控制器重新下发原始的SR-TP转发路径，业务回到原始路径转发。 \n可恢复1：1方式 \n配置SR-TP线性1：1保护后，可恢复1：1方式的倒换和重路由机制为：仅当工作和保护路径同时故障后，控制器会重新计算一条保护路径，用以替代原有保护路径。具体保护过程示例如下： \n工作路径故障后，倒换到保护； \n保护路径再次故障（即工作和保护同时故障）后，控制器重新计算新的保护路径，并下发设备，修改原有保护路径； \n原始保护路径恢复后，控制器下发修改保护路径，恢复为原始保护路径； \n工作路径恢复后（当前业务在保护路径上），经WTR时间（默认5分钟）后，业务返回到工作路径上，即保护倒换回切； \n永久1：1方式 \n配置SR-TP线性1：1保护后，永久1：1方式的倒换和重路由机制为：工作或保护路径之一故障，均会触发重路由计算，生成新的工作或保护路径，替代原有的工作或保护路径。具体保护过程示例如下： \n工作SR-TP故障后，业务先倒换到保护，同时控制器重新计算一条工作SR-TP下发设备，修改原有SR-TP工作路径。重路由后新的工作SR-TP恢复正常，因此经WTR时间后，业务进行保护倒换回切，回到新的工作路径。若WTR时间未到，而保护路径发生故障，则直接回切； \n保护SR-TP故障，分三种场景： \n保护SR-TP故障时，工作SR-TP正常（未故障，或工作SR-TP虽然发生了故障，但已完成了重路由，并且已回切工作），则此时由控制器重新计算新的保护SR-TP路径，并下发修改保护路径，业务不发生倒换； \n保护SR-TP故障时，工作SR-TP发生过故障，并完成了重路由但保护状态未回切，即仍然处于WTR时间，则此时会立刻发生保护倒换回切，且控制器会重新计算新的保护SR-TP路径，并下发修改保护路径； \n保护SR-TP故障时，工作SR-TP也发生故障，且工作SR-TP未完成重路由，则由控制器同时计算生成新的工作和保护SR-TP隧道，下发替代原有工作/保护SR-TP，业务停留在工作SR-TP隧道（保护路径故障时，无论工作SR-TP是否故障，均不会发生倒换）。 \n在实际应用中，根据客户对业务质量等级的要求，按需采用可恢复1：1或者永久1：1，其中最高业务质量等级采用永久1：1方式。', 'id': '963752_15'}
{'label': 'text', 'content': 'SPN保护技术介绍\n切片分组层网络保护\nSR-BE逃生路径\n通过配置隧道策略，可以在SR-TP隧道故障后，采用SR-BE隧道进行业务逃生，使得设备在脱离控制器情况下也能实现业务恢复。隧道策略配置需要下发到设备，SR-TP故障后切换到SR-BE的隧道策略是SPN设备的行为，而前述SR-TP重路由属于控制器的行为，两者是相互独立的，通常控制器的重路由效率要低于设备通过隧道策略切换到SR-BE逃生路径的效率，因此，在同时配置了隧道策略和重路由的场景下，整体的保护机制为： \nSR-TP工作路径和保护路径同时故障，设备根据隧道策略配置触发切换到SR-BE逃生路径； \n控制器发现SR-TP的工作和保护路径同时故障，也同步开始重路由计算，将计算生成的新的工作/保护SR-TP路径下发到设备； \n设备接收到新的SR-TP工作/保护路径配置，发现工作/保护SR-TP路径OAM正常，根据隧道策略优选SR-TP隧道，即，从SR-BE逃生路径回到重路由后的SR-TP隧道；', 'id': '963752_16'}
{'label': 'text', 'content': 'SPN保护技术介绍\n切片分组层网络保护\nSR-BE保护\nSR-BE隧道标签栈中只有目的节点的Node-SID标签，中间节点根据IGP路由计算结果进行转发，当某链路或切点发生故障时，检测到故障的节点会通过IGP协议通告到域内其它节点，收到该故障通告的节点会重新进行IGP路由计算，由于该IGP的收敛过程涉及到故障通告、重新进行SFP计算等过程，因此业务恢复时间较长（受网络规模、路由条目数量等影响，通常为收敛时间为百毫秒至秒级），若需要更快地恢复业务，可采用Ti-LFA保护。', 'id': '963752_17'}
{'label': 'text', 'content': 'SPN保护技术介绍\n切片分组层网络保护\nSR-BE的Ti-LFA保护\nTi-LFA是用于SR-BE转发路径上链路或节点故障的局部保护方案。其保护机制为： \n针对需要保护的节点和链路，提前计算出在该链路故障情况下，到其它节点的SR-BE隧道的保护路径（优先按节点故障计算），备用路径采用标签栈描述； \n故障发生后，将SR-BE的单层Node-SID标签栈替换为备用路径的标签栈； \n以下图为例，在B节点配置启用B-C链路的Ti-LFA保护后，由于B节点无法区分是B-C链路故障，还是C节点故障，B节点优先计算出当C节点故障后，到其它节点的SR-BE备用路径，比如：到D节点的备用路径为<F,H,D>，到H节点的备用路径为<F,H>，到G节点的备用路径为<F,G>，而到A/E/F节点的路径不经过B-C链路，因此无需计算备用路径。由于按C节点故障，无法计算到C节点的备用路径，因此B节点再按链路故障，计算到C节点的备用路径为<F,G,C>。 \n假设此时A-D的SR-BE隧道，在B-C链路发生故障后，流量到达B节点后，B节点快速切换到提前计算好的备用路径<F,H,D>进行流量转发，实现业务的快速恢复。', 'id': '963752_18'}
{'label': 'text', 'content': 'SPN保护技术介绍\n切片分组层业务保护\nVPN FRR保护\nVPN FRR采用VPN的私网路由快速切换技术，通过预先在L3VPN远端PE中配置指向主用PE和备用PE的主备用路由，并结合PE故障快速检测技术，在L3VPN双归PE的网络中，实现PE节点故障时业务的快速恢复。 \n如下图所示，在A节点预先配置某条目的IP的路由，其主路由下一跳指向D节点，备路由下一跳指向H节点，当A节点检测到D节点故障后，快速切换到预配置的备路由，实现业务快速恢复。 \nVPN FRR保护技术有以下特点： \nVPN FRR是单点、单向的保护倒换，不存在双向协同机制，不需要协议； \nVPN FRR是基于目的IP配置主备路由的，不同目的IP，可以配置不同的主备关系。比如上图中，某些目的IP以D节点为主路由下一跳，另一些目的IP可以采用H节点作为主路由下一跳； \nL3VPN PE之间采用SR-TP/MPLS-TP隧道连接，并通过SR-TP/MPLS-TP OAM机制来判定远端节点故障（当到达同一个PE节点的主备隧道同时故障时，认为远端节点故障）；', 'id': '963752_19'}
{'label': 'text', 'content': 'SPN保护技术介绍\n切片分组层业务保护\nIP FRR和混合FRR\nIP FRR主要用于SPN网络的UNI侧，与VPN FRR类似，同样基于私网路由快速切换技术，通过预先在L3VPN PE配置的UNI侧主用路由和备用路由，并结合UNI侧故障快速检测技术（BFD），实现UNI侧链路故障时业务的快速恢复。 \n如下图所示，在SPN网络落地节点PE1配置了指向外部CE的主用路由，同时到达同一目的IP的备用路由，其下一跳为PE2，从而在PE1上形成主备路由保护。 \n可以看出，IP FRR/混合FRR与VPN FRR保护类似，有以下特点： \nIP FRR/混合FRR是单点、单向的保护倒换，不存在双向协同机制，不需要协议； \nIP FRR/混合FRR是基于目的IP配置主备路由的，不同目的IP，可以配置不同的主备关系。但在UNI侧通常将直连外部CE的端口作为主路由接口； \nL3VPN PE之间可以采用UNI（Native IP）方式连接（结合BFD检测），也可以采用SR-TP/MPLS-TP隧道连接（结合SR-TP/MPLS-TP OAM检测），前者称为IP FRR保护，后者称为混合FRR保护；', 'id': '963752_20'}
{'label': 'text', 'content': 'SPN保护技术介绍\n切片分组层业务保护\nLAG保护\nLAG保护主要用于UNI侧，通过将多条同速率的UNI侧以太网链路捆绑为一个链路聚合组（LAG），实现业务的负载分担，也可以实现某个LAG成员故障场景下的业务保护。 \n对于L2VPN业务，通常采用跨机架的LAG（MC-LAG）配合DNI PW双归保护使用，而对于L3VPN业务，通常采用LAG+IP FRR实现UNI侧链路故障和节点故障场景的保护。详情参照《PTN保护技术白皮书》。', 'id': '963752_21'}
{'label': 'text', 'content': '5G承载场景下的典型保护方案\n南北向流量保护\n5G业务采用HoVPN Over SR-TP的方式部署，通常在骨干汇聚节点进行层次化L3VPN的分层，此时保护方案如下图所示： \n在多处故障场景下，则需要采用SR-TP重路由方案，也可以结合部署SR-BE逃生路径，如下图所示。', 'id': '963752_22'}
{'label': 'text', 'content': '5G承载场景下的典型保护方案\n东西向业务保护\n对于5G东西向业务，根据该业务是同一IGP域内的流量，还是不同IGP域但不跨SPE节点的流量，或者是跨SPE节点的东西向流量，其保护方式有所不同，故障场景如下图所示： \n上图中各种故障场景下，不同的东西向业务流量的保护方式如下： \n表 3 5G东西向业务流量故障保护方式', 'id': '963752_23'}
{'label': 'text', 'content': '总结\n5G时代，无线业务从业务连接灵活性和可靠性等多方面提出了更高要求，同时也对移动承载网络提出了新的要求。相比PTN网络，SPN引入了分布式控制平面和集中式控制平面，为业务的保护和逃生提供了更多可选方案，提升了业务可靠性。', 'id': '963752_24'}
{'label': 'text', 'content': '附录1 参考资料\n《中国移动分组传送网（PTN）设备技术规范》 \n《PTN保护技术白皮书》 \n《PTN共享环保护技术白皮书》 \n《DNI-PW双归保护技术白皮书》', 'id': '963752_25'}
{'label': 'text', 'content': '附录2 术语或缩略语\n| 英文缩写 | 英文全称 | 中文含义 |\n|:-|:-|:-|\n| APS | Automatic Protection Switching | 自动保护倒换 |\n| DNI | Dual Node Interworking | 双节点互连 |\n| ECMP | Equal-Cost Multipath Routing | 等价多路径 |\n| FRR | Fast Reroute | 快速重路由 |\n| ICCP | Inter-Chassis Communication Protocol | 机架间互联协议 |\n| LAG | Link Aggregation Group | 链路聚合组 |\n| WTR | Wait To Restore | 等待恢复 |\n| Ti-LFA | Topology-Independent Loop-free Alternate | 拓扑无关的无环备份路径 |\n| | | |\n深圳市科技南路55号中兴通讯大厦\n邮编: 518057\nWeb: www.zte.com\nTel: +86-755-********\nFax: +86-755-********\n', 'id': '963752_26'}
